<?php

class m_api_tw_main
{
    public function get_list($u, $d)
    {
        $u = glwb($u);
        $d = glwb($d);
        $uid = ints($u['uid']);
        //参数校验
        $rules = [
            ['intzs', $uid, '登录超时，请重新登录'],
        ];
        $ck = check($rules);
        if ($ck !== true) {
            langx('validate');
            return rs(lang($ck));
        }
        $where = ['zt' => 1, 'is_show' => 1, 'type' => 1, 'zt_tw' => 1];
        $list = M()->select('x_xm', 'id,name,sn,image,cid,type,icon_tag,display_commission,display_bb_num,tj_zt,tj_star,zt', $where, ['id','asc']);
        if ($list) {
            $xm_ids = array_column($list, 'id');
            //查询文案信息
            $xm_language = M()->select('x_xm_lang', 'xm_id,lang,name,bm', ['xm_id' => $xm_ids, 'lang' => LANG_SET]);
            if ($xm_language && is_array($xm_language)) {
                $xm_language = array_column($xm_language, null, 'xm_id');
            }
            //用户佣金
            $res_price = D('m_api_xm_price')->get_xm_price($u, ['xm_id' => $xm_ids]);
            if ($res_price['code'] != 1) {
                return rs($res_price['msg']);
            }
            $res_price['data'] = arr_sort_multi($res_price['data'], ['px' => 'desc', 'qian' => 'desc']);
            $res_price = arr_zu($res_price['data'], 'xm_id');
            //查询做单人数
            $bb_counts =  RC('xm_bb_counts');
            $commission = 0;
            //总做单金额
            $dczgjs = RC('xm_dczgjs');
            //查询结算数据截止时间
            $tg_notice = M()->select('x_notice', 'xm_id,MAX(js_rq) as js_rq', M()->_build_where(['xm_id' => $xm_ids, 'type' => x_site::NOTICE_TYPE_BILL]) . ' group by xm_id');
            if ($tg_notice) {
                $tg_notice = array_column($tg_notice, null, 'xm_id');
            }
            foreach ($list as &$item) {
                $commission = bcadd($item['display_commission'] ?? 0, $dczgjs[$item['id']]['all_money'] ?? 0, 2);
                $item['image'] = url2oss($item['image']);
                //合并覆盖多语言数据
                $item = array_merge($item, $xm_language[$item['id']] ?? []);
                //项目价格
                if (!empty($res_price[$item['id']])) {
                    $xm_price = reset($res_price[$item['id']]);
                    $qian = $xm_price['qian'];
                    if ($xm_price['js_fs'] == 2) {
                        $item['qian'] = $qian > 0 ? $qian . '%' : 0;
                    } else {
                        $item['qian'] = $qian > 0 ? '$' . $qian : 0;
                    }
                }
                //合并报备数量数据
                $bb_info = $bb_counts[$item['id']] ?? [];
                $item['left'] = [
                    'label' => lang('报备人数'),
                    'value' => $bb_info['num'] ?? 0,
                    'color' => '',
                ];
                $item['right'] = [
                    'label' => lang('最新数据'),
                    'value' => $tg_notice[$item['id']]['js_rq'] ?? 'xxxx-xx-xx',
                    'color' => '',
                ];
                $item['down'] = [
                    'label' => $commission > 0 ? lang('单次最高') : lang('数据计算中，敬请期待'),
                    'value' => $commission,
                    'color' => '',
                ];
                unset($item['display_commission'], $item['display_bb_num']);
            }
        }
        return rs('ok', 1, $list, ['count' => $re['count']]);
    }
}
