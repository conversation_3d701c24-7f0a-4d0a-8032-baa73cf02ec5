<?php

class m_admin_system_user extends m_base
{
	//设置岗位标识
	public function edit($u, $d)
	{
		$u = glwb($u);
		$d = glwb($d);
		$uid = ints($u['uid']);

		$gwbs_arr = array_x($d['gwbs']);
		$id = ints($d['id']);

		// 参数验证
		$rule = [
			['intz', $uid, '用户ID无效'],
			['intz', $id, '操作ID无效'],
			['shouji', $d['phone'], '手机号码格式错误!'],
		];
		$ck = check($rule);
		if ($ck !== true) {
			return rs($ck);
		}
		$gwbs_str = '';
		//岗位标识验证
		if (!empty($gwbs_arr)) {
			foreach ($gwbs_arr as $gwbs) {
				$gwbs = trim($gwbs);
				if (!in_arr($gwbs, array_keys(x_admin::GWBS))) {
					return rs('岗位标识无效!');
				}
			}
			// 将数组转换为逗号分隔的字符串
			$gwbs_str = implode(',', $gwbs_arr);
		}

		//加锁
		$hcm = 'lock_' . __METHOD__ . '_id_' . $id;
		$suo = lock($hcm, 10);
		if ($suo == 0) {
			return rs('操作频繁，请稍后再试');
		}
		//查询旧数据
		$old = M()->has('jm_admin', ['id' => $id]);
		if (empty($old)) {
			unlock($hcm, $suo);
			return rs('操作ID无效!');
		}
		$up = M()->update('jm_admin', ['gwbs' => $gwbs_str, 'phone' => sm4_mi($d['phone']), 'gxsj' => getsj(), 'gxip' => getip(), 'czuid' => $uid], ['id' => $id], 1);
		unlock($hcm, $suo);
		if ($up) {
			xlog(__METHOD__, "{$uid}设置用户:{$id}岗位标识为:{$gwbs_str}，手机号码为:{$d['phone']}", "set_gwbs_{$id}", 1);
			return rs('更新成功', 1);
		} else {
			return rs('更新失败');
		}
	}

	//账号列表
	public function list_zh($u, $d)
	{
		$u = glwb($u);
		$d = glwb($d);
		$uid = ints($u['uid']);

		$limit = min(ints($d['limit'] ?? 20), 200);
		$page = ints($d['page'], 1);

		$rule = [
			['intzs', $uid, '登录超时，请重新登录'],
			['in', [$limit, [20, 50, 100, 200]], "分页错误"], //按后台的分页的来
		];
		$w = ['zt' => ['1', '2']];
		if (issetx('uid', $d)) { //帐号ID
			$rule[] = ['intz', $d['uid'], '用户UID错误!'];
			$w['id'] = $d['uid'];
		}
		if (issetx('gwbs', $d)) { //岗位标识
			$w['gwbs[FIND_IN_SET]'] = $d['gwbs'];
		}
		$ck = check($rule);
		if ($ck !== true) {
			return rs($ck);
		}

		if (issetx('xm', $d)) { //姓名备注
			$w['xm'] = $d['xm'];
		}

		$zd = 'id,zh,phone,zt,qxz,xm,dldq,dlsj,dlip,tjsj,ids_uid,not_gjr,apply_num,gwbs';
		$order = ['id', 'desc'];
		if (issetx('field', $d) && issetx('order', $d)) {
			$order = [[$d['field'], $d['order']], $order];
		}

		$data = M()->s_page('jm_admin', $zd, $w, $order, $limit, $page);
		if (empty($data['data'])) {
			return rs('暂无数据', 1);
		}
		foreach ($data['data'] as &$v) {
			$v['phone'] = $v['phone'] ? sm4_mi($v['phone'], 2) : '';
			$v['gwbs'] = explode(',', $v['gwbs']);
			if (empty($v['gwbs'])) {
				$v['gwbs_text'] = '';
			} else {
				$v['gwbs_text'] = implode(',', array_map(function ($v) {
					return x_admin::GWBS[$v];
				}, $v['gwbs']));
			}
		}
		return rs('查询完成', 1, $data['data'], ['page' => $page, 'pagesize' => $limit, 'count' => $data['count']]);
	}
}
