[{"name": "markbaker/complex", "version": "1.5.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/MarkBaker/PHPComplex.git", "reference": "c3131244e29c08d44fefb49e0dd35021e9e39dd2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MarkBaker/PHPComplex/zipball/c3131244e29c08d44fefb49e0dd35021e9e39dd2", "reference": "c3131244e29c08d44fefb49e0dd35021e9e39dd2", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^5.6.0|^7.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.5.0", "phpcompatibility/php-compatibility": "^9.0", "phpdocumentor/phpdocumentor": "2.*", "phploc/phploc": "^4.0|^5.0|^6.0|^7.0", "phpmd/phpmd": "2.*", "phpunit/phpunit": "^4.8.35|^5.0|^6.0|^7.0", "sebastian/phpcpd": "2.*", "squizlabs/php_codesniffer": "^3.4.0"}, "time": "2020-08-26T19:47:57+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Complex\\": "classes/src/"}, "files": ["classes/src/functions/abs.php", "classes/src/functions/acos.php", "classes/src/functions/acosh.php", "classes/src/functions/acot.php", "classes/src/functions/acoth.php", "classes/src/functions/acsc.php", "classes/src/functions/acsch.php", "classes/src/functions/argument.php", "classes/src/functions/asec.php", "classes/src/functions/asech.php", "classes/src/functions/asin.php", "classes/src/functions/asinh.php", "classes/src/functions/atan.php", "classes/src/functions/atanh.php", "classes/src/functions/conjugate.php", "classes/src/functions/cos.php", "classes/src/functions/cosh.php", "classes/src/functions/cot.php", "classes/src/functions/coth.php", "classes/src/functions/csc.php", "classes/src/functions/csch.php", "classes/src/functions/exp.php", "classes/src/functions/inverse.php", "classes/src/functions/ln.php", "classes/src/functions/log2.php", "classes/src/functions/log10.php", "classes/src/functions/negative.php", "classes/src/functions/pow.php", "classes/src/functions/rho.php", "classes/src/functions/sec.php", "classes/src/functions/sech.php", "classes/src/functions/sin.php", "classes/src/functions/sinh.php", "classes/src/functions/sqrt.php", "classes/src/functions/tan.php", "classes/src/functions/tanh.php", "classes/src/functions/theta.php", "classes/src/operations/add.php", "classes/src/operations/subtract.php", "classes/src/operations/multiply.php", "classes/src/operations/divideby.php", "classes/src/operations/divideinto.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with complex numbers", "homepage": "https://github.com/MarkBaker/PHPComplex", "keywords": ["complex", "mathematics"]}, {"name": "markbaker/matrix", "version": "1.2.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/MarkBaker/PHPMatrix.git", "reference": "182d44c3b2e3b063468f7481ae3ef71c69dc1409"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MarkBaker/PHPMatrix/zipball/182d44c3b2e3b063468f7481ae3ef71c69dc1409", "reference": "182d44c3b2e3b063468f7481ae3ef71c69dc1409", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^5.6.0|^7.0.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "dev-master", "phpcompatibility/php-compatibility": "dev-master", "phploc/phploc": "^4", "phpmd/phpmd": "dev-master", "phpunit/phpunit": "^5.7|^6.0|7.0", "sebastian/phpcpd": "^3.0", "squizlabs/php_codesniffer": "^3.0@dev"}, "time": "2020-08-28T19:41:55+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Matrix\\": "classes/src/"}, "files": ["classes/src/functions/adjoint.php", "classes/src/functions/antidiagonal.php", "classes/src/functions/cofactors.php", "classes/src/functions/determinant.php", "classes/src/functions/diagonal.php", "classes/src/functions/identity.php", "classes/src/functions/inverse.php", "classes/src/functions/minors.php", "classes/src/functions/trace.php", "classes/src/functions/transpose.php", "classes/src/operations/add.php", "classes/src/operations/directsum.php", "classes/src/operations/subtract.php", "classes/src/operations/multiply.php", "classes/src/operations/divideby.php", "classes/src/operations/divideinto.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with matrices", "homepage": "https://github.com/MarkBaker/PHPMatrix", "keywords": ["mathematics", "matrix", "vector"]}, {"name": "phpoffice/phpspreadsheet", "version": "1.8.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/PHPOffice/PhpSpreadsheet.git", "reference": "0c1346a1956347590b7db09533966307d20cb7cc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPOffice/PhpSpreadsheet/zipball/0c1346a1956347590b7db09533966307d20cb7cc", "reference": "0c1346a1956347590b7db09533966307d20cb7cc", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-ctype": "*", "ext-dom": "*", "ext-fileinfo": "*", "ext-gd": "*", "ext-iconv": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-simplexml": "*", "ext-xml": "*", "ext-xmlreader": "*", "ext-xmlwriter": "*", "ext-zip": "*", "ext-zlib": "*", "markbaker/complex": "^1.4", "markbaker/matrix": "^1.1", "php": "^5.6|^7.0", "psr/simple-cache": "^1.0"}, "require-dev": {"doctrine/instantiator": "^1.0.0", "dompdf/dompdf": "^0.8.0", "friendsofphp/php-cs-fixer": "@stable", "jpgraph/jpgraph": "^4.0", "mpdf/mpdf": "^7.0.0", "phpcompatibility/php-compatibility": "^8.0", "phpunit/phpunit": "^5.7", "squizlabs/php_codesniffer": "^3.3", "tecnickcom/tcpdf": "^6.2"}, "suggest": {"dompdf/dompdf": "Option for rendering PDF with PDF Writer", "jpgraph/jpgraph": "Option for rendering charts, or including charts with PDF or HTML Writers", "mpdf/mpdf": "Option for rendering PDF with PDF Writer", "tecnickcom/tcpdf": "Option for rendering PDF with PDF Writer"}, "time": "2019-07-08T21:21:25+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"PhpOffice\\PhpSpreadsheet\\": "src/PhpSpreadsheet"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1-or-later"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://blog.maartenballiauw.be"}, {"name": "<PERSON>", "homepage": "https://markbakeruk.net"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://rootslabs.net"}], "description": "PHPSpreadsheet - Read, Create and Write Spreadsheet documents in PHP - Spreadsheet engine", "homepage": "https://github.com/PHPOffice/PhpSpreadsheet", "keywords": ["OpenXML", "excel", "gnumeric", "ods", "php", "spreadsheet", "xls", "xlsx"]}, {"name": "psr/simple-cache", "version": "1.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/simple-cache.git", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/simple-cache/zipball/408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.0"}, "time": "2017-10-23T01:57:42+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"]}]