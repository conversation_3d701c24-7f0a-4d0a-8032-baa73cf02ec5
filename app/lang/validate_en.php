<?php

return [
    '登录超时，请重新登录' => 'Login session timed out, please login again',
    '语言错误' => 'language error',
    '邮箱不能为空' => 'email cannot be empty',
    '邮箱格式错误' => 'Invalid email format',
    '登录类型数据错误' => 'login type data error',
    '登录类型不正确' => 'Incorrect login type',
    '昵称不能为空' => 'nickname cannot be empty',
    '昵称长度为2~10个汉字或6-30个字符' => 'nickname must be 2-10 chinese characters or 6-30 alphabet characters',
    '昵称只能为中文或字母，请修改后重试' => 'nickname can only contain chinese or alphabet letters, please modify and try again',
    '昵称包含敏感词，请修改后重试！' => 'nickname contains sensitive words, please modify and try again',
    '密码过于简单，密码必须包含数字、大写字母、小写字母、特殊符号中的任意两种' => 'password is too simple, password must contain at least two of the following:  numbers, uppercase letters, lowercase letters, and special symbols',
    '验证码错误!' => 'verification code error!',
    '验证码不能为空' => 'verification code cannot be empty',
    '验证码格式不正确' => 'Invalid verification code format',
    '密码不能为空' => 'password cannot be empty',
    '新密码不能为空' => 'new password cannot be empty',
    '密码格式不正确，只允许输入字母、数字、特殊字符!' => 'Invalid password format, only letters, numbers, and special characters are allowed!',
    '密码长度不小于8位且不超过20个字符!' => 'password length must be between 8 and 20 characters!',
    '新密码长度不小于8位且不超过20个字符' => 'new password length must be between 8 and 20 characters!',
    '确认新密码不能为空' => 'confirm new password cannot be empty',
    '两次密码不同' => 'the two passwords do not match',
    '新密码格式不正确，只允许输入字母、数字、特殊字符' => 'invalid new password format, only letters, numbers, and special characters are allowed',
    '请完善验证数据!' => 'please complete the verification data!',
    '错误次数过多' => 'Too many failed attempts',
    '邀请码不能为空' => 'invitation code cannot be empty',
    '邀请码格式不正确' => 'Invalid invitation code format',
    '邀请码格式错误，请检查您的邀请码' => 'Invalid invitation code format, please check your invitation code',
    '拉黑标识不能为空' => 'blacklist flag cannot be empty',
    '黑名单类型错误' => 'blacklist type error',

    //发送验证码
    '验证码类型不正确' => 'Invalidverification code type',


    //报备
    '项目ID不能为空' => 'project ID cannot be empty',
    '项目不存在' => 'project does not exist',
    '请填写报备数据' => 'please fill in the report data',
    '数据不存在' => 'data does not exist',
    '报备不存在' => 'report does not exist',
    '报备信息不存在，请刷新页面查看' => 'report information does not exist, please refresh the page to view',
    '字段类型错误' => 'field type error',
    '抱歉，' => 'sorry,',
    '抱歉，第{$I}条报备数据：' => 'sorry, the {$I}th data:',
    '[{$UP_BB_NAME}]为唯一项，不可重复，请检查报备信息，重复内容：{$BS}，重复条数：{$COUNT_VALUE}条，请检查并修改' => '[{$UP_BB_NAME}] is unique, cannot be repeated, please check the report information, duplicate content: {$BS}, duplicate number: {$COUNT_VALUE},please check and modify',
    '开始时间格式错误' => 'Invalidstart time format',
    '结束时间格式错误' => 'Invalid end time format',
    '报备状态错误' => 'report status error',
    '报备类型错误' => 'report type error',
    '状态错误' => 'status error',

    //凭证
    '请填写凭证数据' => 'please fill in the certificate data',

    //内容库
    '内容ID不能为空' => 'content ID cannot be empty',
    '内容名称长度不能超过255个字符' => 'Content name cannot exceed 255 characters',

    //广告位
    '广告位标识错误' => 'Invalid ad label',
    '设备类型不正确' => 'Invalid device type!',
    '版本号不正确' => 'Invalid version number',

    //媒体库
    '媒体数据不存在' => 'media data does not exist',

    //公告
    '公告类型错误' => 'notice type error',

    //协议
    '协议ID错误' => 'protocol ID error',
    '协议ID不能为空' => 'protocol ID cannot be empty',
    '协议标识不能为空' => 'protocol labelcannot be empty',
    '协议不存在' => 'protocol does not exist',

    //文章
    '文章名称错误' => 'article name error',
    '文章ID错误' => 'article ID error',
    '文章分类ID错误' => 'article category ID error',
    '文章状态错误' => 'article status error',
    '文章类型错误' => 'article type error',
    '参数错误' => 'parameter error',
    '文章标识不能为空' => 'article labelcannot be empty',

    #用户钱相关
		#银行卡
        '账号长度不正确' => 'account length error',
        '类型不正确' => 'type error',
        '邮箱格式不正确' => 'Invalid email format',
        '汇款路径代码不正确' => 'Invalid remittance path code',
        '账户类型不正确' => 'Invalid account type',
        '地区不能超过1000个字符' => 'region cannot be more than 1000 characters',
        '城市不能超过1000个字符' => 'city cannot be more than 1000 characters',
        '地址不能超过1000个字符' => 'address cannot be more than 1000 characters',
        '邮编不能超过64个字符' => 'postal cannot be more than 64 characters',
		'结算账户已存在' => 'settlement account already exists',
		'结算账户不存在' => 'settlement account does not exist',
		'删除失败' => 'Failed to delete settlement account',
		'删除成功' => 'settlement account deleted successfully',
        #提现
        '请选择要结算的银行卡' => 'please select the settlement account',
        '请输入结算金额' => 'please enter the settlement amount',
        '结算金额格式错误' => 'Invalid settlement amount format',
        '结算金额必须大于0' => 'settlement amount must be greater than 0',
        '提现记录不存在' => 'withdrawal record does not exist',
];