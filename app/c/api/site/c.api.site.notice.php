<?php

class c_api_site_notice extends c_api_base
{
    //获取公告统计
    public function get_tj()
    {
        $d = re();
        $res = D('m_api_site_notice')->get_tj($this->u, $d);
        ajaxReturn($res);
    }
    //获取公告列表
    public function get_list()
    {
        $d = re();
        $res = D('m_api_site_notice')->get_list($this->u, $d);
        ajaxReturn($res);
    }

    //获取公告详情
    public function get_info()
    {
        $d = re();
        $res = D('m_api_site_notice')->get_info($this->u, $d);
        ajaxReturn($res);
    }

    //获取站内信列表
    public function get_znx_list()
    {
        $d = re();
        $res = D('m_msg_znx')->get_list($this->u, $d);
        ajaxReturn($res);
    }
    
    //获取站内信详情
    public function get_znx_info()
    {
        $d = re();
        $res = D('m_msg_znx')->get_info($this->u, $d);
        ajaxReturn($res);
    }
}

