<?php

class c_api_bb_main extends c_api_base
{
    //报备列表
    public function get_list()
    {
        $res = D('m_api_bb_main')->get_list($this->u, re());
        ajaxReturn($res);
    }

    //报备详情
    public function get_info()
    {
        $res = D('m_api_bb_main')->get_info($this->u, re());
        ajaxReturn($res);
    }

    //报备状态统计
    public function bb_zt_tj()
    {
        $res = D('m_api_bb_main')->bb_zt_tj($this->u, re());
        ajaxReturn($res);
    }

    //添加报备
    public function adds()
    {
        $d = re();
        $d['bb_data'] = rea('bb_data', 1, 2);
        $res = D('m_api_bb_main')->adds($this->u, $d);
        ajaxReturn($res);
    }

    //获取我的报备
    public function get_my_bb()
    {
        $res = D('m_api_bb_main')->get_my_bb($this->u, re());
        ajaxReturn($res);
    }
}