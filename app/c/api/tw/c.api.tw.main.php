<?php

class c_api_tw_main extends c_api_base
{
    //投喂项目列表
    public function list_xm()
    {
        $res = D('m_api_tw_main')->list_xm($this->u, re());
        ajaxReturn($res);
    }

    //项目投喂列表
    public function get_list()
    {
        $res = D('m_api_tw_main')->get_list($this->u, re());
        ajaxReturn($res);
    }

    //领取视频
    public function ling()
    {
        $d = re();
        $d['tw_data'] = rea('tw_data', 1, 2);
        $res = D('m_api_tw_main')->ling($this->u, $d);
        ajaxReturn($res);
    }

    //投喂
    public function user_video_list()
    {
        $res = D('m_api_tw_main')->get_info($this->u, re());
        ajaxReturn($res);
    }
}