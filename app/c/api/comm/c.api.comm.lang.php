<?php

class c_api_comm_lang extends c_api_comm_base
{
    public function __construct()
    {
        parent::__construct();
    }

    //获取语言列表
    public function get_lang_list()
    {
        $this->is_post();
        $re = D('m_comm_lang')->get_lang_list();
        ajaxReturn($re);
    }

    //获取语言
    public function get_lang()
    {
        $re = D('m_comm_lang')->get_lang();
        ajaxReturn($re);
    }

    //切换语言
    public function switch_lang()
    {
        $this->is_post();
        $re = D('m_comm_lang')->switch_lang(re('lang'));
        ajaxReturn($re);
    }
}
