<?php

class c_api_comm_auth extends c_api_comm_base
{

    public function __construct()
    {
        parent::__construct();
    }

    //登录
    public function login()
    {
        $this->is_post();
        $re = D('m_comm_auth')->login(re());
        ajaxReturn($re);
    }

    //退出
    public function logout()
    {
        $re = D('m_comm_auth')->logout($this->u, re());
        ajaxReturn($re);
    }

    //注册
    public function register()
    {
        $this->is_post();
        $d = re();

        $d['is_audit'] = $this->is_audit;
        $d['is_ios_audit'] = $this->is_ios_audit;

        $server = re('server.');
        $d['oaid'] = $server['HTTP_OAID'] ?? '';
        $d['idfa'] = $server['HTTP_IDFA'] ?? '';
        $d['brand_name'] = $this->brand_name;
        $re = D('m_comm_auth')->register($d);
        ajaxReturn($re);
    }

    //发送验证码
    public function send_code()
    {
        $d = re();
        if (in_arr($d['lx'], [5, 6, 7, 8])) {
            $d['uid'] = $this->uid;
        }
        $re = D('m_comm_auth')->send_code($d);
        ajaxReturn($re);
    }
}
