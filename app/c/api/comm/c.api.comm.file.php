<?php

class c_api_comm_file extends c_api_comm_base
{
    public function __construct()
    {
        parent::__construct();
        if(!$this->u || ints($this->u['uid']) <= 0){
            ajaxReturn(rs(lang('登录超时，请重新登录')));
        }
    }

    public function up_file()
    {
        $this->is_post();
        $uid = ints($this->u['uid']);
        $re = D('m_base')->up_file($uid, re('request.'));
        if($re['code'] == 1){
            $data = [
                'path' => $re['data'],
                'url' => url2oss($re['data']),
            ];
            ajaxReturn(rs('ok!',1,$data));
        }
        $re['msg'] = lang($re['msg']);
        ajaxReturn($re);
    }

    public function get_r2_sign()
    {
        $this->is_post();
        $re = D('m_base')->get_r2_sign($this->u,re());
        if($re['code'] == 1){
            ajaxReturn(rs('ok!',1,$re['data']));
        }
        $re['msg'] = lang($re['msg']);
        ajaxReturn($re);
    }
}