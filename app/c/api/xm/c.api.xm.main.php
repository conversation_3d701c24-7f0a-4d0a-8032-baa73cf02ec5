<?php

class c_api_xm_main extends c_api_base
{
    //登录用户查询的项目列表
    public function get_list()
    {
        $d = re();
        $res = D('m_api_xm_main')->get_list($this->u, $d);
        ajaxReturn($res);
    }

    //游客模式获取项目列表
    public function get_visitor_list()
    {
        $d = re();
        //目前游客模式和普通模式数据无差异，所以直接调用get_list
        $res = D('m_api_xm_main')->get_list(['uid' => x_visitor_uid()], $d);
        ajaxReturn($res);
    }

    //获取项目详情
    public function get_info()
    {
        $d = re();
        $res = D('m_api_xm_main')->get_info($this->u, $d);
        ajaxReturn($res);
    }

    //游客模式获取项目详情
    public function get_visitor_info()
    {
        $d = re();
        $res = D('m_api_xm_main')->get_info(['uid' => x_visitor_uid()], $d);
        ajaxReturn($res);
    }

    //获取项目报备字段
    public function get_bb_zd()
    {
        $d = re();
        $d['lx'] = 11;
        $res = D('m_api_xm_main')->get_zd($this->u, $d);
        ajaxReturn($res);
    }

    //获取项目投喂字段
    public function get_tw_zd()
    {
        $d = re();
        $d['lx'] = 12;
        $res = D('m_api_xm_main')->get_zd($this->u, $d);
        ajaxReturn($res);
    }

    //获取项目凭证字段
    public function get_pz_zd()
    {
        $d = re();
        $d['lx'] = 21;
        $res = D('m_api_xm_main')->get_zd($this->u, $d);
        ajaxReturn($res);
    }
}
