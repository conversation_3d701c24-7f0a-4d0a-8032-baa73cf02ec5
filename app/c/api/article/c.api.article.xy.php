<?php

class c_api_article_xy extends c_api_base
{
    //协议列表
    public function get_list()
    {
        $d = re();
        $re = D('m_api_article_xy')->get_list($this->u, $d);
        ajaxReturn($re);
    }

    //协议列表
    public function get_visitor_list()
    {
        $d = re();
        $re = D('m_api_article_xy')->get_list(['uid'=>x_visitor_uid()], $d);
        ajaxReturn($re);
    }

    //协议详情
    public function get_info()
    {
        $d = re();
        $re = D('m_api_article_xy')->get_info($this->u, $d);
        ajaxReturn($re);
    }

    //协议详情
    public function get_visitor_info()
    {
        $d = re();
        $re = D('m_api_article_xy')->get_info(['uid'=>x_visitor_uid()], $d);
        ajaxReturn($re);
    }

    //检查协议
    public function check_sign()
    {
        $d = re();
        $re = D('m_api_article_xy')->check_sign($this->u, $d);
        ajaxReturn($re);
    }

    //同意协议
    public function agree()
    {
        $d = re();
        $re = D('m_api_article_xy')->agree($this->u, $d);
        ajaxReturn($re);
    }
}
