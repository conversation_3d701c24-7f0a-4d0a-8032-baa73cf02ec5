<?php

class c_api_article_main extends c_api_base
{
    //文章列表
    public function get_list()
    {
        $d = re();
        $re = D('m_api_article_main')->get_list($this->u, $d);
        ajaxReturn($re);
    }

    //文章列表-游客模式
    public function get_visitor_list()
    {
        $d = re();
        $re = D('m_api_article_main')->get_list(['uid'=>x_visitor_uid()], $d);
        ajaxReturn($re);
    }

    //文章详情
    public function get_info()
    {
        $d = re();
        $re = D('m_api_article_main')->get_info($this->u, $d);
        ajaxReturn($re);
    }
}
