<?php

class c_api_base extends c_base
{
    //不需要验证的接口
    private $_no_yz = [
        'api_comm_auth/login',
        'api_comm_auth/register',
        'api_xm_main/get_visitor_list',
        'api_xm_main/get_visitor_info',
        'api_comm_base/get_config'
    ];

    //游客模式
    private $_guest = [
        'api_comm_conf/get_site_info',
        'api_site_ads/get_ads',
        'api_comm_auth/logout',
        'api_comm_auth/send_code',
        'api/xm_nrk/get_list',
        'api/xm_nrk/get_info',
        'api_article_main/get_visitor_list',
        'api_article_xy/get_visitor_list',
        'api_article_xy/get_visitor_info',
        'api_user_team/get_visitor_team_intr',
    ];

    protected $uid = 0;
    protected $u = [];
    protected $app_version = '';
    protected $is_ios_app = 0; //是否ios
    protected $ios_system_version; //ios系统版本
    protected $is_android_app = 0; //是否安卓

    protected $ios_version = '';
    protected $is_android = 0;
    protected $android_version = '';
    //IOS审核
    protected $is_ios_audit = 0; //ios审核
    protected $is_audit = 0;
    protected $brand_name = '';

    public function __construct()
    {
        parent::__construct();
        //验证权限
        $this->_yzqx();
        $this->_set_app_version();
        $this->request_log();
        $re = rea('post.');
    }

    //验证权限
    private function _yzqx($mca = '')
    {

        $mca = $mca ?: implode('/', MCA);
        $mca = strtolower($mca);
        //不校验权限的MCA 直接返回
        if (in_arr($mca, $this->_no_yz)) {
            return true;
        }
        $token = $this->get_jwt_token();
        $jm = jwt_login_check($token, 'api');
//        if (!in_arr($mca, $this->_guest)) {
//            if ($jm == false || isset($jm['exp']) && $jm['exp'] < time()) {
//                ajaxReturn(rs(lang('登录超时，请重新登录'), x_code::API_USER_ERROR));
//            }
//        }
        // 验证
        $re = $jm;
        $uid = isset($jm['data']) ? ints($jm['data']['uid'] ?? 0) : 0;
        $uid = 10006;
        if ($uid) {
            $user_info = m_api_user_main::get_user_info($uid);
            if (empty($user_info) || $user_info['code'] != 1) {
                ajaxReturn(rs($user_info['msg'], x_code::API_USER_ERROR));
            }
            $this->u = $user_info['data'];
            $check_user_zt = m_api_user_main::check_user_zt($this->u);
            if ($check_user_zt['code'] != 1) {
                ajaxReturn(rs(lang('系统检测账号存在风险，已禁止登录'), x_code::API_USER_FORBIDDEN));
            }

            $this->uid = $uid;
            $_ENV['user_id'] = $this->uid;
            if ($this->u) {
                //记录用户token过期时间
                $this->u['token_exp'] = $re['exp'];
            }
        } else {
            if (!in_arr($mca, $this->_guest)) {
                ajaxReturn(rs(lang('登录超时，请重新登录'), x_code::API_USER_ERROR));
            }
        }
    }

    /**
     * 获取app版本号
     */
    private function _set_app_version()
    {
        if (isset($_SERVER['HTTP_VERSIONS'])) {
            $this->app_version = trim($_SERVER['HTTP_VERSIONS']);
        }
        if (isset($_SERVER['HTTP_IOS_APP'])) {
            $this->is_ios_app = ints($_SERVER['HTTP_IOS_APP']);
        }
        if (isset($_SERVER['HTTP_IOS_SYSTEM_VERSION'])) {
            $this->ios_system_version = trim($_SERVER['HTTP_IOS_SYSTEM_VERSION']);
        }
        if (isset($_SERVER['HTTP_IOS_VERSION'])) {
            $this->ios_version = $_SERVER['HTTP_IOS_VERSION'];
        }
        if (isset($_SERVER['HTTP_IS_ANDROID'])) {
            $this->is_android_app = $_SERVER['HTTP_IS_ANDROID'];
        }
        if (isset($_SERVER['HTTP_BRAND_NAME'])) {
            $this->brand_name = $_SERVER['HTTP_BRAND_NAME'];
        }
        //上架期间参数
        if ($this->is_ios_app == 1 && $this->ios_version == x_common::IOS_AUDIT_VERSION) {
            $this->is_ios_audit = 1;
        }
        //上架应用商店配置
        if (RXX('common', 'UP_APPSTORE_VERSION_CONF')) {
            foreach (RXX('common', 'UP_APPSTORE_VERSION_CONF') as $item) {
                if ($this->brand_name == $item['brand'] && $item['version'] == $this->app_version) {
                    if ($this->brand_name == 'vivo') {
                        $this->is_audit = 0;
                        $this->is_ios_audit = 0;
                    } else {
                        $this->is_ios_audit = 1;
                        $this->is_audit = 1;
                    }
                    break;
                }
            }
        }

        if ($this->uid > 0 && $this->uid == RXX('common', 'IOS_AUDIT_USER')) {
            $this->is_ios_audit = 1;
            $this->is_audit = 1;
        }
    }

    /**
     * 请求日志
     */
    protected function request_log()
    {
        $table_name = 'x_req_log_' . date('Y_m');

        if (!file_exists(LOG . $table_name . '.txt')) {
            $sql = "SHOW TABLES LIKE '{$table_name}'";
            $res = M()->query($sql);
            if (empty($res)) {
                $exist = M()->query("CREATE TABLE IF NOT EXISTS `{$table_name}` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` int(10) unsigned NOT NULL DEFAULT '0',
  `dq` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '',
  `ref` varchar(1000) COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '',
  `ua` varchar(1000) COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '',
  `param` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '',
  `tjsj` datetime NOT NULL COMMENT '添加时间',
  `tjip` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT '' COMMENT '添加IP',
  PRIMARY KEY (`id`),
  KEY `idx_uid` (`uid`),
  KEY `idx_path` (`path`),
  KEY `idx_tjip` (`tjip`),
  KEY `idx_tjsj` (`tjsj`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC");
                if (!$exist) {
                    $table_name = 'x_req_log';
                }
            }
            file_put_contents(LOG . $table_name . '.txt', '1');
        }

        $mca = strtolower(implode('/', MCA));
        $data = rea('post.');
        unset($data['s'], $data['c'], $data['password'], $data['phone'], $data['shouji'], $data['a'], $data['mm'], $data['ymm'], $data['sjh'], $data['email'], $data['qq']);
        $ip = getip();
        $insert = [
            "uid" => $this->uid,
            "dq" => ipgsd($ip),
            "param" => je($data),
            "ref" => glwb($_SERVER['HTTP_REFERER']),
            "ua" => glwb($_SERVER['HTTP_USER_AGENT']),
            "path" => $mca,
            "tjsj" => getsj(),
            "tjip" => $ip,
        ];

        M()->insert($table_name, $insert, 1);
    }

    /**
     * 获取jwt token
     */
    protected function get_jwt_token()
    {
        // 'Authorization': `Bearer ${store.JWT}`
        $token = null;
        if (isset($_SERVER['HTTP_AUTHORIZATION'])) {
            $auth = trim($_SERVER['HTTP_AUTHORIZATION']);
            $token = str_replace('Bearer ', '', $auth);
        } else {
            $token = re('token');
        }
        return $token;
    }
}
