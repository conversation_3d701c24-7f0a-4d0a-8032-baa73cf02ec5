<?php

class c_api_user_media extends c_api_base
{
    public function __construct()
    {
        parent::__construct();
    }

    //获取媒体库统计
    public function get_statistics()
    {
        $d = re();
        $res = D('m_api_user_media')->get_statistics($this->u, $d);
        ajaxReturn($res);
    }

    //获取媒体库列表
    public function get_list()
    {
        $d = re();
        $res = D('m_api_user_media')->get_list($this->u, $d);
        ajaxReturn($res);
    }

    //媒体库详情
    public function get_info()
    {
        $d = re();
        $res = D('m_api_user_media')->get_info($this->u, $d);
        ajaxReturn($res);
    }

    //获取媒体库字段模版
    public function get_zd()
    {
        $d = re();
        $res = D('m_api_user_media')->get_zd($this->u, $d);
        ajaxReturn($res);
    }

    //添加媒体库
    public function add_edit()
    {
        $d = re();
        $d['media_data'] = rea('media_data', 1, 10);
        $res = D('m_api_user_media')->add_edit($this->u, $d);
        ajaxReturn($res);
    }

    //删除媒体库
    public function del()
    {
        $d = re();
        $res = D('m_api_user_media')->del($this->u, $d);
        ajaxReturn($res);
    }

    public function set_zt()
    {
        $re = D('m_api_user_media')->set_zt($this->u, re());
        ajaxReturn($re);
    }
}
