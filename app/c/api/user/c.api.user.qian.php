<?php

class c_api_user_qian extends c_api_base
{
    //获取银行卡列表
    public function bank_list()
    {
        $d = re();
        $re = D('m_api_user_qian')->bank_list($this->u, $d);
        ajaxReturn($re);
    }

    //添加银行卡
    public function add_bank()
    {
        $d = re();
        $re = D('m_api_user_qian')->add_bank($this->u, $d);
        ajaxReturn($re);
    }

    //删除银行卡
    public function del_bank()
    {
        $d = re();
        $re = D('m_api_user_qian')->del_bank($this->u, $d);
        ajaxReturn($re);
    }

    //设置默认银行卡
    public function set_default_bank()
    {
        $d = re();
        $re = D('m_api_user_qian')->set_default_bank($this->u, $d);
        ajaxReturn($re);
    }

    //获取银行卡信息
    public function get_bank_info()
    {
        $d = re();
        $re = D('m_api_user_qian')->get_bank_info($this->u, $d);
        ajaxReturn($re);
    }

    public function get_user_tx_tongji()
    {
        $d = re();
        $re = D('m_api_user_qian')->get_user_tx_tongji($this->u, $d);
        ajaxReturn($re);
    }

    //提现记录
    public function withdraw_record()
    {
        $d = re();
        $re = D('m_api_user_qian')->withdraw_record($this->u, $d);
        ajaxReturn($re);
    }

    //提现详情
    public function withdraw_info()
    {
        $d = re();
        $re = D('m_api_user_qian')->withdraw_info($this->u, $d);
        ajaxReturn($re);
    }

    //提现
    public function withdraw()
    {
        $d = re();
        $re = D('m_api_user_qian')->withdraw($this->u, $d);
        ajaxReturn($re);
    }

    //资金明细
    public function money_list()
    {
        $d = re();
        $re = D('m_api_user_qian')->money_list($this->u, $d);
        ajaxReturn($re);
    }
}