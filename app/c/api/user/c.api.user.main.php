<?php

class c_api_user_main extends c_api_base
{
    public function __construct()
    {
        parent::__construct();
    }

    public function get_user_info()
    {
        $uid = $this->uid;
        $re = D('m_api_user_main')->get_user_info($uid);
        ajaxReturn($re);
    }

    //修改密码
    public function edit_pwd()
    {
        $d = re();
        $res = D('m_api_user_main')->edit_pwd($this->u, $d);
        ajaxReturn($res);
    }

    //修改邮箱步骤一
    public function edit_email_step1()
    {
        $d = re();
        $res = D('m_api_user_main')->edit_email_step1($this->u, $d);
        ajaxReturn($res);
    }

    //修改邮箱步骤二
    public function edit_email_step2()
    {
        $d = re();
        $res = D('m_api_user_main')->edit_email_step2($this->u, $d);
        ajaxReturn($res);
    }

    //验证是否可以注销
    public function check_logoff()
    {
        $res = D('m_api_user_main')->check_logoff($this->u);
        ajaxReturn($res);
    }

    //注销用户
    public function logoff()
    {
        $d = re();
        $res = D('m_api_user_main')->logoff($this->u, $d);
        ajaxReturn($res);
    }
}
