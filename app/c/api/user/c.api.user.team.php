<?php

class c_api_user_team extends c_api_base
{
    public function __construct()
    {
        parent::__construct();
    }

    //获取团队信息
    public function get_team_intr()
    {
        $d = re();
        $res = D('m_api_user_team')->get_team_intr($this->u, $d);
        ajaxReturn($res);
    }

    //获取访客团队信息
    public function get_visitor_team_intr()
    {
        $d = re();
        $res = D('m_api_user_team')->get_team_intr(['uid' => x_visitor_uid()], $d);
        ajaxReturn($res);
    }

    //获取海报
    public function get_invite_poster()
    {
        $res = D('m_api_user_team')->get_invite_poster($this->u);
        ajaxReturn($res);
    }
}
