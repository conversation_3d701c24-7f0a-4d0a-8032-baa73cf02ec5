<?php

class c_admin_zd_tpl extends c_admin_base{
    public function __construct(){
        parent::__construct();
    }

    //获取字段模版列表
    public function get_list(){
        $d = re();
        $res = D('m_admin_zd_tpl')->get_list($this->u, $d);
        ajaxReturn($res);
    }

    //添加编辑字段模版
    public function add_edit(){
        $d = re();
        $res = D('m_admin_zd_tpl')->add_edit($this->u, $d);
        ajaxReturn($res);
    }

    //删除字段模版
    public function del(){
        $d = re();
        $res = D('m_admin_zd_tpl')->del($this->u, $d);
        ajaxReturn($res);
    }

    //获取所有字段模版
    public function get_all(){
        $d = re();
        $res = D('m_admin_zd_tpl')->get_all($this->u, $d);
        ajaxReturn($res);
    }
}