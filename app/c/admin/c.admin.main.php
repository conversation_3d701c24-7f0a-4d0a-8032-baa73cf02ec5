<?php

//总管理后台

class c_admin_main extends c_admin_base
{

	//免登陆验证
	protected $no_yz = [
		// 'admin_main/index',
		// 'admin_main/xcode',
		// 'admin_main/send_yzm',
		// 'admin_main/login',
		'admin_main/akf1s4h2jd',
		'admin_main/index',
	];

	//登录后免验证
	protected $login_no_yz = [
		'admin_main/index',
		// 'admin_main/menu',
		// 'admin_main/logout',
		// 'admin_main/edit_pass',
	];

	public function __construct()
	{
		if (BENDI == 1) {
			$this->no_yz = [
				'admin_main/xcode',
				'admin_main/send_yzm',
				'admin_main/login',
				'admin_main/akf1s4h2jd',
				'admin_main/index',
			];
		}
		parent::__construct();
	}

	//后台首页
	public function index()
	{
		include ROOT . 'www/admin_vue/index.html';
		// tiao('http://localhost:5173/#/login');
		// tracex();
		// if($this->uid > 0 || BENDI == 1){
		// 	$this->ui_v = BENDI == 1 ? time() : x_admin::ui_v;
		// 	include show('admin/index');
		// }
		// else{
		// 	_e404();
		// }
	}

	//登录页入口限制
	public function __call($name, $args)
	{
		if ($name == $this->login_path) {
			tracex();
			if ($this->uid > 0) {
				tiao('/admin/');
			} else {
				include ROOT . 'www/admin_vue/index.html';
			}
		} else {
			_e404();
		}
	}



	//发送登录验证码
	public function send_yzm()
	{
		$this->is_post();
		$this->yzm();
		if (BENDI == 1) {
			ar('本地环境固定验证码:666666', 1);
		}
		$re = D('m_admin_main')->send_yzm(re('zh'), re('mm'));
		ajaxReturn($re);
	}

	//登录
	public function login()
	{
		$this->is_post();
		$re = D('m_admin_main')->login(re('post.'));
		ajaxReturn($re);
	}

	//登出
	public function logout()
	{
		$re = D('m_admin_main')->logout();
		if ($re['code'] == 1) {
			tiao("/admin/{$this->login_path}");
		} else {
			_e404();
		}
	}

	//菜单列表
	public function menu()
	{
		if (issetx('jmx_ids_rt_token', $this->u)) {
			$re = D('c_jmx_admin_login')->get_menu(1);
			$jump = '/dashboard/workplace';
		} else {
			$re = D('m_admin_main')->get_zh_nav($this->uid, 'level');
			if (ADMIN_TOKEN_TIME) {
				$jump = '/dashboard/workplace';
			} else {
				$jump = '/';
			}
		}
		array_unshift($re['data'], ['title' => '控制台', 'icon' => 'layui-icon-home', 'jump' => $jump, 'id' => 0]);

		//下面为后台前端组件用例,线上自行屏蔽
		// if(BENDI==1){
		// 	$demo = jd(file_get_contents(ROOT.'www/admin/demo/json/menu.js'));
		// 	$re['data'] = array_merge($re['data'],$demo['data']);
		// }

		ajaxReturn($re);
	}

	//修改密码
	public function edit_pass()
	{
		$this->is_post();
		$this->yzm();
		$_POST['phone'] = $this->u['phone'];
		$re = D('m_admin_zh')->edit_pass($this->uid, re('post.'));
		ajaxReturn($re);
	}




	//日志列表
	public function list_logs()
	{
		if (re('do') == 'config') { //读取参数
			$re = ['code' => 1, 'log_lx' => x_admin::log_lx, 'qx' => has_qx($this->uid, ['req' => 'admin_main/get_logs_req', 'ok' => 'admin_main/ok_logs'])];
		} else {
			$re = D('m_admin_main')->list_logs($this->uid, re());
		}
		ajaxReturn($re);
	}

	//获取日志请求信息
	public function get_logs_req()
	{
		$re = D('m_admin_main')->get_logs_req($this->uid, re());
		ajaxReturn($re);
	}

	//异常处理
	public function ok_logs()
	{
		$re = D('m_admin_main')->ok_logs(rea('id'));
		ajaxReturn($re);
	}


	//回滚日志列表
	public function list_rb_logs()
	{
		if (re('do') == 'config') { //读取参数
			$re = ['code' => 1, 'log_lx' => x_admin::rb_log_lx, 'qx' => has_qx($this->uid, ['req' => 'admin_main/get_rb_logs_req'])];
		} else {
			$re = D('m_admin_main')->list_rb_logs($this->uid, re());
		}
		ajaxReturn($re);
	}

	//获取回滚日志请求信息
	public function get_rb_logs_req()
	{
		$re = D('m_admin_main')->get_rb_logs_req($this->uid, re());
		ajaxReturn($re);
	}


	//获取系统参数列表
	public function list_config()
	{
		if (re('do') == 'config') { //读取参数
			$re = D('m_admin_config')->config_lx_list($this->uid);
			if ($re['code'] == 1) {
				$re['lx'] = $re['data'];
				unset($re['data']);
			}
		} else {
			$req = re();
			if ($req['lx'] == 'jiami_x') ajaxReturn(rs('类型分组参数异常'));
			$re = D('m_admin_config')->list($this->uid, $req);
		}
		ajaxReturn($re);
	}

	//获取单个系统参数
	public function get_config()
	{
		$req = re();
		if ($req['lx'] == 'jiami_x') ajaxReturn(rs('类型分组参数异常'));
		$re = D('m_admin_config')->get($this->uid, $req);
		ajaxReturn($re);
	}

	//添加系统参数
	public function add_config()
	{
		$req = re();
		if ($req['lx'] == 'jiami_x') ajaxReturn(rs('类型分组参数异常'));
		$re = D('m_admin_config')->add($this->uid, $req);
		ajaxReturn($re);
	}

	//修改系统参数
	public function edit_config()
	{
		$req = re();
		if ($req['lx'] == 'jiami_x') ajaxReturn(rs('类型分组参数异常'));
		$re = D('m_admin_config')->edit($this->uid, $req);
		ajaxReturn($re);
	}

	/**
	 * 获取系统隐私参数列表
	 * @return void
	 */
	public function xconfig_list()
	{
		$req       = re();
		$req['lx'] = 'jiami_x';
		$re        = D('m_admin_config')->list($this->uid, $req);
		ajaxReturn($re);
	}

	/**
	 * 获取单个系统隐私参数
	 * @return void
	 */
	public function xconfig_info()
	{
		$req       = re();
		$req['lx'] = 'jiami_x';
		$re        = D('m_admin_config')->get($this->uid, $req);
		ajaxReturn($re);
	}

	/**
	 * 添加系统隐私参数
	 * @return void
	 */
	public function xconfig_add()
	{
		$req       = re();
		$req['lx'] = 'jiami_x';
		$re        = D('m_admin_config')->add($this->uid, $req);
		ajaxReturn($re);
	}

	/**
	 * 修改系统隐私参数
	 * @return void
	 */
	public function xconfig_edit()
	{
		$req       = re();
		$req['lx'] = 'jiami_x';
		$re        = D('m_admin_config')->edit($this->uid, $req);
		ajaxReturn($re);
	}

	/**
	 * 同步检查系统隐私参数
	 * @return void
	 */
	public function xconfig_check()
	{
		$re = D('m_admin_config')->tb_check($this->uid);
		ajaxReturn($re);
	}

	/**
	 * 同步系统隐私参数
	 * @return void
	 */
	public function xconfig_tb()
	{
		$re = D('m_admin_config')->xconfig_tb($this->uid);
		ajaxReturn($re);
	}



	//账号列表
	public function list_zh()
	{
		if (re('do') == 'config') { //读取参数
			$re = ['code' => 1, 'zt' => x_admin::user_zt];
			$qxz = D('m_admin_qx')->list_qxz(['zt' => 1]);
			$re['qxz'] = array_column($qxz['data'], 'name', 'id');
			$re['qx'] = has_qx($this->uid, [
				'view_zh'      => 'admin_main/get_zh',
				'edit_zh_pass' => 'admin_main/reset_zh_pass',
				'del_zh'       => 'admin_main/del_zh',
				'save_zh'      => 'admin_main/save_zh',
				'save_zh_qxz'  => 'admin_main/save_zh_qxz',
				'save_zh_qx'   => 'admin_main/save_zh_qx',
				'switch_zh_zt' => 'admin_main/switch_zh_zt',
			]);
		} else {
			$re = D('m_admin_zh')->list_zh($this->uid, re());
		}
		ajaxReturn($re);
	}


	//获取账号列表
	public function get_zhs()
	{
		$re = D('m_admin_zh')->get_zhs($this->uid, re());
		ajaxReturn($re);
	}

	//切换帐号状态
	public function switch_zh_zt()
	{
		$this->is_post();
		$rt = D('m_admin_zh')->switch_zh_zt($this->uid, re('post.'));
		ajaxReturn($rt);
	}

	//删除账户
	public function del_zh()
	{
		$this->is_post();
		$rt = D('m_admin_zh')->del_zh($this->uid, re2('post.id'));
		ajaxReturn($rt);
	}

	//重置账号密码
	public function reset_zh_pass()
	{
		$this->is_post();
		$rt = D('m_admin_zh')->reset_zh_pass($this->uid, re('post.'));
		ajaxReturn($rt);
	}

	//获取账号用户组列表
	public function list_zh_qxz()
	{
		$list = D('m_admin_qx')->list_qxz(['zt' => 1], 'id,name');
		$data = [
			'code'      => 1,
			'count'     => count($list['data']),
			'data'      => $list['data'],
			'checkedId' => D('m_admin_qx')->get_zh_qxz(re2('id')),
			'pid'       => '0',
		];
		ajaxReturn($data);
	}

	//设置账号用户组
	public function save_zh_qxz()
	{
		$this->is_post();
		$re = D('m_admin_qx')->save_zh_qxz($this->uid, rea('post.'));
		ajaxReturn($re);
	}

	//获取账号权限列表
	public function list_zh_qx()
	{
		list($list, $checkedId, $disabledId) = D('m_admin_qx')->list_zh_qx(re2('uid'));
		$data = [
			'code'       => 1,
			'count'      => count($list),
			'data'       => $list,
			'checkedId'  => $checkedId,
			'disabledId' => $disabledId,
			'pid'        => '0',
		];
		ajaxReturn($data);
	}

	//设置账号权限
	public function save_zh_qx()
	{
		$this->is_post();
		$rt = D('m_admin_qx')->save_zh_qx($this->uid, rea('post.'));
		ajaxReturn($rt);
	}

	//保存账号
	public function save_zh()
	{
		$this->is_post();
		$re = D('m_admin_zh')->save_zh($this->uid, re('post.'));
		ajaxReturn($re);
	}
}
