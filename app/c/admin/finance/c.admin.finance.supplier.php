<?php

class c_admin_finance_supplier extends c_admin_base
{
    //供应商列表
    public function get_list()
    {
        $re = D('m_admin_finance_supplier')->get_list($this->u, re());
        ajaxReturn($re);
    }

    //获取所有供应商
    public function get_all()
    {
        $re = D('m_admin_finance_supplier')->get_all($this->u, re());
        ajaxReturn($re);
    }

    //添加/编辑供应商
    public function add_edit()
    {
        $d = re();
        $d['xm_ids'] = rea('xm_ids', 1, 2);
        $d['xm_uid'] = rea('xm_uid', 1, 2);
        ajaxReturn(D('m_admin_finance_supplier')->add_edit($this->u, $d));
    }

    //修改供应商状态
    public function up_zt()
    {
        ajaxReturn(D('m_admin_finance_supplier')->up_zt($this->u, re()));
    }

    //供应商账号列表
    public function get_account_list()
    {
        $re = D('m_admin_finance_supplier')->get_account_list($this->u, re());
        ajaxReturn($re);
    }

    //添加/编辑供应商账号
    public function add_edit_account()
    {
        $d = re();
        $d['djr'] = rea('djr', 1, 2);
        ajaxReturn(D('m_admin_finance_supplier')->add_edit_account($this->u, $d));
    }

    //修改供应商账号状态
    public function up_account_zt()
    {
        ajaxReturn(D('m_admin_finance_supplier')->up_account_zt($this->u, re()));
    }
}
