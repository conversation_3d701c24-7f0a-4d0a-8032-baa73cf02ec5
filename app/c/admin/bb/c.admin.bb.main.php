<?php

class c_admin_bb_main extends c_admin_base
{
    public function __construct()
    {
        parent::__construct();
    }

    //获取配置
    public function get_bb_config()
    {
        $res = D('m_admin_bb_main')->get_bb_config($this->u);
        ajaxReturn($res);
    }

    //获取报备列表
    public function get_list()
    {
        $d = re();
        $d['sj'] = rea('sj', 1, 2);
        $d['allow_jssj'] = rea('allow_jssj', 1, 2);
        $d['u_created_at'] = rea('u_created_at', 1, 2);
        $res = D('m_admin_bb_main')->get_list($this->u, $d);
        ajaxReturn($res);
    }

    //获取报备详情
    public function get_info()
    {
        $res = D('m_admin_bb_main')->get_info($this->u, re());
        ajaxReturn($res);
    }

    //批量审核报备
    public function batch_audit()
    {
        $d = re();
        $d['ids'] = rea('ids', 1, 2);
        $res = D('m_admin_bb_main')->batch_audit($this->u, $d);
        ajaxReturn($res);
    }

    //导入数据
    public function upload_excel()
    {
        $res = D('m_admin_bb_main')->upload_excel($this->u, re());
        ajaxReturn($res);
    }

    //导出数据
    public function data_export()
    {
        $res = D('m_admin_bb_main')->data_export($this->u, re());
        ajaxReturn($res);
    }
}
