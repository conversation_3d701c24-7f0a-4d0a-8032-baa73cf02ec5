<?php

//admin 基类

class c_admin_base extends c_jmx_admin_base
{

	protected $no_yz = [
		'admin/akf1s4h2jd',
	];      //免登录方法
	protected $login_no_yz = []; //登录后免验证方法
	protected $login_path;

	public function __construct()
	{
		$_ENV['duan'] = 'admin';
		parent::__construct();
		if (BENDI != 1 && (!in_arr(getym(), $this->ids_conf['admin_ym']) || !in_arr(getip(), $this->ids_conf['admin_fwip']))) { //专用域名限制
			_e404('非法请求!', 'x_bug::admin');
		}
		$this->login_path = strtolower(x_admin::admin_path);
		$this->u = x_user('', 'admin'); //用户信息
		$this->uid = x_adminid(); //用户id
		if (issetx('jmx_ids_rt_token', $this->u)) {
			$mca = implode('/', MCA);
			if (!in_arr($mca, $this->login_no_yz)) {
				$jmx = new c_jmx_admin_base();
				$this->u = $jmx->u;
				$this->uid = $jmx->uid;
				$this->data_rule = $jmx->data_rule;
			}
		} else {
			if (BENDI != 1 && !in_arr(getip(), x_admin::admin_fwip)) { //专用访问ip限制
				_e404('非法请求1!', 'x_bug::admin', '0');
			}
			if (ADMIN_TOKEN_TIME && SESSION_AUTO) {
				// 保存会话数据并结束会话
				session_write_close();
			}
			$_ENV['duan'] = 'admin';
			parent::__construct();
			if (BENDI != 1 && !in_arr(getym(), x_admin::admin_ym)) { //专用域名限制
				_e404('非法请求2!', 'x_bug::admin', '0');
			}
			//p($this->u);exit;
			//本地调试 自动登录
			/*if(BENDI == 1){
				$this->uid = 1;
				$this->u = [
					'uid'    => '1',//权限
					'zh' => '18888888888',
					'xm' => 1,
				];
			}*/

			$this->yzqx(); //验证权限
		}
	}

	//验证权限
	public function yzqx($mca = '')
	{
		$this->no_yz[] = "admin_main/" . $this->login_path;
		$mca = $mca ?: implode('/', MCA);
		$mca = strtolower($mca);
		if (!in_arr($mca, $this->no_yz)) {
			//是否登录
			if ($this->uid == 0) {
				ar('登录超时，请重新登录', -1000);
			}
			if (!in_arr($mca, $this->login_no_yz)) {
				if (!D('m_admin_qx')->check_zh_qx($this->uid, $mca)) {
					ar('权限不足!', -1001);
				}
			}
		}
	}

	//输出验证码
	public function xcode()
	{
		yzm('23456789', ['useCurve' => true, 'useNoise' => true, 'bg' => [255, 255, 255], 'fontSize' => 18]);
	}

	//验证码验证
	public function yzm($yzm = "")
	{
		if (BENDI != 1) {
			if ($yzm == "") {
				$yzm = re('yzm');
			}
			if (!is_yzm($yzm)) {
				ar('验证码错误!请重新输入!', -1002);
			}
		}
	}

	//记录请求信息
	public function __destruct(){
		$lx='user';
		if(issetx('duan',$_ENV)){
			$lx=$_ENV['duan'];
		}
		$jilog=USER_LOG;//是否记录用户访问日志
		if($lx=='admin'){
			$jilog=x_admin::user_log;
		}
		if($jilog==1){
			user_log($this->uid,$lx);//请求日志
		}
	}
}
