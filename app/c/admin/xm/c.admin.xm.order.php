<?php

class c_admin_xm_order extends c_admin_base
{
    public function __construct()
    {
        parent::__construct();
    }

    public function get_list()
    {
        $d = re();
        $d['created_at'] = rea('created_at', 1, 2);
        $d['updated_at'] = rea('updated_at', 1, 2);
        $d['js_date'] = rea('js_date', 1, 2);
        $res = D('m_admin_xm_order')->get_list($this->u, $d);
        ajaxReturn($res);
    }

    public function import_order()
    {
        $excelData = rea('excelData', 1, 2);
        $data = re();
        $data['excelData'] = $excelData;
        $data['unset_type'] = $data['unset_type'] ?? 0;
        $type = ints($data['type']);
        // if (BENDI == 0) {
        //     ini_set('memory_limit', '2280M');
        //     set_time_limit(0);
        //     ignore_user_abort(true); // 后台运行
        //     $data['excelData'] = je($data['excelData']);
        //     $re = D('m_rw_import')->save_import_rw($this->u, $data);
        //     ajaxReturn($re);
        //     exit();
        // }
        switch ($type) {
            case 1: // 正常的推广数据导入
                $re = (new \import\import_order_one())->import($data, 1);
                break;
            case 2: // 含凭证的推广数据导入
                $re = (new \import\import_order_two())->import($data, 2);
                break;
            default:
                $re = rs('type类型不正确');
                break;
        }

        ajaxReturn($re);
    }

    /**
     * 批量队列审核通过，需要过滤已经通过审核的数据
     */
    public function queue_change_status_pass()
    {
        $d = re('', '', '1', '1');
        $re = D('m_admin_xm_order')->queue_change_status_pass($this->u, $d);
        ajaxReturn($re);
    }

    /**
     * 批量审核通过校验接口
     */
    public function batch_change_status_check()
    {
        $d = re('', '', '1', '1');
        $re = D('m_admin_xm_order')->batch_change_status_check($this->u, $d);
        ajaxReturn($re);
    }

    //手动处理审核订单
    // public function batch_change_status_pass()
    // {
    //     $d = re();
    //     $re = D('m_admin_xm_order')->batch_change_status_pass($this->u, $d);
    //     ajaxReturn($re);
    // }

    //退款订单
    public function refund_order()
    {
        $d = re('', '', '1', '1');
        $re = D('m_admin_xm_order')->refund_order($this->u, $d);
        ajaxReturn($re);
    }

    public function delete_order()
    {
        $d = re('', '', '1', '1');
        $re = D('m_admin_xm_order')->delete_order($this->u, $d);
        ajaxReturn($re);
    }
}
