<?php

class c_admin_xm_main extends c_admin_base
{
    public function __construct()
    {
        parent::__construct();
    }

    //获取项目配置
    public function get_xm_config()
    {
        $res = D('m_admin_xm_main')->get_xm_config($this->u);
        ajaxReturn($res);
    }

    //获取项目列表
    public function get_list()
    {
        $d = re();
        $res = D('m_admin_xm_main')->get_list($this->u, $d);
        ajaxReturn($res);
    }
    //获取项目列表
    public function get_all()
    {
        $d = re();
        $res = D('m_admin_xm_main')->get_all($this->u, $d);
        ajaxReturn($res);
    }

    //获取项目详情
    public function get_info()
    {
        $d = re();
        $res = D('m_admin_xm_main')->get_info($this->u, $d);
        ajaxReturn($res);
    }

    //添加编辑项目
    public function add_edit()
    {
        $d = re();
        $d['button_nr'] = rea('button_nr', 1, 2);
        $d['tg_types'] = rea('tg_types', 1, 2);
        $d['qd_types'] = rea('qd_types', 1, 2);
        $d['yy_bb_uids'] = rea('yy_bb_uids', 1, 10);
        $d['yy_js_uids'] = rea('yy_js_uids', 1, 10);
        $d['tw_uids'] = rea('tw_uids', 1, 10);
        $d['tags'] = rea('tags', 1, 2);
        $d['xm_price'] = rea('xm_price', 1, 2);
        $d['bb_zd'] = rea('bb_zd', 1, 10);
        $d['pz_zd'] = rea('pz_zd', 1, 10);
        $d['tw_zd'] = rea('tw_zd', 1, 10);
        $d['xm_extra'] = rea('xm_extra', 1, 10);
        $res = D('m_admin_xm_main')->add_edit($this->u, $d);
        ajaxReturn($res);
    }

    public function update_zt()
    {
        $d = re();
        $res = D('m_admin_xm_main')->update_zt($this->u, $d);
        ajaxReturn($res);
    }
}
