<?php
/**
 * 定时任务后台
 *
 * @package path
 * @access  public
 * <AUTHOR> <<EMAIL>>
 * @link    https://
 */
class c_admin_cronrwx extends c_admin_base{

    //获取定时任务列表
    public function list_crontab()
    {
        if(re('do')=='config'){
            $data=['zt'=>D('m_rw_cronrwx')::crontab_zt,'fs'=>D('m_rw_cronrwx')->list_crontabfs(),'code'=>1];
        }else{
            $data=D('m_rw_cronrwx')->list_crontab($this->uid,re());
        }
        ajaxReturn($data);
    }

    //添加/编辑定时任务
    public function edit_crontab()
    {
        $data=D('m_rw_cronrwx')->edit_crontab($this->uid,re());
        ajaxReturn($data);
    }

    //删除定时任务
    public function del_crontab()
    {
        $data=D('m_rw_cronrwx')->del_crontab($this->uid,re());
        ajaxReturn($data);
    }

    //执行定时任务
    public function zx_crontab()
    {
        $data=D('m_rw_cronrwx')->zx_crontab(re());
        ajaxReturn($data);
    }
}