<?php

class c_admin_site_notice extends c_admin_base
{
    // 公告列表
    public function get_list()
    {
        $re = D('m_admin_site_notice')->get_list($this->u, re());
        ajaxReturn($re);
    }

    // 公告详情
    public function get_info()
    {
        $re = D('m_admin_site_notice')->get_info($this->u, re());
        ajaxReturn($re);
    }

    // 添加/编辑公告
    public function add_edit()
    {
        $re = D('m_admin_site_notice')->add_edit($this->u, re());
        ajaxReturn($re);
    }

    // 删除公告
    public function del()
    {
        $re = D('m_admin_site_notice')->del($this->u, re());
        ajaxReturn($re);
    }
}
