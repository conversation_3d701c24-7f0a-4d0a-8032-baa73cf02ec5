<?php

class c_admin_site_xieyi extends c_admin_base
{

    public function get_list()
    {
        $ret = D('m_admin_site_xieyi')->get_list($this->u, re());
        ajaxReturn($ret);
    }

    public function save_xieyi()
    {
        $ret = D('m_admin_site_xieyi')->save_xieyi($this->u, re());
        ajaxReturn($ret);
    }

    public function record_list()
    {
        $ret = D('m_admin_site_xieyi')->record_list($this->u, re());
        ajaxReturn($ret);
    }

    public function add_edit_record()
    {
        $ret = D('m_admin_site_xieyi')->add_edit_record($this->u, re());
        ajaxReturn($ret);
    }
}