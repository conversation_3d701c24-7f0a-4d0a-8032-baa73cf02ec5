<?php

class c_admin_site_file extends c_admin_base
{
    public function __construct()
    {
        parent::__construct();
        if($this->uid <= 0){
            ajaxReturn(rs(lang('登录超时，请重新登录')));
        }
    }

    public function up_file()
    {
        $this->is_post();
        $re = D('m_base')->up_file($this->uid, re('request.'),'admin');
        if($re['code'] == 1){
            $re['data'] = url2oss($re['data']);
        }
        ajaxReturn($re);
    }
}