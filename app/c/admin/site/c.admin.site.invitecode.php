<?php

class c_admin_site_invitecode extends c_admin_base
{
    //获取列表页
    public function get_list()
    {
        $d = re();
        $re = D('m_admin_site_invitecode')->get_list($this->u, $d);
        ajaxReturn($re);
    }

    //编辑邀请码
    public function edit()
    {
        $re = D('m_admin_site_invitecode')->edit($this->u, re());
        ajaxReturn($re);
    }

    //删除邀请码
    public function del()
    {
        $re = D('m_admin_site_invitecode')->del($this->u, re());
        ajaxReturn($re);
    }

    //调整等级
    public function adjust()
    {
        $re = D('m_admin_site_invitecode')->adjust($this->u, re());
        ajaxReturn($re);
    }
}