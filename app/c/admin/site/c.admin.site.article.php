<?php

class c_admin_site_article extends c_admin_base {
    public function __construct() {
        parent::__construct();
    }

    //获取列表
    public function get_list() {
        $d = re();
        $res = D('m_admin_site_article')->get_list($this->u, $d);
        ajaxReturn($res);
    }

    //获取详情
    public function get_info() {
        $d = re();
        $res = D('m_admin_site_article')->get_info($this->u, $d);
        ajaxReturn($res);
    }

    //添加
    public function add_edit() {
        $d = re();
        $res = D('m_admin_site_article')->add_edit($this->u, $d);
        ajaxReturn($res);
    }

    //删除
    public function del() {
        $d = re();
        $res = D('m_admin_site_article')->del($this->u, $d);
        ajaxReturn($res);
    }

    //修改排序
    public function xg_px() {
        $d = re();
        $res = D('m_admin_site_article')->xg_px($this->u, $d);
        ajaxReturn($res);
    }

    //修改状态
    public function xg_zt() {
        $d = re();
        $res = D('m_admin_site_article')->xg_zt($this->u, $d);
        ajaxReturn($res);
    }
}