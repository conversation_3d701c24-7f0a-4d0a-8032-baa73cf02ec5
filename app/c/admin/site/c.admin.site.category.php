<?php

class c_admin_site_category extends c_admin_base
{
    // 分类列表
    public function get_list()
    {
        $re = D('m_admin_site_category')->get_list($this->u, re());
        ajaxReturn($re);
    }

    // 添加/编辑分类
    public function add_edit()
    {
        $re = D('m_admin_site_category')->add_edit($this->u, re());
        ajaxReturn($re);
    }

    // 删除分类
    public function del()
    {
        $re = D('m_admin_site_category')->del($this->u, re());
        ajaxReturn($re);
    }

    // 查询所有
    public function get_all()
    {
        $re = D('m_admin_site_category')->get_all($this->u, re());
        ajaxReturn($re);
    }
}
