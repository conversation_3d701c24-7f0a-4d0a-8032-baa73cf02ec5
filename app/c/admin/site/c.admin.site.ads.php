<?php

class c_admin_site_ads extends c_admin_base
{
    //查询广告位列表
    public function get_slot()
    {
        $re = D('m_admin_site_ads')->get_slot($this->u, re());
        ajaxReturn($re);
    }

    //添加/编辑广告位
    public function add_edit_slot()
    {
        $d = re();
        $d['zu'] = rea('zu', 1, 2);
        $re = D('m_admin_site_ads')->add_edit_slot($this->u, $d);
        ajaxReturn($re);
    }

    //广告内容列表
    public function get_ads()
    {
        $re = D('m_admin_site_ads')->get_ads($this->u, re());
        ajaxReturn($re);
    }

    //添加/编辑广告内容
    public function add_edit_ads()
    {
        $re = D('m_admin_site_ads')->add_edit_ads($this->u, re());
        ajaxReturn($re);
    }

    //删除广告内容
    public function del_ads()
    {
        $re = D('m_admin_site_ads')->del_ads($this->u, re());
        ajaxReturn($re);
    }
}
