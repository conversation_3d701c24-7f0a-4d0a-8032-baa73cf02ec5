<?php

class c_admin_user_main extends c_admin_base
{
    public function __construct()
    {
        parent::__construct();
    }

    //获取用户列表
    public function get_list()
    {
        $d = re();
        $d['zc_sj'] = rea('zc_sj', 1, 2);
        $res = D('m_admin_user_main')->get_list($this->u, $d);
        ajaxReturn($res);
    }

    //获取用户详情
    public function get_info()
    {
        $d = re();
        $res = D('m_admin_user_main')->get_info($this->u, $d);
        ajaxReturn($res);
    }

    //添加用户
    public function edit()
    {
        $d = re();
        $res = D('m_admin_user_main')->edit($this->u, $d);
        ajaxReturn($res);
    }

    //设置用户状态
    public function set_zt()
    {
        $d = re();
        $res = D('m_admin_user_main')->set_zt($this->u, $d);
        ajaxReturn($res);
    }
}