<?php

//ok__权限管理

class c_admin_qx extends c_admin_base {

	public function __construct() {
		parent::__construct();
	}

//权限

	//权限列表
	public function list_qx() {
		list($list, $checkedId) = D('m_admin_qx')->list_qx($this->uid, 'tree', 'admin');
		$data = [
			'code' => 1,
			'count' => count($list),
			'data' => $list,
			'checkedId' => $checkedId,
			'pid' => '0'
		];
		ajaxReturn($data);
	}

	//添加/修改权限
	public function save_qx() {
		$this->is_post();
		$re = D('m_admin_qx')->save_qx($this->uid, re());
		ajaxReturn($re);
	}

	//删除权限
	public function del_qx() {
		$this->is_post();
		$re = D('m_admin_qx')->del_qx($this->uid, re('id'));
		ajaxReturn($re);
	}

//权限组

	//获取权限组列表
	public function list_qxz() {
		$list = D('m_admin_qx')->list_qxz(re());
		ajaxReturn($list);
	}

	//添加/修改权限组
	public function save_qxz() {
		$this->is_post();
		$re = D('m_admin_qx')->save_qxz($this->uid, re());
		ajaxReturn($re);
	}

	//删除权限组
	public function del_qxz() {
		$this->is_post();
		$re = D('m_admin_qx')->del_qxz($this->uid, re2('id'));
		ajaxReturn($re);
	}

	//获取权限组权限列表
	public function list_zu_qx() {
		$id = re2('id');
		list($list, $checkedId, $pid) = D('m_admin_qx')->list_zu_qx($id);
		$data = [
			'code' => 1,
			'count' => count($list),
			'data' => $list,
			'checkedId' => $checkedId,
			'pid' => (string)$pid
		];
		ajaxReturn($data);
	}

	//添加/修改权限组权限
	public function save_qxz_qx() {
		$this->is_post();
		$re = D('m_admin_qx')->save_qxz_qx($this->uid, re());
		ajaxReturn($re);
	}


}