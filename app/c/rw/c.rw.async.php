<?php

/**
 * 推广数据导入任务
 * sudo -Hu www php ./www/cli.php task_asyn/run_import
 */
class c_rw_async extends c_rw_base
{


    //php ./www/cli.php task_async/queue_run  queue_name
    public function queue_run()
    {
        echo getsj() . " 启动运行\r\n";
        list(, , $type) = $_SERVER['argv'];
        if (empty($type)) {
            echo getsj() . " 请输入任务队列类型参数\r\n";
            exit();
        }
        $class = "m_queue_{$type}";
        echo getsj() . " 任务处理类：{$class}\r\n";
        if (class_exists($class) && method_exists($class, 'run')) {
            echo getsj() . " 开始执行任务队列'{$class}'\r\n";
            D($class)->run();
        } else {
            echo getsj() . " 任务队列'{$class}'不存在\r\n";
        }
    }

    //php ./www/cli.php task_async/queue_run_all
    public function queue_run_all()
    {
        echo getsj() . " 开始运行\r\n";
        list(, , $run_type) = $_SERVER['argv'];
        if (!in_arr($run_type, ['webhook', 'run'])) {
            $run_type = 'webhook';
        }
        $types = array_keys(x_admin::QUEUE_TYPE_LABEL);
//        $types = ['comm', 'zdshenhe','adreport','jpush','wxtmpmsg', 'thirdexport', 'thirdimport','shifang'];//改进思路：读取"m/queue/task"目录文件，解析文件名生成$types数组
//        $types = ['comm',];//改进思路：读取"m/queue/task"目录文件，解析文件名生成$types数组
        foreach ($types as $type) {
            $class = "m_queue_{$type}";
            if (class_exists($class) && method_exists($class, $run_type)) {
                echo getsj() . " 开始执行任务队列'{$class}'\r\n";
                if ($run_type == 'run') {
                    D($class)->run();
                } else {
                    D($class)->webhook();
                }
                echo getsj() . " 开始执行队列监控'{$class}'\r\n";
                D($class)->queue_jiankong();
                echo getsj() . " 执行完毕'{$class}'\r\n";
            } else {
                echo getsj() . " 任务队列'{$class}'不存在\r\n";
            }
        }
    }

    //php ./www/cli.php task_async/queue_qrun  queue_name   id
    public function queue_qrun()
    {
        echo getsj() . " 启动运行\r\n";
        list(, , $type,$id) = $_SERVER['argv'];
        if (empty($type)) {
            echo getsj() . " 请输入任务队列类型\r\n";
            exit();
        }
        $id=ints($id);
        if ($id<=0) {
            echo getsj() . " 请输入任务ID\r\n";
            exit();
        }

        $class = "m_queue_{$type}";
        echo getsj() . " 任务处理类：{$class}\r\n";
        if (class_exists($class) && method_exists($class, 'run')) {
            echo getsj() . " 开始执行任务队列'{$class}'\r\n";
            $res = D($class)->qrun($id);
            // dd(222,$res);
        } else {
            echo getsj() . " 任务队列'{$class}'不存在\r\n";
        }
    }


}