<?php

class c_rw_user extends c_rw_base{

    public function __construct(){
        parent::__construct();
        exit('已处理完成');
        ini_set('memory_limit', '2280M');
    }
    
    
    /**
     * 主方法：生成并插入所有邀请码
     */
    public function generateAllInviteCodes()
    {
        $this->logFile = LOG . '/invite_codes_' . date('YmdHis') . '.log';
        $this->log("开始生成全覆盖邀请码 - " . date('Y-m-d H:i:s'));

        try {
            // 处理6位邀请码 (000000-999999)
            $sixDigitInserted = $this->processSixDigitCodes(5000);
            $this->log("6位码完成! 已插入: {$sixDigitInserted}");

            // 处理7位邀请码 (0000000-9999999)
            $sevenDigitInserted = $this->processSevenDigitCodes(50000);
            $this->log("7位码完成! 已插入: {$sevenDigitInserted}");

            $this->log("全部完成! 6位码: {$sixDigitInserted}, 7位码: {$sevenDigitInserted}, 总计: " . ($sixDigitInserted + $sevenDigitInserted));

            return [
                'six_digit' => $sixDigitInserted,
                'seven_digit' => $sevenDigitInserted,
                'total' => $sixDigitInserted + $sevenDigitInserted
            ];
        } catch (Exception $e) {
            $this->log("错误: " . $e->getMessage() . "\n" . $e->getTraceAsString());
            throw $e;
        }
    }

    /**
     * 处理6位邀请码 - 确保100%覆盖
     */
    private function processSixDigitCodes($batchSize)
    {
        $batchSize = ints($batchSize, 5000);
        $totalInserted = 0;

        // 首先检查数据库中已有的6位邀请码
        $existingCodesCount = M()->count('x_invite_code', "length(invite_code) = 6");
        $this->log("数据库中已有6位邀请码: {$existingCodesCount}个");

        if ($existingCodesCount >= 1000000) {
            $this->log("已经有完整的6位邀请码，无需再生成");
            return $existingCodesCount;
        }

        // 获取现有邀请码的列表用于去重
        $existingCodes = [];
        if ($existingCodesCount > 0) {
            $this->log("获取现有邀请码以避免重复...");
            $existingCodesData = M()->select('x_invite_code', 'invite_code', "length(invite_code) = 6");
            foreach ($existingCodesData as $row) {
                $existingCodes[$row['invite_code']] = true;
            }
            $this->log("已加载 " . count($existingCodes) . " 个现有邀请码");
        }

        // 计算需要生成的邀请码数量
        $neededCodes = 1000000 - $existingCodesCount;
        $this->log("需要生成 {$neededCodes} 个新邀请码");

        // 按数字段分批处理，每段10万个
        $segments = [
            [0, 99999],
            [100000, 199999],
            [200000, 299999],
            [300000, 399999],
            [400000, 499999],
            [500000, 599999],
            [600000, 699999],
            [700000, 799999],
            [800000, 899999],
            [900000, 999999]
        ];

        // 随机化段处理顺序
        shuffle($segments);

        F('invitation_code_classifier');
        $classifier = new invitation_code_classifier();

        foreach ($segments as $segmentIndex => $segment) {
            $this->log("处理段 {$segmentIndex}: {$segment[0]}-{$segment[1]}");

            $segmentCodes = [];
            for ($i = $segment[0]; $i <= $segment[1]; $i++) {
                $code = str_pad($i, 6, '0', STR_PAD_LEFT);
                if (!isset($existingCodes[$code])) {
                    $segmentCodes[] = $code;
                }
            }

            $this->log("段 {$segmentIndex} 包含 " . count($segmentCodes) . " 个新邀请码");

            // 分批插入
            $segmentBatches = array_chunk($segmentCodes, $batchSize);
            $this->log("将分 " . count($segmentBatches) . " 批插入");
            $sj = getsj();
            $ip = getip();
            foreach ($segmentBatches as $batchIndex => $batch) {
                $batchData = [];
                

                foreach ($batch as $code) {
                    try {
                        $tier = $classifier->classify($code);
                    } catch (Exception $e) {
                        $this->log("分类错误: " . $e->getMessage());
                        $tier = 0; // 使用默认值
                    }

                    // 生成随机排序值 - 使用无符号整数范围
                    $sortOrder = mt_rand(1, 4294967295);

                    $batchData[] = [
                        'invite_code' => $code,
                        'level' => $tier,
                        'zt' => 3,
                        'tjsj' => $sj,
                        'tjip' => $ip,
                        'gxsj' => $sj,
                        'gxip' => $ip,
                        'sort_order' => $sortOrder
                    ];
                }

                try {
                    $inserted = M()->inserts('x_invite_code', $batchData);
                    $inserted_count = count($batchData);
                    $totalInserted += $inserted_count;

                    $this->log("段 {$segmentIndex} 批次 {$batchIndex} 插入: {$inserted_count}，累计: {$totalInserted}");
                } catch (Exception $e) {
                    $this->log("插入失败: " . $e->getMessage());
                }

                if ($batchIndex % 10 == 0) {
                    $progress = round(($totalInserted / $neededCodes) * 100, 2);
                    $this->log("总进度: {$progress}%");
                    // 定期清理内存
                    gc_collect_cycles();
                }
            }
        }

        $finalCount = $existingCodesCount + $totalInserted;
        $this->log("6位码插入完成。现有: {$existingCodesCount}, 新增: {$totalInserted}, 总计: {$finalCount}");

        return $finalCount;
    }

    /**
     * 处理7位邀请码 - 确保100%覆盖，使用更小的段和事务
     */
    private function processSevenDigitCodes($batchSize)
    {
        $batchSize = ints($batchSize, 5000);
        $totalInserted = 0;

        // 首先检查数据库中已有的7位邀请码
        $existingCodesCount = M()->count('x_invite_code', "length(invite_code) = 7");
        $this->log("数据库中已有7位邀请码: {$existingCodesCount}个");

        if ($existingCodesCount >= 10000000) {
            $this->log("已经有完整的7位邀请码，无需再生成");
            return $existingCodesCount;
        }

        // 对于7位码，由于数量庞大，避免一次性查询所有已有邀请码
        $this->log("7位码将按段处理，每段查询已有邀请码");

        // 计算需要生成的邀请码数量
        $neededCodes = 10000000 - $existingCodesCount;
        $this->log("需要生成 {$neededCodes} 个七位邀请码");

        // 使用更小的分段处理，避免内存问题
        // 每段50万个数字，总共20个段
        $segmentSize = 500000;
        $totalSegments = 20; // 10000000 / 500000 = 20个段

        // 随机化段处理顺序
        $segmentOrder = range(0, $totalSegments - 1);
        shuffle($segmentOrder);

        // 加载分类器
        F('invitation_code_classifier');
        $classifier = new invitation_code_classifier();

        // 每个段进一步分割为子段，每子段5万个，以减轻内存压力
        $subSegmentSize = 50000;

        foreach ($segmentOrder as $segmentIndex) {
            $segmentStart = $segmentIndex * $segmentSize;
            $segmentEnd = $segmentStart + $segmentSize - 1;

            $this->log("处理七位段 {$segmentIndex}: {$segmentStart}-{$segmentEnd}");

            // 获取该段中已存在的邀请码
            $startCode = str_pad($segmentStart, 7, '0', STR_PAD_LEFT);
            $endCode = str_pad($segmentEnd, 7, '0', STR_PAD_LEFT);

            // 直接查询该范围内已存在的邀请码ID集合（只获取ID以节省内存）
            $this->log("查询范围 {$startCode} 到 {$endCode} 内已有的邀请码");
            $existingCodesQuery = "SELECT invite_code FROM x_invite_code 
                                WHERE invite_code >= '{$startCode}' 
                                AND invite_code <= '{$endCode}'";

            $existingCodes = [];

            // 使用分批查询减少内存消耗
            $offset = 0;
            $limit = 10000;

            do {
                $query = $existingCodesQuery . " LIMIT {$offset}, {$limit}";
                $batch = M()->query($query);

                if (empty($batch)) {
                    break;
                }

                foreach ($batch as $row) {
                    $existingCodes[$row['invite_code']] = true;
                }

                $offset += $limit;
                $this->log("已加载 " . count($existingCodes) . " 个已有邀请码，继续查询...");

                // 记录内存使用
                $memUsage = round(memory_get_usage(true) / 1024 / 1024, 2);
                $this->log("当前内存使用: {$memUsage}MB");

            } while (count($batch) == $limit);

            $existingCount = count($existingCodes);
            $this->log("段 {$segmentIndex} 中已有 {$existingCount} 个邀请码");

            // 计算该段需要生成多少个邀请码
            $segmentNeededCount = $segmentSize - $existingCount;

            if ($segmentNeededCount <= 0) {
                $this->log("该段不需要生成新邀请码，跳过");
                continue;
            }

            $this->log("该段需要生成 {$segmentNeededCount} 个邀请码");

            // 使用非常小的子段（5000个）来处理，进一步减少内存使用
            // $subSegmentSize = 5000;
            $subSegments = ceil($segmentSize / $subSegmentSize);

            for ($subIdx = 0; $subIdx < $subSegments; $subIdx++) {
                $subStart = $segmentStart + ($subIdx * $subSegmentSize);
                $subEnd = min($subStart + $subSegmentSize - 1, $segmentEnd);

                $this->log("处理子段 {$segmentIndex}-{$subIdx}: {$subStart}-{$subEnd}");

                // 生成子段中所有可能的邀请码
                $subSegmentCodes = [];
                for ($i = $subStart; $i <= $subEnd; $i++) {
                    $code = str_pad($i, 7, '0', STR_PAD_LEFT);
                    if (!isset($existingCodes[$code])) {
                        $subSegmentCodes[] = $code;
                    }

                    // 每生成1000个，检查一下内存
                    if (count($subSegmentCodes) % 10000 == 0) {
                        $memUsage = round(memory_get_usage(true) / 1024 / 1024, 2);
                        if ($memUsage > 1000) { // 接近内存限制
                            $this->log("内存使用过高: {$memUsage}MB，强制垃圾回收");
                            gc_collect_cycles();
                        }
                    }
                }

                $codeCount = count($subSegmentCodes);
                $this->log("子段 {$segmentIndex}-{$subIdx} 包含 {$codeCount} 个新邀请码");

                if (empty($subSegmentCodes)) {
                    $this->log("子段内没有需要生成的邀请码，跳过");
                    continue;
                }

                // 随机打乱子段内的邀请码
                shuffle($subSegmentCodes);

                // 分批插入数据库，使用更小的批次
                $batches = array_chunk($subSegmentCodes, $batchSize);
                $this->log("将分 " . count($batches) . " 批插入");
                $sj = getsj();
                $ip = getip();
                foreach ($batches as $batchIndex => $batch) {
                    $batchData = [];
                    
                    foreach ($batch as $code) {
                        try {
                            $tier = $classifier->classify($code);
                        } catch (Exception $e) {
                            $this->log("分类错误: " . $e->getMessage());
                            $tier = 0; // 默认级别
                        }

                        // 生成随机排序值
                        $sortOrder = mt_rand(1, 4294967295);

                        $batchData[] = [
                            'invite_code' => $code,
                            'level' => $tier,
                            'zt' => 3,
                            'tjsj' => $sj,
                            'tjip' => $ip,
                            'gxsj' => $sj,
                            'gxip' => $ip,
                            'sort_order' => $sortOrder
                        ];
                    }

                    // 确保每次插入前检查内存
                    $memUsage = round(memory_get_usage(true) / 1024 / 1024, 2);
                    $this->log("插入前内存使用: {$memUsage}MB");

                    // 尝试不使用事务，减少数据库负担
                    try {
                        $inserted = M()->inserts('x_invite_code', $batchData);
                        $inserted_count = count($batchData);
                        $totalInserted += $inserted_count;

                        $this->log("段 {$segmentIndex} 子段 {$subIdx} 批次 {$batchIndex} 插入: {$inserted_count}, 累计: {$totalInserted}");
                    } catch (Exception $e) {
                        $this->log("批量插入失败: " . $e->getMessage());

                        // 尝试单条插入恢复
                        $this->log("尝试单条插入恢复...");
                        $recovered = 0;

                        foreach ($batchData as $record) {
                            try {
                                $success = M()->insert('x_invite_code', $record);
                                if ($success) {
                                    $totalInserted++;
                                    $recovered++;
                                }
                            } catch (Exception $e2) {
                                // 忽略单条插入错误
                            }
                        }

                        $this->log("恢复插入成功: {$recovered}/{$inserted_count}");
                    }

                    // 每批次后强制垃圾回收
                    unset($batchData);
                    gc_collect_cycles();

                    // 频繁报告进度
                    $progress = round(($totalInserted / $neededCodes) * 100, 2);
                    $this->log("7位码总进度: {$progress}%, 已插入: {$totalInserted}");

                    $memUsage = round(memory_get_usage(true) / 1024 / 1024, 2);
                    $this->log("插入后内存使用: {$memUsage}MB");

                    // 给数据库一些喘息时间
                    if ($batchIndex % 10 == 0) {
                        $this->log("暂停1秒，让数据库有喘息时间...");
                        sleep(1);
                    }
                }

                // 清理内存
                unset($subSegmentCodes);
                unset($batches);
                gc_collect_cycles();
            }

            // 清理段级内存
            unset($existingCodes);
            gc_collect_cycles();
        }

        $finalCount = $existingCodesCount + $totalInserted;
        $this->log("7位码插入完成。原有: {$existingCodesCount}, 新增: {$totalInserted}, 总计: {$finalCount}");

        return $finalCount;
    }

    /**
     * 记录日志
     */
    private function log($message)
    {
        $message = glwb($message);
        $logMessage = date('Y-m-d H:i:s') . " - " . $message . "\n";
        file_put_contents($this->logFile, $logMessage, FILE_APPEND);

        // 输出到控制台
        if (php_sapi_name() == 'cli') {
            echo $logMessage;
        }
    }
}

?>