<?php

/**
 * 任务调度
 * Class c_rw_command
 */
class c_rw_command extends c_rw_base
{
    //php ./www/cli.php task_command/rw_run?type=class_name&ff=run
    public function rw_run()
    {
        _log('rw_run'," 启动运行",1);
        $params = re();
        $type = $params['type'] ?? '';
        $ff = $params['ff'] ?? 'run';
        if (empty($type)) {
            _log('rw_run'," 请输入任务队列类型参数",1);
            exit();
        }
        $class = "m_queue_{$type}";
        _log('rw_run'," 任务处理类：{$class}",1);
        if (class_exists($class) && method_exists($class, $ff)) {
            _log('rw_run'," 开始执行任务队列'{$class}'",1);
            $res = D($class)->$ff();
            ar($res);
        } else {
            _log('rw_run'," 任务队列'{$class}'不存在",1);
        }
    }
}
