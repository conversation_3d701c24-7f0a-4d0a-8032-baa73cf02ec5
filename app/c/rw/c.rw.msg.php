<?php

class c_rw_msg extends c_rw_base
{
    public function __construct()
    {
        parent::__construct();
    }

    //发送邮件
    public function send_email()
    {
        //加锁
        $hcm = x_rk::LOCK_EMAIL_SEND;
        $suo = lock($hcm, 3600);
        if ($suo == 0 && !BENDI) {
            return rs('系统繁忙,请重试!');
        }
        $limit = 1000;
        $where = ['zt' => 1,'id[>]' => 0];
        while (true) {
            $res = M()->select('x_msg_email', 'id,uid,email,type,title,nr,zt,tjsj,gxsj,czuid', $where,['id','asc'],$limit);
            if (empty($res)) {
                break;
            }
            $where['id[>]'] = end($res)['id'];
            M()->updates('x_msg_email', ['zt' => 4,'gxsj' => getsj()], ['id' => array_column($res, 'id')]);
            unlock($hcm, $suo);
            foreach ($res as $item) {
                //邮件单个进行队列发送
                D('m_queue_async')->add(0, 'email', ['u' => ['uid' => x_task_uid()], 'op' => 'send_email', 'd' => $item], 1);
            }
        }
        return rs('ok', 1, $res);
    }
}