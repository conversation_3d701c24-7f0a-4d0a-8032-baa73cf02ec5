<?php

class c_rw_lang extends c_rw_base
{
    public function trans_lang()
    {
        $d = re();
        $re = D('m_rw_langai')->trans_lang(['uid' => x_task_uid()], $d);
        ajaxReturn($re);
    }

    //翻译项目数据
    public function trans_xm()
    {
        $d = re();
        $re = D('m_rw_langai')->trans_xm(['uid' => x_task_uid()], $d);
        ajaxReturn($re);
    }

    //翻译字段类型数据 (x_zd_lang)
    public function trans_zd()
    {
        $d = re();
        $re = D('m_rw_langai')->trans_zd(['uid' => x_task_uid()], $d);
        ajaxReturn($re);
    }

    //翻译协议配置数据 (x_xieyi_lang)
    public function trans_xieyi()
    {
        $d = re();
        $re = D('m_rw_langai')->trans_xieyi(['uid' => x_task_uid()], $d);
        ajaxReturn($re);
    }

    //翻译项目标签数据 (x_xm_tag_lang)
    public function trans_xm_tag()
    {
        $d = re();
        $re = D('m_rw_langai')->trans_xm_tag(['uid' => x_task_uid()], $d);
        ajaxReturn($re);
    }

    //翻译公告数据 (x_notice_lang)
    public function trans_notice()
    {
        $d = re();
        $re = D('m_rw_langai')->trans_notice(['uid' => x_task_uid()], $d);
        ajaxReturn($re);
    }

    //翻译项目任务数据 (x_xm_price_lang)
    public function trans_xm_price()
    {
        $d = re();
        $re = D('m_rw_langai')->trans_xm_price(['uid' => x_task_uid()], $d);
        ajaxReturn($re);
    }

    //翻译分类数据 (x_category_lang)
    public function trans_category()
    {
        $d = re();
        $re = D('m_rw_langai')->trans_category(['uid' => x_task_uid()], $d);
        ajaxReturn($re);
    }

    //翻译资讯数据 (x_article_lang)
    public function trans_article()
    {
        $d = re();
        $re = D('m_rw_langai')->trans_article(['uid' => x_task_uid()], $d);
        ajaxReturn($re);
    }

    //翻译字段模板数据 (x_zd_tpl)
    public function trans_zd_tpl()
    {
        $d = re();
        $re = D('m_rw_langai')->trans_zd_tpl(['uid' => x_task_uid()], $d);
        ajaxReturn($re);
    }
}