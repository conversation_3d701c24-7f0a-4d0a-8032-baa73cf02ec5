<?php
/**
 * 定时任务获取和执行
 *
 * @package path
 * @access  public
 * <AUTHOR> <<EMAIL>>
 * @link    https://
 */
class c_rw_cronrwx extends c_rw_base{
    //获取后台任务 1秒一次
	public function huo()
	{
		$re=D('m_rw_cronrwx')->huo_crontab(re());
        ajaxReturn($re);
	}


    //执行后台任务
	public function zx()
	{
		$re=D('m_rw_cronrwx')->zx_crontab(re());
        ajaxReturn($re);
	}

    //报警
    public function bj()
    {
        $bj=[
            '当前时间：'.date('Y-m-d H:i:s'),
            '任务名：'.re('name'),
            '异常返回：'.re('data'),
        ];
        //报警通知 如钉钉
    }
}