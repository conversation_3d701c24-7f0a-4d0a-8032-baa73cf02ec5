<?php

/**
 * 报警通知-配置任务
 * 1- zy_err_tz 重要的错误通知，目前监控 数据库日志表 jm_log lx=2  重要异常 通知
 * 2- zy_log_tz 重要的日志通知，目前监控 log/sql_log 下面的文件通知
 * Class c_admin_rw
 */
class c_rw_baojing
{

    function __construct()
    {
        $ip = getip();
        if(!in_arr($ip,['127.0.0.1']) &&  php_sapi_name() !== 'cli'){
            exit('非法请求!'.$ip);
        }
        ignore_user_abort(true); // 后台运行
        set_time_limit(0);
        header('X-Accel-Buffering: no');
    }

    /**
     * 重要异常错误通知->数据库日志
     */
    public function db_bug()
    {
        $res = D('baojing')->db_bug(re2('num', 1000));
        ajaxReturn($res);
    }

    /**
     * 重要日志监控通知
     */
    public function file_bug()
    {
        $res = D('baojing')->file_bug(re2('num',1000));
        ajaxReturn($res);
    }
}