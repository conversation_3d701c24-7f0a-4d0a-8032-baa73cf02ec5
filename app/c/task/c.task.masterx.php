<?php

class c_task_masterx
{
    //同步小说
    public function sync_novel()
    {
        D('m_third_masterx')->sync_novel(re());
        echo '小说同步完成';
    }

    //同步下架小说
    public function sync_off_novel()
    {
        D('m_third_masterx')->sync_off_novel();
        echo '下架小说同步完成';
    }

    //同步报备
    public function sync_bb()
    {
        D('m_third_masterx')->sync_bb();
        echo '报备同步完成';
    }

    //同步订单
    public function sync_order()
    {
        $res = D('m_third_masterx')->sync_order(['uid' => x_task_uid()], re());
        ajaxReturn($res);
    }

    //同步退款订单
    public function sync_refund_order()
    {
        $res = D('m_third_masterx')->sync_refund_order();
        ajaxReturn($res);
    }
}
