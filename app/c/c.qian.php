<?php

/**
 * 资金测试程序
 */
class c_qian extends c_base
{
    public function __construct()
    {
        die('禁止访问');
        parent::__construct();
    }

    //收益账户ID
    const PT_SY_UID = 1;
    //银行账户
    const BANK_UID = 2;
    //支付宝用户
    const ZFB_UID = 3;
    //微信账户
    const WECHAT_UID = 4;

    //聚结算用户
    const WISE_UID = 5;
    //结算手续费用户
    const SXF_UID = 6;
    //平账用户
    const PING_ZHANG_UID = 7;
    public function chushihua()
    {
        M()->query('truncate x_user');
        M()->query('truncate x_order');
        M()->query('truncate x_zjmx');
    }

    public function step1()
    {
        //0=普通用户 1=收益账户 2=记账账户
        $sj = getsj2();
        $users = [
            ['id' => 1, 'lx' => 1, 'bz' => '收益账户', 'jrsj' => $sj],
            ['id' => 2, 'lx' => 2, 'bz' => '银行账户', 'jrsj' => $sj],
            ['id' => 3, 'lx' => 2, 'bz' => '支付宝', 'jrsj' => $sj],
            ['id' => 4, 'lx' => 2, 'bz' => '微信', 'jrsj' => $sj],
            ['id' => 5, 'lx' => 2, 'bz' => 'wise账户', 'jrsj' => $sj],
            ['id' => 6, 'lx' => 2, 'bz' => 'wise手续费', 'jrsj' => $sj],
            ['id' => 7, 'lx' => 2, 'bz' => '平账账户', 'jrsj' => $sj],
            //供应商账户
            ['id' => 101, 'lx' => 2, 'bz' => '供应商账户1', 'jrsj' => $sj],
            ['id' => 102, 'lx' => 2, 'bz' => '供应商账户2', 'jrsj' => $sj],
            ['id' => 103, 'lx' => 2, 'bz' => '供应商账户3', 'jrsj' => $sj],
            ['id' => 104, 'lx' => 2, 'bz' => '供应商账户4', 'jrsj' => $sj],
            ['id' => 105, 'lx' => 2, 'bz' => '供应商账户5', 'jrsj' => $sj],
            ['id' => 106, 'lx' => 2, 'bz' => '供应商账户6', 'jrsj' => $sj],
            //普通用户
            ['id' => 10001, 'lx' => 0, 'bz' => '测试账户1', 'jrsj' => $sj],
            ['id' => 10002, 'lx' => 0, 'bz' => '测试账户2', 'jrsj' => $sj],
            ['id' => 10003, 'lx' => 0, 'bz' => '测试账户3', 'jrsj' => $sj],
            ['id' => 10004, 'lx' => 0, 'bz' => '测试账户4', 'jrsj' => $sj],
            ['id' => 10005, 'lx' => 0, 'bz' => '测试账户5', 'jrsj' => $sj],
            ['id' => 10006, 'lx' => 0, 'bz' => '测试账户6', 'jrsj' => $sj],
            ['id' => 10007, 'lx' => 0, 'bz' => '测试账户7', 'jrsj' => $sj],
            ['id' => 10008, 'lx' => 0, 'bz' => '平账账户8', 'jrsj' => $sj],
            ['id' => 10009, 'lx' => 0, 'bz' => '测试账户9', 'jrsj' => $sj],
            ['id' => 10010, 'lx' => 0, 'bz' => '测试账户10', 'jrsj' => $sj],
        ];

        $res = M()->inserts('x_user', $users);
        if ($res) {
            die('添加用户成功');
        }
        die('添加用户失败');
    }

    //发放佣金
    public function step2()
    {
        $kou_uid = 101;
        $zqian = 1000;
        $cbqian1 = 800;
        $cbqian2 = 100;
        $cbqian3 = 50;

        //标识1/2/3 字段有索引 按需存业务数据，回头可查询
        $ym1 = 1;
        $ym2 = 2;
        $ym3 = 3;
        //cb正常来说只有1个，Rento特殊情况，发放佣金类型 ，同时会给 A B C 用户价钱，订单用户只能有1个 故Rento为反向操作，扣供应商 成本为加 fs 加钱
        $cb_uid = 10001; //成本uid
        $cb_uid2 = 10002; //成本uid2 非必填
        $cb_uid3 = 10003; //成本uid3 非必填

        $sj = date('Y-m-d');
        $d = [
            'lx' => '发放佣金',
            'zj' => [
                'uid' => $kou_uid,
                'qian' => $zqian,
                'zffs' => 3, //强制扣款 不够就扣成负数
                'sm' => "发放佣金，做单日期：" . $sj,
                'ym' => $ym1, //标识1 有索引 按需存业务数据
                'ym2' => $ym2, //标识2 有索引 按需存业务数据
                'ym3' => $ym3, //标识3 有索引 按需存业务数据
            ],
        ];
        $cb = [
            [
                'uid' => $cb_uid,
                'fs' => 1,
                'qian' => $cbqian1,
                'sm' => "用户做单佣金，做单日期：" . $sj,
                'ym' => $ym1, //标识1 有索引 按需存业务数据
                'ym2' => $ym2, //标识2 有索引 按需存业务数据
                'ym3' => $ym3, //标识3 有索引 按需存业务数据
            ]
        ];

        if ($cbqian2 > 0) {
            $cb[] = [
                'uid' => $cb_uid2,
                'fs' => 1,
                'qian' => $cbqian2,
                'sm' => "上级抽佣佣金，做单日期：" . $sj,
                'ym' => $ym1, //标识1 有索引 按需存业务数据
                'ym2' => $ym2, //标识2 有索引 按需存业务数据
                'ym3' => $ym3, //标识3 有索引 按需存业务数据
            ];
        }

        if ($cbqian3 > 0 && $cb_uid3) {
            $cb[] = [
                'uid' => $cb_uid3,
                'fs' => 1,
                'qian' => $cbqian3,
                'sm' => "上上级抽佣佣金，做单日期：" . $sj,
                'ym' => $ym1, //标识1 有索引 按需存业务数据
                'ym2' => $ym2, //标识2 有索引 按需存业务数据
                'ym3' => $ym3, //标识3 有索引 按需存业务数据
            ];
        }

        $d['cb'] = $cb;
        $res = D('m_qian_dd')->dd_add($d);
        p($res);
    }

    //项目补贴 平台补贴
    public function step3()
    {
        $qian = 1000;
        $ym = 90;
        $uid = 10001;
        $d = [
            'lx' => '项目补贴',
            'zj' => [
                'uid' => $uid,
                'qian' => $qian,
                'sm' => '测试补贴',
                'ym' => $ym
            ]
        ];

        $d['cb'] = [
            'uid' => self::PT_SY_UID,
            'fs' => 2,
            'qian' => $qian,
            'sm' => '测试补贴',
            'ym' => $ym
        ];

        $res = D('m_qian_dd')->dd_add($d);
        p($res);

        $d = [
            'lx' => '平台补贴',
            'zj' => [
                'uid' => $uid,
                'qian' => $qian,
                'sm' => '测试平台补贴',
                'ym' => $ym
            ]
        ];

        $d['cb'] = [
            'uid' => self::PT_SY_UID,
            'fs' => 2,
            'qian' => $qian,
            'sm' => '测试平台补贴',
            'ym' => $ym
        ];

        $res = D('m_qian_dd')->dd_add($d);
        p($res);
    }

    //开通会员
    public function step4()
    {
        $qian = 500;
        $ym = '金牌账户';
        $uid = 10001;
        $p_uid = 10002;
        //支付方式余额
        $d = [
            'lx' => '开通会员',
            'zj' => [
                'uid' => $uid,
                'qian' => $qian,
                'sm' => '开通会员',
                'ym' => '余额支付'
            ]
        ];

        $res = D('m_qian_dd')->dd_add($d);
        p($res);

        $d = [
            'lx' => '开通会员',
            'zj' => [
                'uid' => $uid,
                'qian' => 0,
                'sm' => '开通会员',
                'ym' => '支付宝支付'
            ]
        ];

        $cb = [];
        $cb[] = [
            'uid' => $p_uid,
            'fs' => 1,
            'qian' => 200,
            'sm' => '下级开通金牌账户，获取返佣',
            'ym' => $ym,
        ];

        $cb[] = [
            'uid' => self::ZFB_UID,
            'fs' => 2,
            'qian' => $qian,
            'sm' => '测试支付宝收款',
            'ym' => $ym
        ];

        $d['cb'] = $cb;
        $res = D('m_qian_dd')->dd_add($d);
        p($res);

        $d = [
            'lx' => '开通会员',
            'zj' => [
                'uid' => $uid,
                'qian' => 0,
                'sm' => '开通会员',
                'ym' => '微信支付'
            ]
        ];

        $cb = [];
        $cb[] = [
            'uid' => self::WECHAT_UID,
            'fs' => 2,
            'qian' => $qian,
            'sm' => '测试微信收款',
            'ym' => $ym
        ];

        $d['cb'] = $cb;
        $res = D('m_qian_dd')->dd_add($d);
        p($res);
    }

    //用户提现
    public function step5()
    {
        $uid = 10001;
        $tx_money = 100; //起始提现金额
        $user_sxf = 10; //我们收用户手续费
        $pt_sxf = 20; //我们给wise的手续费
        $sxf_diff = bc('-', $user_sxf, $pt_sxf);
        $shiji_money = bc('-', $tx_money, $user_sxf);
        $wise_money = bc('+', $shiji_money, $pt_sxf); //包含我们给wise手续费的金额
        $d = [
            'lx' => '用户提现',
            'zj' => [
                'uid' => $uid,
                'qian' => $tx_money,
                'sm' => '手续费亏损情况测试',
                'ym' => 'wise',
            ],
        ];

        $cb = [];
        $cb[] = [
            'uid' => self::WISE_UID,
            'fs' => 1,
            'qian' => $wise_money,
            'sm' => "结算通道:wise",
            'ym' => 'wise',
        ];

        $cb[] = [
            'uid' => self::SXF_UID,
            'fs' => $sxf_diff >= 0 ? 1 : 2,
            'qian' => abs($sxf_diff),
            'sm' => "手续费差额",
            'ym' => 'wise',
        ];

        if (!empty($cb)) {
            $d['cb'] = $cb;
        }

        $res = D('m_qian_dd')->dd_add($d);
        p($res);

        $uid = 10001;
        $tx_money = 100; //起始提现金额
        $user_sxf = 20; //我们收用户手续费
        $pt_sxf = 1; //我们给wise的手续费
        $sxf_diff = bc('-', $user_sxf, $pt_sxf);
        $shiji_money = bc('-', $tx_money, $user_sxf);
        $wise_money = bc('+', $shiji_money, $pt_sxf); //包含我们给wise手续费的金额
        $d = [
            'lx' => '用户提现',
            'zj' => [
                'uid' => $uid,
                'qian' => $tx_money,
                'sm' => '手续费小赚测试',
                'ym' => 'wise',
            ],
        ];

        $cb = [];
        $cb[] = [
            'uid' => self::WISE_UID,
            'fs' => 1,
            'qian' => $wise_money,
            'sm' => "结算通道:wise",
            'ym' => 'wise',
        ];

        $cb[] = [
            'uid' => self::SXF_UID,
            'fs' => $sxf_diff >= 0 ? 1 : 2,
            'qian' => abs($sxf_diff),
            'sm' => "手续费差额",
            'ym' => 'wise',
        ];

        if (!empty($cb)) {
            $d['cb'] = $cb;
        }

        $res = D('m_qian_dd')->dd_add($d);
        p($res);

        $uid = 10001;
        $tx_money = 100; //起始提现金额
        $user_sxf = 0; //我们收用户手续费
        $pt_sxf = 0; //我们给wise的手续费
        $sxf_diff = bc('-', $user_sxf, $pt_sxf);
        $shiji_money = bc('-', $tx_money, $user_sxf);
        $wise_money = bc('+', $shiji_money, $pt_sxf); //包含我们给wise手续费的金额
        $d = [
            'lx' => '用户提现',
            'zj' => [
                'uid' => $uid,
                'qian' => $tx_money,
                'sm' => '手续费持平测试',
                'ym' => 'wise',
            ],
        ];

        $cb = [];
        $cb[] = [
            'uid' => self::WISE_UID,
            'fs' => 1,
            'qian' => $wise_money,
            'sm' => "结算通道:wise",
            'ym' => 'wise',
        ];

        $cb[] = [
            'uid' => self::SXF_UID,
            'fs' => $sxf_diff >= 0 ? 1 : 2,
            'qian' => abs($sxf_diff),
            'sm' => "手续费差额",
            'ym' => 'wise',
        ];

        if (!empty($cb)) {
            $d['cb'] = $cb;
        }

        $res = D('m_qian_dd')->dd_add($d);
        p($res);
    }

    //甲方打款
    public function step6()
    {
        $supplier_uid = 101;
        $money = 100; //起始提现金额
        $d = [
            'lx' => '甲方打款',
            'zj' => [
                'uid' => self::BANK_UID,
                'qian' => $money,
                'sm' => '甲方打款',
                'ym' => '甲方打款',
            ],
        ];

        $cb = [];
        $cb[] = [
            'uid' => $supplier_uid,
            'fs' => 1,
            'qian' => $money,
            'sm' => "甲方打款",
            'ym' => '甲方打款',
        ];

        $d['cb'] = $cb;
        $res = D('m_qian_dd')->dd_add($d);
        p($res);
    }

    public function tuikuan()
    {
        $id = 10;
        $res = D('m_qian_dd')->dd_tui($id, ['sm' => '说明111']);
        p($res);
    }
}
