<?php

class c_base{


	function __construct(){
		// $this->uid = x_userid();//会员id
		// $this->u = $this->uid>0 ? x_user() : []; //用户信息
	}


	//是否post请求
	public function is_post(){
		if(BENDI != 1 && !is_post()){
			ar('非法请求!',-1003);
		}
	}

    // oss文件下载/显示
    public function oss_file() {
        $filepath = re('filepath');
        $path_arr = explode('.', $filepath);
        $path_ext = strtolower(end($path_arr));
        $exts  = RX('download', 'exts');
        if (empty($filepath) || !in_arr($path_ext, $exts)) {
            ajaxReturn(['code' => -1, 'msg' => '参数错误']);
        }
        $re = D('m_base')->show_oss_file($filepath);
        ajaxReturn($re);
    }
}