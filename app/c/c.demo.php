<?php

//框架使用示例


class c_demo extends c_base
{


	function __construct()
	{
		if (BENDI != 1) {
			exit('非法请求!');
		}
		parent::__construct();
	}

	public function test_rxx()
	{
		dd(ipgsd('**************'));
        dd(RC('xm_dczgjs'));
        dd(RC('site_config'));
	}

	public function test_tran()
	{
		ignore_user_abort(true); // 后台运行
		set_time_limit(0);

		$translator = new m_rw_langai();
		$translator->set_api_key('721295c8d23d4fb89565e6e23d7f9f05.RtI4QWxNPnhp2jxB');
		$texts = '&#60;div style=&#34;line-height: 1.5;&#34;&#62;&#60;span style=&#34;font-family: mrztxxx; font-size: 12px; color: #000000;&#34;&#62;&#60;img src=&#34;https://rtboss.bd.cn/up/img/0/20250630175105jhhWAgvgkC.png&#34; alt=&#34;&#34; width=&#34;600&#34; height=&#34;192&#34;&#62;&#60;br&#62;★注意！目前UC Browser项目仅支持在&#60;strong&#62;印度尼西亚&#60;/strong&#62;当地推广！其他地区推广暂时无效！后续会陆续开放，请关注项目动态！&#60;br&#62;★注意！一定要引导用户点击分享链接里的下载UC Browser按钮去下载app,不要自行去Google play下载！否则容易造成版本错误无法归因！&#60;/span&#62;&#60;/div&#62;
&#60;p&#62;&#92;r&#92;n&#60;span style=&#34;font-family: mrztxxx; font-size: 12px; color: #000000;&#34;&#62;1.禁止机刷，机刷不结算，禁止任务量/激励量！禁止一切以完成任务的方式让用户下载的方式，不用试肯定不结算！只收真实自然量！&#60;/span&#62;&#92;r&#92;n&#60;/p&#62;
&#60;div&#62;&#60;span style=&#34;font-family: mrztxxx; font-size: 12px; color: #000000;&#34;&#62;2.【三方引流】禁止在&ldquo;UC Browser&rdquo;内向外部引流(如在网盘通过任意方法预留其他平台外部链接/密码/邀请码等）；&#60;br&#62;3.【版权问题】因内容产生此类纠纷，需自行承担结果，请对自己内容负责；&#60;br&#62;&#60;/span&#62;&#92;r&#92;n
&#60;div&#62;&#92;r&#92;n
&#60;div style=&#34;line-height: 1.5;&#34;&#62;&#60;span style=&#34;font-family: mrztxxx; font-size: 12px; color: #000000;&#34;&#62;4.自媒体推广要能看到引流过程，并请时常截图留证，uc每周抽查；&#60;br&#62;5.以UC网盘官方返回数据为结算标准，介意者勿做。&#60;br&#62;&#60;/span&#62;&#60;/div&#62;
&#92;r&#92;n&#60;/div&#62;
&#92;r&#92;n&#60;/div&#62;';
		$results = $translator->translate_text(glwb_f($texts), 'auto', 'en');
		dd($results);
	}

	public function index()
	{
		$multiArray = [
			["2", "45"],
			["21", "41"],
			["2", "42"]
		];

		// 一行搞定！
		$flatUniqueArray = array_unique(array_merge(...$multiArray));
		dd($flatUniqueArray);
		$d = glwb(re());
		$d = '';
		var_dump(DEFAULT_LANG);
		$d['lang'] = DEFAULT_LANG;
		var_dump($d);
		exit;
		dd(RXX('xapi', 'alihd][value][ALI_HD_YZM_SCENE'));
	}

	public function tree()
	{
		$data = [
			['id' => 1, 'parent_id' => 0, 'name' => 'Electronics'],
			['id' => 2, 'parent_id' => 1, 'name' => 'Televisions'],
			['id' => 3, 'parent_id' => 1, 'name' => 'Mobile Phones'],
			['id' => 4, 'parent_id' => 2, 'name' => 'Smart TVs'],
		];
		$tree1 = TreeHelper::buildTree($data, 'id', 'parent_id', 'children', 0);
		dd($tree1);
	}

	// // 1. 构建树 (标准键名)
	// $tree1 = TreeHelper::buildTree($data, 'id', 'parent_id', 'children', 0);

	public function test_email()
	{
		$tip = '邮箱验证码发送成功,有效期5分钟,请注意查收!' . PHP_EOL;
		$code = suiji(6, '123456789');
		$tip .= '验证码：' . $code;
		L('mail/myMail');
		$mail = D('myMail', RXX('xapi', 'myMail'));
		$yzm = $mail->sendMail('<EMAIL>', '验证码', $tip);
		dd($yzm);
	}

	//钉钉通知
	public function ddmsg()
	{
		$send = ddmsg::send('需求通知', '测试钉钉通知,@所有人', 'all');
		p($send);
		$send = ddmsg::send('需求通知', '测试钉钉通知,@指定人', '18939317670,18888888888');
		p($send);
		$markdown = [
			"msgtype"  => "markdown",
			"markdown" => [
				"title" => "markdown通知测试",
				"text"  => "#### 今天天气 \n  #### 西北风1级，空气良89，相对温度73% \n---\n > 文本行",
			],
		];
		$send = ddmsg::send('需求通知', $markdown, '18939317670');
		p($send);
	}
}
