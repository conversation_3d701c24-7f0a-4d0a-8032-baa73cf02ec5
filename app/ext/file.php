<?php
//文件操作

//获取文件行数
function lines($file){
	$fp=fopen($file, "r");
	$i=1;
	while(!feof($fp)) {
		if($data=fread($fp,1024*1024*2)){
			$num=substr_count($data,"\n");
			$i+=$num;
		}
	}
	fclose($fp);
	return $i;
}

//循环读取文件,每次$limit条
function get_line( $file_name , $start , $limit ){
	$f = new SplFileObject( $file_name,'r');
	$f->seek( $start );
	$ret = [];
	for( $i = 0 ; $i < $limit ; $i++ )
	{
		$line = to_utf8(trim($f->current()));
		if($line==""){
			$f=null;
			return $ret;
		}
		$ret[]=$line;
		$f->next();
	}
	$f=null;
	return $ret;
}

//分段读取文件
function getFileLines($filename, $startLine = 1, $endLine=50, $method='rb')
{
	$content = [];
	$count = $endLine - $startLine;
	$fp = new SplFileObject($filename, $method);
	$fp->seek($startLine-1);
	for($i = 0; $i <= $count; ++$i) {
		$content[]=$fp->current();
		$fp->next();
	}
	$fp = null;
	return array_filter($content);
}

//文件大小格式化
function file_size($size=0,$dec=2,$pos=0){
	$unit = ['B','KB','MB','GB','TB','PB'];
	while($size >= 1024){
		$size /= 1024;
		$pos++;
	}
	return round($size,$dec) . $unit[$pos];
}

//遍历删除目录和目录下所有文件
function del_dir($dir){
	if (!is_dir($dir))
	{
		return false;
	}
	$handle = opendir($dir);
	while (($file = readdir($handle)) !== false)
	{
		if ($file != "." && $file != "..")
		{
			is_dir("$dir/$file")? del_dir("$dir/$file") : @unlink("$dir/$file");
		}
	}
	if (readdir($handle) == false)
	{
		closedir($handle);
		@rmdir($dir);
	}
}

