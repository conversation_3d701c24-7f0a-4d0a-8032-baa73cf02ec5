<?php

/**
 * Class invitation_code_classifier
 *
 * Classifies 7-digit invitation codes into different tiers based on predefined rules.
 * 对7位数字邀请码根据预定义规则进行分级。
 */
class invitation_code_classifier
{
    // Tier constants - 等级常量
    const TIER_DIAMOND = '6'; // 钻石级
    const TIER_PLATINUM = '5'; // 铂金级
    const TIER_GOLD = '4'; // 黄金级
    const TIER_SILVER = '3'; // 白银级
    const TIER_BRONZE = '1'; // 青铜级
    const TIER_REGULAR = '1'; // 普通级 (If no rule matches - 如果无规则匹配)

    /**
     * Classifies a 7-digit invitation code.
     * 对一个7位邀请码进行分级。
     *
     * @param string $code The 7-digit invitation code. 7位邀请码字符串。
     * @return string The tier of the code. 返回代码的等级。
     */
    public function classify(string $code): string
    {
        // Input validation - 输入验证
        //6或7位数字
       if (!preg_match('/^\d{4,8}$/', $code)) {
           throw new InvalidArgumentException($code." 邀请码必须是4-8位数字。");
       }

        $digits = str_split($code); // Convert string to array of digits - 将字符串转换为数字数组
        $counts = array_count_values($digits); // Count occurrences of each digit - 统计每个数字出现的次数
        $unique_digits_count = count($counts); // Number of unique digits - 不同数字的数量

        // --- Diamond Tier Check ---
        // 1. AAAAAAA (Seven Same) - 七同号
        if ($unique_digits_count === 1) {
            return self::TIER_DIAMOND;
        }

        // --- Platinum Tier Checks ---
        // 2. ABCDEFG (Ascending Sequence, step 1) - 升序七连顺
        if ($this->isConsecutive($digits, 1)) {
            return self::TIER_PLATINUM;
        }
        // 3. GFEDCBA (Descending Sequence, step 1) - 降序七连顺
        if ($this->isConsecutive($digits, -1)) {
            return self::TIER_PLATINUM;
        }
        // 4. AAAAAAB / ABBBBBB (Six Same) - 六同号
        if ($unique_digits_count === 2 && in_array(6, $counts)) {
            return self::TIER_PLATINUM;
        }
        // 5. AAABBBB / AAAABBB (4+3 / 3+4) - 四带三 / 三带四
        if ($unique_digits_count === 2 && ((in_array(4, $counts) && in_array(3, $counts)))) {
            return self::TIER_PLATINUM;
        }
        // 6. ABCXCBA (Palindrome) - 回文号
        if ($code === strrev($code)) {
            return self::TIER_PLATINUM;
        }

        // --- Gold Tier Checks ---
        // 7. AAAAABB / AABBBBB (Five Same) - 五同号
        if ($unique_digits_count >= 2 && in_array(5, $counts)) {
            return self::TIER_GOLD;
        }
        // 8. XABCDEF / ABCDEFX (Six Consecutive, step 1) - 六连顺 (升序或降序)
        if ($this->hasConsecutiveSubsequence($digits, 6, 1) || $this->hasConsecutiveSubsequence($digits, 6, -1)) {
            return self::TIER_GOLD;
        }
        // 9. AAABCCC (3+3+1) - 三带三加一
        if ($unique_digits_count === 3) {
            $counts_values = array_values($counts);
            sort($counts_values);
            if ($counts_values === [1, 3, 3]) {
                return self::TIER_GOLD;
            }
        }
        // 10. AABBCCC (2+2+3) - 两对加三条
        if ($unique_digits_count === 3) {
            $counts_values = array_values($counts);
            sort($counts_values);
            if ($counts_values === [2, 2, 3]) {
                return self::TIER_GOLD;
            }
        }
        // 11. ABABABC / ABCBCBC / ABABABA (Repeating Patterns) - 重复模式
        if (preg_match('/^(\d)(\d)\1\2\1\2\d$/', $code) || // ABABABC
            preg_match('/^(\d)(\d)(\d)\1\2\3\d$/', $code) || // ABCBCBC (Typo in rule? Should be ABCABCX?) Assuming ABCABCX
            preg_match('/^\d(\d)(\d)(\d)\1\2\3$/', $code) || // XABCABC
            preg_match('/^(\d)(\d)\1\2\1\2\1$/', $code)) {   // ABABABA
            return self::TIER_GOLD;
        }
        // 12. AAABAAA / BBBABBB (Center Different Symmetric) - 中间不同对称
        if ($digits[0] == $digits[1] && $digits[1] == $digits[2] &&
            $digits[4] == $digits[5] && $digits[5] == $digits[6] &&
            $digits[0] == $digits[6] && $digits[0] != $digits[3]) {
            return self::TIER_GOLD;
        }
        // 13. ABCDEFG (Arithmetic Progression, step > 1) - 等差数列 (步长>1)
        $ap_diff = $this->isArithmeticProgression($digits);
        if ($ap_diff !== false && abs($ap_diff) > 1) {
            return self::TIER_GOLD;
        }

        // --- Silver Tier Checks ---
        // 14. AAAABCD / ABCDDDD (Four Same) - 四同号
        if ($unique_digits_count >= 2 && in_array(4, $counts)) {
            return self::TIER_SILVER;
        }
        // 15. AABBCCD (Three Pairs) - 三对子
        if ($unique_digits_count === 4) {
            $pair_count = 0;
            foreach ($counts as $count) {
                if ($count === 2) {
                    $pair_count++;
                }
            }
            if ($pair_count === 3) {
                return self::TIER_SILVER;
            }
        }
        // 16. XYABCDE / ABCDEXY (Five Consecutive, step 1) - 五连顺 (升序或降序)
        if ($this->hasConsecutiveSubsequence($digits, 5, 1) || $this->hasConsecutiveSubsequence($digits, 5, -1)) {
            return self::TIER_SILVER;
        }
        // 17. AABCCAA / ABCCCBA (Partial Palindrome/Symmetry) - 部分回文/对称
        // AABCCAA: 1=2, 6=7, 1=7, 3=5, 1!=3
        if ($digits[0] == $digits[1] && $digits[5] == $digits[6] && $digits[0] == $digits[6] && $digits[2] == $digits[4] && $digits[0] != $digits[2]) {
            return self::TIER_SILVER;
        }
        // ABCCCBA: 1=7, 2=6, 3=4=5, 1!=2, 2!=3
        if ($digits[0] == $digits[6] && $digits[1] == $digits[5] && $digits[2] == $digits[3] && $digits[3] == $digits[4] && $digits[0] != $digits[1] && $digits[1] != $digits[2]) {
            return self::TIER_SILVER;
        }
        // 18. Contains Lucky Numbers - 含特殊数字组合 (PHP 7.4 compatible check - 兼容 PHP 7.4 的检查)
        if (strpos($code, '168') !== false || strpos($code, '518') !== false || strpos($code, '520') !== false ||
            strpos($code, '1314') !== false || strpos($code, '666') !== false || strpos($code, '888') !== false ||
            strpos($code, '999') !== false) {
            // Note: 666/888/999 might already be caught by higher tiers (e.g., AAA...), but check anyway per rule.
            // 注意：666/888/999 可能已被更高级别捕获（例如 AAA...），但根据规则仍在此检查。
            return self::TIER_SILVER;
        }

        // --- Bronze Tier Checks ---
        // 19. AABCDDE / AABBCCD (At least Two Pairs) - 至少两对子
        $pair_count = 0;
        foreach ($counts as $count) {
            if ($count >= 2) {
                $pair_count++;
            }
        }
        if ($pair_count >= 2) {
            return self::TIER_BRONZE;
        }
        // 20. XYZABCD / ABCDXYZ (Four Consecutive, step 1) - 四连顺 (升序或降序)
        if ($this->hasConsecutiveSubsequence($digits, 4, 1) || $this->hasConsecutiveSubsequence($digits, 4, -1)) {
            return self::TIER_BRONZE;
        }
        // 21. AxxxxxxA (First/Last Same) - 首尾相同
        if ($digits[0] == $digits[6]) {
            return self::TIER_BRONZE;
        }

        // If none of the above rules match - 如果以上规则都不匹配
        return self::TIER_REGULAR;
    }

    /**
     * Checks if an array of digits forms a consecutive sequence.
     * 检查数字数组是否构成连续序列。
     *
     * @param array $digits Array of single-digit strings or integers. 数字数组。
     * @param int $step The step (e.g., 1 for ascending, -1 for descending). 步长（例如 1 表示升序，-1 表示降序）。
     * @return bool True if consecutive, False otherwise. 如果连续则返回 true，否则返回 false。
     */
    private function isConsecutive(array $digits, int $step = 1): bool
    {
        for ($i = 0; $i < count($digits) - 1; $i++) {
            // Handle wrap-around (9 to 0 or 0 to 9) if needed, but rules imply standard sequence.
            // 根据需要处理环绕（9到0或0到9），但规则暗示标准序列。
            // Example: 7890123 - not consecutive by rule 2. 例子：7890123 - 根据规则2不连续。
            if ((int)$digits[$i+1] !== (int)$digits[$i] + $step) {
                return false;
            }
        }
        return true;
    }

    /**
     * Checks if the digits form an arithmetic progression.
     * 检查数字是否构成等差数列。
     *
     * @param array $digits Array of single-digit strings or integers. 数字数组。
     * @return int|false The common difference if it's an AP, otherwise false. 如果是等差数列则返回公差，否则返回 false。
     */
    private function isArithmeticProgression(array $digits)
    {
        if (count($digits) < 2) {
            return false; // Need at least 2 numbers for an AP - 至少需要两个数才能构成等差数列
        }
        $diff = (int)$digits[1] - (int)$digits[0];
        for ($i = 1; $i < count($digits) - 1; $i++) {
            if (((int)$digits[$i+1] - (int)$digits[$i]) !== $diff) {
                return false;
            }
        }
        return $diff;
    }

    /**
     * Checks if the code contains a consecutive subsequence of a given length.
     * 检查代码是否包含给定长度的连续子序列。
     *
     * @param array $digits Array of single-digit strings or integers. 数字数组。
     * @param int $length The required length of the consecutive subsequence. 连续子序列所需的长度。
     * @param int $step The step (e.g., 1 for ascending, -1 for descending). 步长。
     * @return bool True if such a subsequence exists, False otherwise. 如果存在这样的子序列则返回 true，否则返回 false。
     */
    private function hasConsecutiveSubsequence(array $digits, int $length, int $step): bool
    {
        if ($length > count($digits) || $length < 2) {
            return false;
        }
        for ($i = 0; $i <= count($digits) - $length; $i++) {
            $subsequence = array_slice($digits, $i, $length);
            if ($this->isConsecutive($subsequence, $step)) {
                return true;
            }
        }
        return false;
    }
}
