<?php
/**
 * 其他常用方法,可按需拿到/app/fun.php中使用
 */

//获取字符串中英文混合长度
function zfcd($str,$charset = 'utf-8'){
	preg_match_all("/./us",$str,$matches);
	return count(current($matches));
}

//不区分大小写的in_array
function in_array_x($value,$array){
	return in_arr(strtolower($value),array_map('strtolower',$array));
}

//获取域名根后缀
function gethz($ym){
	$yms = explode('.',strtolower($ym));
	return ".".end($yms);
}

//验证域名
function isym($str){
	return D('check')->ym($str);
}

//Xml转数组
function xmlToArray($xml){
	$arr = xml_to_array($xml);
	$key = array_keys($arr);
	return $arr[$key[0]];
}

function xml_to_array($xml){
	$reg = "/<(\\w+)[^>]*?>([\\x00-\\xFF]*?)<\\/\\1>/";
	if(preg_match_all($reg,$xml,$matches)){
		$count = count($matches[0]);
		$arr = [];
		for($i = 0;$i < $count;$i++){
			$key = $matches[1][$i];
			$val = xml_to_array($matches[2][$i]);  // 递归
			if(array_key_exists($key,$arr)){
				if(is_array($arr[$key])){
					if(!array_key_exists(0,$arr[$key])){
						$arr[$key] = [$arr[$key]];
					}
				}
				else{
					$arr[$key] = [$arr[$key]];
				}
				$arr[$key][] = $val;
			}
			else{
				$arr[$key] = $val;
			}
		}
		return $arr;
	}
	else{
		return $xml;
	}
}

//数字替换 txt:字符串 type:0=数字转为汉字,1=汉字转为数字,2=数字转为英文,3=英文转为数字
function shuzitihuan($txt,$type = 0){
	$words = [
		'cn' => [
			"零" => "0",
			"一" => "1",
			"二" => "2",
			"三" => "3",
			"四" => "4",
			"五" => "5",
			"六" => "6",
			"七" => "7",
			"八" => "8",
			"九" => "9",
		],
		'en' => [
			"zero"  => "0",
			"one"   => "1",
			"two"   => "2",
			"three" => "3",
			"four"  => "4",
			"five"  => "5",
			"six"   => "6",
			"seven" => "7",
			"eight" => "8",
			"nine"  => "9",
		],
	];
	$index = in_array($type,['0','1']) ? 'cn' : 'en';
	$words = $words[$index];
	if(in_array($type,['0','2'])){
		$words = array_flip($words);
	}
	$searchs = [];
	$replaces = [];
	foreach($words as $search => $replace){
		$searchs[] = $search;
		$replaces[] = $replace;
	}
    return str_replace($searchs,$replaces,$txt);
}

//时间处理 支持时间戳和标准时间
function dateadd($dw = 'd',$n = '',$date = '',$re_gs = 'Y-m-d',$jiajian = "+"){
	if($n == 0){
		return ints($date) > 0 ? date($re_gs,$date) : $date;
	}
	$date = ints($date) > 0 ? ints($date) : strtotime($date);
	if(!$date){
		return false;
	}
	$dw = strtolower($dw);
	switch($dw){
		case "y":
			$val = 'year';
			break;
		case "m":
			$val = 'month';
			break;
		case "d":
			$val = 'days';
			break;
		case "h":
			$val = 'hour';
			break;
		case "i":
			$val = 'minute';
			break;
		case "s":
			$val = 'second';
			break;
		default:
			$val = 'days';
	}
	$date1 = $date;
	$date = date("Y-m-d H:i:s",$date1);
	$jiajian = $jiajian == '-' ? '-' : '+';
	if(ints($n) > 0){
		$n = $jiajian.$n;
	}
	//月份需要特殊处理一下
	if($dw == 'm'){
		$date2 = date('Y-m-01',$date1);
		$lastday = date('t',strtotime("{$date2} {$n} {$val}"));
		$day = date('d',$date1);
		if($day > $lastday){
			$date = date("Y-m-{$lastday} H:i:s",$date1);
		}
	}
	return date($re_gs,strtotime("{$date} {$n} {$val}"));
}

//时间处理 支持时间戳和标准时间
function datejian($dw = 'd',$n = '',$date = '',$re_gs = 'Y-m-d'){
	return dateadd($dw,$n,$date,$re_gs,'-');
}

//计算时间差
//$t1 $t2时间戳或日期
//$dw  返回结果类型   y-年，m-月，d-天，h-小时，i-分钟，s-秒
//$abs true=返回正数
function datediff($t1,$t2,$dw='d',$abs=false){
	$t1= ints($t1)>0 ? ints($t1) : strtotime($t1);
	$t2= ints($t2)>0 ? ints($t2) : strtotime($t2);
	if(!$t1 || !$t2){
		return false;
	}
	$dw=strtolower($dw);
	switch($dw){
		case 'y':$re=date('Y',$t2)-date('Y',$t1);break;
		case 'm':$re=(date('Y',$t2)-date('Y',$t1))*12+date('m',$t2)-date('m', $t1);break;
		case 'd':$re=(strtotime(date('Y-m-d',$t2))-strtotime(date('Y-m-d',$t1)))/86400;break;
		case 'h':$re=(strtotime(date('Y-m-d H:00:00',$t2))-strtotime(date('Y-m-d H:00:00',$t1)))/3600;break;
		case 'i':$re=(strtotime(date('Y-m-d H:i:00',$t2))-strtotime(date('Y-m-d H:i:00',$t1)))/60;break;
		case 's':$re=$t2-$t1;break;
		default:$re=$t2-$t1;
	}
	return $abs ? abs(intval($re)) : intval($re);
}

//浮点数计算 bc('+','1.5','2.2');
function bc($ysf,$left,$right,$scale = 2){
	if(!is_numeric($left) || !is_numeric($right)){
		return false;
	}
	switch($ysf){
		case '+':
			return bcadd($left,$right,$scale);
		case '-':
			return bcsub($left,$right,$scale);
		case '*':
			return bcmul($left,$right,$scale);
		case '/':
			if($right == 0){
				return false;
			}
			return bcdiv($left,$right,$scale);
		case '%':
			return bcmod($left,$right);
		default:
			return false;
	}
}

//表达式bc浮点数运算
//bcstr('2.2*3.3-4.4/5.5');
//bcstr('(1+(3*(2+1)))*5');
function bcstr($str, $scale = 2) {
    $tokens = preg_split('/([()+\-*\/%])/',$str,-1,PREG_SPLIT_DELIM_CAPTURE | PREG_SPLIT_NO_EMPTY); //切割字符串,包括括号
    $yxj = ['+' => 1,'-' => 1,'*' => 2,'/' => 2,'%' => 2]; //优先级
    $re_stack = [];
    $stack = [];

    foreach($tokens as $token) {
        if ($token == '(') {
            $stack[] = $token;
        }
        elseif ($token == ')') {
            while (!empty($stack) && end($stack) != '(') {
                $right = array_pop($re_stack);
                $left = array_pop($re_stack);
                $ysf = array_pop($stack);
                $re = bc($ysf, $left, $right, $scale);
                if($re === false) {
                    return false;
                }
                $re_stack[] = $re;
            }
            array_pop($stack);
        }
        elseif (isset($yxj[$token])) {
            while (!empty($stack) && $yxj[$token] <= $yxj[end($stack)] && end($stack) != '(') {
                $right = array_pop($re_stack);
                $left = array_pop($re_stack);
                $ysf = array_pop($stack);
                $re = bc($ysf, $left, $right, $scale);
                if ($re === false) {
                    return false;
                }
                $re_stack[] = $re;
            }
            $stack[] = $token;
        } else {
            $re = filter_var($token, FILTER_VALIDATE_FLOAT);
            if ($re === false) {
                return false;
            }
            $re_stack[] = $re;
        }
    }
    while (!empty($stack)) {
        $right = array_pop($re_stack);
        $left = array_pop($re_stack);
        $ysf = array_pop($stack);
        $re = bc($ysf, $left, $right, $scale);
        if ($re === false) {
            return false;
        }
        $re_stack[] = $re;
    }

    return end($re_stack);
}

//拼接url
function buildurl($baseURL,$req){
	$baseURL = glwb($baseURL);
	$req = glwb($req);
	return $baseURL."?".http_build_query($req);
}

//IP是否在列表中  支持IP段  127.0.*.*
function in_ip($ips = "",$ip = ""){
	if(!$ip){
		$ip = getip(1);
	}
	if($ips){
		if(is_string($ips)){ //ip用"," 例如白名单IP：************,************,193.134.*.*
			$ips = explode(",",$ips);
		}
	}
	else{
		return false;
	}
	if(in_array($ip,$ips)){
		return true;
	}
	$ipregexp = "/^(".implode('|',str_replace(['*','.'],['\d+','\.'],$ips)).")$/";
	return (bool)preg_match($ipregexp,$ip);
}


//生成错误信息
function cuowu($msg = ''){
	$header = "";
	foreach($_SERVER as $name => $value){
		if(substr($name,0,5) == 'HTTP_'){
			$header .= str_replace(' ','-',ucwords(strtolower(str_replace('_',' ',substr($name,5))))).":".$value.PHP_EOL;
		}
	}
	$fail_msg = "发生时间：".date('Y-m-d H:i:s').PHP_EOL;
	if($msg != ""){
		$fail_msg .= "错误信息：".$msg.PHP_EOL;
	}
	$fail_msg .= $_SERVER['REQUEST_METHOD']." ".$_SERVER['PHP_SELF'].'?'.$_SERVER['QUERY_STRING'].PHP_EOL;
	$fail_msg .= "IP：".getip().PHP_EOL;
	$fail_msg .= $header.PHP_EOL;
	return glwb($fail_msg);
}

//获取时间戳
function strtotime_x($str_time){
	$result = strtotime($str_time);
	if(empty($result)){
		$date = new DateTime($str_time);
		$result = $date->format('U');
	}
	return $result;
}

//字符串方式in_array 左右加,
function in_str($str,$need){
	return !is_array($str) && !empty($str) && stripos(','.$str.',',','.$need.',') !== false ? 1 : 0;
}

//headx 允许跨域调用
function headx(){
	header("Access-Control-Allow-Origin: *");//全域名
	header('P3P: CP="CAO PSA OUR"');
	header("Access-Control-Allow-Credentials: true");                   //是否可以携带cookie
	header("Access-Control-Allow-Methods: POST,GET,PUT,OPTIONS,DELETE");//允许请求方式
	header("Access-Control-Allow-Headers: X-Custom-Header");            //允许请求字段，由客户端决定
}

//输出下载头
function head_down($filename){
	header("Content-Type: application/octet-stream");
	if(preg_match("/MSIE/",$_SERVER['HTTP_USER_AGENT'])){
		header('Content-Disposition:  attachment; filename="'.$filename.'"');
	}
	elseif(preg_match("/Firefox/",$_SERVER['HTTP_USER_AGENT'])){
		header('Content-Disposition: attachment; filename="'.$filename.'"');
	}
	else{
		header('Content-Disposition: attachment; filename="'.$filename.'"');
	}
	if(right($filename,4) == '.csv'){
		echo chr(0xEF).chr(0xBB).chr(0xBF);
	}
}

//隐藏字符
function str_yin($str,$start = 1,$len = 2){
	if(empty($str)){
		return '';
	}
	$count = mb_strlen($str,'UTF-8');
	if(!$count){
		return $str;
	}
	if($count == 1){ //只有1位则直接隐藏
		return '*';
	}
	$len += $start;
	if($len == 0){
		$len = $count;
	}
	$i = 0;
	$re = '';
	while($i < $count){
		$tmp = mb_substr($str,$i,1,'UTF-8');
		if($start <= $i && $i < $len){
			$re .= '*';
		}
		else{
			$re .= $tmp;
		}
		$i++;
	}
	return $re;
}

//时间格式转换
function sjzh($date,$gs = 'Y-m-d'){
	$time = ints($date) > 0 ? $date : strtotime($date);
	return date($gs,$time);
}

//下载远程文件
function download($url,$path,$filename){
	if(!$url || !$path || !$filename){
		return '';
	}
	$ch = curl_init();
	curl_setopt($ch,CURLOPT_URL,$url);
	curl_setopt($ch,CURLOPT_RETURNTRANSFER,1);
	curl_setopt($ch,CURLOPT_CONNECTTIMEOUT,30);
	$file = curl_exec($ch);
	curl_close($ch);
	$resource = fopen($path.$filename,'a');
	fwrite($resource,$file);
	fclose($resource);
	return $path.$filename;
}

//并发curl请求
function multi_curl($data,$timeout = 10,$conn_timeout = 10){
	$mch = curl_multi_init();
	$connarr = [];
	foreach($data as $key => $val){
		$url = $val['url'];
		$connarr[$key] = curl_init($url);
		//设置参数 start
		if(substr($url,0,5) == 'https'){//判断是否为https
			$ishttps = true;
		}
		curl_setopt($connarr[$key],CURLOPT_PROXY,$val['proxy']);//设置代理
		if(strpos(strtolower($val['proxy_type']),'so') !== false){//socket代理
			curl_setopt($connarr[$key],CURLOPT_PROXYTYPE,CURLPROXY_SOCKS5);
		}
		else{
			curl_setopt($connarr[$key],CURLOPT_PROXYTYPE,CURLPROXY_HTTP);//http代理
		}
		// curl_setopt($connarr[$key], CURLOPT_URL, $val['url']);//设置目标url
		curl_setopt($connarr[$key],CURLOPT_RETURNTRANSFER,true);//返回结果，不输出内容
		curl_setopt($connarr[$key],CURLOPT_TIMEOUT,$timeout);
		curl_setopt($connarr[$key],CURLOPT_CONNECTTIMEOUT,$conn_timeout);

		//设置header
		if(isset($val['header']) && $val['header']){
			curl_setopt($connarr[$key],CURLOPT_HEADER,1);
		}
		$httpheader = 'User-Agent: Mozilla/5.0 (Windows NT 10.0; WOW64)AppleWebKit/537.36 (KHTML, like Gecko)Chrome/63.0.3239.26 Safari/537.36';
		if(isset($val['httpheader'])){
			$httpheader = $val['httpheader'];
		}
		curl_setopt($connarr[$key],CURLOPT_HTTPHEADER,$httpheader);
		//设置referer
		if(isset($val['referer'])){
			curl_setopt($connarr[$key],CURLOPT_REFERER,$val['referer']);
		}
		//是否走post请求
		if(isset($val['post'])){
			curl_setopt($connarr[$key],CURLOPT_POST,1);
			curl_setopt($connarr[$key],CURLOPT_POSTFIELDS,$val['post']);
		}
		//是否跟踪重定向
		if(isset($val['followlocation'])){
			curl_setopt($connarr[$key],CURLOPT_FOLLOWLOCATION,$val['followlocation']);
		}
		if(isset($val['cookiefile'])){//cookie文件
			curl_setopt($connarr[$key],CURLOPT_COOKIEFILE,$val['cookiefile']);
		}
		if(isset($val['cookiejar'])){
			curl_setopt($connarr[$key],CURLOPT_COOKIEJAR,$val['cookiejar']);
		}
		if(isset($val['useragent'])){
			curl_setopt($connarr[$key],CURLOPT_USERAGENT,$val['useragent']);
		}
		// 增加https的。
		if($ishttps){
			curl_setopt($connarr[$key],CURLOPT_SSL_VERIFYPEER,false); // 对认证证书来源的检查
			curl_setopt($connarr[$key],CURLOPT_SSL_VERIFYHOST,false); // 从证书中检查SSL加密算法是否存在
		}
		//设置参数 end
		curl_multi_add_handle($mch,$connarr[$key]);             // 添加线程
	}

	//执行线程
	do{
		$mrc = curl_multi_exec($mch,$active);
	}while($mrc == CURLM_CALL_MULTI_PERFORM);

	while($active and $mrc == CURLM_OK){
		if(curl_multi_select($mch) != -1){
			do{
				$mrc = curl_multi_exec($mch,$active);
			}while($mrc == CURLM_CALL_MULTI_PERFORM);
		}
	}

	//搜集curl信息
	$arrinfo = [];
	foreach($data as $i => $url){
		$arrinfo[$i]['html'] = curl_multi_getcontent($connarr[$i]);
		$curlinfo = curl_getinfo($connarr[$i]);
		$arrinfo[$i]['total_time'] = $curlinfo['total_time'];
		$arrinfo[$i]['http_code'] = $curlinfo['http_code'];
		curl_multi_remove_handle($mch,$connarr[$i]);
		curl_close($connarr[$i]);
	}
	curl_multi_close($mch);

	return $arrinfo;
}

//返回一个绝对唯一ID
function unqid(){
	return substr(md5(uniqid(mt_rand(),true)),8,16);
}

//字符串转ASCII
function to_ascii($c,$prefix = "&#"){
	$len = strlen($c);
	$a = 0;
	while($a < $len){
		$ud = 0;
		if(ord($c[$a]) >= 0 && ord($c[$a]) <= 127){
			$ud = ord($c[$a]);
			$a += 1;
		}
		elseif(ord($c[$a]) >= 192 && ord($c[$a]) <= 223){
			$ud = (ord($c[$a]) - 192) * 64 + (ord($c[$a + 1]) - 128);
			$a += 2;
		}
		elseif(ord($c[$a]) >= 224 && ord($c[$a]) <= 239){
			$ud = (ord($c[$a]) - 224) * 4096 + (ord($c[$a + 1]) - 128) * 64 + (ord($c[$a + 2]) - 128);
			$a += 3;
		}
		elseif(ord($c[$a]) >= 240 && ord($c[$a]) <= 247){
			$ud = (ord($c[$a]) - 240) * 262144 + (ord($c[$a + 1]) - 128) * 4096 + (ord($c[$a + 2]) - 128) * 64 + (ord($c[$a + 3]) - 128);
			$a += 4;
		}
		elseif(ord($c[$a]) >= 248 && ord($c[$a]) <= 251){
			$ud = (ord($c[$a]) - 248) * 16777216 + (ord($c[$a + 1]) - 128) * 262144 + (ord($c[$a + 2]) - 128) * 4096 + (ord($c[$a + 3]) - 128) * 64 + (ord($c[$a + 4]) - 128);
			$a += 5;
		}
		elseif(ord($c[$a]) >= 252 && ord($c[$a]) <= 253){
			$ud = (ord($c[$a]) - 252) * 1073741824 + (ord($c[$a + 1]) - 128) * 16777216 + (ord($c[$a + 2]) - 128) * 262144 + (ord($c[$a + 3]) - 128) * 4096 + (ord($c[$a + 4]) - 128) * 64 + (ord($c[$a + 5]) - 128);
			$a += 6;
		}
		elseif(ord($c[$a]) >= 254 && ord($c[$a]) <= 255){
			$ud = false;
		}
		$scill .= $prefix.$ud.";";
	}
	return $scill;
}
