<?php

//短信发送接口
function sendsms($to,$yzm,$qm = '聚名网',$mb = 'SMS_20190914054812'){
	$pdata = [
		"url"  => "http://dx.jm.boogooo.com:8188/api/sms/send",
		'data' => [
			'code'       => $mb,//模板ID
			'sign'       => $qm,//短信签名
			'mobile'     => $to,
			'data'       => ['code' => $yzm],
			'timestamp'  => time(),
			'access_key' => RXX('x','sms_key'), //access_key请联系王宗强同学进行开通
		],
	];
	$re = chttp($pdata);
	$rea = jd($re);
	if(empty($re) || !is_array($rea)){
		return ['code' => -1,'msg' => "发送失败,请求超时"];
	}
	if($rea['code'] == 1){
		return ['code' => 1,'msg' => "发送成功"];
	}
	return ['code' => -1,'msg' => $rea['msg']];
}