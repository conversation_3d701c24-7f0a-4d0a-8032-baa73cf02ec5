2025-07-31 14:13:39	127.0.0.1	/admin_xm_order/queue_change_status_pass	执行SQL错误!<br>SELECT id,xm_id,ks_rq,js_rq FROM x_tg_order WHERE zt = '3' AND sh_zt = '1' AND id IN ('4')<br>Table 'dev_hwrtb_bd_cn.x_tg_order' doesn't exist
2025-07-31 14:16:25	127.0.0.1	/admin_xm_order/queue_change_status_pass	执行SQL错误!<br>UPDATE x_tg_order SET `sh_zt`='2',`gx_sj`='2025-07-31 14:16:25' WHERE id IN ('4') AND zt = '3' AND sh_zt = '1'<br>
2025-07-31 15:42:33	127.0.0.1	/admin_xm_order/batch_change_status_pass	执行SQL错误!<br>SELECT id,xm_id,uid,total_money,pt_money,u_money,p_uid,p_u_money,grand_p_uid,grand_p_u_money,bt_money,pz_id,app_rw_id,app_rw_name,zt,ms,total_count,rate,bb_id,js_rq,order_lx,is_dong FROM x_tg_order WHERE zt = '3' AND id IN ('4') ORDER BY uid asc<br>Unknown column 'grand_p_uid' in 'field list'
2025-07-31 15:42:35	127.0.0.1	/admin_xm_order/batch_change_status_pass	执行SQL错误!<br>SELECT id,xm_id,uid,total_money,pt_money,u_money,p_uid,p_u_money,grand_p_uid,grand_p_u_money,bt_money,pz_id,app_rw_id,app_rw_name,zt,ms,total_count,rate,bb_id,js_rq,order_lx,is_dong FROM x_tg_order WHERE zt = '3' AND id IN ('4') ORDER BY uid asc<br>Unknown column 'grand_p_uid' in 'field list'
2025-07-31 15:43:41	127.0.0.1	/admin_xm_order/batch_change_status_pass	执行SQL错误!<br>SELECT id,xm_id,uid,total_money,pt_money,u_money,p_uid,p_u_money,g_uid,grand_p_u_money,bt_money,pz_id,app_rw_id,app_rw_name,zt,ms,total_count,rate,bb_id,js_rq,order_lx,is_dong FROM x_tg_order WHERE zt = '3' AND id IN ('4') ORDER BY uid asc<br>Unknown column 'app_rw_id' in 'field list'
2025-07-31 15:43:58	127.0.0.1	/admin_xm_order/batch_change_status_pass	执行SQL错误!<br>SELECT id,xm_id,uid,total_money,pt_money,u_money,p_uid,p_u_money,g_uid,grand_p_u_money,bt_money,pz_id,xm_price_id,app_rw_name,zt,ms,total_count,rate,bb_id,js_rq,order_lx,is_dong FROM x_tg_order WHERE zt = '3' AND id IN ('4') ORDER BY uid asc<br>Unknown column 'app_rw_name' in 'field list'
2025-07-31 15:48:00	127.0.0.1	/admin_xm_order/batch_change_status_pass	执行SQL错误!<br>SELECT id FROM   WHERE lx = '1' LIMIT 1<br>
2025-07-31 15:49:42	127.0.0.1	/admin_xm_order/batch_change_status_pass	执行SQL错误!<br>SELECT id FROM x_user  WHERE lx = '1' LIMIT 1<br>
2025-07-31 17:06:12	127.0.0.1	/admin_xm_order/batch_change_status_pass	执行SQL错误!<br>SELECT id FROM x_user  WHERE lx = '2' LIMIT 1<br>
2025-07-31 17:38:04	127.0.0.1	/admin_xm_order/batch_change_status_pass	执行SQL错误!<br>SELECT id FROM x_user  WHERE  = '2' LIMIT 1<br>
2025-07-31 17:41:10	127.0.0.1	/admin_xm_order/batch_change_status_pass	执行SQL错误!<br>SELECT id FROM x_user  WHERE  = '2' LIMIT 1<br>
2025-07-31 17:42:08	127.0.0.1	/admin_xm_order/batch_change_status_pass	执行SQL错误!<br>SELECT id FROM x_user  WHERE  = '2' LIMIT 1<br>
2025-07-31 17:42:35	127.0.0.1	/admin_xm_order/batch_change_status_pass	执行SQL错误!<br>SELECT id FROM x_user  WHERE  = '2' LIMIT 1<br>
2025-07-31 17:46:36	127.0.0.1	/admin_xm_order/batch_change_status_pass	执行SQL错误!<br>SELECT lx FROM x_user  WHERE id = '200115' LIMIT 1<br>
2025-07-31 17:51:26	127.0.0.1	/admin_xm_order/batch_change_status_pass	执行SQL错误!<br>SELECT lx FROM x_user  WHERE id = '200115' LIMIT 1<br>
2025-07-31 18:05:14	127.0.0.1	/admin_xm_order/batch_change_status_pass	执行SQL错误!<br>SELECT id,lx FROM x_user WHERE id IN ('10029')<br>
2025-07-31 18:07:26	127.0.0.1	/admin_xm_order/batch_change_status_pass	执行SQL错误!<br>INSERT INTO x_order SET `lx`='发放佣金',`uid`='101',`cbuid`='10029',`ym`='4',`ym2`='6',`ym3`='13',`zqian`='99999.99',`zqian2`='99999.99',`jzqian`='0',`zkqian`='0',`qian`='0',`cbqian`='0',`cbqian2`='0',`cbqianx`='99994.99',`syqian`='0',`syqianx`='5',`sm`='发放佣金，做单日期：2025-07-31',`tjsj`='2025-07-31 18:07:26',`gxsj`='2025-07-31 18:07:26',`odata`='{"zj":[{"uid":101,"zqian":99999.999999,"zkqian":0,"zffs":3,"ym":"4","ym2":"6","ym3":"13","fs":2,"zjlx":"发放佣金","zjzu":"发放佣金","qt":{"bb_id":"4","bb_value":"","pz_id":"0"},"sm":"发放佣金，做单日期：2025-07-31","fapiao":0,"qian":99999.99,"jzqian":0,"vblx":3}],"cb":[{"uid":10029,"zqian":99994.999999,"zkqian":0,"zffs":0,"ym":"4","ym2":"6","ym3":"13","fs":1,"zjlx":"发放佣金","zjzu":"发放佣金","qt":{"bb_id":"4","bb_value":"","pz_id":"0"},"sm":"用户做单佣金，做单日期：2025-07-31","cbifqian":0,"qian":99994.99,"jzqian":0,"vblx":1}]}',`syuid`='10014',`ifxin`='1',`czip`='127.0.0.1',`czip2`='127.0.0.1',`zu`='发放佣金',`czuid_zhu`='101',`qt`='',`zjfs`='2'<br>
2025-07-31 18:11:02	127.0.0.1	/admin_xm_order/batch_change_status_pass	执行SQL错误!<br>SELECT  FROM x_user  WHERE id = '101' LIMIT 1<br>
2025-07-31 19:37:01	127.0.0.1	/admin_xm_order/batch_change_status_pass	执行SQL错误!<br>SELECT  FROM x_user  WHERE id = '101' LIMIT 1<br>
2025-07-31 19:39:48	127.0.0.1	/admin_xm_order/batch_change_status_pass	执行SQL错误!<br>SELECT  FROM x_user  WHERE id = '101' LIMIT 1<br>
2025-07-31 19:40:33	127.0.0.1	/admin_xm_order/batch_change_status_pass	执行SQL错误!<br>SELECT  FROM x_user  WHERE id = '101' LIMIT 1<br>
2025-07-31 19:40:47	127.0.0.1	/admin_xm_order/batch_change_status_pass	执行SQL错误!<br>SELECT  FROM x_user  WHERE id = '101' LIMIT 1<br>
2025-07-31 19:47:07	127.0.0.1	/admin_xm_order/batch_change_status_pass	执行SQL错误!<br>SELECT  FROM x_user  WHERE id = '101' LIMIT 1<br>
2025-07-31 19:48:09	127.0.0.1	/admin_xm_order/batch_change_status_pass	执行SQL错误!<br>SELECT  FROM x_user  WHERE id = '101' LIMIT 1<br>
2025-07-31 19:49:31	127.0.0.1	/admin_xm_order/batch_change_status_pass	执行SQL错误!<br>SELECT  FROM x_user  WHERE id = '101' LIMIT 1<br>
2025-07-31 19:50:07	127.0.0.1	/admin_xm_order/batch_change_status_pass	执行SQL错误!<br>UPDATE x_user SET money=money+'99994.999999',zd_money=zd_money+'99994.999999',all_money=all_money+'99994.999999' WHERE id = '10029'<br>
2025-07-31 20:01:39	127.0.0.1	/admin_xm_order/batch_change_status_pass	执行SQL错误!<br>UPDATE x_xm_bb SET `latest_jsrq`='2025-07-31',`latest_jssj`='2025-07-31 20:01:38' WHERE id = '4' AND (latest_jsrq <= '2025-07-31' OR latest_jsrq IS NULL) AND is_del = '0'<br>
2025-07-31 20:03:44	127.0.0.1	/admin_xm_order/batch_change_status_pass	执行SQL错误!<br>UPDATE x_tg_user SET money=money+'5.000000' WHERE id = '1' AND user_lx = '2'<br>
2025-07-31 20:08:40	127.0.0.1	/admin_xm_order/batch_change_status_pass	执行SQL错误!<br>UPDATE x_tg_user SET money=money+'5.000000' WHERE id = '1' AND user_lx = '2'<br>
2025-07-31 20:15:32	127.0.0.1	/admin_xm_order/batch_change_status_pass	执行SQL错误!<br>UPDATE x_tg_detail SET `status`='1',`updated_at`='2025-07-31 20:15:32',`supplier_uid`='101' WHERE id = '4' AND status != '1'<br>
