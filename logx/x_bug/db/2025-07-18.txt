2025-07-18 00:09:43	127.0.0.1	/admin_xm_main/get_xm_config	连接数据库失败!<br>Network is unreachable
2025-07-18 00:09:43	127.0.0.1	/admin_xm_main/get_list	连接数据库失败!<br>Network is unreachable
2025-07-18 21:07:49	127.0.0.1	/api/comm_auth/send_code	执行SQL错误!<br>SELECT id,email,zt,is_sm FROM x_user  WHERE id = '10007' order by zt asc,id desc LIMIT 1<br>Unknown column 'is_sm' in 'field list'
2025-07-18 21:38:05	172.24.64.177	/admin_user_main/edit	执行SQL错误!<br>UPDATE x_user SET  WHERE id = '10001'<br>You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'WHERE id = '10001'' at line 1
2025-07-18 21:38:08	172.24.64.177	/admin_user_main/edit	执行SQL错误!<br>UPDATE x_user SET  WHERE id = '10001'<br>You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'WHERE id = '10001'' at line 1
2025-07-18 21:38:48	172.24.64.177	/admin_user_main/edit	执行SQL错误!<br>UPDATE x_user SET  WHERE id = '10001'<br>You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'WHERE id = '10001'' at line 1
2025-07-18 22:02:03	172.24.64.177	/admin_user_main/set_zt	执行SQL错误!<br>SELECT id,zt,disable_reason,disable_bz,forbid_tx,disable_tx_reason,disable_tx_bz,is_wg,has_bt_qx FROM x_user  WHERE id = '10001' LIMIT 1<br>Unknown column 'disable_tx_bz' in 'field list'
2025-07-18 22:02:06	172.24.64.177	/admin_user_main/set_zt	执行SQL错误!<br>SELECT id,zt,disable_reason,disable_bz,forbid_tx,disable_tx_reason,disable_tx_bz,is_wg,has_bt_qx FROM x_user  WHERE id = '10001' LIMIT 1<br>Unknown column 'disable_tx_bz' in 'field list'
2025-07-18 22:27:28	172.24.64.177	/admin_user_main/set_zt	执行SQL错误!<br>SELECT id,zt,disable_reason,disable_bz,forbid_tx,disable_tx_reason,disable_tx_bz,is_wg,has_bt_qx FROM x_user  WHERE id = '10001' LIMIT 1<br>Unknown column 'disable_tx_bz' in 'field list'
2025-07-18 22:29:26	172.24.64.177	/admin_user_main/set_zt	执行SQL错误!<br>UPDATE x_user SET  WHERE id = '10001'<br>You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'WHERE id = '10001'' at line 1
2025-07-18 22:29:28	172.24.64.177	/admin_user_main/set_zt	执行SQL错误!<br>UPDATE x_user SET  WHERE id = '10001'<br>You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'WHERE id = '10001'' at line 1
