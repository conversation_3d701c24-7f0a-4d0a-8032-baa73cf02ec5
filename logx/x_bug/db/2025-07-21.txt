2025-07-21 10:35:15	127.0.0.1	/admin_main/xconfig_list?page=1&limit=20	连接数据库失败!<br>Operation timed out
2025-07-21 11:29:58	127.0.0.1	/admin_main/xconfig_list?page=1&limit=20	连接数据库失败!<br>Operation timed out
2025-07-21 13:38:59	127.0.0.1	/api/user/edit_email_step1	执行SQL错误!<br>UPDATE x_user SET `email`='',`gxsj`='2025-07-21 13:38:59' WHERE id = '10007'<br>Duplicate entry '' for key 'x_user.idx_email'
2025-07-21 13:58:30	*************	/admin_pz_main/batch_audit	执行SQL错误!<br>INSERT INTO `x_msg_email` (`uid`,`email`,`type`,`title`,`nr`,`tjsj`,`tjip`,`gxsj`,`gxip`,`czuid`,`zt`,`bz`) VALUES ('10007','+UclTuyHtDF5uB5NqoWEVLSRKu+Z8+bN0G6NAug4woA=','4','【测试项目】Report Message Notification','DEAR USER, YOUR CERTIFICATE FOR THE 测试项目 PROJECT  REPORT passed HAS BEEN AUDITED, PLEASE LOG IN TO THE SYSTEM TO VIEW THE DETAILS.','2025-07-21 13:58:30','*************','2025-07-21 13:58:30','*************','78'),('10007','+UclTuyHtDF5uB5NqoWEVLSRKu+Z8+bN0G6NAug4woA=','4','【测试项目】Report Message Notification','DEAR USER, YOUR CERTIFICATE FOR THE 测试项目 PROJECT  REPORT passed HAS BEEN AUDITED, PLEASE LOG IN TO THE SYSTEM TO VIEW THE DETAILS.','2025-07-21 13:58:30','*************','2025-07-21 13:58:30','*************','78'),('10007','+UclTuyHtDF5uB5NqoWEVLSRKu+Z8+bN0G6NAug4woA=','4','【测试项目】Report Message Notification','DEAR USER, YOUR CERTIFICATE FOR THE 测试项目 PROJECT  REPORT passed HAS BEEN AUDITED, PLEASE LOG IN TO THE SYSTEM TO VIEW THE DETAILS.','2025-07-21 13:58:30','*************','2025-07-21 13:58:30','*************','78'),('10014','','4','【测试项目】Report Message Notification','DEAR USER, YOUR CERTIFICATE FOR THE 测试项目 PROJECT  REPORT passed HAS BEEN AUDITED, PLEASE LOG IN TO THE SYSTEM TO VIEW THE DETAILS.','2025-07-21 13:58:30','*************','2025-07-21 13:58:30','*************','78','3','用户邮箱不存在'),('10014','','4','【测试项目】Report Message Notification','DEAR USER, YOUR CERTIFICATE FOR THE 测试项目 PROJECT  REPORT passed HAS BEEN AUDITED, PLEASE LOG IN TO THE SYSTEM TO VIEW THE DETAILS.','2025-07-21 13:58:30','*************','2025-07-21 13:58:30','*************','78','3','用户邮箱不存在'),('10014','','4','【测试项目】Report Message Notification','DEAR USER, YOUR CERTIFICATE FOR THE 测试项目 PROJECT  REPORT passed HAS BEEN AUDITED, PLEASE LOG IN TO THE SYSTEM TO VIEW THE DETAILS.','2025-07-21 13:58:30','*************','2025-07-21 13:58:30','*************','78','3','用户邮箱不存在'),('10007','+UclTuyHtDF5uB5NqoWEVLSRKu+Z8+bN0G6NAug4woA=','4','【测试项目】Report Message Notification','DEAR USER, YOUR CERTIFICATE FOR THE 测试项目 PROJECT  REPORT passed HAS BEEN AUDITED, PLEASE LOG IN TO THE SYSTEM TO VIEW THE DETAILS.','2025-07-21 13:58:30','*************','2025-07-21 13:58:30','*************','78'),('10014','','4','【测试项目】Report Message Notification','DEAR USER, YOUR CERTIFICATE FOR THE 测试项目 PROJECT  REPORT passed HAS BEEN AUDITED, PLEASE LOG IN TO THE SYSTEM TO VIEW THE DETAILS.','2025-07-21 13:58:30','*************','2025-07-21 13:58:30','*************','78','3','用户邮箱不存在')<br>Column count doesn't match value count at row 1
2025-07-21 17:47:54	*************	/admin_zd_tpl/get_all	连接数据库失败!<br>Can't assign requested address
2025-07-21 17:47:54	*************	/admin_xm_main/get_xm_config	连接数据库失败!<br>Network is unreachable
2025-07-21 17:55:39	*************	/admin_xm_main/get_list	连接数据库失败!<br>Network is unreachable
2025-07-21 17:55:39	*************	/admin_main/list_zh	连接数据库失败!<br>Network is unreachable
2025-07-21 19:57:04	127.0.0.1	/admin_zd_tpl/add_edit	执行SQL错误!<br>INSERT INTO `x_zd_tpl` (`name`,`lang`,`lx`,`type`,`tips`,`help`,`rule_id`,`rules`,`len_min`,`len_max`,`is_required`,`is_multi`,`px`,`is_check_wgc`,`is_ty`,`pid`,`top_pid`,`bs`) VALUES ('通用报备','zh-Hans','11','10','请选择报备','报备选择说明','12','YmJfaWQ=','1','255','1','0','91','0','1','0','0','BS_BB_ID'),('通用报备','en','11','10','请选择报备','报备选择说明','12','YmJfaWQ=','1','255','1','0','91','0','1','0','0','BS_BB_ID'),('通用报备','zh-Hant','11','10','请选择报备','报备选择说明','12','YmJfaWQ=','1','255','1','0','91','0','1','0','0','BS_BB_ID')<br>Duplicate entry 'BS_BB_ID' for key 'x_zd_tpl.idx_bs'
2025-07-21 19:57:10	127.0.0.1	/admin_zd_tpl/add_edit	执行SQL错误!<br>INSERT INTO `x_zd_tpl` (`name`,`lang`,`lx`,`type`,`tips`,`help`,`rule_id`,`rules`,`len_min`,`len_max`,`is_required`,`is_multi`,`px`,`is_check_wgc`,`is_ty`,`pid`,`top_pid`,`bs`) VALUES ('通用报备','zh-Hans','11','10','请选择报备','报备选择说明','12','YmJfaWQ=','1','255','1','0','91','0','1','0','0','BS_BB_ID'),('通用报备','en','11','10','请选择报备','报备选择说明','12','YmJfaWQ=','1','255','1','0','91','0','1','0','0','BS_BB_ID'),('通用报备','zh-Hant','11','10','请选择报备','报备选择说明','12','YmJfaWQ=','1','255','1','0','91','0','1','0','0','BS_BB_ID')<br>Duplicate entry 'BS_BB_ID' for key 'x_zd_tpl.idx_bs'
