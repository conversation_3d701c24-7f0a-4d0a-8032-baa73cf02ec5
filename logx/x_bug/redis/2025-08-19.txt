2025-08-19 16:03:47	0.0.0.0		
Redis连接失败!RedisException: Connection refused in /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php:42
Stack trace:
#0 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php(42): Redis->pconnect('127.0.0.1', '6379')
#1 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php(215): redisx->_get_redis_inst(true)
#2 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php(79): redisx->_call_redis_method('get', Array)
#3 /Users/<USER>/wwwroot/rtb_haiwai/ju/fun.php(1472): redisx->get('xt_config:jiami...')
#4 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/db.php(747): S('xt_config:jiami...')
#5 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/db.php(569): db->gethc('SELECT data FRO...', 864000)
#6 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/db.php(597): db->get('jm_config', 'data', ' WHERE name = '...', 864000, false)
#7 /Users/<USER>/wwwroot/rtb_haiwai/app/m/m.base.php(58): db->get_field('jm_config', 'data', 'name = 'jiami_x...', 864000)
#8 /Users/<USER>/wwwroot/rtb_haiwai/app/fun.php(67): m_base->get_config('jiami_x_xapi', 1)
#9 /Users/<USER>/wwwroot/rtb_haiwai/ju/fun.php(172): RC('jiami_x_xapi')
#10 /Users/<USER>/wwwroot/rtb_haiwai/app/m/rw/m.rw.langai.php(25): RXX('xapi', 'translate_key')
#11 /Users/<USER>/wwwroot/rtb_haiwai/ju/fun.php(200): m_rw_langai->__construct()
#12 /Users/<USER>/wwwroot/rtb_haiwai/app/c/rw/c.rw.lang.php(8): D('m_rw_langai')
#13 /Users/<USER>/wwwroot/rtb_haiwai/ju/ju.php(89): c_rw_lang->trans_lang()
#14 /Users/<USER>/wwwroot/rtb_haiwai/cli.php(21): require('/Users/<USER>/ww...')
#15 {main}
