2025-07-18 15:13:03	172.24.64.6	/api/site_ads/get_ads	
Redis连接失败!RedisException: Connection refused in /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php:42
Stack trace:
#0 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php(42): Redis->pconnect('127.0.0.1', '6379')
#1 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php(215): redisx->_get_redis_inst(true)
#2 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php(79): redisx->_call_redis_method('get', Array)
#3 /Users/<USER>/wwwroot/rtb_haiwai/ju/fun.php(1472): redisx->get('xt_config:jiami...')
#4 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/db.php(747): S('xt_config:jiami...')
#5 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/db.php(569): db->gethc('SELECT data FRO...', 864000)
#6 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/db.php(597): db->get('jm_config', 'data', ' WHERE name = '...', 864000, false)
#7 /Users/<USER>/wwwroot/rtb_haiwai/app/m/m.base.php(54): db->get_field('jm_config', 'data', 'name = 'jiami_x...', 864000)
#8 /Users/<USER>/wwwroot/rtb_haiwai/app/fun.php(67): m_base->get_config('jiami_x_common', 1)
#9 /Users/<USER>/wwwroot/rtb_haiwai/ju/fun.php(172): RC('jiami_x_common')
#10 /Users/<USER>/wwwroot/rtb_haiwai/app/c/api/c.api.base.php(114): RXX('common', 'UP_APPSTORE_VER...')
#11 /Users/<USER>/wwwroot/rtb_haiwai/app/c/api/c.api.base.php(37): c_api_base->_set_app_version()
#12 /Users/<USER>/wwwroot/rtb_haiwai/ju/ju.php(88): c_api_base->__construct()
#13 /Users/<USER>/wwwroot/rtb_haiwai/www/index.php(8): require('/Users/<USER>/ww...')
#14 {main}
2025-07-18 15:13:03	172.24.64.6	/api/xm/get_visitor_list	
Redis连接失败!RedisException: Connection refused in /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php:42
Stack trace:
#0 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php(42): Redis->pconnect('127.0.0.1', '6379')
#1 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php(215): redisx->_get_redis_inst(true)
#2 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php(79): redisx->_call_redis_method('get', Array)
#3 /Users/<USER>/wwwroot/rtb_haiwai/ju/fun.php(1472): redisx->get('xt_config:jiami...')
#4 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/db.php(747): S('xt_config:jiami...')
#5 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/db.php(569): db->gethc('SELECT data FRO...', 864000)
#6 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/db.php(597): db->get('jm_config', 'data', ' WHERE name = '...', 864000, false)
#7 /Users/<USER>/wwwroot/rtb_haiwai/app/m/m.base.php(54): db->get_field('jm_config', 'data', 'name = 'jiami_x...', 864000)
#8 /Users/<USER>/wwwroot/rtb_haiwai/app/fun.php(67): m_base->get_config('jiami_x_common', 1)
#9 /Users/<USER>/wwwroot/rtb_haiwai/ju/fun.php(172): RC('jiami_x_common')
#10 /Users/<USER>/wwwroot/rtb_haiwai/app/c/api/c.api.base.php(114): RXX('common', 'UP_APPSTORE_VER...')
#11 /Users/<USER>/wwwroot/rtb_haiwai/app/c/api/c.api.base.php(37): c_api_base->_set_app_version()
#12 /Users/<USER>/wwwroot/rtb_haiwai/ju/ju.php(88): c_api_base->__construct()
#13 /Users/<USER>/wwwroot/rtb_haiwai/www/index.php(8): require('/Users/<USER>/ww...')
#14 {main}
2025-07-18 15:13:11	172.24.64.6	/api/xm/get_visitor_list	
Redis连接失败!RedisException: Connection refused in /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php:42
Stack trace:
#0 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php(42): Redis->pconnect('127.0.0.1', '6379')
#1 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php(215): redisx->_get_redis_inst(true)
#2 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php(79): redisx->_call_redis_method('get', Array)
#3 /Users/<USER>/wwwroot/rtb_haiwai/ju/fun.php(1472): redisx->get('xt_config:jiami...')
#4 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/db.php(747): S('xt_config:jiami...')
#5 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/db.php(569): db->gethc('SELECT data FRO...', 864000)
#6 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/db.php(597): db->get('jm_config', 'data', ' WHERE name = '...', 864000, false)
#7 /Users/<USER>/wwwroot/rtb_haiwai/app/m/m.base.php(54): db->get_field('jm_config', 'data', 'name = 'jiami_x...', 864000)
#8 /Users/<USER>/wwwroot/rtb_haiwai/app/fun.php(67): m_base->get_config('jiami_x_common', 1)
#9 /Users/<USER>/wwwroot/rtb_haiwai/ju/fun.php(172): RC('jiami_x_common')
#10 /Users/<USER>/wwwroot/rtb_haiwai/app/c/api/c.api.base.php(114): RXX('common', 'UP_APPSTORE_VER...')
#11 /Users/<USER>/wwwroot/rtb_haiwai/app/c/api/c.api.base.php(37): c_api_base->_set_app_version()
#12 /Users/<USER>/wwwroot/rtb_haiwai/ju/ju.php(88): c_api_base->__construct()
#13 /Users/<USER>/wwwroot/rtb_haiwai/www/index.php(8): require('/Users/<USER>/ww...')
#14 {main}
2025-07-18 15:13:11	172.24.64.6	/api/site_ads/get_ads	
Redis连接失败!RedisException: Connection refused in /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php:42
Stack trace:
#0 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php(42): Redis->pconnect('127.0.0.1', '6379')
#1 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php(215): redisx->_get_redis_inst(true)
#2 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php(79): redisx->_call_redis_method('get', Array)
#3 /Users/<USER>/wwwroot/rtb_haiwai/ju/fun.php(1472): redisx->get('xt_config:jiami...')
#4 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/db.php(747): S('xt_config:jiami...')
#5 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/db.php(569): db->gethc('SELECT data FRO...', 864000)
#6 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/db.php(597): db->get('jm_config', 'data', ' WHERE name = '...', 864000, false)
#7 /Users/<USER>/wwwroot/rtb_haiwai/app/m/m.base.php(54): db->get_field('jm_config', 'data', 'name = 'jiami_x...', 864000)
#8 /Users/<USER>/wwwroot/rtb_haiwai/app/fun.php(67): m_base->get_config('jiami_x_common', 1)
#9 /Users/<USER>/wwwroot/rtb_haiwai/ju/fun.php(172): RC('jiami_x_common')
#10 /Users/<USER>/wwwroot/rtb_haiwai/app/c/api/c.api.base.php(114): RXX('common', 'UP_APPSTORE_VER...')
#11 /Users/<USER>/wwwroot/rtb_haiwai/app/c/api/c.api.base.php(37): c_api_base->_set_app_version()
#12 /Users/<USER>/wwwroot/rtb_haiwai/ju/ju.php(88): c_api_base->__construct()
#13 /Users/<USER>/wwwroot/rtb_haiwai/www/index.php(8): require('/Users/<USER>/ww...')
#14 {main}
2025-07-18 15:13:57	172.24.64.177	/api/site_ads/get_ads	
Redis连接失败!RedisException: Connection refused in /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php:42
Stack trace:
#0 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php(42): Redis->pconnect('127.0.0.1', '6379')
#1 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php(215): redisx->_get_redis_inst(true)
#2 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php(79): redisx->_call_redis_method('get', Array)
#3 /Users/<USER>/wwwroot/rtb_haiwai/ju/fun.php(1472): redisx->get('xt_config:jiami...')
#4 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/db.php(747): S('xt_config:jiami...')
#5 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/db.php(569): db->gethc('SELECT data FRO...', 864000)
#6 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/db.php(597): db->get('jm_config', 'data', ' WHERE name = '...', 864000, false)
#7 /Users/<USER>/wwwroot/rtb_haiwai/app/m/m.base.php(54): db->get_field('jm_config', 'data', 'name = 'jiami_x...', 864000)
#8 /Users/<USER>/wwwroot/rtb_haiwai/app/fun.php(67): m_base->get_config('jiami_x_common', 1)
#9 /Users/<USER>/wwwroot/rtb_haiwai/ju/fun.php(172): RC('jiami_x_common')
#10 /Users/<USER>/wwwroot/rtb_haiwai/app/c/api/c.api.base.php(114): RXX('common', 'UP_APPSTORE_VER...')
#11 /Users/<USER>/wwwroot/rtb_haiwai/app/c/api/c.api.base.php(37): c_api_base->_set_app_version()
#12 /Users/<USER>/wwwroot/rtb_haiwai/ju/ju.php(88): c_api_base->__construct()
#13 /Users/<USER>/wwwroot/rtb_haiwai/www/index.php(8): require('/Users/<USER>/ww...')
#14 {main}
2025-07-18 15:13:57	172.24.64.177	/api/xm/get_visitor_list	
Redis连接失败!RedisException: Connection refused in /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php:42
Stack trace:
#0 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php(42): Redis->pconnect('127.0.0.1', '6379')
#1 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php(215): redisx->_get_redis_inst(true)
#2 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php(79): redisx->_call_redis_method('get', Array)
#3 /Users/<USER>/wwwroot/rtb_haiwai/ju/fun.php(1472): redisx->get('xt_config:jiami...')
#4 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/db.php(747): S('xt_config:jiami...')
#5 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/db.php(569): db->gethc('SELECT data FRO...', 864000)
#6 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/db.php(597): db->get('jm_config', 'data', ' WHERE name = '...', 864000, false)
#7 /Users/<USER>/wwwroot/rtb_haiwai/app/m/m.base.php(54): db->get_field('jm_config', 'data', 'name = 'jiami_x...', 864000)
#8 /Users/<USER>/wwwroot/rtb_haiwai/app/fun.php(67): m_base->get_config('jiami_x_common', 1)
#9 /Users/<USER>/wwwroot/rtb_haiwai/ju/fun.php(172): RC('jiami_x_common')
#10 /Users/<USER>/wwwroot/rtb_haiwai/app/c/api/c.api.base.php(114): RXX('common', 'UP_APPSTORE_VER...')
#11 /Users/<USER>/wwwroot/rtb_haiwai/app/c/api/c.api.base.php(37): c_api_base->_set_app_version()
#12 /Users/<USER>/wwwroot/rtb_haiwai/ju/ju.php(88): c_api_base->__construct()
#13 /Users/<USER>/wwwroot/rtb_haiwai/www/index.php(8): require('/Users/<USER>/ww...')
#14 {main}
2025-07-18 15:14:02	172.24.64.177	/api/xm/get_visitor_list	
Redis连接失败!RedisException: Connection refused in /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php:42
Stack trace:
#0 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php(42): Redis->pconnect('127.0.0.1', '6379')
#1 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php(215): redisx->_get_redis_inst(true)
#2 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php(79): redisx->_call_redis_method('get', Array)
#3 /Users/<USER>/wwwroot/rtb_haiwai/ju/fun.php(1472): redisx->get('xt_config:jiami...')
#4 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/db.php(747): S('xt_config:jiami...')
#5 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/db.php(569): db->gethc('SELECT data FRO...', 864000)
#6 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/db.php(597): db->get('jm_config', 'data', ' WHERE name = '...', 864000, false)
#7 /Users/<USER>/wwwroot/rtb_haiwai/app/m/m.base.php(54): db->get_field('jm_config', 'data', 'name = 'jiami_x...', 864000)
#8 /Users/<USER>/wwwroot/rtb_haiwai/app/fun.php(67): m_base->get_config('jiami_x_common', 1)
#9 /Users/<USER>/wwwroot/rtb_haiwai/ju/fun.php(172): RC('jiami_x_common')
#10 /Users/<USER>/wwwroot/rtb_haiwai/app/c/api/c.api.base.php(114): RXX('common', 'UP_APPSTORE_VER...')
#11 /Users/<USER>/wwwroot/rtb_haiwai/app/c/api/c.api.base.php(37): c_api_base->_set_app_version()
#12 /Users/<USER>/wwwroot/rtb_haiwai/ju/ju.php(88): c_api_base->__construct()
#13 /Users/<USER>/wwwroot/rtb_haiwai/www/index.php(8): require('/Users/<USER>/ww...')
#14 {main}
2025-07-18 15:14:02	172.24.64.177	/api/site_ads/get_ads	
Redis连接失败!RedisException: Connection refused in /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php:42
Stack trace:
#0 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php(42): Redis->pconnect('127.0.0.1', '6379')
#1 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php(215): redisx->_get_redis_inst(true)
#2 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php(79): redisx->_call_redis_method('get', Array)
#3 /Users/<USER>/wwwroot/rtb_haiwai/ju/fun.php(1472): redisx->get('xt_config:jiami...')
#4 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/db.php(747): S('xt_config:jiami...')
#5 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/db.php(569): db->gethc('SELECT data FRO...', 864000)
#6 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/db.php(597): db->get('jm_config', 'data', ' WHERE name = '...', 864000, false)
#7 /Users/<USER>/wwwroot/rtb_haiwai/app/m/m.base.php(54): db->get_field('jm_config', 'data', 'name = 'jiami_x...', 864000)
#8 /Users/<USER>/wwwroot/rtb_haiwai/app/fun.php(67): m_base->get_config('jiami_x_common', 1)
#9 /Users/<USER>/wwwroot/rtb_haiwai/ju/fun.php(172): RC('jiami_x_common')
#10 /Users/<USER>/wwwroot/rtb_haiwai/app/c/api/c.api.base.php(114): RXX('common', 'UP_APPSTORE_VER...')
#11 /Users/<USER>/wwwroot/rtb_haiwai/app/c/api/c.api.base.php(37): c_api_base->_set_app_version()
#12 /Users/<USER>/wwwroot/rtb_haiwai/ju/ju.php(88): c_api_base->__construct()
#13 /Users/<USER>/wwwroot/rtb_haiwai/www/index.php(8): require('/Users/<USER>/ww...')
#14 {main}
2025-07-18 15:14:19	172.24.64.63	/api/comm_auth/logout	
Redis连接失败!RedisException: Connection refused in /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php:42
Stack trace:
#0 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php(42): Redis->pconnect('127.0.0.1', '6379')
#1 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php(215): redisx->_get_redis_inst(true)
#2 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php(79): redisx->_call_redis_method('get', Array)
#3 /Users/<USER>/wwwroot/rtb_haiwai/ju/fun.php(1472): redisx->get('auth-user-api::...')
#4 /Users/<USER>/wwwroot/rtb_haiwai/app/fun.php(611): S('auth-user-api::...')
#5 /Users/<USER>/wwwroot/rtb_haiwai/app/c/api/c.api.base.php(53): jwt_login_check('eyJkYXRhIjp7InV...', 'api')
#6 /Users/<USER>/wwwroot/rtb_haiwai/app/c/api/c.api.base.php(36): c_api_base->_yzqx()
#7 /Users/<USER>/wwwroot/rtb_haiwai/app/c/api/comm/c.api.comm.base.php(7): c_api_base->__construct()
#8 /Users/<USER>/wwwroot/rtb_haiwai/app/c/api/comm/c.api.comm.auth.php(8): c_api_comm_base->__construct()
#9 /Users/<USER>/wwwroot/rtb_haiwai/ju/ju.php(88): c_api_comm_auth->__construct()
#10 /Users/<USER>/wwwroot/rtb_haiwai/www/index.php(8): require('/Users/<USER>/ww...')
#11 {main}
2025-07-18 15:14:24	172.24.64.63	/api/xm/get_list	
Redis连接失败!RedisException: Connection refused in /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php:42
Stack trace:
#0 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php(42): Redis->pconnect('127.0.0.1', '6379')
#1 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php(215): redisx->_get_redis_inst(true)
#2 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php(79): redisx->_call_redis_method('get', Array)
#3 /Users/<USER>/wwwroot/rtb_haiwai/ju/fun.php(1472): redisx->get('auth-user-api::...')
#4 /Users/<USER>/wwwroot/rtb_haiwai/app/fun.php(611): S('auth-user-api::...')
#5 /Users/<USER>/wwwroot/rtb_haiwai/app/c/api/c.api.base.php(53): jwt_login_check('eyJkYXRhIjp7InV...', 'api')
#6 /Users/<USER>/wwwroot/rtb_haiwai/app/c/api/c.api.base.php(36): c_api_base->_yzqx()
#7 /Users/<USER>/wwwroot/rtb_haiwai/ju/ju.php(88): c_api_base->__construct()
#8 /Users/<USER>/wwwroot/rtb_haiwai/www/index.php(8): require('/Users/<USER>/ww...')
#9 {main}
2025-07-18 15:14:24	172.24.64.63	/api/site_ads/get_ads	
Redis连接失败!RedisException: Connection refused in /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php:42
Stack trace:
#0 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php(42): Redis->pconnect('127.0.0.1', '6379')
#1 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php(215): redisx->_get_redis_inst(true)
#2 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php(79): redisx->_call_redis_method('get', Array)
#3 /Users/<USER>/wwwroot/rtb_haiwai/ju/fun.php(1472): redisx->get('auth-user-api::...')
#4 /Users/<USER>/wwwroot/rtb_haiwai/app/fun.php(611): S('auth-user-api::...')
#5 /Users/<USER>/wwwroot/rtb_haiwai/app/c/api/c.api.base.php(53): jwt_login_check('eyJkYXRhIjp7InV...', 'api')
#6 /Users/<USER>/wwwroot/rtb_haiwai/app/c/api/c.api.base.php(36): c_api_base->_yzqx()
#7 /Users/<USER>/wwwroot/rtb_haiwai/ju/ju.php(88): c_api_base->__construct()
#8 /Users/<USER>/wwwroot/rtb_haiwai/www/index.php(8): require('/Users/<USER>/ww...')
#9 {main}
2025-07-18 15:15:11	172.24.64.63	/api/site_ads/get_ads	
Redis连接失败!RedisException: Connection refused in /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php:42
Stack trace:
#0 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php(42): Redis->pconnect('127.0.0.1', '6379')
#1 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php(215): redisx->_get_redis_inst(true)
#2 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php(79): redisx->_call_redis_method('get', Array)
#3 /Users/<USER>/wwwroot/rtb_haiwai/ju/fun.php(1472): redisx->get('xt_config:jiami...')
#4 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/db.php(747): S('xt_config:jiami...')
#5 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/db.php(569): db->gethc('SELECT data FRO...', 864000)
#6 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/db.php(597): db->get('jm_config', 'data', ' WHERE name = '...', 864000, false)
#7 /Users/<USER>/wwwroot/rtb_haiwai/app/m/m.base.php(54): db->get_field('jm_config', 'data', 'name = 'jiami_x...', 864000)
#8 /Users/<USER>/wwwroot/rtb_haiwai/app/fun.php(67): m_base->get_config('jiami_x_common', 1)
#9 /Users/<USER>/wwwroot/rtb_haiwai/ju/fun.php(172): RC('jiami_x_common')
#10 /Users/<USER>/wwwroot/rtb_haiwai/app/c/api/c.api.base.php(114): RXX('common', 'UP_APPSTORE_VER...')
#11 /Users/<USER>/wwwroot/rtb_haiwai/app/c/api/c.api.base.php(37): c_api_base->_set_app_version()
#12 /Users/<USER>/wwwroot/rtb_haiwai/ju/ju.php(88): c_api_base->__construct()
#13 /Users/<USER>/wwwroot/rtb_haiwai/www/index.php(8): require('/Users/<USER>/ww...')
#14 {main}
2025-07-18 15:15:11	172.24.64.63	/api/xm/get_visitor_list	
Redis连接失败!RedisException: Connection refused in /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php:42
Stack trace:
#0 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php(42): Redis->pconnect('127.0.0.1', '6379')
#1 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php(215): redisx->_get_redis_inst(true)
#2 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php(79): redisx->_call_redis_method('get', Array)
#3 /Users/<USER>/wwwroot/rtb_haiwai/ju/fun.php(1472): redisx->get('xt_config:jiami...')
#4 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/db.php(747): S('xt_config:jiami...')
#5 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/db.php(569): db->gethc('SELECT data FRO...', 864000)
#6 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/db.php(597): db->get('jm_config', 'data', ' WHERE name = '...', 864000, false)
#7 /Users/<USER>/wwwroot/rtb_haiwai/app/m/m.base.php(54): db->get_field('jm_config', 'data', 'name = 'jiami_x...', 864000)
#8 /Users/<USER>/wwwroot/rtb_haiwai/app/fun.php(67): m_base->get_config('jiami_x_common', 1)
#9 /Users/<USER>/wwwroot/rtb_haiwai/ju/fun.php(172): RC('jiami_x_common')
#10 /Users/<USER>/wwwroot/rtb_haiwai/app/c/api/c.api.base.php(114): RXX('common', 'UP_APPSTORE_VER...')
#11 /Users/<USER>/wwwroot/rtb_haiwai/app/c/api/c.api.base.php(37): c_api_base->_set_app_version()
#12 /Users/<USER>/wwwroot/rtb_haiwai/ju/ju.php(88): c_api_base->__construct()
#13 /Users/<USER>/wwwroot/rtb_haiwai/www/index.php(8): require('/Users/<USER>/ww...')
#14 {main}
2025-07-18 15:15:24	172.24.64.63	/api/comm_auth/logout	
Redis连接失败!RedisException: Connection refused in /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php:42
Stack trace:
#0 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php(42): Redis->pconnect('127.0.0.1', '6379')
#1 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php(215): redisx->_get_redis_inst(true)
#2 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php(79): redisx->_call_redis_method('get', Array)
#3 /Users/<USER>/wwwroot/rtb_haiwai/ju/fun.php(1472): redisx->get('xt_config:user_...')
#4 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/db.php(747): S('xt_config:user_...')
#5 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/db.php(569): db->gethc('SELECT data FRO...', 864000)
#6 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/db.php(597): db->get('jm_config', 'data', ' WHERE name = '...', 864000, false)
#7 /Users/<USER>/wwwroot/rtb_haiwai/app/m/m.base.php(54): db->get_field('jm_config', 'data', 'name = 'user_lo...', 864000)
#8 /Users/<USER>/wwwroot/rtb_haiwai/app/fun.php(67): m_base->get_config('user_log_no', 1)
#9 /Users/<USER>/wwwroot/rtb_haiwai/app/m/m.base.php(32): RC('user_log_no')
#10 /Users/<USER>/wwwroot/rtb_haiwai/app/fun.php(82): m_base->user_log(0, '/Users/<USER>/ww...', -1005)
#11 /Users/<USER>/wwwroot/rtb_haiwai/app/c/c.base.php(30): user_log(0, 'user')
#12 [internal function]: c_base->__destruct()
#13 {main}
2025-07-18 15:15:25	172.24.64.63	/api/xm/get_visitor_list	
Redis连接失败!RedisException: Connection refused in /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php:42
Stack trace:
#0 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php(42): Redis->pconnect('127.0.0.1', '6379')
#1 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php(215): redisx->_get_redis_inst(true)
#2 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php(79): redisx->_call_redis_method('get', Array)
#3 /Users/<USER>/wwwroot/rtb_haiwai/ju/fun.php(1472): redisx->get('xt_config:jiami...')
#4 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/db.php(747): S('xt_config:jiami...')
#5 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/db.php(569): db->gethc('SELECT data FRO...', 864000)
#6 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/db.php(597): db->get('jm_config', 'data', ' WHERE name = '...', 864000, false)
#7 /Users/<USER>/wwwroot/rtb_haiwai/app/m/m.base.php(54): db->get_field('jm_config', 'data', 'name = 'jiami_x...', 864000)
#8 /Users/<USER>/wwwroot/rtb_haiwai/app/fun.php(67): m_base->get_config('jiami_x_common', 1)
#9 /Users/<USER>/wwwroot/rtb_haiwai/ju/fun.php(172): RC('jiami_x_common')
#10 /Users/<USER>/wwwroot/rtb_haiwai/app/c/api/c.api.base.php(114): RXX('common', 'UP_APPSTORE_VER...')
#11 /Users/<USER>/wwwroot/rtb_haiwai/app/c/api/c.api.base.php(37): c_api_base->_set_app_version()
#12 /Users/<USER>/wwwroot/rtb_haiwai/ju/ju.php(88): c_api_base->__construct()
#13 /Users/<USER>/wwwroot/rtb_haiwai/www/index.php(8): require('/Users/<USER>/ww...')
#14 {main}
2025-07-18 15:15:25	172.24.64.63	/api/site_ads/get_ads	
Redis连接失败!RedisException: Connection refused in /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php:42
Stack trace:
#0 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php(42): Redis->pconnect('127.0.0.1', '6379')
#1 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php(215): redisx->_get_redis_inst(true)
#2 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php(79): redisx->_call_redis_method('get', Array)
#3 /Users/<USER>/wwwroot/rtb_haiwai/ju/fun.php(1472): redisx->get('xt_config:jiami...')
#4 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/db.php(747): S('xt_config:jiami...')
#5 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/db.php(569): db->gethc('SELECT data FRO...', 864000)
#6 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/db.php(597): db->get('jm_config', 'data', ' WHERE name = '...', 864000, false)
#7 /Users/<USER>/wwwroot/rtb_haiwai/app/m/m.base.php(54): db->get_field('jm_config', 'data', 'name = 'jiami_x...', 864000)
#8 /Users/<USER>/wwwroot/rtb_haiwai/app/fun.php(67): m_base->get_config('jiami_x_common', 1)
#9 /Users/<USER>/wwwroot/rtb_haiwai/ju/fun.php(172): RC('jiami_x_common')
#10 /Users/<USER>/wwwroot/rtb_haiwai/app/c/api/c.api.base.php(114): RXX('common', 'UP_APPSTORE_VER...')
#11 /Users/<USER>/wwwroot/rtb_haiwai/app/c/api/c.api.base.php(37): c_api_base->_set_app_version()
#12 /Users/<USER>/wwwroot/rtb_haiwai/ju/ju.php(88): c_api_base->__construct()
#13 /Users/<USER>/wwwroot/rtb_haiwai/www/index.php(8): require('/Users/<USER>/ww...')
#14 {main}
2025-07-18 15:15:54	127.0.0.1	/api/site_ads/get_ads	
Redis连接失败!RedisException: Connection refused in /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php:42
Stack trace:
#0 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php(42): Redis->pconnect('127.0.0.1', '6379')
#1 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php(215): redisx->_get_redis_inst(true)
#2 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php(79): redisx->_call_redis_method('get', Array)
#3 /Users/<USER>/wwwroot/rtb_haiwai/ju/fun.php(1472): redisx->get('xt_config:jiami...')
#4 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/db.php(747): S('xt_config:jiami...')
#5 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/db.php(569): db->gethc('SELECT data FRO...', 864000)
#6 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/db.php(597): db->get('jm_config', 'data', ' WHERE name = '...', 864000, false)
#7 /Users/<USER>/wwwroot/rtb_haiwai/app/m/m.base.php(54): db->get_field('jm_config', 'data', 'name = 'jiami_x...', 864000)
#8 /Users/<USER>/wwwroot/rtb_haiwai/app/fun.php(67): m_base->get_config('jiami_x_common', 1)
#9 /Users/<USER>/wwwroot/rtb_haiwai/ju/fun.php(172): RC('jiami_x_common')
#10 /Users/<USER>/wwwroot/rtb_haiwai/app/c/api/c.api.base.php(114): RXX('common', 'UP_APPSTORE_VER...')
#11 /Users/<USER>/wwwroot/rtb_haiwai/app/c/api/c.api.base.php(37): c_api_base->_set_app_version()
#12 /Users/<USER>/wwwroot/rtb_haiwai/ju/ju.php(88): c_api_base->__construct()
#13 /Users/<USER>/wwwroot/rtb_haiwai/www/index.php(8): require('/Users/<USER>/ww...')
#14 {main}
2025-07-18 15:15:54	127.0.0.1	/api/xm/get_visitor_list	
Redis连接失败!RedisException: Connection refused in /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php:42
Stack trace:
#0 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php(42): Redis->pconnect('127.0.0.1', '6379')
#1 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php(215): redisx->_get_redis_inst(true)
#2 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php(79): redisx->_call_redis_method('get', Array)
#3 /Users/<USER>/wwwroot/rtb_haiwai/ju/fun.php(1472): redisx->get('xt_config:jiami...')
#4 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/db.php(747): S('xt_config:jiami...')
#5 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/db.php(569): db->gethc('SELECT data FRO...', 864000)
#6 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/db.php(597): db->get('jm_config', 'data', ' WHERE name = '...', 864000, false)
#7 /Users/<USER>/wwwroot/rtb_haiwai/app/m/m.base.php(54): db->get_field('jm_config', 'data', 'name = 'jiami_x...', 864000)
#8 /Users/<USER>/wwwroot/rtb_haiwai/app/fun.php(67): m_base->get_config('jiami_x_common', 1)
#9 /Users/<USER>/wwwroot/rtb_haiwai/ju/fun.php(172): RC('jiami_x_common')
#10 /Users/<USER>/wwwroot/rtb_haiwai/app/c/api/c.api.base.php(114): RXX('common', 'UP_APPSTORE_VER...')
#11 /Users/<USER>/wwwroot/rtb_haiwai/app/c/api/c.api.base.php(37): c_api_base->_set_app_version()
#12 /Users/<USER>/wwwroot/rtb_haiwai/ju/ju.php(88): c_api_base->__construct()
#13 /Users/<USER>/wwwroot/rtb_haiwai/www/index.php(8): require('/Users/<USER>/ww...')
#14 {main}
2025-07-18 15:16:03	127.0.0.1	/api/xm/get_visitor_list	
Redis连接失败!RedisException: Connection refused in /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php:42
Stack trace:
#0 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php(42): Redis->pconnect('127.0.0.1', '6379')
#1 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php(215): redisx->_get_redis_inst(true)
#2 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php(79): redisx->_call_redis_method('get', Array)
#3 /Users/<USER>/wwwroot/rtb_haiwai/ju/fun.php(1472): redisx->get('xt_config:jiami...')
#4 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/db.php(747): S('xt_config:jiami...')
#5 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/db.php(569): db->gethc('SELECT data FRO...', 864000)
#6 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/db.php(597): db->get('jm_config', 'data', ' WHERE name = '...', 864000, false)
#7 /Users/<USER>/wwwroot/rtb_haiwai/app/m/m.base.php(54): db->get_field('jm_config', 'data', 'name = 'jiami_x...', 864000)
#8 /Users/<USER>/wwwroot/rtb_haiwai/app/fun.php(67): m_base->get_config('jiami_x_common', 1)
#9 /Users/<USER>/wwwroot/rtb_haiwai/ju/fun.php(172): RC('jiami_x_common')
#10 /Users/<USER>/wwwroot/rtb_haiwai/app/c/api/c.api.base.php(114): RXX('common', 'UP_APPSTORE_VER...')
#11 /Users/<USER>/wwwroot/rtb_haiwai/app/c/api/c.api.base.php(37): c_api_base->_set_app_version()
#12 /Users/<USER>/wwwroot/rtb_haiwai/ju/ju.php(88): c_api_base->__construct()
#13 /Users/<USER>/wwwroot/rtb_haiwai/www/index.php(8): require('/Users/<USER>/ww...')
#14 {main}
2025-07-18 15:16:03	127.0.0.1	/api/site_ads/get_ads	
Redis连接失败!RedisException: Connection refused in /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php:42
Stack trace:
#0 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php(42): Redis->pconnect('127.0.0.1', '6379')
#1 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php(215): redisx->_get_redis_inst(true)
#2 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php(79): redisx->_call_redis_method('get', Array)
#3 /Users/<USER>/wwwroot/rtb_haiwai/ju/fun.php(1472): redisx->get('xt_config:jiami...')
#4 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/db.php(747): S('xt_config:jiami...')
#5 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/db.php(569): db->gethc('SELECT data FRO...', 864000)
#6 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/db.php(597): db->get('jm_config', 'data', ' WHERE name = '...', 864000, false)
#7 /Users/<USER>/wwwroot/rtb_haiwai/app/m/m.base.php(54): db->get_field('jm_config', 'data', 'name = 'jiami_x...', 864000)
#8 /Users/<USER>/wwwroot/rtb_haiwai/app/fun.php(67): m_base->get_config('jiami_x_common', 1)
#9 /Users/<USER>/wwwroot/rtb_haiwai/ju/fun.php(172): RC('jiami_x_common')
#10 /Users/<USER>/wwwroot/rtb_haiwai/app/c/api/c.api.base.php(114): RXX('common', 'UP_APPSTORE_VER...')
#11 /Users/<USER>/wwwroot/rtb_haiwai/app/c/api/c.api.base.php(37): c_api_base->_set_app_version()
#12 /Users/<USER>/wwwroot/rtb_haiwai/ju/ju.php(88): c_api_base->__construct()
#13 /Users/<USER>/wwwroot/rtb_haiwai/www/index.php(8): require('/Users/<USER>/ww...')
#14 {main}
2025-07-18 15:16:15	127.0.0.1	/api/user/get_user_info	
Redis连接失败!RedisException: Connection refused in /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php:42
Stack trace:
#0 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php(42): Redis->pconnect('127.0.0.1', '6379')
#1 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php(215): redisx->_get_redis_inst(true)
#2 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php(79): redisx->_call_redis_method('get', Array)
#3 /Users/<USER>/wwwroot/rtb_haiwai/ju/fun.php(1472): redisx->get('auth-user-api::...')
#4 /Users/<USER>/wwwroot/rtb_haiwai/app/fun.php(611): S('auth-user-api::...')
#5 /Users/<USER>/wwwroot/rtb_haiwai/app/c/api/c.api.base.php(53): jwt_login_check('eyJkYXRhIjp7InV...', 'api')
#6 /Users/<USER>/wwwroot/rtb_haiwai/app/c/api/c.api.base.php(36): c_api_base->_yzqx()
#7 /Users/<USER>/wwwroot/rtb_haiwai/app/c/api/user/c.api.user.main.php(7): c_api_base->__construct()
#8 /Users/<USER>/wwwroot/rtb_haiwai/ju/ju.php(88): c_api_user_main->__construct()
#9 /Users/<USER>/wwwroot/rtb_haiwai/www/index.php(8): require('/Users/<USER>/ww...')
#10 {main}
2025-07-18 15:16:29	172.24.64.6	/api/comm_auth/login	
Redis连接失败!RedisException: Connection refused in /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php:42
Stack trace:
#0 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php(42): Redis->pconnect('127.0.0.1', '6379')
#1 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php(215): redisx->_get_redis_inst(true)
#2 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/redisx.php(79): redisx->_call_redis_method('get', Array)
#3 /Users/<USER>/wwwroot/rtb_haiwai/ju/fun.php(1472): redisx->get('xt_config:jiami...')
#4 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/db.php(747): S('xt_config:jiami...')
#5 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/db.php(569): db->gethc('SELECT data FRO...', 864000)
#6 /Users/<USER>/wwwroot/rtb_haiwai/ju/lib/db.php(597): db->get('jm_config', 'data', ' WHERE name = '...', 864000, false)
#7 /Users/<USER>/wwwroot/rtb_haiwai/app/m/m.base.php(54): db->get_field('jm_config', 'data', 'name = 'jiami_x...', 864000)
#8 /Users/<USER>/wwwroot/rtb_haiwai/app/fun.php(67): m_base->get_config('jiami_x_common', 1)
#9 /Users/<USER>/wwwroot/rtb_haiwai/ju/fun.php(172): RC('jiami_x_common')
#10 /Users/<USER>/wwwroot/rtb_haiwai/app/c/api/c.api.base.php(114): RXX('common', 'UP_APPSTORE_VER...')
#11 /Users/<USER>/wwwroot/rtb_haiwai/app/c/api/c.api.base.php(37): c_api_base->_set_app_version()
#12 /Users/<USER>/wwwroot/rtb_haiwai/app/c/api/comm/c.api.comm.base.php(7): c_api_base->__construct()
#13 /Users/<USER>/wwwroot/rtb_haiwai/app/c/api/comm/c.api.comm.auth.php(8): c_api_comm_base->__construct()
#14 /Users/<USER>/wwwroot/rtb_haiwai/ju/ju.php(88): c_api_comm_auth->__construct()
#15 /Users/<USER>/wwwroot/rtb_haiwai/www/index.php(8): require('/Users/<USER>/ww...')
#16 {main}
