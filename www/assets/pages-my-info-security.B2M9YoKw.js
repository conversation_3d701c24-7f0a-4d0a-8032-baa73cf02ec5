import{_ as e}from"./prompt-popup.vue_vue_type_script_setup_true_lang.DNXGzWqX.js";import{_ as l}from"./submitButton.0smgT9Uf.js";import{_ as a}from"./ali-popup.D1Nn9niy.js";import{_ as t}from"./navbar.vue_vue_type_script_setup_true_lang.DC1OHhyC.js";import{d as o,u as s,k as n,r,D as c,ad as u,v as i,x as d,h as p,w as m,ae as f,i as v,o as b,e as y,g as x,t as g,a as _,s as k,U as h,ab as w,N as C,b as T,j as I,af as j,c as O,ag as E,ah as z,ai as V,aj as P,F,R,z as B}from"./index-BBirLt11.js";import{_ as S,a as L}from"./uv-steps.stmGVDDL.js";import{_ as G}from"./uv-input.Q1TIxLPl.js";import{d as A,c as U,l as N}from"./login.uhDfajUZ.js";import{V as $}from"./system.DZjCJFRG.js";import{d as D}from"./debounce.Ce2HeGXN.js";import{_ as W}from"./uv-icon.Dp0oPivN.js";import{_ as H}from"./uv-tabs.DzY9siFk.js";import{_ as M,a as J}from"./config.CmejPA8i.js";import"./uv-parse.CpsDxc7n.js";import"./uv-popup.BGprV-fU.js";import"./uv-transition.B3tXKydv.js";import"./uv-status-bar.CB0-bOg6.js";import"./uv-text.D1tdgrqN.js";const K=W(o({__name:"deleteAccount",emits:["logout"],setup(e,{expose:o,emit:O}){const{t:E}=s(),z=n(),V=O,P=r(0),F=r(),R=r(!1),B=r(!1),U=r(!1),N=c({certifyId:"",deviceToken:"",data:"",yzm:""}),W=r(),H=r(E("login.codeText")),M=r(null),J=r(!1),K=()=>{R.value=!1},Z=()=>{P.value?X():P.value=1},q=u(()=>J.value?"text-blueTwo":"text-blueOne"),Q=()=>{const e=n();I(`/pages/system/xy?mark=${e.appSystem.xy_mask[3]}`)},X=D(async()=>{try{const{msg:e}=await A({yzm:N.yzm});V("logout"),f(e||E("sys.success"))}catch(e){}finally{P.value=0,N.yzm="",U.value=!1}},300,{leading:!0,trailing:!0}),Y=D(()=>{var e,l;J.value||(null==(e=F.value)||e.open(),null==(l=F.value)||l.activateCaptcha())},500,{leading:!0,trailing:!0}),ee=async e=>{if(e.certifyId&&e.deviceToken&&e.data){Object.assign(N,{certifyId:e.certifyId,deviceToken:e.deviceToken,data:e.data});try{const e=$.DELETE_ACCOUNT,{msg:l}=await j({lx:e,email:"",certifyId:N.certifyId,deviceToken:N.deviceToken,data:N.data});f(l||E("sys.success")),le()}catch(l){}}else f(E("sys.loseEfficacy"))},le=()=>{ae(),J.value=!0;let e=60;M.value=setInterval(()=>{e--,H.value=`${E("login.codeText")} ${e}s`,e<=0&&ae()},1e3)},ae=()=>{M.value&&(clearInterval(M.value),M.value=null),J.value=!1,H.value=E("login.codeText")};return o({showOpen:()=>{R.value=!0}}),(e,o)=>{const s=t,n=v,r=_(T("uv-steps-item"),S),c=_(T("uv-steps"),L),u=_(T("uv-input"),G),f=h,I=a,j=l;return p(R)?(b(),i(n,{key:0,class:"fixed top-0 bottom-0 bg-white w-750 z-[1000]"},{default:m(()=>[y(s,{"bg-color":"transparent",leftIcon:"close",back:!1,onLeftClick:K}),y(n,{class:"py-32 px-32 env-bottom"},{default:m(()=>[y(n,{class:"text-blackOne leading-56 text-40 font-semibold"},{default:m(()=>[x(g(p(P)?p(E)("sys.accountCancellationRisk2"):p(E)("sys.accountCancellationRisk")),1)]),_:1}),p(P)?(b(),i(n,{key:0,class:"mt-20 leading-36 text-26 text-blackTwo"},{default:m(()=>[x(g(p(E)("sys.accountCancellationRisktip")+p(z).userInfo.email),1)]),_:1})):d("",!0),p(P)?(b(),i(n,{key:2,class:"mt-60"},{default:m(()=>[y(n,{class:k([p(B)?"border-blueOne":"border-borderGray","form-item relative h-100 rounded-20 border pl-30 pr-30 flex items-center box-border mb-24"])},{default:m(()=>[y(u,{placeholder:p(E)("login.code"),border:"none",maxlength:"6",modelValue:p(N).yzm,"onUpdate:modelValue":o[0]||(o[0]=e=>p(N).yzm=e),onFocus:o[1]||(o[1]=e=>B.value=!0),onBlur:o[2]||(o[2]=e=>B.value=!1)},null,8,["placeholder","modelValue"]),y(n,{class:"h-full absolute right-0 flex justify-center items-center"},{default:m(()=>[y(n,{class:k(["height-36 text-24 Code relative pl-30 pr-30 text-center border-l",p(q)]),onClick:o[3]||(o[3]=e=>p(Y)())},{default:m(()=>[x(g(p(H)),1)]),_:1},8,["class"])]),_:1})]),_:1},8,["class"])]),_:1})):(b(),i(n,{key:1,class:"p-32 bg-[#F6F7F9] rounded-30 mt-24"},{default:m(()=>[y(c,{current:"0",direction:"column",activeColor:"#969799",inactiveColor:"#969799",lineColor:"#E8E8EA"},{default:m(()=>[y(r,null,{title:m(()=>[y(n,{class:"text-blackOne font-semibold leading-40 text-28"},{default:m(()=>[x(g(p(E)("sys.accountCancellationSteps.unableToLogin.title")),1)]),_:1})]),desc:m(()=>[y(n,{class:"text-blackTwo text-26 leading-36"},{default:m(()=>[x(g(p(E)("sys.accountCancellationSteps.unableToLogin.desc")),1)]),_:1})]),_:1}),y(r,null,{title:m(()=>[y(n,{class:"text-blackOne font-semibold leading-40 text-28"},{default:m(()=>[x(g(p(E)("sys.accountCancellationSteps.informationErasure.title")),1)]),_:1})]),desc:m(()=>[y(n,{class:"text-blackTwo text-26 leading-36"},{default:m(()=>[x(g(p(E)("sys.accountCancellationSteps.informationErasure.desc")),1)]),_:1})]),_:1}),y(r,null,{title:m(()=>[y(n,{class:"text-blackOne font-semibold leading-40 text-28"},{default:m(()=>[x(g(p(E)("sys.accountCancellationSteps.reRegistration.title")),1)]),_:1})]),desc:m(()=>[y(n,{class:"text-blackTwo text-26 leading-36"},{default:m(()=>[x(g(p(E)("sys.accountCancellationSteps.reRegistration.desc")),1)]),_:1})]),_:1})]),_:1})]),_:1})),p(P)?d("",!0):(b(),i(n,{key:3,class:"text-blackTwo mt-28 flex"},{default:m(()=>[y(f,{class:k(["iconfont !text-28 !text-blueOne",p(U)?"icon-a-Property1xieyigouxuan":"icon-a-Property1xieyi"]),onClick:o[4]||(o[4]=e=>U.value=!p(U))},null,8,["class"]),y(n,{class:"text-26 ml-12 text-blackTwo",onClick:o[5]||(o[5]=w(e=>U.value=!p(U),["stop"]))},{default:m(()=>[C("span",null,g(p(E)("agreement.accountDeletion")),1),C("span",{onClick:w(Q,["stop"]),class:"text-blueOne"},g(p(E)("agreement.accountDeletionAgreement")),1)]),_:1})]),_:1}))]),_:1}),y(I,{ref_key:"aliPopupRef",ref:F,onCallBack:ee,code:p(W)},null,8,["code"]),y(j,{onBack:Z,status:p(P)?!!p(N).yzm:p(U),title:p(P)?"sys.accountCancellationConfirm":"login.unsubscribe"},null,8,["status","title"])]),_:1})):d("",!0)}}}),[["__scopeId","data-v-7798f621"]]),Z=W(o({__name:"checkPopup",emits:["logout"],setup(e,{expose:l,emit:o}){const{t:h}=s(),w=r(!1),C=r(0),I=r(!1),V=r(!1),P=r(h("login.codeText")),F=r(!1),R=r(null),B=r(),S=r(),L=o,A=c({yzm:"",email:"",certifyId:"",deviceToken:"",data:"",newEmail:""}),U=n(),N=u(()=>0===C.value?!!A.yzm:!(!A.yzm||!A.newEmail)),W=u(()=>N.value?"bg-blueOne":"bg-blueTwo"),H=u(()=>F.value?"text-blueTwo":"text-blueOne"),M=()=>{R.value&&(clearInterval(R.value),R.value=null),F.value=!1,P.value=h("login.codeText")},J=()=>{M(),w.value=!1},K=()=>{N.value&&(0===C.value?Z():q())},Z=async()=>{try{await E(A.yzm),A.yzm="",M(),C.value=1}catch(e){M()}},q=async()=>{try{await z(A.yzm,A.newEmail),f(h("sys.success")),L("logout"),J()}catch(e){}},Q=D(()=>{var e,l;(0===C.value?A.email:A.newEmail)?F.value||(null==(e=B.value)||e.open(),null==(l=B.value)||l.activateCaptcha()):f(h("sys.enter")+h("login.emailPlaceholder"))},500,{leading:!0,trailing:!0}),X=async e=>{if(e.certifyId&&e.deviceToken&&e.data){Object.assign(A,{certifyId:e.certifyId,deviceToken:e.deviceToken,data:e.data});try{const e=0===C.value?"":A.newEmail,l=0===C.value?$.CHANGE_EMAIL_STEP1:$.CHANGE_EMAIL_STEP2,{msg:a}=await j({lx:l,email:e,certifyId:A.certifyId,deviceToken:A.deviceToken,data:A.data});f(a||h("sys.success")),Y()}catch(l){M()}}else f(h("sys.loseEfficacy"))},Y=()=>{M(),F.value=!0;let e=60;R.value=setInterval(()=>{e--,P.value=`${h("login.codeText")} ${e}s`,e<=0&&M()},1e3)};return l({showOpen:e=>{A.email=e??"<EMAIL>",w.value=!0,C.value=0}}),(e,l)=>{const o=t,s=v,n=_(T("uv-input"),G),r=a;return p(w)?(b(),O("div",{key:0,class:"fixed !top-0 bottom-0 bg-white z-[1000]"},[y(o,{"bg-color":"transparent",leftIcon:"close",back:!1,onLeftClick:J}),0===p(C)?(b(),i(s,{key:0},{default:m(()=>[y(s,{class:"flex items-center justify-center mt-40 text-blackTwo text-28 leading-36"},{default:m(()=>[x(g(p(h)("userInfo.emailTip")),1)]),_:1}),y(s,{class:"text-blackOne font-din text-60 leading-60 text-center mt-40"},{default:m(()=>{var e;return[x(g(null==(e=p(U).userInfo)?void 0:e.email),1)]}),_:1}),y(s,{class:"p-60"},{default:m(()=>[y(s,{class:k([p(I)?"border-blueOne":"border-borderGray","form-item relative h-100 rounded-20 border pl-30 pr-30 flex items-center box-border mb-24"])},{default:m(()=>[y(n,{placeholder:p(h)("login.code"),border:"none",maxlength:"6",modelValue:p(A).yzm,"onUpdate:modelValue":l[0]||(l[0]=e=>p(A).yzm=e),onFocus:l[1]||(l[1]=e=>I.value=!0),onBlur:l[2]||(l[2]=e=>I.value=!1)},null,8,["placeholder","modelValue"]),y(s,{class:"h-full absolute right-0 flex justify-center items-center"},{default:m(()=>[y(s,{class:k(["height-36 text-24 Code relative pl-30 pr-30 text-center border-l",p(H)]),onClick:l[3]||(l[3]=e=>p(Q)())},{default:m(()=>[x(g(p(P)),1)]),_:1},8,["class"])]),_:1})]),_:1},8,["class"])]),_:1}),y(s,{class:"p-60 py-0"},{default:m(()=>[y(s,{class:k(["flex justify-center items-center w-630 text-white text-28 leading-44 py-28 rounded-20",p(W)]),onClick:K},{default:m(()=>[x(g(p(h)("sys.next")),1)]),_:1},8,["class"])]),_:1})]),_:1})):(b(),i(s,{key:1},{default:m(()=>[y(s,{class:"flex items-center px-60 mt-40 text-blackOne text-48 leading-68 font-semibold"},{default:m(()=>[x(g(p(h)("userInfo.newEmail")),1)]),_:1}),y(s,{class:"text-blackTwo px-60 text-26 leading-36 mt-20"},{default:m(()=>[x(g(p(h)("userInfo.newEmailTip")),1)]),_:1}),y(s,{class:"p-60"},{default:m(()=>[y(s,{class:k([p(V)?"border-blueOne":"border-borderGray","form-item h-100 relative rounded-20 border pl-30 pr-30 flex items-center box-border mb-24"])},{default:m(()=>[y(n,{placeholder:p(h)("login.emailPlaceholder"),border:"none",modelValue:p(A).newEmail,"onUpdate:modelValue":l[4]||(l[4]=e=>p(A).newEmail=e),onFocus:l[5]||(l[5]=e=>V.value=!0),onBlur:l[6]||(l[6]=e=>V.value=!1)},null,8,["placeholder","modelValue"])]),_:1},8,["class"]),y(s,{class:k([p(I)?"border-blueOne":"border-borderGray","form-item relative h-100 rounded-20 border pl-30 pr-30 flex items-center box-border mb-24"])},{default:m(()=>[y(n,{placeholder:p(h)("login.code"),border:"none",maxlength:"6",modelValue:p(A).yzm,"onUpdate:modelValue":l[7]||(l[7]=e=>p(A).yzm=e),onFocus:l[8]||(l[8]=e=>I.value=!0),onBlur:l[9]||(l[9]=e=>I.value=!1)},null,8,["placeholder","modelValue"]),y(s,{class:"h-full absolute right-0 flex justify-center items-center"},{default:m(()=>[y(s,{class:k(["height-36 text-24 Code relative pl-30 pr-30 text-center border-l",p(H)]),onClick:l[10]||(l[10]=e=>p(Q)())},{default:m(()=>[x(g(p(P)),1)]),_:1},8,["class"])]),_:1})]),_:1},8,["class"])]),_:1}),y(s,{class:"p-60 py-0"},{default:m(()=>[y(s,{class:k(["flex justify-center items-center w-630 text-white text-28 leading-44 py-28 rounded-20",p(W)]),onClick:K},{default:m(()=>[x(g(p(h)("sys.sure")),1)]),_:1},8,["class"])]),_:1})]),_:1})),y(r,{ref_key:"aliPopupRef",ref:B,onCallBack:X,code:p(S)},null,8,["code"])])):d("",!0)}}}),[["__scopeId","data-v-6f7bce5e"]]),q=W(o({__name:"changePassword",emits:["logout"],setup(e,{expose:l,emit:o}){const{t:w}=s(),C=n(),I=r(!1),E=r(0),z=r(0),F=r(!1),R=r(!1),B=r(!1),S=r(!1),L=r(w("login.codeText")),A=r(!1),U=r(),N=r(),W=o,M=r(!0),J=r(!0),K=c({}),Z=r(0),q=[{name:w("my.emailVerif"),value:"phone"},{name:w("my.currentPWD"),value:"email"}],Q=u(()=>0===z.value?E.value?!!K.old_password:!!K.yzm:!(!K.repeat_password||!K.password)),X=u(()=>Q.value?"bg-blueOne":"bg-blueTwo"),Y=u(()=>A.value?"text-blueTwo":"text-blueOne"),ee=()=>{I.value=!1,K.email="",K.yzm="",K.old_password="",K.password="",K.repeat_password=""},le=()=>{Q.value&&(0===z.value?z.value=1:ae())},ae=D(async()=>{try{const e={code:K.yzm??"",old_password:V(K.old_password??""),password:V(K.password??""),repeat_password:V(K.repeat_password??"")},{msg:l}=await P(e);f(l??""),W("logout"),K.email=""}catch(e){}},500,{leading:!0,trailing:!0}),te=D(()=>{var e,l;A.value||(null==(e=U.value)||e.open(),null==(l=U.value)||l.activateCaptcha())},500,{leading:!0,trailing:!0}),oe=async e=>{if(e.certifyId&&e.deviceToken&&e.data){Object.assign(K,{certifyId:e.certifyId,deviceToken:e.deviceToken,data:e.data});try{const e=$.CHANGE_PASSWORD,{msg:l}=await j({lx:e,email:"",certifyId:K.certifyId,deviceToken:K.deviceToken,data:K.data});f(l||w("sys.success")),se()}catch(l){A.value=!1}}else f(w("sys.loseEfficacy"))},se=()=>{A.value=!0;let e=60;const l=setInterval(()=>{e--,L.value=`${w("login.codeText")} ${e}s`,e<=0&&(clearInterval(l),A.value=!1,L.value=w("login.codeText"))},1e3)};return l({showOpen:e=>{K.email=e,I.value=!0,z.value=0}}),(e,l)=>{const o=t,s=v,n=_(T("uv-tabs"),H),r=_(T("uv-input"),G),c=h,u=a;return p(I)?(b(),O("div",{key:0,class:"fixed top-0 bottom-0 bg-white z-[1000] max-w-750"},[y(o,{"bg-color":"transparent",leftIcon:"close",back:!1,onLeftClick:ee}),y(s,null,{default:m(()=>[y(s,{class:"flex items-center px-60 mt-40 text-blackOne text-48 leading-68 font-semibold"},{default:m(()=>[x(g(0===p(z)?p(w)("userInfo.verifyIdentity"):p(w)("userInfo.changePassword")),1)]),_:1}),y(s,{class:"text-blackTwo px-60 text-26 leading-36 mt-20 mb-60"},{default:m(()=>[x(g(0===p(z)?p(w)("userInfo.tips1"):p(w)("userInfo.tips2")),1)]),_:1}),0===p(z)?(b(),i(s,{key:0,class:"flex justify-center"},{default:m(()=>[y(n,{list:q,current:p(E),activeStyle:{color:"#131726",fontWeight:"bold",fontSize:"28rpx"},inactiveStyle:{color:"#444B56"},lineColor:"#0165FF",lineWidth:"66rpx",onClick:l[0]||(l[0]=e=>{E.value=e.index})},null,8,["current"])]),_:1})):d("",!0),0===p(z)?(b(),i(s,{key:1},{default:m(()=>[p(E)?(b(),i(s,{key:1,class:"p-60 pt-40"},{default:m(()=>[p(Z)?d("",!0):(b(),i(s,{key:0,class:k([p(F)?"border-blueOne":"border-borderGray","form-item h-100 rounded-20 border pl-30 pr-30 flex items-center box-border mb-24"])},{default:m(()=>[y(r,{placeholder:p(w)("login.passwordPlaceholder"),border:"none",type:p(M)?"password":"text",modelValue:p(K).old_password,"onUpdate:modelValue":l[5]||(l[5]=e=>p(K).old_password=e),onFocus:l[6]||(l[6]=e=>F.value=!0),onBlur:l[7]||(l[7]=e=>F.value=!1)},null,8,["placeholder","type","modelValue"]),y(c,{class:k(["iconfont !text-44",p(M)?"icon-a-kejianoff":"icon-a-kejianon"]),onClick:l[8]||(l[8]=e=>M.value=!p(M))},null,8,["class"])]),_:1},8,["class"]))]),_:1})):(b(),i(s,{key:0,class:"p-60 pt-40"},{default:m(()=>[y(s,{class:k([p(S)?"border-blueOne":"border-borderGray","form-item h-100 relative rounded-20 border pl-30 pr-30 flex items-center box-border mb-24"])},{default:m(()=>[x(g(p(C).userInfo.email),1)]),_:1},8,["class"]),y(s,{class:k([p(F)?"border-blueOne":"border-borderGray","form-item relative h-100 rounded-20 border pl-30 pr-30 flex items-center box-border mb-24"])},{default:m(()=>[y(r,{placeholder:p(w)("login.code"),border:"none",maxlength:"6",modelValue:p(K).yzm,"onUpdate:modelValue":l[1]||(l[1]=e=>p(K).yzm=e),onFocus:l[2]||(l[2]=e=>F.value=!0),onBlur:l[3]||(l[3]=e=>F.value=!1)},null,8,["placeholder","modelValue"]),y(s,{class:"h-full absolute right-0 flex justify-center items-center"},{default:m(()=>[y(s,{class:k(["height-36 text-24 Code relative pl-30 pr-30 text-center border-l",p(Y)]),onClick:l[4]||(l[4]=e=>p(te)())},{default:m(()=>[x(g(p(L)),1)]),_:1},8,["class"])]),_:1})]),_:1},8,["class"])]),_:1}))]),_:1})):(b(),i(s,{key:2},{default:m(()=>[y(s,{class:"p-60 pt-40"},{default:m(()=>[p(Z)?d("",!0):(b(),i(s,{key:0,class:k([p(R)?"border-blueOne":"border-borderGray","form-item h-100 rounded-20 border pl-30 pr-30 flex items-center box-border mb-24"])},{default:m(()=>[y(r,{placeholder:p(w)("login.passwordPlaceholder"),border:"none",type:p(M)?"password":"text",modelValue:p(K).password,"onUpdate:modelValue":l[9]||(l[9]=e=>p(K).password=e),onFocus:l[10]||(l[10]=e=>R.value=!0),onBlur:l[11]||(l[11]=e=>R.value=!1)},null,8,["placeholder","type","modelValue"]),y(c,{class:k(["iconfont !text-44",p(M)?"icon-a-kejianoff":"icon-a-kejianon"]),onClick:l[12]||(l[12]=e=>M.value=!p(M))},null,8,["class"])]),_:1},8,["class"])),p(Z)?d("",!0):(b(),i(s,{key:1,class:k([p(B)?"border-blueOne":"border-borderGray","form-item h-100 rounded-20 border pl-30 pr-30 flex items-center box-border mb-24"])},{default:m(()=>[y(r,{placeholder:p(w)("login.repeatPasswordPlaceholder"),border:"none",type:p(J)?"password":"text",modelValue:p(K).repeat_password,"onUpdate:modelValue":l[13]||(l[13]=e=>p(K).repeat_password=e),onFocus:l[14]||(l[14]=e=>B.value=!0),onBlur:l[15]||(l[15]=e=>B.value=!1)},null,8,["placeholder","type","modelValue"]),y(c,{class:k(["iconfont !text-44",p(J)?"icon-a-kejianoff":"icon-a-kejianon"]),onClick:l[16]||(l[16]=e=>J.value=!p(J))},null,8,["class"])]),_:1},8,["class"]))]),_:1})]),_:1})),y(s,{class:"p-60 py-0"},{default:m(()=>[y(s,{class:k(["flex justify-center items-center w-630 text-white text-28 leading-44 py-28 rounded-20",p(X)]),onClick:le},{default:m(()=>[x(g(p(w)("sys.next")),1)]),_:1},8,["class"])]),_:1})]),_:1}),y(u,{ref_key:"aliPopupRef",ref:U,onCallBack:oe,code:p(N)},null,8,["code"])])):d("",!0)}}}),[["__scopeId","data-v-02dc8267"]]),Q=o({__name:"security",setup(l){const a=r(J),{t:o}=s(),c=r(),u=r(),d=r(),x=r(),g=r(),_=n(),k=D(e=>{var l,a;switch(e.title){case"changeEmail":null==(l=c.value)||l.showOpen();break;case"changePassword":null==(a=u.value)||a.showOpen();break;default:h(e)}},300,{leading:!0,trailing:!0}),h=async e=>{var l,a;try{const{code:e,msg:t}=await U();1===e?null==(l=d.value)||l.showOpen():-2===e?(x.value=t??"",null==(a=g.value)||a.open()):-1005===e||-1004===e?(n().setRedirectUrl(""),n().setToken(""),R({url:"/pages/login/index"})):f(t??"")}catch(t){}},w=()=>{var e;null==(e=d.value)||e.showOpen()},T=D(async()=>{try{await N(),_.deleteToken()}catch(e){}},300,{leading:!0,trailing:!0});return(l,s)=>{const n=t,r=M,f=v,_=q,h=Z,I=K,j=e;return b(),O(F,null,[C("div",null,[y(n,{bgColor:"transparent",title:p(o)("userInfo.account")},null,8,["title"]),y(f,{class:"p-32"},{default:m(()=>[(b(!0),O(F,null,B(p(a),e=>(b(),i(f,{class:"user-info",key:e.title,onClick:l=>p(k)(e)},{default:m(()=>[y(r,{item:e},null,8,["item"])]),_:2},1032,["onClick"]))),128))]),_:1})]),y(_,{ref_key:"updatePasswordRef",ref:u,onLogout:p(T)},null,8,["onLogout"]),y(h,{ref_key:"checkPopupRef",ref:c,onLogout:p(T)},null,8,["onLogout"]),y(I,{ref_key:"deleteAccountRef",ref:d,onLogout:p(T)},null,8,["onLogout"]),y(j,{ref_key:"warningRef",ref:g,content:p(x),showIcon:!1,tipType:"login",iconName:"warning-fill",iconColor:"#FF6B35",showCancel:!0,onConfirm:w},null,8,["content"])],64)}}});export{Q as default};
