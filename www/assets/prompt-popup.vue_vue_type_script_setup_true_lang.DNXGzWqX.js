import{d as e,r as t,u as a,ad as l,a3 as s,p as n,aK as o,v as c,w as r,a as u,o as i,e as f,i as p,x as d,U as m,g as x,t as y,h as v,P as _,a1 as b,s as h,b as w}from"./index-BBirLt11.js";import{b as F}from"./uv-icon.Dp0oPivN.js";import{_ as g}from"./uv-parse.CpsDxc7n.js";import{_ as T}from"./uv-popup.BGprV-fU.js";const k=e({__name:"prompt-popup",props:{title:{default:"Prompt"},content:{default:""},showIcon:{type:Boolean,default:!1},iconName:{default:"info-circle"},iconSize:{default:"32rpx"},iconColor:{default:"#0165FF"},showCancel:{type:Boolean,default:!1},cancelText:{default:"sys.cancel"},confirmText:{default:"sys.sure"},confirmType:{default:"primary"},tipType:{default:"login"},contentType:{default:"text"},type:{}},emits:["update:show","confirm","cancel"],setup(e,{expose:k,emit:j}){const C=t(),{t:z}=a(),B=e,I=j,K=l(()=>B.showCancel?"space-between":"justify-center"),N=l(()=>{switch(B.confirmType){case"primary":return"bg-[#0165FF] ";case"outline":return"bg-[#F6F7F9] ";case"text":return"bg-transparent ";default:return"bg-[#0165FF]"}}),O=l(()=>{switch(B.confirmType){case"primary":default:return"text-white";case"outline":case"text":return"text-[#0165FF]"}}),P=()=>{var e;null==(e=C.value)||e.close(),I("confirm",B.type||"")},S=()=>{var e;I("cancel"),null==(e=C.value)||e.close()};return k({open(){var e;null==(e=C.value)||e.open()},close(){var e;"login"===B.tipType&&s(),null==(e=C.value)||e.close()}}),n(()=>{var e;null==(e=C.value)||e.close()}),o(()=>{var e;null==(e=C.value)||e.close()}),(e,t)=>{const a=u(w("uv-icon"),F),l=m,s=p,n=u(w("uv-parse"),g),o=u(w("uv-popup"),T);return i(),c(o,{mode:"center",ref_key:"popupRef",ref:C,safeAreaInsetBottom:!0,round:20,bgColor:"transparent","custom-style":"min-height:50rpx;"},{default:r(()=>[f(s,{class:"bg-white rounded-30 p-48 w-640"},{default:r(()=>[f(s,{class:"flex items-center justify-center mb-20"},{default:r(()=>[e.showIcon?(i(),c(a,{key:0,name:e.iconName,size:e.iconSize,color:e.iconColor,class:"mr-8"},null,8,["name","size","color"])):d("",!0),f(l,{class:"text-36 leading-48 font-semibold text-blackOne"},{default:r(()=>[x(y(v(_)(e.title)),1)]),_:1})]),_:1}),f(s,{class:"text-28 text-blackTwo text-center leading-40 mb-48"},{default:r(()=>["text"===e.contentType?(i(),c(s,{key:0},{default:r(()=>[x(y(v(b)(e.content)),1)]),_:1})):(i(),c(s,{key:1,class:"max-h-600 overflow-y-auto !text-left"},{default:r(()=>[f(n,{content:v(b)(e.content)},null,8,["content"])]),_:1}))]),_:1}),f(s,{class:h(["flex",K.value])},{default:r(()=>[e.showCancel?(i(),c(s,{key:0,class:"flex-1 h-88 bg-[#F6F7F9] rounded-20 flex items-center justify-center mr-24",onClick:S},{default:r(()=>[f(l,{class:"text-28 text-blackOne"},{default:r(()=>[x(y(v(z)(e.cancelText)),1)]),_:1})]),_:1})):d("",!0),f(s,{class:h(["flex-1 h-88 rounded-20 flex items-center justify-center",N.value]),onClick:P},{default:r(()=>[f(l,{class:h(["text-28",O.value])},{default:r(()=>[x(y(v(z)(e.confirmText)),1)]),_:1},8,["class"])]),_:1},8,["class"])]),_:1},8,["class"])]),_:1})]),_:1},512)}}});export{k as _};
