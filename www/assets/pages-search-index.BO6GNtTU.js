import{_ as s,S as a}from"./config.CdjvJKQB.js";import{d as e,u as t,r as l,l as o,D as r,m as i,c as n,e as c,w as u,s as p,h as m,G as d,H as f,i as g,a as h,b as v,o as _,q as j,N as x,f as b,j as y,t as w}from"./index-BBirLt11.js";import{_ as k}from"./uv-search.CiAqqsGD.js";import{_ as T}from"./uv-skeletons.BJIINbO6.js";import{_ as C}from"./search.CpVgtt1a.js";import{_ as z}from"./uv-icon.Dp0oPivN.js";import"./empty.vue_vue_type_script_setup_true_lang.SETk5GIM.js";import"./uv-empty.Xz_IG7Bm.js";import"./uv-load-more.SNzw0348.js";import"./uv-line.CNQanRFo.js";import"./uv-loading-icon.G79bokgG.js";const A=z(e({__name:"index",setup(e){const{t:z}=t(),A=l(!0),H=o(),S=l(H.statusBarHeight||0),q=l(!0),B=l([]),D=l(""),F=r({name:"",page:1,limit:5}),G=s=>{F.name=s,F.page=1,I()};i(s=>{s.scrollTop>0?A.value=!1:A.value=!0});const I=async()=>{try{1===F.page&&(q.value=!0);const{data:s}=await d(F);B.value=s??[],q.value=!1,f(()=>{})}catch(s){q.value=!1}};return I(),(e,t)=>{const l=g,o=b,r=h(v("uv-search"),k),i=s,d=h(v("uv-skeletons"),T);return _(),n("div",null,[c(l,{class:p(["sticky top-0 z-50",m(A)?"bg-transparent":"bg-white"])},{default:u(()=>[c(l,{class:"status-bar",style:j({height:m(S)+"px"})},null,8,["style"]),c(l,{class:"p-32 flex justify-between items-center"},{default:u(()=>[c(r,{inputAlign:"left",bgColor:"#fff",height:"88rpx",focus:!0,placeholder:m(z)("search.placeholder"),showAction:!1,fontSize:"28rpx",onChange:G},{prefix:u(()=>[c(o,{class:"w-48 h-48",src:C,mode:"aspectFill"})]),_:1},8,["placeholder"]),x("span",{class:"ml-10 text-28",onClick:t[0]||(t[0]=s=>m(y)("/pages/index/index","switchTab"))},w(m(z)("search.actionText")),1)]),_:1})]),_:1},8,["class"]),c(d,{loading:m(q),skeleton:m(a)},{default:u(()=>[c(i,{list:m(B),loading:m(q),status:m(D)},null,8,["list","loading","status"]),c(l,{ref:"observerTarget"},null,512)]),_:1},8,["loading","skeleton"])])}}}),[["__scopeId","data-v-2e4d125b"]]);export{A as default};
