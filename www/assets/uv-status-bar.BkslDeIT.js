import{v as t,o as s,w as a,A as e,q as o,i as r}from"./index-CIPK2z2P.js";import{_ as i,m as n,a as u}from"./uv-icon.UcuauzO0.js";const d=i({name:"uv-status-bar",mixins:[n,u,{props:{bgColor:{type:String,default:"transparent"}}}],data:()=>({}),computed:{style(){const t={};return t.height=this.$uv.addUnit(this.$uv.sys().statusBarHeight,"px"),this.bgColor&&(this.bgColor.indexOf("gradient")>-1?t.backgroundImage=this.bgColor:t.background=this.bgColor),this.$uv.deepMerge(t,this.$uv.addStyle(this.customStyle))}}},[["render",function(i,n,u,d,l,g){const h=r;return s(),t(h,{style:o([g.style]),class:"uv-status-bar"},{default:a(()=>[e(i.$slots,"default",{},void 0,!0)]),_:3},8,["style"])}],["__scopeId","data-v-08fe2518"]]);export{d as _};
