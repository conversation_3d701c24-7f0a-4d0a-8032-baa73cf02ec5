import{a0 as t}from"./index-BBirLt11.js";function a(a){return t({url:"/api/comm_auth/register",method:"POST",data:a})}function o(a){return t({url:"/api/comm_auth/login",method:"POST",data:a})}function r(){return t({url:"/api/comm_auth/logout",method:"POST"})}function u(){return t({url:"/api/user/check_logoff",method:"POST"})}function n(a){return t({url:"/api/user_main/logoff",method:"POST",data:a})}export{o as a,u as c,n as d,a as g,r as l};
