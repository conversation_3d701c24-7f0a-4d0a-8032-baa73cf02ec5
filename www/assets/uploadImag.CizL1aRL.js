import{ao as e,u as a,ap as t,r as l,ad as s,ak as u,v as i,o as r,w as o,e as n,i as c,c as p,x as m,F as d,z as g,A,f,h as y,U as h,g as v,t as F,aL as x,aW as E,aX as w,aY as Q,aZ as k,ae as C,k as I,R as L,j as U}from"./index-CIPK2z2P.js";import{F as R}from"./system.DZjCJFRG.js";import{_ as S}from"./uv-icon.UcuauzO0.js";const W=S({__name:"uploadImag",props:e({value:{type:[String,Array],default:""},multiple:{type:Boolean,default:!1},limit:{type:Number,default:1},maxCount:{type:Number,default:null},maxSize:{type:Number,default:5},accept:{type:String,default:"img"},lx:{type:String,default:"img"},form:{type:String,default:"img"},delIcon:{type:Boolean,default:!0}},{img:{},imgModifiers:{}}),emits:["update:img"],setup(e,{expose:S}){const{t:W}=a(),B=e,J=t(e,"img"),K=l([]),z=l(null),Z=l(!1),V=l(-1),b=l(new Map),j=s(()=>null!==B.limit&&1!==B.limit?B.limit:null!==B.maxCount?B.maxCount:B.multiple?9:1);u(()=>B.value,e=>{e&&e.length?(K.value=[],Array.isArray(e)?e.forEach(e=>{if("string"==typeof e){if(e.trim()){const a="https://lxzcms.top/",t=e.startsWith("http")?e:`${a}${e}`;K.value.push({url:t,name:e,status:"success"})}}else if("object"==typeof e&&null!==e){const a="https://lxzcms.top/",t=e.url&&e.url.startsWith("http")?e.url:`${a}${e.url||""}`;K.value.push({...e,url:t,status:e.status||"success"})}}):"string"==typeof e&&e.split(",").forEach(e=>{if(e.trim()){const a="https://lxzcms.top/",t=e.startsWith("http")?e:`${a}${e}`;K.value.push({url:t,name:e,status:"success"})}})):K.value=[]},{immediate:!0,deep:!0});const H=()=>{O()?T():G()?D():q()},O=()=>{const e=B.accept.toLowerCase();return"image/*"===e||"image"===e||/^image\//.test(e)},G=()=>{const e=B.accept.toLowerCase();return"video/*"===e||"video"===e||/^video\//.test(e)},M=e=>{if(!e)return!1;if(e.url){const a=e.url.toLowerCase().split(".").pop();return["jpg","jpeg","png","gif","bmp","webp","svg"].includes(a)}return!1},P=e=>{M(e)&&x({urls:[e.url],current:e.url})},T=()=>{E({count:j.value-K.value.length,sizeType:["compressed","original"],sourceType:["album","camera"],success:e=>{X(e.tempFilePaths)}})},D=()=>{w({count:j.value-K.value.length,maxDuration:60,camera:"back",success:e=>{X([e.tempFilePath])}})},q=()=>{Q({count:6,extension:[".xlsx",".xls"],success:e=>{X(e.tempFilePaths)}})},X=async e=>{const a=N(e);K.value.push(...a),await Y(a)},N=e=>Array.isArray(e)?e.map(e=>{if("string"==typeof e)return{url:e,status:"uploading",tempPath:e,name:ee(e)};if(e.file||e.rawFile){let t=e.url;if(!t&&(e.file||e.rawFile))try{t=URL.createObjectURL(e.file||e.rawFile)}catch(a){t=""}return{url:t,status:"uploading",tempPath:t,file:e.file||e.rawFile,name:e.name,size:e.size,type:e.type}}return{url:e.url||e.tempFilePath,status:"uploading",tempPath:e.tempFilePath||e.url,name:e.name||ee(e.url||e.tempFilePath),size:e.size,type:e.type}}):[],Y=async e=>{for(let t=0;t<e.length;t++)try{const a=await k(e[t].tempPath||e[t].url,_());if(1===a.code){const l=K.value.findIndex(a=>a.url===e[t].url);-1!==l&&(K.value[l].status="success",K.value[l].url=a.data.path||a.data)}else C(a.msg,"none"),-1004===a.code||-1005===a.code?(I().setRedirectUrl(""),I().setToken(""),L({url:"/pages/login/index"})):(K.value.splice(t,1),$(e[t],a.msg))}catch(a){}ae()},_=()=>{if(B.lx){if(B.lx===R.VIDEO)return"video";if(B.lx===R.IMAGE)return"img";if(B.lx===R.FILE)return B.form}return"img"},$=(e,a)=>{const t=K.value.findIndex(a=>a===e);t>-1&&K.value.splice(t,1),U(`${e.name} ${a}`)},ee=e=>{if(!e)return"未知文件";const a=e.split("/");return a[a.length-1]||"未知文件"},ae=()=>{const e=K.value.filter(e=>"success"===e.status);J.value=e.map(e=>e.url).join(",")};return S({pauseVideo:()=>{if(z.value)try{z.value.pause(),Z.value=!1}catch(e){}},resumeVideo:()=>{if(z.value)try{z.value.play(),Z.value=!0}catch(e){}},stopVideo:()=>{if(z.value)try{z.value.stop(),z.value.exitFullScreen(),Z.value=!1,V.value=-1,z.value=null}catch(e){}},isVideoPlaying:()=>Z.value,currentVideoIndex:()=>V.value,getUploadingFiles:()=>K.value.filter(e=>"uploading"===e.status),cancelAllUploads:()=>{b.value.forEach(e=>{e.abort()}),b.value.clear()}}),(a,t)=>{const l=f,s=h,u=c;return r(),i(u,{class:"upload-container"},{default:o(()=>[n(u,{class:"file-list"},{default:o(()=>[(r(!0),p(d,null,g(K.value,(t,c)=>(r(),i(u,{key:c,class:"file-item"},{default:o(()=>[A(a.$slots,"file",{file:t,index:c},()=>["success"===t.status&&M(t)?(r(),i(l,{key:0,src:t.url,class:"preview-image",mode:"aspectFill",onClick:e=>P(t)},null,8,["src","onClick"])):"success"!==t.status||B.lx!==y(R).Video&&!G()?"success"!==t.status||M(t)?"uploading"===t.status?(r(),i(u,{key:3,class:"uploading-mask"},{default:o(()=>[n(u,{class:"loading-text"},{default:o(()=>[v(F(y(W)("sys.upload"))+"...",1)]),_:1}),t.progress?(r(),i(u,{key:0,class:"progress-text"},{default:o(()=>[v(F(t.progress)+"%",1)]),_:2},1024)):m("",!0)]),_:2},1024)):m("",!0):(r(),i(u,{key:2,class:"file-display",onClick:e=>P(t)},{default:o(()=>[n(l,{class:"file-icon",src:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEQAAABMCAYAAADUQBHfAAAAAXNSR0IArs4c6QAAAppJREFUeF7tnLtuU0EQhr+JgkQBXUQNEl2EwiNQpIt4BxvZXCR3aVLCA0CTSJFiI5s3QEJ0eQBKJEIVClqiFEhxESkmg06ME8dJzBntFOcyLo92xzvf/v/sTrPC7K+ty0BHlFWE+8DilTHF/TBUZQ/hI8qAnvyyLlXOJzT0NrfYFmhYgxR0/LEqHXry3rK+MZAxjE8Cq5bJZRirymt68ibvWsdA2tqvkDKu5G6BIrzQFTnla16CZR2XF4rQ0q4IrbImall3HigiLd1HeGgJXOax/4Mi0taTkh2tyfsxD0oGRJP/oYQBboJSWyDZHl4HpdZAroNSeyCzUALIv/qnQoMd+RBAJgeC8ltHPAggUyekwkYAmQaifAkgl+9QwwAyc6k0A1l7BP0mLN1Ju54eDqHZh8/f0uJ4zzYDOXiXDmOSxOER3Fv3TiktnhnI6U7aH87OXnjuGy81WgBJrSEHb2Hpbuo+jOdXwjJnRbWRDiWD0RxUoKj6aKO4Ucw1pLip+KwsgKQWVZ99KG6UUEgoZL46QyGhEGeFeHW7XmXVu2s2W8az23WD4tg1m4F4d7teULy65gCSWlQ9u10vdXh2zWaFeHW7njA8u2YzEK9EihongKTWkKLurNe6QiGhEOeru5c0ixonLBOWCcuY3BmWCcuEZcIyJgJhmbCMSTBxyoRlwjJhGROBsExYxiSYOGXCMmGZsIyJQFgmLGMSTJwyYZmwjNkytXsMYQ6hUfZ+yL7U6LmMeXJR5UcGZFuElyZdVXSwKj2hrcsCexXN0ZSW/uFxLR5lykNFYUBXmpV/tisXDGWXEU8ZyPHlh90W2azLA00TUGfKOOFVBiP7dgFkMuKZrrBAB3gi5Xv6L48gRqr8BHYRtujK9+lJfwF8LUBAT2p7tAAAAABJRU5ErkJggg==",mode:"scaleToFill"}),n(u,{class:"file-name line-clamp-2"},{default:o(()=>[v(F(t.name),1)]),_:2},1024)]),_:2},1032,["onClick"])):(r(),i(u,{key:1,class:"video-preview-container"},{default:o(()=>[n(u,{class:"play-overlay"},{default:o(()=>[n(u,{class:"play-button"},{default:o(()=>[n(s,{class:"play-icon"},{default:o(()=>[v("▶")]),_:1})]),_:1})]),_:1})]),_:1}))],!0),"uploading"!==t.status&&e.delIcon?(r(),i(u,{key:0,class:"delete-btn",onClick:e=>(e=>{K.value.splice(e,1),ae()})(c)},{default:o(()=>[n(l,{class:"delete-icon",src:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADQAAAA0CAYAAADFeBvrAAAAAXNSR0IArs4c6QAAA25JREFUaEPtmcur00AYxU+VWxQfVS5XKQiioIIPrvhAQRSrKIIg0rpqC4IIfXBFEUQpKIJgV7qw0CK4sqTuKghiF65dC+pCF7q7C8W/QC7YIxmJcZLMJJO0EbPoovlm5vzyzZz5Jsng7+uF5L9p+msJwFcA7wC8BPDFKS4jUfocwPJpIgjQ8hpAD8APxsmAngFYlSIgSn07TsI9QsmAHgPIpwyIcpmpRzKgBwC2pRCIkhdkQC0Ah1MK9EoGdBnAuZQCLcqAzgKopRRoSQa0G8D9lAJJbZuWTetO5SXLEEG6ADalkcgLaAHAadNAlUplzrKsb0H9ZrPZTK1Wy3c6ncWgWPd9L6DjAK7rduYX3+12tzcajR2DweBzpVL54BVLmNFotL9QKOR7vd7HZrP5SUeHF9AaAE9N1XTVanWu3+//3tu8oJwwAqJUKr0ZDoffVaG8gNjeaMVgWdaucrm8VQhzQ8lgTGaI454HcEn1yajEeUGZgvGqtoW22bHTPTE17USnMqh8Pr+Sa0bEhMmMaOs35Rhze3yIOqjy9HVi3FDOtlFggjLE+wcA3NERqxorg4oKowK0zD4NGj0fydYMxQRZusrDCppy7OMUgCsqnanEeMF4uZ9Kn84YFaAZ2xzW63bujvdys1wuN+Nn6TrjqgAZyVKQNQftU6pQqkCR1lIQjJ+l+5VJMkhVILbdN/65q/qknHH1ep11GR3z1+XnZu5MmSx9ZNpvAjgSBkoUpyrWLKDa7fb7Vqv1x4vEoLF1MsS+WD10AKwO6lh2v1gszqoWmjqxui7n1nYUwI0wQEm00c2Q0HQVwMkkBOqOERaIe9NDAJt1B4w7PiwQdW2wodbGLVKn/yhAHGfersizOoPGGRsViNoOAbhl+twUFtoEEMc+AeBaWBEm25kCoiZW5c1JZ8okEKFY3nD6TWxNmQYSRsGNdyLuFweQsHS+j0h8n4oLiFDcfLmmEq0o4gQS5sXajx/RIp94VdwwCSDqWGdDHVMRFSUmKSChkS54Mc61lTSQWFtnAFyIYxpOAkhki6ZBMH6g3hhlmjnbThLIqYP1IOH2Rq00pgVIwPGIz49tfJ++M0zWpg3IycCPbnzTtMeGU/rmO81A7gTx6/wW2yH5rp0HTO5t3BL40mYFp2uagJRm4H8gpcc0waB/LkM/AV/t9tOxmpn4AAAAAElFTkSuQmCC",mode:"scaleToFill"})]),_:2},1032,["onClick"])):m("",!0)]),_:2},1024))),128)),K.value.length<j.value?(r(),i(u,{key:0,onClick:H},{default:o(()=>[A(a.$slots,"default",{},()=>[n(u,{class:"upload-btn"},{default:o(()=>[n(u,{class:"upload-icon"},{default:o(()=>[n(s,{class:"plus-icon"},{default:o(()=>[v("+")]),_:1})]),_:1}),n(s,{class:"upload-text"},{default:o(()=>[v(F(y(W)("sys.upload")),1)]),_:1})]),_:1})],!0)]),_:3})):m("",!0)]),_:3})]),_:3})}}},[["__scopeId","data-v-ee052384"]]);export{W as _};
