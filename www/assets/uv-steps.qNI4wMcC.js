var t,e,a,s;import{a as i,b as r,v as n,o,w as l,x as p,e as c,i as u,q as d,s as h,A as v,U as m,g as D,t as _}from"./index-CIPK2z2P.js";import{_ as f,m as y,a as x,b as C,f as g}from"./uv-icon.UcuauzO0.js";import{_ as w}from"./uv-text.DpB-FlH4.js";const S=f({name:"uv-steps-item",mixins:[y,x,{props:{title:{type:[String,Number],default:""},desc:{type:[String,Number],default:""},iconSize:{type:[String,Number],default:17},error:{type:Boolean,default:!1},...null==(e=null==(t=uni.$uv)?void 0:t.props)?void 0:e.stepsItem}}],data:()=>({index:0,childLength:0,showLine:!1,size:{height:0,width:0},parentData:{direction:"row",current:0,activeColor:"",inactiveColor:"",activeIcon:"",inactiveIcon:"",dot:!1}}),watch:{parentData(t,e){}},created(){this.init()},computed:{lineStyle(){var t,e;const a={};return"row"===this.parentData.direction?(a.width=this.size.width+"px",a.left=this.size.width/2+"px"):a.height=this.size.height+"px",a.backgroundColor=(null==(e=null==(t=this.parent.children)?void 0:t[this.index+1])?void 0:e.error)?"#f56c6c":this.index<this.parentData.current?this.parentData.activeColor:this.parentData.inactiveColor,a},statusClass(){const{index:t,error:e}=this,{current:a}=this.parentData;return a==t?!0===e?"error":"process":e?"error":a>t?"finish":"wait"},statusColor(){let t="";switch(this.statusClass){case"finish":t=this.parentData.activeColor;break;case"error":t="#f56c6c";break;case"process":t=this.parentData.dot?this.parentData.activeColor:"transparent";break;default:t=this.parentData.inactiveColor}return t},contentStyle(){const t={};return"column"===this.parentData.direction?(t.marginLeft=this.parentData.dot?"2px":"6px",t.marginTop=this.parentData.dot?"0px":"6px"):(t.marginTop=this.parentData.dot?"2px":"6px",t.marginLeft=this.parentData.dot?"2px":"6px"),t}},mounted(){this.parent&&this.parent.updateFromChild(),this.$uv.sleep().then(()=>{this.getStepsItemRect()})},methods:{init(){if(this.updateParentData(),!this.parent)return this.$uv.error("uv-steps-item必须要搭配uv-steps组件使用");this.index=this.parent.children.indexOf(this),this.childLength=this.parent.children.length},updateParentData(){this.getParentData("uv-steps")},updateFromParent(){this.init()},getStepsItemRect(){this.$uvGetRect(".uv-steps-item").then(t=>{this.size=t})}}},[["render",function(t,e,a,s,f,y){const x=u,g=i(r("uv-icon"),C),S=m,$=i(r("uv-text"),w);return o(),n(x,{class:h(["uv-steps-item",[`uv-steps-item--${f.parentData.direction}`]]),ref:"uv-steps-item",style:d([t.$uv.addStyle(t.customStyle)])},{default:l(()=>[f.index+1<f.childLength?(o(),n(x,{key:0,class:h(["uv-steps-item__line",[`uv-steps-item__line--${f.parentData.direction}`]]),style:d([y.lineStyle])},null,8,["class","style"])):p("",!0),c(x,{class:h(["uv-steps-item__wrapper",`uv-steps-item__wrapper--${f.parentData.direction}`,f.parentData.dot&&`uv-steps-item__wrapper--${f.parentData.direction}--dot`])},{default:l(()=>[v(t.$slots,"icon",{},()=>[f.parentData.dot?(o(),n(x,{key:0,class:"uv-steps-item__wrapper__dot",style:d({backgroundColor:y.statusColor})},null,8,["style"])):f.parentData.activeIcon||f.parentData.inactiveIcon?(o(),n(x,{key:1,class:"uv-steps-item__wrapper__icon"},{default:l(()=>[c(g,{name:f.index<=f.parentData.current?f.parentData.activeIcon:f.parentData.inactiveIcon,size:t.iconSize,color:f.index<=f.parentData.current?f.parentData.activeColor:f.parentData.inactiveColor},null,8,["name","size","color"])]),_:1})):(o(),n(x,{key:2,style:d({backgroundColor:"process"===y.statusClass?f.parentData.activeColor:"transparent",borderColor:y.statusColor}),class:"uv-steps-item__wrapper__circle"},{default:l(()=>["process"===y.statusClass||"wait"===y.statusClass?(o(),n(S,{key:0,class:"uv-steps-item__wrapper__circle__text",style:d({color:f.index==f.parentData.current?"#ffffff":f.parentData.inactiveColor})},{default:l(()=>[D(_(f.index+1),1)]),_:1},8,["style"])):(o(),n(g,{key:1,color:"error"===y.statusClass?"error":f.parentData.activeColor,size:"12",name:"error"===y.statusClass?"close":"checkmark"},null,8,["color","name"]))]),_:1},8,["style"]))],!0)]),_:3},8,["class"]),c(x,{class:h(["uv-steps-item__content",`uv-steps-item__content--${f.parentData.direction}`]),style:d([y.contentStyle])},{default:l(()=>[v(t.$slots,"title",{},()=>[c($,{text:t.title,type:f.parentData.current==f.index?"main":"content",lineHeight:"20px",size:f.parentData.current==f.index?14:13},null,8,["text","type","size"])],!0),v(t.$slots,"desc",{},()=>[c($,{text:t.desc,type:"tips",size:"12"},null,8,["text"])],!0)]),_:3},8,["class","style"])]),_:3},8,["class","style"])}],["__scopeId","data-v-74163a7f"]]);const $=f({name:"uv-steps",mixins:[y,x,{props:{direction:{type:String,default:"row"},current:{type:[String,Number],default:0},activeColor:{type:String,default:"#3c9cff"},inactiveColor:{type:String,default:"#969799"},activeIcon:{type:String,default:""},inactiveIcon:{type:String,default:""},dot:{type:Boolean,default:!1},...null==(s=null==(a=uni.$uv)?void 0:a.props)?void 0:s.steps}}],data:()=>({}),watch:{children(){this.updateChildData()},parentData(){this.updateChildData()}},computed:{parentData(){return[this.current,this.direction,this.activeColor,this.inactiveColor,this.activeIcon,this.inactiveIcon,this.dot]}},methods:{updateChildData(){this.children.map(t=>{g((t||{}).updateFromParent())&&t.updateFromParent()})},updateFromChild(){this.updateChildData()}},created(){this.children=[]}},[["render",function(t,e,a,s,i,r){const p=u;return o(),n(p,{class:h(["uv-steps",`uv-steps--${t.direction}`]),style:d([t.$uv.addStyle(t.customStyle)])},{default:l(()=>[v(t.$slots,"default",{},void 0,!0)]),_:3},8,["class","style"])}],["__scopeId","data-v-0487c027"]]);export{S as _,$ as a};
