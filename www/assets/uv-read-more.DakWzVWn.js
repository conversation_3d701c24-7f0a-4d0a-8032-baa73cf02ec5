var t,e;import{a as s,b as o,v as n,o as a,w as i,e as l,x as r,i as d,q as u,s as g,A as h}from"./index-BBirLt11.js";import{_ as c}from"./uv-text.D1tdgrqN.js";import{_ as m,m as f,a as p,b as _}from"./uv-icon.Dp0oPivN.js";const v=m({name:"uv-read-more",mixins:[f,p,{props:{showHeight:{type:[String,Number],default:400},toggle:{type:Boolean,default:!1},closeText:{type:String,default:"展开阅读全文"},openText:{type:String,default:"收起"},color:{type:String,default:"#2979ff"},fontSize:{type:[String,Number],default:14},shadowStyle:{type:Object,default:()=>({backgroundImage:"linear-gradient(-180deg, rgba(255, 255, 255, 0) 0%, #fff 80%)",paddingTop:"100px",marginTop:"-100px"})},textIndent:{type:String,default:"2em"},name:{type:[String,Number],default:""},...null==(e=null==(t=uni.$uv)?void 0:t.props)?void 0:e.readMore}}],data:()=>({isLongContent:!1,status:"close",elId:"",contentHeight:100}),computed:{innerShadowStyle(){return"open"===this.status?{}:this.shadowStyle}},created(){this.elId=this.$uv.guid()},mounted(){this.init()},methods:{async init(){this.getContentHeight().then(t=>{this.contentHeight=t,t>this.$uv.getPx(this.showHeight)&&(this.isLongContent=!0,this.status="close")})},async getContentHeight(){return await this.$uv.sleep(30),new Promise(t=>{this.$uvGetRect("."+this.elId).then(e=>{t(e.height)})})},toggleReadMore(){this.status="close"===this.status?"open":"close",0==this.toggle&&(this.isLongContent=!1),this.$emit(this.status,this.name)}}},[["render",function(t,e,m,f,p,v){const x=d,y=s(o("uv-text"),c),S=s(o("uv-icon"),_);return a(),n(x,{class:"uv-read-more"},{default:i(()=>[l(x,{class:"uv-read-more__content",style:u({height:p.isLongContent&&"close"===p.status?t.$uv.addUnit(t.showHeight):t.$uv.addUnit(p.contentHeight,"px"),textIndent:t.textIndent})},{default:i(()=>[l(x,{class:g(["uv-read-more__content__inner",[p.elId]]),ref:"uv-read-more__content__inner"},{default:i(()=>[h(t.$slots,"default",{},void 0,!0)]),_:3},8,["class"])]),_:3},8,["style"]),p.isLongContent?(a(),n(x,{key:0,class:"uv-read-more__toggle",style:u([v.innerShadowStyle])},{default:i(()=>[h(t.$slots,"toggle",{},()=>[l(x,{class:"uv-read-more__toggle__text",onClick:v.toggleReadMore},{default:i(()=>[l(y,{text:"close"===p.status?t.closeText:t.openText,color:t.color,size:t.fontSize,lineHeight:t.fontSize,margin:"0 5px 0 0"},null,8,["text","color","size","lineHeight"]),l(x,{class:"uv-read-more__toggle__icon"},{default:i(()=>[l(S,{color:t.color,size:t.fontSize+2,name:"close"===p.status?"arrow-down":"arrow-up"},null,8,["color","size","name"])]),_:1})]),_:1},8,["onClick"])],!0)]),_:3},8,["style"])):r("",!0)]),_:3})}],["__scopeId","data-v-5e353f0f"]]);export{v as _};
