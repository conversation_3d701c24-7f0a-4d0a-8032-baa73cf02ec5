import{_ as e}from"./empty.vue_vue_type_script_setup_true_lang.BXZPGwCi.js";import{_ as a}from"./media-item.vue_vue_type_script_setup_true_lang.C8dLc39B.js";import{_ as t}from"./navbar.vue_vue_type_script_setup_true_lang.CSl8hyKC.js";import{d as s,u as l,r as i,D as o,E as n,m as r,n as u,$ as m,I as d,J as c,c as p,e as v,h as _,w as g,a as f,b as y,o as b,v as x,i as k,s as j,F as h,z as w,g as T,t as C,j as S}from"./index-CIPK2z2P.js";import{_ as D}from"./uv-tabs.CWR5nOXF.js";import{_ as P}from"./uv-sticky.CXYYNICz.js";import{_ as W}from"./uv-load-more.DsJkRw2i.js";import{_ as z}from"./uv-skeletons.D1UL33yi.js";import{g as N,a as O}from"./media.Ds2RueMI.js";import{s as $}from"./config.CV-betc9.js";import{D as q}from"./decimal.B1oHnkff.js";import"./uv-empty.BH_ZJrMJ.js";import"./uv-icon.UcuauzO0.js";import"./uv-status-bar.BkslDeIT.js";import"./debounce.Ce2HeGXN.js";import"./uv-line.CaGHsg1_.js";import"./uv-loading-icon.Bi7ZFsTo.js";const A=s({__name:"index",setup(s){const{t:A}=l(),B=i(!0),E=i([]),F=i(""),I=i(0),J=i(0),K=o({mtpt_id:"",page:1,limit:10}),L=i(0),M=i([]),Q=async()=>{try{const{data:e,count:a}=await N(K);1===K.page?E.value=e||[]:E.value=E.value.concat(e||[]),L.value=a||0,K.limit*K.page>=L.value&&(F.value="nomore")}catch(e){}},R=()=>{S("/pages/my/media/add")},U=()=>{S("/pages/my/index","switchTab")};n(()=>{if(L.value<=q(K.page*K.limit).toNumber())return F.value="nomore";K.page++,Q(),F.value="loading"}),r(e=>{J.value=e.scrollTop});const V=async()=>{try{const{data:e}=await O();return e&&e.length?(M.value=e.map(e=>({name:e.name,value:e.mtpt_id,badge:{value:e.num||0}})),M.value.unshift({name:A("notice.all"),value:"",badge:{value:0}}),e||[]):void(M.value=[])}catch(e){return[]}};return u(()=>{m("back-media"),d("back-media",()=>{K.mtpt_id="",I.value=0,K.page=1,B.value=!0,Promise.allSettled([Q(),V()]).finally(()=>{B.value=!1})})}),c(()=>{K.page=1,B.value=!0,Promise.allSettled([Q(),V()]).finally(()=>{B.value=!1})}),(s,l)=>{const i=t,o=f(y("uv-tabs"),D),n=k,r=f(y("uv-sticky"),P),u=a,m=f(y("uv-load-more"),W),d=e,c=f(y("uv-skeletons"),z);return b(),p("div",null,[v(i,{"bg-color":"transparent",title:_(A)("navTitle.media"),scrollTextColor:"blackOne",isDarkMode:!0,back:!1,"right-text":"+ Add",onLeftClick:U,onRightClick:R},null,8,["title"]),v(c,{loading:_(B),skeleton:_($)},{default:g(()=>[v(r,null,{default:g(()=>[v(n,{class:j(["pb-20 pl-30",{"bg-white":_(J)>30}])},{default:g(()=>[v(o,{list:_(M),current:_(I),activeStyle:{color:"#131726",fontWeight:"bold",fontSize:"28rpx"},inactiveStyle:{color:"#444B56"},lineColor:"#0165ff",lineWidth:"66rpx",onClick:l[0]||(l[0]=e=>{_(K).mtpt_id=e.value,I.value=e.index,_(K).page=1,Q()})},null,8,["list","current"])]),_:1},8,["class"])]),_:1}),_(E).length?(b(),x(n,{key:0,class:"p-20"},{default:g(()=>[(b(!0),p(h,null,w(_(E),e=>(b(),x(n,{class:"px-30 py-40 bg-white rounded-24 mb-20 last:mb-0 flex items-center",key:e.id,onClick:a=>{return t=e.id,void S(`/pages/my/media/add?id=${t}`);var t}},{default:g(()=>[v(u,{item:e,select:!1},null,8,["item"])]),_:2},1032,["onClick"]))),128)),v(m,{status:_(F),class:"p-32",loadingText:_(A)("sys.loading"),nomoreText:_(A)("sys.noData")},null,8,["status","loadingText","nomoreText"])]),_:1})):(b(),x(n,{key:1,class:"flex flex-col items-center"},{default:g(()=>[v(d,{class:"mt-[30%]"}),v(n,{class:"bg-blueOne w-400 py-22 text-center leading-44 text-28 text-white px-50 mt-60 rounded-20",onClick:R},{default:g(()=>[T(C(_(A)("sys.addNow")),1)]),_:1})]),_:1}))]),_:1},8,["loading","skeleton"])])}}});export{A as default};
