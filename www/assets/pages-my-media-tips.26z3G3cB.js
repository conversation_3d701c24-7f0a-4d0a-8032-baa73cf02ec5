import{d as t,r as e,u as a,J as s,v as l,w as i,i as n,aq as o,j as c,o as r,e as u,h as d,f as m,g as f,t as _,U as p}from"./index-BBirLt11.js";import{_ as b}from"./success.njPbLiaa.js";import{_ as v}from"./navbar.vue_vue_type_script_setup_true_lang.DC1OHhyC.js";import{d as x}from"./debounce.Ce2HeGXN.js";import{_ as k}from"./uv-icon.Dp0oPivN.js";import"./uv-status-bar.CB0-bOg6.js";const g=k(t({__name:"tips",setup(t){const k=e(),{t:g}=a(),j=x(()=>{var t;if(!(null==(t=k.value)?void 0:t.id))return o("back-media",!0),void c("","back",2);o("selectMedia",k.value),c("","back",3)},300,{leading:!0,trailing:!0});return s(t=>{t&&t.id&&(k.value=t)}),(t,e)=>{const a=m,s=n,o=p;return r(),l(s,{class:"container"},{default:i(()=>[u(v,{title:d(g)("navTitle.media"),showBack:!0},null,8,["title"]),u(s,{class:"content-container flex flex-col items-center justify-center"},{default:i(()=>[u(a,{class:"w-136 h-136 mt-140 mb-20",src:b,mode:"aspectFill"}),u(s,{class:"title w-492 leading-50 text-36 font-semibold m-auto mb-20 text-center text-blackOne"},{default:i(()=>[f(_(d(g)("media.title")),1)]),_:1}),u(s,{class:"hint m-auto leading-48 text-28 text-blackTwo w-622 text-center"},{default:i(()=>[u(o,{class:"code"},{default:i(()=>[f(_(d(g)("media.tips")),1)]),_:1})]),_:1}),u(s,{class:"button-area"},{default:i(()=>[u(s,{class:"primary-button h-88 bg-blueOne px-162 w-fit m-auto mt-40 rounded-20 text-white leading-88 text-30",onClick:d(j)},{default:i(()=>[f(_(d(g)("sys.back")),1)]),_:1},8,["onClick"])]),_:1})]),_:1})]),_:1})}}}),[["__scopeId","data-v-98d8e33e"]]);export{g as default};
