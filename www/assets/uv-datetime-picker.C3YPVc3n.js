var t,e;import{_ as n}from"./submitButton.0smgT9Uf.js";import{_ as i}from"./empty.vue_vue_type_script_setup_true_lang.SETk5GIM.js";import{d as r,u as a,r as s,ak as o,ae as u,H as l,v as c,o as h,w as d,e as m,i as f,g as p,t as y,h as g,a as $,a8 as v,x,a7 as b,c as D,z as M,F as _,R as S,b as w}from"./index-BBirLt11.js";import{_ as C}from"./uv-input.Q1TIxLPl.js";import{_ as O,a as k}from"./uv-radio-group.CF_hqWfL.js";import{_ as T}from"./uv-load-more.SNzw0348.js";import{_ as V}from"./uv-popup.BGprV-fU.js";import{d as I}from"./debounce.Ce2HeGXN.js";import{_ as H,m as F,a as N}from"./uv-icon.Dp0oPivN.js";import{_ as Y}from"./uv-picker.CEmVlaLq.js";const j=H(r({__name:"report-popup",props:{list:{type:Array,default:()=>[]},id:{type:String,default:""},title:{type:String,default:""},total:{type:Number,default:0}},emits:["select","scrollToLower","backValue"],setup(t,{expose:e,emit:r}){const{t:H}=a(),F=r,N=t,Y=s("");o(()=>N.list,t=>{t.length<N.total?Y.value="":Y.value="nomore"},{immediate:!0});const j=s(),B=s(""),Z=()=>{S({url:`/pages/details/form?id=${N.id}`})},A=I(()=>{N.total>N.list.length&&F("scrollToLower")},500,{leading:!0,trailing:!0}),L=t=>{Y.value="loading",F("select",t)},W=I(()=>{B.value?F("backValue",B.value):u(H("sys.pleaseSelect"))},300,{leading:!0,trailing:!0});return e({open(){l(()=>{j.value.open()})},close(){l(()=>{j.value.close()})}}),(e,r)=>{const a=f,s=$(w("uv-input"),C),o=$(w("uv-radio"),O),u=$(w("uv-radio-group"),k),l=$(w("uv-load-more"),T),S=v,I=i,F=n,N=$(w("uv-popup"),V);return h(),c(N,{ref_key:"popup",ref:j,mode:"bottom",closeable:"",overlayStyle:"background: rgba(0, 0, 0, 0.3);",round:"15"},{default:d(()=>[m(a,{class:"bg-[#F6F7F9] env-bottom"},{default:d(()=>[m(a,{class:"text-black font-semibold pt-26 pl-32"},{default:d(()=>[p(y(g(H)("promotion.selectSettlementMethod")),1)]),_:1}),m(a,{class:"p-32 pt-42"},{default:d(()=>[m(s,{customStyle:{width:"686rpx",height:"88rpx",borderRadius:"20rpx",backgroundColor:"#FFFFFF"},onInput:L,clearable:"",placeholder:g(H)("sys.searchTip"),prefixIcon:"search",prefixIconStyle:"font-size: 22px;color: #909399"},null,8,["placeholder"])]),_:1}),t.list.length?(h(),c(a,{key:0,class:"p-32 pt-0"},{default:d(()=>[m(S,{"scroll-y":"true","scroll-left":"120",class:"h-[50vh]",onScrolltolower:g(A)},{default:d(()=>[m(u,{modelValue:g(B),"onUpdate:modelValue":r[0]||(r[0]=t=>b(B)?B.value=t:null),placement:"column",iconPlacement:"right",customStyle:{margin:"0"}},{default:d(()=>[(h(!0),D(_,null,M(t.list,t=>(h(),c(a,{key:t.id,class:"p-32 bg-white rounded-20 mb-20 last:mb-0"},{default:d(()=>[m(o,{name:t.id,class:"flex items-center mb-20"},{default:d(()=>[m(a,{class:"text-black text-28 leading-40 max-w-550"},{default:d(()=>[m(a,{class:"text-blackThree leading-36 w-fit text-26 line-clamp-1"},{default:d(()=>[p(y(t.uniq_bb_zd)+":",1)]),_:2},1024),m(a,{class:"text-blackOne text-26 w-fit line-clamp-2 leading-36 mt-8"},{default:d(()=>[p(y(t.uniq_bb_value),1)]),_:2},1024)]),_:2},1024)]),_:2},1032,["name"])]),_:2},1024))),128))]),_:1},8,["modelValue"]),t.list.length?(h(),c(l,{key:0,status:g(Y),class:"p-32",loadingText:g(H)("sys.loading"),nomoreText:g(H)("sys.noData")},null,8,["status","loadingText","nomoreText"])):x("",!0)]),_:1},8,["onScrolltolower"])]),_:1})):(h(),c(a,{key:1,class:"bg-[#F6F7F9] py-100 flex items-center flex-col"},{default:d(()=>[m(I),m(a,{class:"bg-blueOne py-15 leading-40 text-26 text-white px-50 mt-20 rounded-10",onClick:Z},{default:d(()=>[p(y(g(H)("sys.applyNow")),1)]),_:1})]),_:1}))]),_:1}),m(F,{onBack:g(W)},null,8,["onBack"])]),_:1},512)}}}),[["__scopeId","data-v-44fd21e5"]]),B={props:{value:{type:[String,Number],default:""},modelValue:{type:[String,Number],default:""},show:{type:Boolean,default:!1},showToolbar:{type:Boolean,default:!0},title:{type:String,default:""},mode:{type:String,default:"datetime"},maxDate:{type:Number,default:new Date((new Date).getFullYear()+10,0,1).getTime()},minDate:{type:Number,default:new Date((new Date).getFullYear()-10,0,1).getTime()},minHour:{type:Number,default:0},maxHour:{type:Number,default:23},minMinute:{type:Number,default:0},maxMinute:{type:Number,default:59},filter:{type:[Function,null],default:null},formatter:{type:[Function,null],default:null},loading:{type:Boolean,default:!1},itemHeight:{type:[String,Number],default:44},cancelText:{type:String,default:"取消"},confirmText:{type:String,default:"确认"},cancelColor:{type:String,default:"#909193"},confirmColor:{type:String,default:"#3c9cff"},visibleItemCount:{type:[String,Number],default:5},closeOnClickOverlay:{type:Boolean,default:!0},closeOnClickConfirm:{type:Boolean,default:!0},clearDate:{type:Boolean,default:!1},round:{type:[String,Number],default:0},...null==(e=null==(t=uni.$uv)?void 0:t.props)?void 0:e.datetimePicker}};var Z,A,L=Object.getOwnPropertyNames;const W=(Z={uvuidayjs(t,e){var n,i;n=t,i=function(){var t=6e4,e=36e5,n="millisecond",i="second",r="minute",a="hour",s="day",o="week",u="month",l="quarter",c="year",h="date",d="Invalid Date",m=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,f=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,p={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(t){var e=["th","st","nd","rd"],n=t%100;return"["+t+(e[(n-20)%10]||e[n]||e[0])+"]"}},y=function(t,e,n){var i=String(t);return!i||i.length>=e?t:""+Array(e+1-i.length).join(n)+t},g={s:y,z:function(t){var e=-t.utcOffset(),n=Math.abs(e),i=Math.floor(n/60),r=n%60;return(e<=0?"+":"-")+y(i,2,"0")+":"+y(r,2,"0")},m:function t(e,n){if(e.date()<n.date())return-t(n,e);var i=12*(n.year()-e.year())+(n.month()-e.month()),r=e.clone().add(i,u),a=n-r<0,s=e.clone().add(i+(a?-1:1),u);return+(-(i+(n-r)/(a?r-s:s-r))||0)},a:function(t){return t<0?Math.ceil(t)||0:Math.floor(t)},p:function(t){return{M:u,y:c,w:o,d:s,D:h,h:a,m:r,s:i,ms:n,Q:l}[t]||String(t||"").toLowerCase().replace(/s$/,"")},u:function(t){return void 0===t}},$="en",v={};v[$]=p;var x=function(t){return t instanceof _},b=function t(e,n,i){var r;if(!e)return $;if("string"==typeof e){var a=e.toLowerCase();v[a]&&(r=a),n&&(v[a]=n,r=a);var s=e.split("-");if(!r&&s.length>1)return t(s[0])}else{var o=e.name;v[o]=e,r=o}return!i&&r&&($=r),r||!i&&$},D=function(t,e){if(x(t))return t.clone();var n="object"==typeof e?e:{};return n.date=t,n.args=arguments,new _(n)},M=g;M.l=b,M.i=x,M.w=function(t,e){return D(t,{locale:e.$L,utc:e.$u,x:e.$x,$offset:e.$offset})};var _=function(){function p(t){this.$L=b(t.locale,null,!0),this.parse(t)}var y=p.prototype;return y.parse=function(t){this.$d=function(t){var e=t.date,n=t.utc;if(null===e)return new Date(NaN);if(M.u(e))return new Date;if(e instanceof Date)return new Date(e);if("string"==typeof e&&!/Z$/i.test(e)){var i=e.match(m);if(i){var r=i[2]-1||0,a=(i[7]||"0").substring(0,3);return n?new Date(Date.UTC(i[1],r,i[3]||1,i[4]||0,i[5]||0,i[6]||0,a)):new Date(i[1],r,i[3]||1,i[4]||0,i[5]||0,i[6]||0,a)}}return new Date(e)}(t),this.$x=t.x||{},this.init()},y.init=function(){var t=this.$d;this.$y=t.getFullYear(),this.$M=t.getMonth(),this.$D=t.getDate(),this.$W=t.getDay(),this.$H=t.getHours(),this.$m=t.getMinutes(),this.$s=t.getSeconds(),this.$ms=t.getMilliseconds()},y.$utils=function(){return M},y.isValid=function(){return!(this.$d.toString()===d)},y.isSame=function(t,e){var n=D(t);return this.startOf(e)<=n&&n<=this.endOf(e)},y.isAfter=function(t,e){return D(t)<this.startOf(e)},y.isBefore=function(t,e){return this.endOf(e)<D(t)},y.$g=function(t,e,n){return M.u(t)?this[e]:this.set(n,t)},y.unix=function(){return Math.floor(this.valueOf()/1e3)},y.valueOf=function(){return this.$d.getTime()},y.startOf=function(t,e){var n=this,l=!!M.u(e)||e,d=M.p(t),m=function(t,e){var i=M.w(n.$u?Date.UTC(n.$y,e,t):new Date(n.$y,e,t),n);return l?i:i.endOf(s)},f=function(t,e){return M.w(n.toDate()[t].apply(n.toDate("s"),(l?[0,0,0,0]:[23,59,59,999]).slice(e)),n)},p=this.$W,y=this.$M,g=this.$D,$="set"+(this.$u?"UTC":"");switch(d){case c:return l?m(1,0):m(31,11);case u:return l?m(1,y):m(0,y+1);case o:var v=this.$locale().weekStart||0,x=(p<v?p+7:p)-v;return m(l?g-x:g+(6-x),y);case s:case h:return f($+"Hours",0);case a:return f($+"Minutes",1);case r:return f($+"Seconds",2);case i:return f($+"Milliseconds",3);default:return this.clone()}},y.endOf=function(t){return this.startOf(t,!1)},y.$set=function(t,e){var o,l=M.p(t),d="set"+(this.$u?"UTC":""),m=(o={},o[s]=d+"Date",o[h]=d+"Date",o[u]=d+"Month",o[c]=d+"FullYear",o[a]=d+"Hours",o[r]=d+"Minutes",o[i]=d+"Seconds",o[n]=d+"Milliseconds",o)[l],f=l===s?this.$D+(e-this.$W):e;if(l===u||l===c){var p=this.clone().set(h,1);p.$d[m](f),p.init(),this.$d=p.set(h,Math.min(this.$D,p.daysInMonth())).$d}else m&&this.$d[m](f);return this.init(),this},y.set=function(t,e){return this.clone().$set(t,e)},y.get=function(t){return this[M.p(t)]()},y.add=function(n,l){var h,d=this;n=Number(n);var m=M.p(l),f=function(t){var e=D(d);return M.w(e.date(e.date()+Math.round(t*n)),d)};if(m===u)return this.set(u,this.$M+n);if(m===c)return this.set(c,this.$y+n);if(m===s)return f(1);if(m===o)return f(7);var p=(h={},h[r]=t,h[a]=e,h[i]=1e3,h)[m]||1,y=this.$d.getTime()+n*p;return M.w(y,this)},y.subtract=function(t,e){return this.add(-1*t,e)},y.format=function(t){var e=this,n=this.$locale();if(!this.isValid())return n.invalidDate||d;var i=t||"YYYY-MM-DDTHH:mm:ssZ",r=M.z(this),a=this.$H,s=this.$m,o=this.$M,u=n.weekdays,l=n.months,c=function(t,n,r,a){return t&&(t[n]||t(e,i))||r[n].slice(0,a)},h=function(t){return M.s(a%12||12,t,"0")},m=n.meridiem||function(t,e,n){var i=t<12?"AM":"PM";return n?i.toLowerCase():i},p={YY:String(this.$y).slice(-2),YYYY:this.$y,M:o+1,MM:M.s(o+1,2,"0"),MMM:c(n.monthsShort,o,l,3),MMMM:c(l,o),D:this.$D,DD:M.s(this.$D,2,"0"),d:String(this.$W),dd:c(n.weekdaysMin,this.$W,u,2),ddd:c(n.weekdaysShort,this.$W,u,3),dddd:u[this.$W],H:String(a),HH:M.s(a,2,"0"),h:h(1),hh:h(2),a:m(a,s,!0),A:m(a,s,!1),m:String(s),mm:M.s(s,2,"0"),s:String(this.$s),ss:M.s(this.$s,2,"0"),SSS:M.s(this.$ms,3,"0"),Z:r};return i.replace(f,function(t,e){return e||p[t]||r.replace(":","")})},y.utcOffset=function(){return 15*-Math.round(this.$d.getTimezoneOffset()/15)},y.diff=function(n,h,d){var m,f=M.p(h),p=D(n),y=(p.utcOffset()-this.utcOffset())*t,g=this-p,$=M.m(this,p);return $=(m={},m[c]=$/12,m[u]=$,m[l]=$/3,m[o]=(g-y)/6048e5,m[s]=(g-y)/864e5,m[a]=g/e,m[r]=g/t,m[i]=g/1e3,m)[f]||g,d?$:M.a($)},y.daysInMonth=function(){return this.endOf(u).$D},y.$locale=function(){return v[this.$L]},y.locale=function(t,e){if(!t)return this.$L;var n=this.clone(),i=b(t,e,!0);return i&&(n.$L=i),n},y.clone=function(){return M.w(this.$d,this)},y.toDate=function(){return new Date(this.valueOf())},y.toJSON=function(){return this.isValid()?this.toISOString():null},y.toISOString=function(){return this.$d.toISOString()},y.toString=function(){return this.$d.toUTCString()},p}(),S=_.prototype;return D.prototype=S,[["$ms",n],["$s",i],["$m",r],["$H",a],["$W",s],["$M",u],["$y",c],["$D",h]].forEach(function(t){S[t[1]]=function(e){return this.$g(e,t[0],t[1])}}),D.extend=function(t,e){return t.$i||(t(e,_,D),t.$i=!0),D},D.locale=b,D.isDayjs=x,D.unix=function(t){return D(1e3*t)},D.en=v[$],D.Ls=v,D.p={},D},"object"==typeof t&&void 0!==e?e.exports=i():"function"==typeof define&&define.amd?define(i):(n="undefined"!=typeof globalThis?globalThis:n||self).dayjs=i()}},function(){return A||(0,Z[L(Z)[0]])((A={exports:{}}).exports,A),A.exports})();const U=H({name:"uv-datetime-picker",emits:["close","cancel","confirm","input","change","update:modelValue"],mixins:[F,N,B],data:()=>({columns:[],innerDefaultIndex:[],innerFormatter:(t,e)=>e}),watch:{propsChange(){this.init()}},computed:{propsChange(){const t=this.value||this.modelValue;return[this.mode,this.maxDate,this.minDate,this.minHour,this.maxHour,this.minMinute,this.maxMinute,this.filter,t]}},mounted(){this.init()},methods:{init(){this.getValue(),this.updateColumnValue(this.innerValue)},getValue(){const t=this.value||this.modelValue;this.innerValue=this.correctValue(t)},setFormatter(t){this.innerFormatter=t},open(){this.$refs.picker.open(),this.getValue(),this.updateColumnValue(this.innerValue)},close(){this.$emit("close")},cancel(){this.$emit("cancel")},confirm(){this.$emit("confirm",{value:this.innerValue,mode:this.mode}),this.clearDate||(this.$emit("input",this.innerValue),this.$emit("update:modelValue",this.innerValue))},intercept(t,e){let n=t.match(/\d+/g);return n.length>1?(this.$uv.error("请勿在过滤或格式化函数时添加数字"),0):e&&4==n[0].length?n[0]:n[0].length>2?(this.$uv.error("请勿在过滤或格式化函数时添加数字"),0):n[0]},change(t){const{indexs:e,values:n}=t;let i="";if("time"===this.mode)i=`${this.intercept(n[0][e[0]])}:${this.intercept(n[1][e[1]])}`;else if("year"===this.mode){const t=parseInt(this.intercept(n[0][e[0]],"year"));i=Number(new Date(t,0))}else{const t=parseInt(this.intercept(n[0][e[0]],"year")),r=parseInt(this.intercept(n[1][e[1]]));let a=parseInt(n[2]?this.intercept(n[2][e[2]]):1),s=0,o=0;const u=W(`${t}-${r}`).daysInMonth();"year-month"===this.mode&&(a=1),a=Math.min(u,a),"datetime"===this.mode&&(s=parseInt(this.intercept(n[3][e[3]])),o=parseInt(this.intercept(n[4][e[4]]))),i=Number(new Date(t,r-1,a,s,o))}i=this.correctValue(i),this.innerValue=i,this.updateColumnValue(i),this.$emit("change",{value:i,mode:this.mode})},updateColumnValue(t){this.innerValue=t,this.updateColumns(),this.updateIndexs(t)},updateIndexs(t){let e=[];const n=this.formatter||this.innerFormatter;if("time"===this.mode){const i=t.split(":");e=[n("hour",i[0]),n("minute",i[1])]}else e=[n("year",`${W(t).year()}`),n("month",this.$uv.padZero(W(t).month()+1))],"date"===this.mode&&e.push(n("day",this.$uv.padZero(W(t).date()))),"datetime"===this.mode&&e.push(n("day",this.$uv.padZero(W(t).date())),n("hour",this.$uv.padZero(W(t).hour())),n("minute",this.$uv.padZero(W(t).minute())));const i=this.columns.map((t,n)=>Math.max(0,t.findIndex(t=>t===e[n])));this.$nextTick(()=>{this.$uv.sleep(100).then(t=>{this.$refs.picker.setIndexs(i,!0)})})},updateColumns(){const t=this.formatter||this.innerFormatter,e=this.getOriginColumns().map(e=>e.values.map(n=>t(e.type,n)));this.columns=e},getOriginColumns(){return this.getRanges().map(({type:t,range:e})=>{let n=function(t,e){let n=-1;const i=Array(t<0?0:t);for(;++n<t;)i[n]=e(n);return i}(e[1]-e[0]+1,n=>{let i=e[0]+n;return i="year"===t?`${i}`:this.$uv.padZero(i),i});return this.filter&&(n=this.filter(t,n)),{type:t,values:n}})},generateArray:(t,e)=>Array.from(new Array(e+1).keys()).slice(t),correctValue(t){const e="time"!==this.mode;if(e&&!this.$uv.test.date(t)?t=this.minDate:e||t||(t=`${this.$uv.padZero(this.minHour)}:${this.$uv.padZero(this.minMinute)}`),e)return t=W(t).isBefore(W(this.minDate))?this.minDate:t,t=W(t).isAfter(W(this.maxDate))?this.maxDate:t;{if(-1===String(t).indexOf(":"))return this.$uv.error("时间错误，请传递如12:24的格式");let[e,n]=t.split(":");return e=this.$uv.padZero(this.$uv.range(this.minHour,this.maxHour,Number(e))),n=this.$uv.padZero(this.$uv.range(this.minMinute,this.maxMinute,Number(n))),`${e}:${n}`}},getRanges(){if("time"===this.mode)return[{type:"hour",range:[this.minHour,this.maxHour]},{type:"minute",range:[this.minMinute,this.maxMinute]}];const{maxYear:t,maxDate:e,maxMonth:n,maxHour:i,maxMinute:r}=this.getBoundary("max",this.innerValue),{minYear:a,minDate:s,minMonth:o,minHour:u,minMinute:l}=this.getBoundary("min",this.innerValue),c=[{type:"year",range:[a,t]},{type:"month",range:[o,n]},{type:"day",range:[s,e]},{type:"hour",range:[u,i]},{type:"minute",range:[l,r]}];return"date"===this.mode&&c.splice(3,2),"year-month"===this.mode&&c.splice(2,3),"year"===this.mode&&c.splice(1,4),c},getBoundary(t,e){const n=new Date(e),i=new Date(this[`${t}Date`]),r=W(i).year();let a=1,s=1,o=0,u=0;return"max"===t&&(a=12,s=W(n).daysInMonth(),o=23,u=59),W(n).year()===r&&(a=W(i).month()+1,W(n).month()+1===a&&(s=W(i).date(),W(n).date()===s&&(o=W(i).hour(),W(n).hour()===o&&(u=W(i).minute())))),{[`${t}Year`]:r,[`${t}Month`]:a,[`${t}Date`]:s,[`${t}Hour`]:o,[`${t}Minute`]:u}}}},[["render",function(t,e,n,i,r,a){const s=$(w("uv-picker"),Y);return h(),c(s,{ref:"picker",closeOnClickOverlay:t.closeOnClickOverlay,closeOnClickConfirm:t.closeOnClickConfirm,columns:r.columns,title:t.title,itemHeight:t.itemHeight,showToolbar:t.showToolbar,visibleItemCount:t.visibleItemCount,defaultIndex:r.innerDefaultIndex,cancelText:t.cancelText,confirmText:t.confirmText,cancelColor:t.cancelColor,confirmColor:t.confirmColor,round:t.round,onClose:a.close,onCancel:a.cancel,onConfirm:a.confirm,onChange:a.change},null,8,["closeOnClickOverlay","closeOnClickConfirm","columns","title","itemHeight","showToolbar","visibleItemCount","defaultIndex","cancelText","confirmText","cancelColor","confirmColor","round","onClose","onCancel","onConfirm","onChange"])}]]);export{U as _,j as a};
