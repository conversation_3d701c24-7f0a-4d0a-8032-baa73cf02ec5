import{d as e,u as t,r as a,a as s,b as l,c as n,o,e as r,w as c,i as u,g as d,t as i,h as p,N as m}from"./index-BBirLt11.js";import{_ as b}from"./uv-popup.BGprV-fU.js";const x=e({__name:"xyPopup",emits:["call-back"],setup(e,{expose:x,emit:f}){const{t:g}=t(),_=a(),y=f,v=()=>{_.value.close()},k=()=>{_.value.close(),y("call-back")};return x({open:()=>_.value.open()}),(e,t)=>{const a=u,x=s(l("uv-popup"),b);return o(),n("div",null,[r(x,{ref_key:"popup",ref:_,overlayStyle:"background: rgba(0, 0, 0, 0.3);",round:"15",mode:"bottom"},{default:c(()=>[r(a,{class:"pt-40 mb-40"},{default:c(()=>[r(a,{class:"px-32"},{default:c(()=>[r(a,{class:"text-32 leading-44 text-center font-semibold"},{default:c(()=>[d(i(p(g)("sys.serviceAgreementAndPrivacyPolicy")),1)]),_:1}),r(a,{class:"text-blackTwo text-28 leading-40 mt-20 text-center"},{default:c(()=>[d(i(p(g)("agreement.tit"))+" ",1),m("span",{class:"text-blueOne"},"《 "+i(p(g)("agreement.service"))+"》",1),d(" "+i(p(g)("agreement.and"))+" ",1),m("span",{class:"text-blueOne"},"《"+i(p(g)("agreement.privacy"))+"》",1)]),_:1})]),_:1}),r(a,{class:"p-32 flex justify-between pt-52"},{default:c(()=>[r(a,{class:"h-88 w-240 px-20 border-blueOne border border-box rounded-20 flex items-center justify-center text-30 leading-30 font-semibold text-blueOne",onClick:v},{default:c(()=>[d(i(p(g)("agreement.no")),1)]),_:1}),r(a,{class:"h-88 w-426 px-20 bg-blueOne rounded-20 flex items-center justify-center text-30 leading-30 font-semibold text-white",onClick:k},{default:c(()=>[d(i(p(g)("agreement.yes")),1)]),_:1})]),_:1})]),_:1})]),_:1},512)])}}});export{x as _};
