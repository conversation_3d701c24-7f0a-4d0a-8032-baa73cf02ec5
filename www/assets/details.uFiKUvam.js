import{a0 as a,k as t}from"./index-BBirLt11.js";const d=t(),i=t=>{const i=d.token?"/api/xm/get_info":"/api/xm/get_visitor_info";return a({url:i,method:"POST",data:{id:t}})},e=t=>a({url:"/api/xm/get_bb_zd",method:"POST",data:{xm_id:t}}),_=t=>a({url:"/api/xm/get_pz_zd",method:"POST",data:{xm_id:t}}),o=t=>a({url:"/api/pz/get_list",method:"POST",data:t}),m=t=>a({url:"/api/bb/adds",method:"POST",data:{xm_id:t.xm_id,bb_data:t.bb_data}}),s=t=>a({url:"/api/bb/bb_zt_tj",method:"POST",data:{xm_id:t}}),p=t=>a({url:"/api/bb/get_list",method:"POST",data:t}),r=(t,d)=>a({url:"/api/bb/get_info",method:"POST",data:{id:t,xm_id:d}}),b=t=>a({url:"/api/pz/adds",method:"POST",data:{xm_id:t.xm_id,pz_data:t.pz_data}}),n=t=>a({url:"/api/xm_notice/get_list",method:"POST",data:t}),x=(t,d,i)=>{let e;return e=d&&!i?"/api/xm_notice/get_info":i?"/api/site_notice/get_znx_info":"/api/site_notice/get_info",a({url:e,method:"POST",data:{id:t}})};export{n as a,i as b,p as c,e as d,r as e,_ as f,x as g,b as h,o as i,s as j,m as s};
