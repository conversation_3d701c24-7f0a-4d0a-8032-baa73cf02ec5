var t,e,o,s;import{_ as i}from"./uv-transition.tIadgx1N.js";import{a as n,b as a,v as r,o as l,w as p,A as u,ab as h,q as d,s as c,i as m,aJ as f,x as y,e as g}from"./index-CIPK2z2P.js";import{_ as b,m as v,a as k,b as w}from"./uv-icon.UcuauzO0.js";import{_ as x}from"./uv-status-bar.BkslDeIT.js";const C=b({name:"uv-overlay",emits:["click"],mixins:[v,k,{props:{show:{type:Boolean,default:!1},zIndex:{type:[String,Number],default:10070},duration:{type:[String,Number],default:300},opacity:{type:[String,Number],default:.5},...null==(e=null==(t=uni.$uv)?void 0:t.props)?void 0:e.overlay}}],watch:{show(t){document.querySelector("body").style.overflow=t?"hidden":""}},computed:{overlayStyle(){const t={position:"fixed",top:0,left:0,right:0,zIndex:this.zIndex,bottom:0,"background-color":`rgba(0, 0, 0, ${this.opacity})`};return this.$uv.deepMerge(t,this.$uv.addStyle(this.customStyle))}},methods:{clickHandler(){this.$emit("click")},clear(){}}},[["render",function(t,e,o,s,d,c){const m=n(a("uv-transition"),i);return l(),r(m,{show:t.show,mode:"fade","custom-class":"uv-overlay",duration:t.duration,"custom-style":c.overlayStyle,onClick:c.clickHandler,onTouchmove:h(c.clear,["stop","prevent"])},{default:p(()=>[u(t.$slots,"default",{},void 0,!0)]),_:3},8,["show","duration","custom-style","onClick","onTouchmove"])}],["__scopeId","data-v-9cc2c1b3"]]);const S=b({name:"uv-safe-bottom",mixins:[v,k],data:()=>({safeAreaBottomHeight:0,isNvue:!1}),computed:{style(){return this.$uv.deepMerge({},this.$uv.addStyle(this.customStyle))}},mounted(){}},[["render",function(t,e,o,s,i,n){const a=m;return l(),r(a,{class:c(["uv-safe-bottom",[!i.isNvue&&"uv-safe-area-inset-bottom"]]),style:d([n.style])},null,8,["style","class"])}],["__scopeId","data-v-8065622b"]]);const _=b({name:"uv-popup",components:{keypress:{name:"Keypress",props:{disable:{type:Boolean,default:!1}},mounted(){const t={esc:["Esc","Escape"],tab:"Tab",enter:"Enter",space:[" ","Spacebar"],up:["Up","ArrowUp"],left:["Left","ArrowLeft"],right:["Right","ArrowRight"],down:["Down","ArrowDown"],delete:["Backspace","Delete","Del"]};document.addEventListener("keyup",e=>{if(this.disable)return;const o=Object.keys(t).find(o=>{const s=e.key,i=t[o];return i===s||Array.isArray(i)&&i.includes(s)});o&&setTimeout(()=>{this.$emit(o,{})},0)})},render:()=>{}}},mixins:[v,k],emits:["change","maskClick"],props:{mode:{type:String,default:"center"},duration:{type:[String,Number],default:300},zIndex:{type:[String,Number],default:997},bgColor:{type:String,default:"#ffffff"},safeArea:{type:Boolean,default:!0},overlay:{type:Boolean,default:!0},closeOnClickOverlay:{type:Boolean,default:!0},overlayOpacity:{type:[Number,String],default:.4},overlayStyle:{type:[Object,String],default:""},safeAreaInsetBottom:{type:Boolean,default:!0},safeAreaInsetTop:{type:Boolean,default:!1},closeable:{type:Boolean,default:!1},closeIconPos:{type:String,default:"top-right"},zoom:{type:Boolean,default:!0},round:{type:[Number,String],default:0},...null==(s=null==(o=uni.$uv)?void 0:o.props)?void 0:s.popup},watch:{type:{handler:function(t){this.config[t]&&this[this.config[t]](!0)},immediate:!0},isDesktop:{handler:function(t){this.config[t]&&this[this.config[this.mode]](!0)},immediate:!0},showPopup(t){document.getElementsByTagName("body")[0].style.overflow=t?"hidden":"visible"}},data(){return{ani:[],showPopup:!1,showTrans:!1,popupWidth:0,popupHeight:0,config:{top:"top",bottom:"bottom",center:"center",left:"left",right:"right",message:"top",dialog:"center",share:"bottom"},transitionStyle:{position:"fixed",left:0,right:0},maskShow:!0,mkclick:!0,popupClass:this.isDesktop?"fixforpc-top":"top",direction:""}},computed:{isDesktop(){return this.popupWidth>=500&&this.popupHeight>=500},bg(){return""===this.bgColor||"none"===this.bgColor||this.$uv.getPx(this.round)>0?"transparent":this.bgColor},contentStyle(){const t={};if(this.bgColor&&(t.backgroundColor=this.bg),this.round){const e=this.$uv.addUnit(this.round),o=this.direction?this.direction:this.mode;t.backgroundColor=this.bgColor,"top"===o?(t.borderBottomLeftRadius=e,t.borderBottomRightRadius=e):"bottom"===o?(t.borderTopLeftRadius=e,t.borderTopRightRadius=e):"center"===o&&(t.borderRadius=e)}return this.$uv.deepMerge(t,this.$uv.addStyle(this.customStyle))}},unmounted(){this.setH5Visible()},created(){this.messageChild=null,this.clearPropagation=!1},methods:{setH5Visible(){document.getElementsByTagName("body")[0].style.overflow="visible"},closeMask(){this.maskShow=!1},clear(t){t.stopPropagation(),this.clearPropagation=!0},open(t){if(this.showPopup)return;if(t&&-1!==["top","center","bottom","left","right","message","dialog","share"].indexOf(t)?this.direction=t:t=this.mode,!this.config[t])return this.$uv.error(`缺少类型：${t}`);this[this.config[t]](),this.$emit("change",{show:!0,type:t})},close(t){this.showTrans=!1,this.$emit("change",{show:!1,type:this.mode}),clearTimeout(this.timer),this.timer=setTimeout(()=>{this.showPopup=!1},300)},touchstart(){this.clearPropagation=!1},onTap(){this.clearPropagation?this.clearPropagation=!1:(this.$emit("maskClick"),this.closeOnClickOverlay&&this.close())},top(t){this.popupClass=this.isDesktop?"fixforpc-top":"top",this.ani=["slide-top"],this.transitionStyle={position:"fixed",zIndex:this.zIndex,left:0,right:0,backgroundColor:this.bg},t||(this.showPopup=!0,this.showTrans=!0,this.$nextTick(()=>{this.messageChild&&"message"===this.mode&&this.messageChild.timerClose()}))},bottom(t){this.popupClass="bottom",this.ani=["slide-bottom"],this.transitionStyle={position:"fixed",zIndex:this.zIndex,left:0,right:0,bottom:0,backgroundColor:this.bg},t||(this.showPopup=!0,this.showTrans=!0)},center(t){this.popupClass="center",this.ani=this.zoom?["zoom-in","fade"]:["fade"],this.transitionStyle={position:"fixed",zIndex:this.zIndex,display:"flex",flexDirection:"column",bottom:0,left:0,right:0,top:0,justifyContent:"center",alignItems:"center"},t||(this.showPopup=!0,this.showTrans=!0)},left(t){this.popupClass="left",this.ani=["slide-left"],this.transitionStyle={position:"fixed",zIndex:this.zIndex,left:0,bottom:0,top:0,backgroundColor:this.bg,display:"flex",flexDirection:"column"},t||(this.showPopup=!0,this.showTrans=!0)},right(t){this.popupClass="right",this.ani=["slide-right"],this.transitionStyle={position:"fixed",zIndex:this.zIndex,bottom:0,right:0,top:0,backgroundColor:this.bg,display:"flex",flexDirection:"column"},t||(this.showPopup=!0,this.showTrans=!0)}}},[["render",function(t,e,o,s,b,v){const k=n(a("uv-overlay"),C),_=n(a("uv-status-bar"),x),I=n(a("uv-safe-bottom"),S),T=n(a("uv-icon"),w),z=m,$=n(a("uv-transition"),i),P=f("keypress");return b.showPopup?(l(),r(z,{key:0,class:c(["uv-popup",[b.popupClass,v.isDesktop?"fixforpc-z-index":""]]),style:d([{zIndex:o.zIndex}])},{default:p(()=>[g(z,{onTouchstart:v.touchstart},{default:p(()=>[b.maskShow&&o.overlay?(l(),r(k,{key:"1",show:b.showTrans,duration:o.duration,"custom-style":o.overlayStyle,opacity:o.overlayOpacity,zIndex:o.zIndex,onClick:v.onTap},null,8,["show","duration","custom-style","opacity","zIndex","onClick"])):y("",!0),g($,{key:"2",mode:b.ani,name:"content","custom-style":b.transitionStyle,duration:o.duration,show:b.showTrans,onClick:v.onTap},{default:p(()=>[g(z,{class:c(["uv-popup__content",[b.popupClass]]),style:d([v.contentStyle]),onClick:v.clear},{default:p(()=>[o.safeAreaInsetTop?(l(),r(_,{key:0})):y("",!0),u(t.$slots,"default",{},void 0,!0),o.safeAreaInsetBottom?(l(),r(I,{key:1})):y("",!0),o.closeable?(l(),r(z,{key:2,onClick:h(v.close,["stop"]),class:c(["uv-popup__content__close",["uv-popup__content__close--"+o.closeIconPos]]),"hover-class":"uv-popup__content__close--hover","hover-stay-time":"150"},{default:p(()=>[g(T,{name:"close",color:"#909399",size:"18",bold:""})]),_:1},8,["onClick","class"])):y("",!0)]),_:3},8,["style","class","onClick"])]),_:3},8,["mode","custom-style","duration","show","onClick"])]),_:3},8,["onTouchstart"]),b.maskShow?(l(),r(P,{key:0,onEsc:v.onTap},null,8,["onEsc"])):y("",!0)]),_:3},8,["class","style"])):y("",!0)}],["__scopeId","data-v-db11292f"]]);export{_};
