import{_ as e}from"./filter-popup.vue_vue_type_script_setup_true_lang.hyGhGyJf.js";import{_ as t}from"./empty.vue_vue_type_script_setup_true_lang.BXZPGwCi.js";import{_ as s}from"./promotionCodeInfo.BJy-mwMn.js";import{_ as a}from"./navbar.vue_vue_type_script_setup_true_lang.CSl8hyKC.js";import{_ as o}from"./uv-read-more.0DxKa8S8.js";import{d as l,u as r,r as i,D as u,m,E as n,J as p,c as d,e as v,w as _,F as c,i as j,a as f,b as g,o as k,h as x,v as y,x as b,z as T,g as h,t as D,j as F}from"./index-CIPK2z2P.js";import{_ as w}from"./uv-load-more.DsJkRw2i.js";import{_ as C}from"./uv-skeletons.D1UL33yi.js";import{c as I}from"./details.B83uqO-n.js";import{P as z}from"./system.DZjCJFRG.js";import{l as E}from"./config.CwGRXwA6.js";import{D as O}from"./decimal.B1oHnkff.js";import{_ as R}from"./uv-icon.UcuauzO0.js";import"./uv-datetime-picker.CJSFIHl6.js";import"./submitButton.Cin0VP_1.js";import"./uv-input.CdM7e6ra.js";import"./uv-radio-group.CHWPt9Cy.js";import"./uv-popup.ewhZSqs9.js";import"./uv-transition.tIadgx1N.js";import"./uv-status-bar.BkslDeIT.js";import"./debounce.Ce2HeGXN.js";import"./uv-picker.C6UOrbqb.js";import"./uv-loading-icon.Bi7ZFsTo.js";import"./uv-empty.BH_ZJrMJ.js";import"./uploadImag.CizL1aRL.js";import"./uv-text.DpB-FlH4.js";import"./uv-line.CaGHsg1_.js";const N=R(l({__name:"promotionRecord",setup(l){const{t:R}=r(),N=i(!0),P=i([]),S=i(!1),$=i(),J=i(),A=i(0),B=i(0),K=u({xm_id:"",page:1,limit:5,kssj:"",jssj:""}),M=(e,t)=>{e&&(N.value=!0,Object.assign(K,{kssj:t.kssj,jssj:t.jssj}),P.value=[],K.page=1,Q())},Q=async()=>{var e;try{const{data:t,count:s}=await I(K);if(1===K.page?($.value="",P.value=t):P.value=P.value.concat(t),A.value=s||0,N.value=!1,S.value=1===K.page&&!(null==(e=P.value)?void 0:e.length),A.value&&A.value<=O(K.page*K.limit).toNumber())return $.value="nomore"}catch(t){N.value=!1,S.value=!0}};return m(e=>{B.value=e.scrollTop}),n(()=>{if(A.value<=O(K.page*K.limit).toNumber())return $.value="nomore";K.page++,Q(),$.value="loading"}),p(e=>{(null==e?void 0:e.id)&&(K.xm_id=e.id,Q())}),(l,r)=>{const i=a,u=s,m=f(g("uv-read-more"),o),n=j,p=f(g("uv-load-more"),w),I=t,O=f(g("uv-skeletons"),C),A=e;return k(),d(c,null,[v(n,null,{default:_(()=>[v(i,{"bg-color":"transparent",title:x(R)("navTitle.applicationRecords"),scrollTextColor:"blackOne",isDarkMode:!0},null,8,["title"]),v(O,{loading:x(N),skeleton:x(E)},{default:_(()=>[v(n,{class:"box p-32"},{default:_(()=>[(k(!0),d(c,null,T(x(P),e=>(k(),y(n,{class:"bg-white p-32 rounded-30 mb-20",key:e},{default:_(()=>[3!==e.zt?(k(),y(n,{key:0},{default:_(()=>[v(m,{toggle:!1,textIndent:"0",closeText:`${x(R)("sys.expand")}`},{default:_(()=>[v(u,{item:e},null,8,["item"])]),_:2},1032,["closeText"]),e.u_tip?(k(),y(n,{key:0,class:"mt-40 w-622 p-28 bg-[#FFEDE7] rounded-20 text-orange text-26 leading-40"},{default:_(()=>[h(D(e.u_tip),1)]),_:2},1024)):b("",!0),e.zt===x(z).NOT_PASSED?(k(),y(n,{key:1,class:"mt-32 w-622 py-28 flex justify-center items-center leading-44 text-32 bg-[#F6F7F9] rounded-20 text-blueOne",onClick:t=>(e=>{F(`/pages/details/form?id=${e.xm_id}&detailId=${e.id}`)})(e)},{default:_(()=>[h(D(x(R)("sys.resubmit")),1)]),_:2},1032,["onClick"])):b("",!0)]),_:2},1024)):(k(),y(n,{key:1},{default:_(()=>[v(u,{item:e},null,8,["item"])]),_:2},1024))]),_:2},1024))),128)),x($)&&x(P).length?(k(),y(p,{key:0,status:x($),class:"p-32",loadingText:x(R)("sys.loading"),nomoreText:x(R)("sys.noData")},null,8,["status","loadingText","nomoreText"])):b("",!0)]),_:1}),x(S)?(k(),y(I,{key:0,class:"mt-[20%]"})):b("",!0)]),_:1},8,["loading","skeleton"])]),_:1}),v(A,{ref_key:"popupRef",ref:J,onCallback:M},null,512)],64)}}}),[["__scopeId","data-v-9d9b3235"]]);export{N as default};
