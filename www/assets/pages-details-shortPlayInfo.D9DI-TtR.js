import{d as e,u as t,r as a,c as s,e as o,w as l,a as n,b as r,o as i,i as u,g as c,t as d,h as f,v as p,a1 as m,F as v,z as _,am as b,H as y,f as k,x,s as g,D as h,J as w,n as C,a$ as j,aq as B,j as F}from"./index-CIPK2z2P.js";import{_ as I}from"./uv-popup.ewhZSqs9.js";import{_ as T}from"./detail-bottom.CMEFRwuj.js";import{_ as $}from"./uv-parse.wZPhbfSD.js";import{_ as M,b as R}from"./uv-icon.UcuauzO0.js";import{_ as O}from"./navbar.vue_vue_type_script_setup_true_lang.CSl8hyKC.js";import{_ as P}from"./uv-skeletons.D1UL33yi.js";import{a as S}from"./skeleton.BDseN9kT.js";import"./uv-transition.tIadgx1N.js";import"./uv-status-bar.BkslDeIT.js";import"./debounce.Ce2HeGXN.js";const D=e({__name:"synopsisPopup",props:{desc:{type:String,default:""}},setup(e,{expose:p}){const{t:m}=t(),v=a(),_=()=>{v.value.close()};return p({open:()=>{v.value.open("bottom")},close:()=>{_()}}),(t,a)=>{const p=u,b=n(r("uv-popup"),I);return i(),s("div",{class:"popup-bottom"},[o(b,{ref_key:"popupRef",ref:v,isDarkMode:!0,title:t.$t("sys.synopsis"),showClose:!0,closeable:"",overlayStyle:"background: rgba(0, 0, 0, 0.3);",round:"15",onMaskClick:_,onClose:_,showMask:!0,maskClosable:!0},{default:l(()=>[o(p,{class:"p-32 pt-20"},{default:l(()=>[o(p,{class:"synopsis-text",ref:"descRef"},{default:l(()=>[c(d(f(m)("sys.synopsis")),1)]),_:1},512),o(p,{class:"max-h-[60vh] overflow-auto my-30 text-blackTwo text-28"},{default:l(()=>[c(d(e.desc),1)]),_:1})]),_:1})]),_:1},8,["title"])])}}}),H=M(e({__name:"shortContent",props:{info:{type:Object,default:()=>({})}},setup(e){const{t:a}=t();return(t,b)=>{const y=u,k=n(r("uv-parse"),$);return i(),p(y,{class:"rounded-30 -mt-30 bg-[#F6F7F9] p-32"},{default:l(()=>[o(y,{class:"flex leading-40 text-32 text-black"},{default:l(()=>[c(d("2"===e.info.read_type?f(a)("details.taster"):f(a)("details.chapter")),1)]),_:1}),"2"===e.info.read_type?(i(),p(y,{key:0,class:"mt-24 text-28 leading-40 text-blackTwo env-bottom"},{default:l(()=>[o(k,{content:f(m)(e.info.nr)},null,8,["content"])]),_:1})):(i(),p(y,{key:1,class:"mt-24 text-28 leading-40 text-blackTwo env-bottom grid grid-cols-2 gap-18 custom-grid"},{default:l(()=>[(i(),s(v,null,_(9,(e,t)=>o(y,{key:e,onClick:e=>{},class:"w-334 h-88 rounded-20 bg-white text-center leading-88 text-blackTwo text-28"},{default:l(()=>[c(d(t),1)]),_:2},1032,["onClick"])),64))]),_:1}))]),_:1})}}}),[["__scopeId","data-v-8f54a980"]]),z=M(e({__name:"shortBar",props:{info:{type:Object,default:()=>({})}},emits:["callBack"],setup(e,{emit:m}){const{t:v}=t(),_=m,h=a(""),w=a(),C=a(0),j=()=>{_("callBack",h.value)};return b(async()=>{await y();const e=w.value&&w.value.$el?w.value.$el:w.value;if(e){const t=window.getComputedStyle(e),a=parseFloat(t.lineHeight)||29,s=e.scrollHeight;C.value=Math.ceil(s/a)}}),(t,a)=>{const m=O,_=k,b=u,y=n(r("uv-icon"),R);return i(),s("div",{class:"header"},[o(m,{"bg-color":"transparent",title:f(v)("details.tit"),leftIconColor:"#fff",titleColor:"#fff",scrollBgColor:"#fff",scrollTextColor:"blackOne",isDarkMode:!0},null,8,["title"]),o(b,{class:"px-32 pt-20 pb-74 flex box-border"},{default:l(()=>[o(_,{src:e.info.cover,mode:"aspectFill",class:"w-220 h-328 rounded-20 mr-20"},null,8,["src"]),o(b,null,{default:l(()=>[o(b,{class:"font-semibold text-32 text-white line-clamp-2 leading-43 w-418"},{default:l(()=>[c(d(e.info.name),1)]),_:1}),o(b,{ref_key:"descRef",ref:w,class:g(["w-418 text-24 leading-29 mt-20 text-[#C4C3C0]",f(C)>7?"h-178, line-clamp-6":"h-auto"])},{default:l(()=>[c(d(e.info.intro),1)]),_:1},8,["class"]),f(C)>6?(i(),p(b,{key:0,onClick:j,class:"flex items-center text-white text-24 leading-30 mt-12"},{default:l(()=>[c(d(f(v)("sys.more"))+" ",1),o(y,{color:"#fff",size:"24rpx",class:"mt-2",name:"arrow-right"})]),_:1})):x("",!0)]),_:1})]),_:1})])}}}),[["__scopeId","data-v-1fbfb74a"]]),J=M(e({__name:"shortPlayInfo",setup(e){const c=a(),{t:d}=t(),p=a(""),m=a(!0),_=h({}),b=a(""),k=a(!1),x=a(0),g=e=>{_.intro=e,y(()=>{var e;null==(e=c.value)||e.open()})},I=()=>{if(k.value)return B("select:shortPlayInfo",{id:_.id,index:x.value}),void F("","back");F(`/pages/details/form?id=${b.value}&nr_id=${p.value}`)};return w(e=>{"undefined"!=typeof document&&(document.body.style.overflow=""),"select"===(null==e?void 0:e.type)?(x.value=e.index,k.value=!0):k.value=!1,(null==e?void 0:e.id)&&(b.value=e.xm_id,p.value=e.id,(async()=>{try{const{data:e}=await j(p.value);Object.assign(_,e),m.value=!1}catch(e){}})())}),C(()=>{"undefined"!=typeof document&&(document.body.style.overflow="")}),(e,t)=>{const a=z,p=H,b=T,y=n(r("uv-skeletons"),P),k=u,x=D;return i(),s(v,null,[o(k,{class:"warp"},{default:l(()=>[o(y,{loading:f(m),skeleton:f(S)},{default:l(()=>[o(a,{onCallBack:g,info:f(_)},null,8,["info"]),o(p,{info:f(_)},null,8,["info"]),o(b,{info:[{name:f(d)("details.promoteBtn")}],onBack:I},null,8,["info"])]),_:1},8,["loading","skeleton"])]),_:1}),o(x,{desc:f(_).intro,ref_key:"popupRef",ref:c},null,8,["desc"])],64)}}}),[["__scopeId","data-v-27fab680"]]);export{J as default};
