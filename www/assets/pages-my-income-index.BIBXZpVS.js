import{a as e,b as t,c as a,o as l,e as s,w as n,i,f as c,g as o,d as r,u as d,r as u,h as f,t as m,F as A,z as x,j as g}from"./index-BBirLt11.js";import{_ as b,b as p}from"./uv-icon.Dp0oPivN.js";import{_}from"./navbar.vue_vue_type_script_setup_true_lang.DC1OHhyC.js";import{B as h}from"./system.DZjCJFRG.js";import"./uv-status-bar.CB0-bOg6.js";import"./debounce.Ce2HeGXN.js";const w=b({},[["render",function(r,d){const u=c,f=i,m=e(t("uv-icon"),p);return l(),a("div",null,[s(f,{class:"flex justify-between"},{default:n(()=>[s(f,{class:"flex items-center max-w-[70%]"},{default:n(()=>[s(u,{src:"/assets/banner-B0kdvb3x.png",class:"w-48 h-48 rounded-10 mr-16",mode:"aspectFill"}),s(f,{class:"text-blackOne flex-1 text-28 leading-40 line-clamp-2"},{default:n(()=>[o(" Bilibi Shuqitehui ")]),_:1})]),_:1}),s(f,{class:"flex text-blackOne font-din text-32 leading-32 items-center"},{default:n(()=>[o(" $1,001.50 "),s(m,{name:"arrow-right",color:"#9BA7B5",size:"28rpx"})]),_:1})]),_:1})])}]]),B=r({__name:"index",setup(r){const{t:b}=d(),B=u("0"),k=()=>{B.value=B.value===h.IS_FALSE?h.IS_TRUE:h.IS_FALSE},j=()=>{g("/pages/my/income/details")};return(r,d)=>{const u=_,g=i,S=c,y=e(t("uv-icon"),p),C=w;return l(),a("div",null,[s(u,{"bg-color":"transparent",title:f(b)("navTitle.income"),scrollTextColor:"blackOne",isDarkMode:!0},null,8,["title"]),s(g,{class:"p-32"},{default:n(()=>[s(g,{class:"text-32 leading-44 text-blackOne font-semibold"},{default:n(()=>[o(m(f(b)("income.income")),1)]),_:1}),s(g,{class:"bg-white rounded-30 p-32 mt-24 flex justify-between"},{default:n(()=>[s(g,{class:"flex-1 pr-20"},{default:n(()=>[s(g,{class:"text-blackTwo text-24 leading-32 mb-12"},{default:n(()=>[o(m(f(b)("income.cumulativeGain"))+" ($)",1)]),_:1}),s(g,{class:"font-din text-60 leading-68"},{default:n(()=>[o(" 50,232.32 ")]),_:1})]),_:1}),s(g,{class:"max-w-[30%] flex flex-col items-end justify-between"},{default:n(()=>[s(g,{class:"flex",onClick:k},{default:n(()=>[s(S,{class:"w-32 h-32",src:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAX1JREFUWEftlj9PwlAUxc+tYJhLIo4OJSHpAhMLgx/Fpc4O+ilgYJbFj+LA4gSLCQkdXDGhs0lLr3mvQZL+ea34ShtD17737q/nnHtfCRU/VHF9nAH+qQIO2zJbM3rPy5h+BRy2CViKwkwY4ZneVBBlAjQYWMHHAC/0lQWhH0BUcnhMwKNUAZhgRk+nBbjjFppYENADEKisKEcB8bn3PCTGHIDSinQAkWKGlZfgAu8fiHCrsiIJcEhxo0CB3ywJGOjHW7OGAFGKdVlwUIjgpg2m8kJY0Jx6ApimZYfNCx1d8KOD4e9cz3MTd0NCAfPashEaSyJo7QJmBAjCfhyifgBCM/OqOyYiOcsBft2BpwUzlbnM8FHMguiEm1a7c7kA0BPSEfFou1krr9VjATO7oN3pDplpHmWBV9uNPwA+Mq9V7QBxK5h54n2uM6/VUgDiVqSl+NjC+325g2hvhdyQ0kalA0grTEv+ZKYNkpMA/LWIan+uBWUWF2efASpX4BuPC5QhhciblgAAAABJRU5ErkJggg==",mode:"aspectFill"}),s(g,{class:"text-24 leading-32 text-blackTwo ml-4"},{default:n(()=>[o(m(f(B)===f(h).IS_FALSE?"7":"30")+m(f(b)("sys.days")),1)]),_:1})]),_:1}),s(g,{class:"text-32 leading-56 text-orange font-din"},{default:n(()=>[o(" +205.32 ")]),_:1})]),_:1})]),_:1}),s(g,{class:"title mt-32 flex items-center justify-between mb-24"},{default:n(()=>[s(g,{class:"max-w-500 line-clamp-1 text-blackOne font-semibold text-32 leading-44"},{default:n(()=>[o(m(f(b)("income.totalEarnings")),1)]),_:1}),s(g,{class:"flex text-26 leading-48 text-blackTwo items-center"},{default:n(()=>[o(m(f(b)("income.timeSlot"))+" ",1),s(y,{name:"arrow-right",color:"#67707D",size:"28rpx"})]),_:1})]),_:1}),s(g,{class:"px-32 bg-white rounded-16"},{default:n(()=>[(l(),a(A,null,x(13,e=>s(g,{class:"py-32 border-b border-dashed last:border-b-0",key:e},{default:n(()=>[s(C,{item:e,onClick:j},null,8,["item"])]),_:2},1024)),64))]),_:1})]),_:1})])}}});export{B as default};
