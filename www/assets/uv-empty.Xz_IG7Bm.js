var t,e;import{_ as i,m as s,a,b as o}from"./uv-icon.Dp0oPivN.js";import{a as n,b as r,v as d,x as l,o as u,w as c,e as m,f as p,q as y,U as f,g as h,t as g,i as S,A as v}from"./index-BBirLt11.js";const x=i({name:"uv-empty",mixins:[s,a,{props:{icon:{type:String,default:""},text:{type:String,default:""},textColor:{type:String,default:"#c0c4cc"},textSize:{type:[String,Number],default:14},iconColor:{type:String,default:"#c0c4cc"},iconSize:{type:[String,Number],default:90},mode:{type:String,default:"data"},width:{type:[String,Number],default:160},height:{type:[String,Number],default:160},show:{type:<PERSON>olean,default:!0},marginTop:{type:[String,Number],default:0},...null==(e=null==(t=uni.$uv)?void 0:t.props)?void 0:e.empty}}],data:()=>({icons:{car:"购物车为空",page:"页面不存在",search:"没有搜索结果",address:"没有收货地址","wifi-off":"没有WiFi",order:"订单为空",coupon:"没有优惠券",favor:"暂无收藏",permission:"无权限",history:"无历史记录",news:"无新闻列表",message:"消息列表为空",list:"列表为空",data:"数据为空",comment:"暂无评论"}}),computed:{emptyStyle(){const t={};return t.marginTop=this.$uv.addUnit(this.marginTop),this.$uv.deepMerge(t,this.$uv.addStyle(this.customStyle))},textStyle(){const t={};return t.color=this.textColor,t.fontSize=this.$uv.addUnit(this.textSize),t},isImg(){const t=this.icon.indexOf("data:")>-1&&this.icon.indexOf("base64")>-1;return-1!==this.icon.indexOf("/")||t}}},[["render",function(t,e,i,s,a,x){const _=n(r("uv-icon"),o),w=p,$=f,b=S;return t.show?(u(),d(b,{key:0,class:"uv-empty",style:y([x.emptyStyle])},{default:c(()=>[x.isImg?(u(),d(w,{key:1,style:y({width:t.$uv.addUnit(t.width),height:t.$uv.addUnit(t.height)}),src:t.icon,mode:"widthFix"},null,8,["style","src"])):(u(),d(_,{key:0,name:"message"===t.mode?"chat":`empty-${t.mode}`,size:t.iconSize,color:t.iconColor,"margin-top":"14"},null,8,["name","size","color"])),m($,{class:"uv-empty__text",style:y([x.textStyle])},{default:c(()=>[h(g(t.text?t.text:a.icons[t.mode]),1)]),_:1},8,["style"]),m(b,{class:"uv-empty__wrap"},{default:c(()=>[v(t.$slots,"default",{},void 0,!0)]),_:3})]),_:3},8,["style"])):l("",!0)}],["__scopeId","data-v-d585060f"]]);export{x as _};
