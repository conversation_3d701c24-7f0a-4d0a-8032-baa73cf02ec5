var e,t,n,s;import{v as i,o as l,w as a,g as o,t as r,q as u,ab as d,U as p,a as c,b as f,x as y,s as m,i as h,e as g,aR as v,aS as S}from"./index-BBirLt11.js";import{_ as x,m as k,a as b,f as _,p as $,d as N,e as I,t as w,b as C}from"./uv-icon.Dp0oPivN.js";const z=x({name:"uv-link",emits:["click"],mixins:[k,b,{props:{color:{type:String,default:""},fontSize:{type:[String,Number],default:14},underLine:{type:Boolean,default:!1},href:{type:String,default:""},mpTips:{type:String,default:"链接已复制，请在浏览器打开"},lineColor:{type:String,default:""},text:{type:String,default:""},...null==(t=null==(e=uni.$uv)?void 0:e.props)?void 0:t.link}}],computed:{linkStyle(){return{color:this.color,fontSize:this.$uv.addUnit(this.fontSize),lineHeight:this.$uv.addUnit(this.$uv.getPx(this.fontSize)+2),textDecoration:this.underLine?"underline":"none"}}},methods:{openLink(){window.open(this.href),this.$emit("click")}}},[["render",function(e,t,n,s,c,f){const y=p;return l(),i(y,{class:"uv-link",onClick:d(f.openLink,["stop"]),style:u([f.linkStyle,e.$uv.addStyle(e.customStyle)])},{default:a(()=>[o(r(e.text),1)]),_:1},8,["onClick","style"])}],["__scopeId","data-v-3420edd1"]]);const L=x({name:"uv-text",emits:["click"],mixins:[k,b,{computed:{value(){const{text:e,mode:t,format:n,href:s}=this;return"price"===t?_(n)?n(e):$(e,2):"date"===t?(!N(e)&&I(),_(n)?n(e):w(e,n||"yyyy-mm-dd")):"phone"===t?_(n)?n(e):"encrypt"===n?`${e.substr(0,3)}****${e.substr(7)}`:e:"name"===t?_(n)?n(e):"encrypt"===n?this.formatName(e):e:e}},methods:{formatName(e){let t="";if(2===e.length)t=e.substr(0,1)+"*";else if(e.length>2){let n="";for(let t=0,s=e.length-2;t<s;t++)n+="*";t=e.substr(0,1)+n+e.substr(-1,1)}else t=e;return t}}},{props:{type:{type:String,default:""},show:{type:Boolean,default:!0},text:{type:[String,Number],default:""},prefixIcon:{type:String,default:""},suffixIcon:{type:String,default:""},mode:{type:String,default:""},href:{type:String,default:""},format:{type:[String,Function],default:""},call:{type:Boolean,default:!0},openType:{type:String,default:""},bold:{type:Boolean,default:!1},block:{type:Boolean,default:!1},lines:{type:[String,Number],default:""},color:{type:String,default:"#303133"},size:{type:[String,Number],default:15},iconStyle:{type:[Object,String],default:()=>({fontSize:"15px"})},decoration:{type:String,default:"none"},margin:{type:[Object,String,Number],default:0},lineHeight:{type:[String,Number],default:""},align:{type:String,default:"left"},wordWrap:{type:String,default:"normal"},...null==(s=null==(n=uni.$uv)?void 0:n.props)?void 0:s.text}}],computed:{valueStyle(){const e={textDecoration:this.decoration,fontWeight:this.bold?"bold":"normal",wordWrap:this.wordWrap,fontSize:this.$uv.addUnit(this.size)};return!this.type&&(e.color=this.color),this.isNvue&&this.lines&&(e.lines=this.lines),!this.isNvue||"price"==this.mode||this.prefixIcon||this.suffixIcon||(e.flex=1,e.textAlign="left"===this.align?"flex-start":"center"===this.align?"center":"right"),this.lineHeight&&(e.lineHeight=this.$uv.addUnit(this.lineHeight)),!this.isNvue&&this.block&&(e.display="block"),this.$uv.deepMerge(e,this.$uv.addStyle(this.customStyle))},isNvue:()=>!1,isMp:()=>!1},data:()=>({}),methods:{clickHandler(){this.call&&"phone"===this.mode&&S({phoneNumber:this.text}),this.$emit("click")}}},[["render",function(e,t,n,s,d,S){const x=p,k=c(f("uv-icon"),C),b=h,_=c(f("uv-link"),z),$=v;return e.show?(l(),i(b,{key:0,class:m(["uv-text",[]]),style:u({margin:e.margin,justifyContent:"left"===e.align?"flex-start":"center"===e.align?"center":"flex-end"}),onClick:S.clickHandler},{default:a(()=>["price"===e.mode?(l(),i(x,{key:0,class:m(["uv-text__price",e.type&&`uv-text__value--${e.type}`]),style:u([S.valueStyle])},{default:a(()=>[o("￥")]),_:1},8,["class","style"])):y("",!0),e.prefixIcon?(l(),i(b,{key:1,class:"uv-text__prefix-icon"},{default:a(()=>[g(k,{name:e.prefixIcon,customStyle:e.$uv.addStyle(e.iconStyle)},null,8,["name","customStyle"])]),_:1})):y("",!0),"link"===e.mode?(l(),i(_,{key:2,text:e.value,href:e.href,underLine:""},null,8,["text","href"])):e.openType&&S.isMp?(l(),i($,{key:3,class:"uv-reset-button uv-text__value",style:u([S.valueStyle]),openType:e.openType,onGetuserinfo:e.onGetUserInfo,onContact:e.onContact,onGetphonenumber:e.onGetPhoneNumber,onError:e.onError,onLaunchapp:e.onLaunchApp,onOpensetting:e.onOpenSetting,lang:e.lang,"session-from":e.sessionFrom,"send-message-title":e.sendMessageTitle,"send-message-path":e.sendMessagePath,"send-message-img":e.sendMessageImg,"show-message-card":e.showMessageCard,"app-parameter":e.appParameter},{default:a(()=>[o(r(e.value),1)]),_:1},8,["style","openType","onGetuserinfo","onContact","onGetphonenumber","onError","onLaunchapp","onOpensetting","lang","session-from","send-message-title","send-message-path","send-message-img","show-message-card","app-parameter"])):(l(),i(x,{key:4,class:m(["uv-text__value",[e.type&&`uv-text__value--${e.type}`,e.lines&&`uv-line-${e.lines}`]]),style:u([S.valueStyle])},{default:a(()=>[o(r(e.value),1)]),_:1},8,["style","class"])),e.suffixIcon?(l(),i(b,{key:5,class:"uv-text__suffix-icon"},{default:a(()=>[g(k,{name:e.suffixIcon,customStyle:e.$uv.addStyle(e.iconStyle)},null,8,["name","customStyle"])]),_:1})):y("",!0)]),_:1},8,["style","onClick"])):y("",!0)}],["__scopeId","data-v-6fde0f74"]]);export{L as _};
