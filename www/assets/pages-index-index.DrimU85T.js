import{d as e,u as t,r as a,a as n,b as r,c as i,o as s,e as l,w as o,i as c,f as u,g as p,t as d,h as m,j as g,k as f,l as v,m as b,n as w,p as y,F as h,q as x,s as B,v as C,x as A,y as k,z as _,S as L,A as j,B as S,C as F,D as T,E as I,G as R,H as K,$ as M,I as U,J as D,K as Q,L as N,M as P}from"./index-CIPK2z2P.js";import{_ as q}from"./uv-popup.ewhZSqs9.js";import{_ as J}from"./uv-icon.UcuauzO0.js";import{_ as Z}from"./set-language.BfCwdxCn.js";import{_ as O,S as V}from"./config.DrO4s-Uw.js";import{_ as H}from"./prompt-popup.vue_vue_type_script_setup_true_lang.CHEY8D5W.js";import{_ as E}from"./search.CpVgtt1a.js";import{_ as G}from"./uv-skeletons.D1UL33yi.js";import{D as Y}from"./decimal.B1oHnkff.js";import"./uv-transition.tIadgx1N.js";import"./uv-status-bar.BkslDeIT.js";import"./debounce.Ce2HeGXN.js";import"./empty.vue_vue_type_script_setup_true_lang.BXZPGwCi.js";import"./uv-empty.BH_ZJrMJ.js";import"./uv-load-more.DsJkRw2i.js";import"./uv-line.CaGHsg1_.js";import"./uv-loading-icon.Bi7ZFsTo.js";import"./uv-parse.wZPhbfSD.js";const z=J(e({__name:"improve-popup",setup(e,{expose:f}){const{t:v}=t(),b=a();function w(){var e;null==(e=b.value)||e.close()}const y=()=>{g("/pages/my/improve/country")};return f({open:()=>{var e;null==(e=b.value)||e.open()},close:()=>{var e;null==(e=b.value)||e.close()}}),(e,t)=>{const a=u,g=c,f=n(r("uv-popup"),q);return s(),i("div",null,[l(f,{ref_key:"popup",ref:b,mode:"center",overlayStyle:"background: rgba(0, 0, 0, 0.3);",round:"30rpx","z-index":"1000",closeOnClickOverlay:!1},{default:o(()=>[l(g,{class:"relative bg-transparent pt-44"},{default:o(()=>[l(a,{class:"absolute top-0 left-[50%] translate-x-[-50%] w-264 h-200",src:"/assets/improve-DqnXOald.png",mode:"aspectFill"}),l(g,{class:"w-640 p-48 rounded-30",style:{background:"linear-gradient(180deg, #d5e6ff 0%, #fff 100%)"}},{default:o(()=>[l(g,{class:"mt-136 text-36 font-semibold text-center text-blackOne leading-48"},{default:o(()=>[p(" Complete the information to get internally - recommended projects. ")]),_:1}),l(g,{onClick:y,class:"py-28 bg-blueOne text-32 leading-44 text-white text-center rounded-20 mt-48"},{default:o(()=>[p(d(m(v)("index.goNow")),1)]),_:1}),l(g,{onClick:w,class:"py-28 bg-white text-32 leading-44 text-blackTwo text-center rounded-20 mt-20"},{default:o(()=>[p(d(m(v)("index.later")),1)]),_:1})]),_:1})]),_:1})]),_:1},512)])}}}),[["__scopeId","data-v-6dde80b0"]]),X=J(e({__name:"homeBar",props:{count:{type:Number,default:0}},setup(e){const{t:n}=t(),r=a(!0),_=f(),L=a(!1),j=a(0),S=v(),F=a(S.statusBarHeight||0),T=a(),I=()=>{g("/pages/search/index")},R=()=>{_.token?g("/pages/my/notice/index"):T.value.open()};b(e=>{j.value=e.scrollTop,r.value&&(e.scrollTop>0?L.value=!0:L.value=!1)});const K=()=>{k(),T.value.close(),g("/pages/login/index","reLaunch")};return w(()=>{r.value=!0}),y(()=>{r.value=!1}),(t,a)=>{const r=c,g=u,f=H;return s(),i(h,null,[l(r,{class:"sticky top-0 z-50",id:"homeBar"},{default:o(()=>[l(r,{class:B(["status-bar",m(L)||m(j)?"!bg-white":"!bg-transparent"]),style:x({height:m(F)+"px"})},null,8,["class","style"]),l(r,{class:B(["h-88 pl-32 pr-32 flex items-center justify-between",m(L)||m(j)?"!bg-white":"!bg-transparent"])},{default:o(()=>[l(r,{class:"text-blackOne text-40 font-semibold"},{default:o(()=>[p(d(m(n)("tab.home")),1)]),_:1}),l(r,{class:"flex"},{default:o(()=>[l(g,{class:"w-72 h-72",onClick:I,src:E,mode:"aspectFill"}),l(r,{class:"relative flex items-center"},{default:o(()=>[l(g,{class:B(["w-72 h-72",{"horn-shake":!!e.count}]),onClick:R,src:"data:image/png;base64,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",mode:"aspectFill"},null,8,["class"]),e.count?(s(),C(r,{key:0,class:"absolute right-10 top-10 w-10 h-10 leading-10 rounded-10 bg-red-500 text-22 font-semibold"})):A("",!0)]),_:1})]),_:1})]),_:1},8,["class"])]),_:1}),l(f,{ref_key:"warningRef",ref:T,title:m(n)("sys.pleaseLogin"),content:m(n)("sys.loginTip"),showIcon:!1,tipType:"login",iconName:"warning-fill",iconColor:"#FF6B35",showCancel:!1,confirmText:m(n)("sys.goLogin"),onConfirm:K},null,8,["title","content","confirmText"])],64)}}}),[["__scopeId","data-v-52d9427c"]]);const W=J({name:"kong-swiper",props:{indicatorDots:{type:Boolean,default:!1},indicatorColor:{type:String,default:"rgba(0, 0, 0, .3)"},indicatorActiveColor:{type:String,default:"#000000"},activeClass:String,changingClass:String,autoplay:{type:Boolean,default:!1},current:{type:Number,default:0},currentItemId:String,interval:{type:Number,default:3e3},duration:{type:Number,default:1e3},circular:{type:Boolean,default:!1},vertical:{type:Boolean,default:!1},previousMargin:{type:String,default:"0px"},nextMargin:{type:String,default:"0px"},acceleration:{type:Boolean,default:!1},disableProgrammaticAnimation:{type:Boolean,default:!1},displayMultipleItems:{type:Number,default:1},skipHiddenItemLayout:{type:Boolean,default:!1},disableTouch:{type:Boolean,default:!1},touchable:{type:Boolean,default:!0},easingFunction:{type:String,default:"default"},spaceBetween:{type:String,default:"0px"},borderRadius:{type:String,default:"0px"},swiperList:Array,scale:{type:[Number,String],default:1}},data:()=>({swiperCurrent:0}),computed:{listLen(){return this.swiperList.length-1}},watch:{current:{handler(e){this.swiperCurrent=e},immediate:!0}},methods:{swiperChange(e){this.$emit("change",e),this.swiperCurrent=e.detail.current},swiperTransition(e){this.$emit("transition",e)},swiperAnimationfinish(e){this.$emit("animationfinish",e)},swiperClick(e){const t=e.currentTarget.dataset.index;this.$emit("click",t)}}},[["render",function(e,t,a,n,r,p){const d=u,m=c,g=L,f=S;return s(),C(f,{class:"swiper","indicator-dots":a.indicatorDots,"indicator-color":a.indicatorColor,"indicator-active-color":a.indicatorActiveColor,"active-class":a.activeClass,"changing-class":a.changingClass,autoplay:a.autoplay,current:a.current,"current-item-id":a.currentItemId,interval:a.interval,duration:a.duration,circular:a.circular,vertical:a.vertical,"previous-margin":a.previousMargin,"next-margin":a.nextMargin,acceleration:a.acceleration,"disable-programmatic-animation":a.disableProgrammaticAnimation,"display-multiple-items":a.displayMultipleItems,"skip-hidden-item-layout":a.skipHiddenItemLayout,"disable-touch":a.disableTouch,touchable:a.touchable,"easing-function":a.easingFunction,onChange:p.swiperChange,onTransition:p.swiperTransition,onAnimationfinish:p.swiperAnimationfinish},{default:o(()=>[(s(!0),i(h,null,_(a.swiperList,(t,n)=>(s(),C(g,{key:n},{default:o(()=>[l(m,{class:"swiper_item",style:x({transitionDuration:a.duration/2e3+"s",padding:0==r.swiperCurrent?n==p.listLen?a.vertical?"0 0 "+a.spaceBetween:"0 "+a.spaceBetween+" 0 0 ":n==r.swiperCurrent+1?a.vertical?a.spaceBetween+" 0 0":"0 0 0 "+a.spaceBetween:"0":r.swiperCurrent==p.listLen?0==n?a.vertical?a.spaceBetween+" 0 0":"0 0 0 "+a.spaceBetween:n==r.swiperCurrent-1?a.vertical?"0 0 "+a.spaceBetween:"0 "+a.spaceBetween+" 0 0 ":"0":n==r.swiperCurrent+1?a.vertical?a.spaceBetween+" 0 0":"0 0 0 "+a.spaceBetween:n==r.swiperCurrent-1?a.vertical?"0 0 "+a.spaceBetween:"0 "+a.spaceBetween+" 0 0 ":"0"}),"data-index":n,onClick:p.swiperClick},{default:o(()=>[l(m,{class:"swiper_item_wrapper",style:x({transform:"scale("+(n==r.swiperCurrent?1:a.scale)+")",borderRadius:a.borderRadius,transitionDuration:a.duration/2e3+"s",transformOrigin:0==r.swiperCurrent?n==p.listLen?a.vertical?"center bottom":"right center":n==r.swiperCurrent+1?a.vertical?"center top":"left center":"center center":r.swiperCurrent==p.listLen?0==n?a.vertical?"center top":"left center":n==r.swiperCurrent-1?a.vertical?"center bottom":"right center":"center center":n==r.swiperCurrent+1?a.vertical?"center top":"left center":n==r.swiperCurrent-1?a.vertical?"center bottom":"right center":"center center"})},{default:o(()=>[j(e.$slots,"default",{swiperItem:t,swiperCurrent:n},()=>[l(d,{class:"image",src:t.image,mode:"aspectFill"},null,8,["src"])],!0)]),_:2},1032,["style"])]),_:2},1032,["style","data-index","onClick"])]),_:2},1024))),128))]),_:3},8,["indicator-dots","indicator-color","indicator-active-color","active-class","changing-class","autoplay","current","current-item-id","interval","duration","circular","vertical","previous-margin","next-margin","acceleration","disable-programmatic-animation","display-multiple-items","skip-hidden-item-layout","disable-touch","touchable","easing-function","onChange","onTransition","onAnimationfinish"])}],["__scopeId","data-v-558f06bb"]]),$=J(e({__name:"homeSwiper",props:{banner:{type:Array,default:()=>[]}},setup(e){const{t:n}=t(),r=f(),p=a(),d=a(0),v=()=>{p.value.close(),g("/pages/login/index","reLaunch")};return(t,a)=>{const g=u,f=W,b=c,w=H;return s(),i(h,null,[l(b,{class:"mb-32 w-750 mt-20"},{default:o(()=>[l(f,{class:"swiper",swiperList:e.banner,"next-margin":"36rpx","previous-margin":"36rpx","space-between":"15rpx",scale:"0.8",autoplay:"",circular:"",current:m(d)},{default:o(({swiperItem:e})=>[l(g,{class:"img",src:e.image,onClick:t=>(e=>{"1"===e.visitor_ok||r.token?F(e.link_url,"2"!==e.target?1:0):p.value.open()})(e),mode:"aspectFill"},null,8,["src","onClick"])]),_:1},8,["swiperList","current"])]),_:1}),l(w,{ref_key:"warningRef",ref:p,title:m(n)("sys.pleaseLogin"),content:m(n)("sys.loginTip"),showIcon:!1,iconName:"warning-fill",iconColor:"#FF6B35",showCancel:!1,confirmText:m(n)("sys.goLogin"),onConfirm:v},null,8,["title","content","confirmText"])],64)}}}),[["__scopeId","data-v-dfd09d35"]]),ee=e({__name:"index",setup(e){const{t:u}=t(),p=V,d=a(),g=a(),v=a(!0),b=a(""),y=T({page:1,limit:20}),x=a(0),B=a([]),C=a([]),A=a(0);I(()=>{Y(y.page*y.limit).toNumber()<x.value?(b.value="loading",y.page+=1,k()):b.value="noMore"});const k=async()=>{try{1===y.page&&(B.value=[],v.value=!0);const{data:e,count:t}=await R(y);1===y.page?B.value=e??[]:B.value=B.value.concat(e??[]),Y(y.page*y.limit).toNumber()>=x.value?b.value="noMore":b.value="",v.value=!1,x.value=t??0,K(()=>{})}catch(e){v.value=!1}};return w(()=>{M("page:show"),U("page:show",()=>{y.page=1,k()}),f().token&&(async()=>{try{const{data:e}=await Q();A.value=e.some(e=>e.count)?1:0}catch(e){}})()}),D(()=>{v.value=!1,y.page=1,k(),(async()=>{var e;try{const{data:t}=await N("mk_index_banner");C.value=(null==(e=null==t?void 0:t.mk_index_banner)?void 0:e.data)??[]}catch(t){}})()}),P({index:0,text:u("tab.home")}),P({index:1,text:u("tab.team")}),P({index:2,text:u("tab.account")}),(e,t)=>{const a=X,u=O,f=c,w=n(r("uv-skeletons"),G),y=Z,x=z;return s(),i(h,null,[l(a,{count:m(A)},null,8,["count"]),l($,{banner:m(C)},null,8,["banner"]),l(w,{loading:m(v),skeleton:m(p)},{default:o(()=>[l(u,{list:m(B),loading:m(v),status:m(b)},null,8,["list","loading","status"]),l(f,{ref:"observerTarget",id:"scroll-observer-target"},null,512)]),_:1},8,["loading","skeleton"]),l(y,{ref_key:"setLanguageRef",ref:d},null,512),l(x,{ref_key:"improvePopupRef",ref:g},null,512)],64)}}});export{ee as default};
