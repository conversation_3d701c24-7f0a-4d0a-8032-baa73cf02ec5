import{_ as e}from"./detail-bottom.DXyPlEgb.js";import{_ as a}from"./empty.vue_vue_type_script_setup_true_lang.SETk5GIM.js";import{_ as t}from"./media-item.vue_vue_type_script_setup_true_lang.CFbV5CFx.js";import{_ as l}from"./navbar.vue_vue_type_script_setup_true_lang.DC1OHhyC.js";import{d as s,u as o,r as i,D as u,m as n,E as r,J as m,c as d,e as v,h as c,w as p,a as _,b as f,o as g,v as b,x,i as y,g as j,t as k,s as h,a7 as T,F as w,z as F,j as S,ae as D,aq as M}from"./index-BBirLt11.js";import{_ as V}from"./uv-tabs.DzY9siFk.js";import{_ as $}from"./uv-sticky.SyKwVUE-.js";import{_ as B,a as C}from"./uv-radio-group.CF_hqWfL.js";import{_ as E}from"./uv-load-more.SNzw0348.js";import{_ as I}from"./uv-skeletons.BJIINbO6.js";import{m as O}from"./config.CwGRXwA6.js";import{g as W,a as q}from"./media.D7Io5yin.js";import{D as z}from"./decimal.B1oHnkff.js";import"./uv-icon.Dp0oPivN.js";import"./uv-empty.Xz_IG7Bm.js";import"./uv-status-bar.CB0-bOg6.js";import"./debounce.Ce2HeGXN.js";import"./uv-line.CNQanRFo.js";import"./uv-loading-icon.G79bokgG.js";const N=s({__name:"media",setup(s){const{t:N}=o(),P=i(!0),U=i(""),H=i({id:"",index:"",bs:""}),J=i(""),K=u({mtpt_id:"",page:1,limit:20,mtk_name:""}),Q=i([]),X=i(0),Y=i([]),A=i(""),G=i(0),L=i(""),R=i(0),Z=e=>{if("add"===e)S(`/pages/my/media/add?xm_id=${L.value}&bs=${H.value.bs}&index=${H.value.index}&rules=${K.mtk_name}`);else if("confirm"===e){if(!A.value)return void D(N("sys.pleaseSelect"));const e={id:A.value,bs:H.value.bs,index:H.value.index};M("selectMedia",e),S("","back",1)}},ee=async()=>{try{const{data:e,count:a}=await W(K);1===K.page?Y.value=e||[]:Y.value=Y.value.concat(e||[]),G.value=a||0,G.value&&z(K.page*K.limit).toNumber()>=G.value&&(U.value="noMore")}catch(e){}finally{P.value=!1}},ae=e=>{var a;P.value=!0,Y.value=[],X.value=e.index,K.mtpt_id=(null==(a=Q.value[X.value])?void 0:a.id)||"",K.page=1,ee()};n(e=>{R.value=e.scrollTop}),r(()=>{if(G.value<=z(K.page*K.limit).toNumber())return U.value="noMore";U.value="loading",K.page++,ee()});return m(e=>{e&&e.id&&e.rules&&(Object.assign(H.value,e),L.value=e.id,K.mtk_name=e.rules,J.value=e.rules,(async e=>{var a,t;try{const l=await q(e);l.data&&l.data.length?(Q.value=l.data.map(e=>({name:e.name,badge:{value:e.num||0},id:e.mtpt_id})),(null==(a=Q.value)?void 0:a.length)&&(K.mtpt_id=null==(t=Q.value[0])?void 0:t.id,ee())):(Q.value=[],P.value=!1)}catch(l){}})(e.rules),e.defaultId&&(A.value=e.defaultId))}),(s,o)=>{const i=l,u=y,n=_(f("uv-tabs"),V),r=_(f("uv-sticky"),$),m=t,S=_(f("uv-radio"),B),D=_(f("uv-radio-group"),C),M=a,W=_(f("uv-load-more"),E),q=e,z=_(f("uv-skeletons"),I);return g(),d("div",null,[v(i,{"bg-color":"transparent",title:c(N)("media.select"),scrollTextColor:"blackOne",isDarkMode:!0},null,8,["title"]),v(z,{loading:c(P),skeleton:c(O)},{default:p(()=>[v(r,null,{default:p(()=>[v(u,{class:"py-22 px-32 bg-[#FFEDE7] text-orange text-24 leading-34"},{default:p(()=>[v(u,null,{default:p(()=>[j(k(c(N)("media.tip1"))+"“"+k(c(J))+"”"+k(c(N)("media.tip2")),1)]),_:1})]),_:1}),c(Y).length?(g(),b(u,{key:0,class:h(["pb-20 pl-30",{"bg-white":c(R)>30}])},{default:p(()=>[v(n,{list:c(Q),current:c(X),activeStyle:{color:"#131726",fontWeight:"bold",fontSize:"28rpx"},lineColor:"#0165ff",inactiveStyle:{color:"#444B56"},lineWidth:"66rpx",onClick:ae},null,8,["list","current"])]),_:1},8,["class"])):x("",!0)]),_:1}),v(u,{class:"px-32 env-bottom"},{default:p(()=>[v(u,{class:"mt-32"},{default:p(()=>[v(D,{modelValue:c(A),"onUpdate:modelValue":o[0]||(o[0]=e=>T(A)?A.value=e:null),placement:"column",iconPlacement:"right",customStyle:{margin:"0"}},{default:p(()=>[(g(!0),d(w,null,F(c(Y),e=>(g(),b(u,{key:e.id,class:"p-32 bg-white rounded-20 mb-20 last:mb-0"},{default:p(()=>[v(S,{name:e.id,class:"flex items-center mb-20"},{default:p(()=>[v(u,{class:"flex box-border"},{default:p(()=>[v(m,{item:e,select:!0},null,8,["item"])]),_:2},1024)]),_:2},1032,["name"])]),_:2},1024))),128))]),_:1},8,["modelValue"]),c(Y).length?x("",!0):(g(),b(M,{key:0,class:"mt-[20%]"}))]),_:1}),c(Y).length&&c(K).page>1?(g(),b(W,{key:0,status:c(U),class:"p-32",loadingText:c(N)("sys.loading"),nomoreText:c(N)("sys.noData")},null,8,["status","loadingText","nomoreText"])):x("",!0)]),_:1}),v(q,{info:[{name:c(N)("media.fillInByHand"),color:"white",bs:"add"},{name:c(N)("sys.confirm"),bs:"confirm"}],bg:"bg-[#F6F7F9]","left-text-color":"blackOne",onBack:Z},null,8,["info"])]),_:1},8,["loading","skeleton"])])}}});export{N as default};
