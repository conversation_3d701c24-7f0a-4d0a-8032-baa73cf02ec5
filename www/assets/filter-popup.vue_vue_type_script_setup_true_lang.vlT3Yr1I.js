import{_ as e,a}from"./uv-datetime-picker.C3YPVc3n.js";import{d as l,u as t,r as s,D as o,c as u,e as r,w as n,a7 as c,h as i,F as d,a as m,b as p,o as b,i as v,v as f,x as y,g as x,t as _,z as k,s as g,ae as w}from"./index-BBirLt11.js";import{_ as j}from"./uv-input.Q1TIxLPl.js";import{b as h}from"./uv-icon.Dp0oPivN.js";import{_ as S}from"./uv-popup.BGprV-fU.js";import{P as T}from"./system.DZjCJFRG.js";import{c as C}from"./details.uFiKUvam.js";import{D as F}from"./decimal.B1oHnkff.js";import{d as O}from"./debounce.Ce2HeGXN.js";const D=l({__name:"filter-popup",props:{xm_Id:{type:String,default:""},show_type:{type:Boolean,default:!1}},emits:["callback"],setup(l,{expose:D,emit:V}){const{t:$}=t(),z=l,P=s(),I=s(),A=s([]),B=s(0),L=s(),M=V,N=Number(new Date),U=s(""),q=s(),E=s(""),J=o({xm_id:z.xm_Id,page:1,limit:5,kssj:"",jssj:"",keyword:"",zt:T.PASSED}),K=s(""),Q=s(""),R=o([{label:$("sys.reviewStatusObj.all"),value:""},{label:$("sys.reviewStatusObj.pending"),value:"1"},{label:$("sys.reviewStatusObj.rejected"),value:"2"},{label:$("sys.reviewStatusObj.passed"),value:"3"}]);function Y(){P.value.close(),K.value="",Q.value="",U.value="",E.value="",M("callback",!0,{kssj:K.value,jssj:Q.value,zt:U.value,bb_id:""})}const G=()=>{const e=!(!K.value||!Q.value);K.value&&Q.value||z.show_type?(P.value.close(),M("callback",e,{kssj:le(K.value),jssj:le(Q.value),zt:U.value,bb_id:E.value})):w(`${$("sys.pleaseSelect")} ${K.value?$("sys.endTime"):$("sys.startTime")}`)},H=e=>{const a=A.value.find(a=>a.id===e);if(a)return a.uniq_bb_value;E.value=""};function W(e){q.value=e,L.value.open()}const X=e=>{J.keyword=e,J.page=1,te()},Z=O(()=>{F(J.page*J.limit).toNumber()<B.value&&(J.page++,te())},300,{leading:!0,trailing:!0}),ee=()=>{I.value.open()};function ae(e){"start"===q.value?K.value=e.value:Q.value=e.value}const le=e=>{if(!e)return"";const a=new Date(e);return`${a.getFullYear()}-${(a.getMonth()+1).toString().padStart(2,"0")}-${a.getDate().toString().padStart(2,"0")}`},te=async()=>{try{const{data:e,count:a}=await C(J);e&&e.length?(1===J.page?A.value=e:A.value=A.value.concat(e),B.value=a??0):(A.value=[],B.value=0)}catch(e){}},se=e=>{E.value=e,I.value.close()};return D({open:()=>{P.value.open()},close:()=>{P.value.close()}}),(t,s)=>{const o=v,w=m(p("uv-input"),j),T=m(p("uv-icon"),h),C=m(p("uv-popup"),S),F=m(p("uv-datetime-picker"),e),O=a;return b(),u(d,null,[r(C,{ref_key:"popup",ref:P,mode:"bottom",overlayStyle:"background: rgba(0, 0, 0, 0.3);",round:"15",closeable:"",showMask:!0,maskClosable:!0},{default:n(()=>[r(o,{class:"p-32 pt-20 bg-[#F6F7F9]"},{default:n(()=>[r(o,{class:"text-32 leading-44 font-semibold mb-42"},{default:n(()=>[x(_(i($)("sys.filter")),1)]),_:1}),l.show_type?(b(),f(o,{key:0},{default:n(()=>[r(o,{class:"mb-24"},{default:n(()=>[x(_(i($)("sys.reviewStatus")),1)]),_:1}),r(o,{class:"grid grid-cols-2 gap-24 mb-60"},{default:n(()=>[(b(!0),u(d,null,k(i(R),e=>(b(),f(o,{class:g(["item text-center box-border border h-88 leading-88 bg-white rounded-20 text-28",i(U)===e.value?" !border-blueOne text-blueOne":"border-transparent text-blackFour"]),onClick:a=>U.value=e.value,key:e.value},{default:n(()=>[x(_(e.label),1)]),_:2},1032,["class","onClick"]))),128))]),_:1})]),_:1})):y("",!0),r(o,null,{default:n(()=>[r(o,{class:"mb-24"},{default:n(()=>[x(_(i($)("sys.time")),1)]),_:1}),r(o,{class:"flex justify-between items-center mb-60"},{default:n(()=>[r(o,{class:g([i(K)?"text-blackTwo":"text-blackFour","w-310 h-88 bg-white rounded-20 text-center leading-88 text-28"]),onClick:s[0]||(s[0]=e=>W("start"))},{default:n(()=>[x(_(i(K)?le(i(K)):i($)("sys.startTime")),1)]),_:1},8,["class"]),r(o,{class:"bg-blackTwo w-18 h-4 m-24"}),r(o,{class:g([i(Q)?"text-blackTwo":"text-blackFour","w-310 h-88 bg-white rounded-20 text-center leading-88 text-28"]),onClick:s[1]||(s[1]=e=>W("end"))},{default:n(()=>[x(_(i(Q)?le(i(Q)):i($)("sys.endTime")),1)]),_:1},8,["class"])]),_:1}),l.show_type?(b(),f(o,{key:0,class:"mb-24"},{default:n(()=>[r(o,{class:"mb-24"},{default:n(()=>[x(_(i($)("sys.select")),1)]),_:1}),r(o,{class:"mt-12 bg-white px-28 py-24 rounded-20 flex items-center justify-between",onClick:ee},{default:n(()=>[i(E)?(b(),f(o,{key:1},{default:n(()=>[x(_(H(i(E))),1)]),_:1})):(b(),f(w,{key:0,disabledColor:"transparent",placeholder:`${i($)("details.selectPromotionCode")}`,border:"none",modelValue:i(E),"onUpdate:modelValue":s[2]||(s[2]=e=>c(E)?E.value=e:null),disabled:""},null,8,["placeholder","modelValue"])),r(T,{name:"arrow-right",class:"ml-8",size:"28rpx",color:"#727A86"})]),_:1})]),_:1})):y("",!0),r(o,{class:"flex justify-between"},{default:n(()=>[r(o,{class:"bg-white rounded-20 px-60 text-center leading-44 text-blackOne text-30 py-20 mr-22",onClick:Y},{default:n(()=>[x(_(i($)("sys.reset")),1)]),_:1}),r(o,{class:"bg-blueOne rounded-20 px-60 text-center leading-44 text-white text-30 py-20 flex-1",onClick:G},{default:n(()=>[x(_(i($)("sys.confirm")),1)]),_:1})]),_:1})]),_:1})]),_:1})]),_:1},512),r(F,{ref_key:"datetimePicker",ref:L,confirmText:i($)("sys.sure"),cancelText:i($)("sys.cancel"),minDate:15875248e5,modelValue:i(N),"onUpdate:modelValue":s[3]||(s[3]=e=>c(N)?N.value=e:null),mode:"date",onConfirm:ae},null,8,["confirmText","cancelText","modelValue"]),r(O,{ref_key:"reportPopupRef",ref:I,list:i(A),id:z.xm_Id,total:i(B),onSelect:X,onScrollToLower:i(Z),onBackValue:se},null,8,["list","id","total","onScrollToLower"])],64)}}});export{D as _};
