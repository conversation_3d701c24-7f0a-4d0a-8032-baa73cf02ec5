/*!
 *  decimal.js v10.6.0
 *  An arbitrary-precision Decimal type for JavaScript.
 *  https://github.com/MikeMcl/decimal.js
 *  Copyright (c) 2025 <PERSON> <<EMAIL>>
 *  MIT Licence
 */
var n,e,i=9e15,r=1e9,t="0123456789abcdef",s="2.3025850929940456840179914546843642076011014886287729760333279009675726096773524802359972050895982983419677840422862486334095254650828067566662873690987816894829072083255546808437998948262331985283935053089653777326288461633662222876982198867465436674744042432743651550489343149393914796194044002221051017141748003688084012647080685567743216228355220114804663715659121373450747856947683463616792101806445070648000277502684916746550586856935673420670581136429224554405758925724208241314695689016758940256776311356919292033376587141660230105703089634572075440370847469940168269282808481184289314848524948644871927809676271275775397027668605952496716674183485704422507197965004714951050492214776567636938662976979522110718264549734772662425709429322582798502585509785265383207606726317164309505995087807523710333101197857547331541421808427543863591778117054309827482385045648019095610299291824318237525357709750539565187697510374970888692180205189339507238539205144634197265287286965110862571492198849978748873771345686209167058",o="3.1415926535897932384626433832795028841971693993751058209749445923078164062862089986280348253421170679821480865132823066470938446095505822317253594081284811174502841027019385211055596446229489549303819644288109756659334461284756482337867831652712019091456485669234603486104543266482133936072602491412737245870066063155881748815209209628292540917153643678925903600113305305488204665213841469519415116094330572703657595919530921861173819326117931051185480744623799627495673518857527248912279381830119491298336733624406566430860213949463952247371907021798609437027705392171762931767523846748184676694051320005681271452635608277857713427577896091736371787214684409012249534301465495853710507922796892589235420199561121290219608640344181598136297747713099605187072113499999983729780499510597317328160963185950244594553469083026425223082533446850352619311881710100031378387528865875332083814206171776691473035982534904287554687311595628638823537875937519577818577805321712268066130019278766111959092164201989380952572010654858632789",u={precision:20,rounding:4,modulo:1,toExpNeg:-7,toExpPos:21,minE:-i,maxE:i,crypto:!1},c=!0,f="[DecimalError] ",a=f+"Invalid argument: ",h=f+"Precision limit exceeded",d=f+"crypto unavailable",l="[object Decimal]",g=Math.floor,p=Math.pow,w=/^0b([01]+(\.[01]*)?|\.[01]+)(p[+-]?\d+)?$/i,m=/^0x([0-9a-f]+(\.[0-9a-f]*)?|\.[0-9a-f]+)(p[+-]?\d+)?$/i,v=/^0o([0-7]+(\.[0-7]*)?|\.[0-7]+)(p[+-]?\d+)?$/i,N=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,b=1e7,E=s.length-1,x=o.length-1,y={toStringTag:l};function M(n){var e,i,r,t=n.length-1,s="",o=n[0];if(t>0){for(s+=o,e=1;e<t;e++)(i=7-(r=n[e]+"").length)&&(s+=_(i)),s+=r;(i=7-(r=(o=n[e])+"").length)&&(s+=_(i))}else if(0===o)return"0";for(;o%10==0;)o/=10;return s+o}function q(n,e,i){if(n!==~~n||n<e||n>i)throw Error(a+n)}function O(n,e,i,r){var t,s,o,u;for(s=n[0];s>=10;s/=10)--e;return--e<0?(e+=7,t=0):(t=Math.ceil((e+1)/7),e%=7),s=p(10,7-e),u=n[t]%s|0,null==r?e<3?(0==e?u=u/100|0:1==e&&(u=u/10|0),o=i<4&&99999==u||i>3&&49999==u||5e4==u||0==u):o=(i<4&&u+1==s||i>3&&u+1==s/2)&&(n[t+1]/s/100|0)==p(10,e-2)-1||(u==s/2||0==u)&&!(n[t+1]/s/100|0):e<4?(0==e?u=u/1e3|0:1==e?u=u/100|0:2==e&&(u=u/10|0),o=(r||i<4)&&9999==u||!r&&i>3&&4999==u):o=((r||i<4)&&u+1==s||!r&&i>3&&u+1==s/2)&&(n[t+1]/s/1e3|0)==p(10,e-3)-1,o}function F(n,e,i){for(var r,s,o=[0],u=0,c=n.length;u<c;){for(s=o.length;s--;)o[s]*=e;for(o[0]+=t.indexOf(n.charAt(u++)),r=0;r<o.length;r++)o[r]>i-1&&(void 0===o[r+1]&&(o[r+1]=0),o[r+1]+=o[r]/i|0,o[r]%=i)}return o.reverse()}y.absoluteValue=y.abs=function(){var n=new this.constructor(this);return n.s<0&&(n.s=1),D(n)},y.ceil=function(){return D(new this.constructor(this),this.e+1,2)},y.clampedTo=y.clamp=function(n,e){var i=this,r=i.constructor;if(n=new r(n),e=new r(e),!n.s||!e.s)return new r(NaN);if(n.gt(e))throw Error(a+e);return i.cmp(n)<0?n:i.cmp(e)>0?e:new r(i)},y.comparedTo=y.cmp=function(n){var e,i,r,t,s=this,o=s.d,u=(n=new s.constructor(n)).d,c=s.s,f=n.s;if(!o||!u)return c&&f?c!==f?c:o===u?0:!o^c<0?1:-1:NaN;if(!o[0]||!u[0])return o[0]?c:u[0]?-f:0;if(c!==f)return c;if(s.e!==n.e)return s.e>n.e^c<0?1:-1;for(e=0,i=(r=o.length)<(t=u.length)?r:t;e<i;++e)if(o[e]!==u[e])return o[e]>u[e]^c<0?1:-1;return r===t?0:r>t^c<0?1:-1},y.cosine=y.cos=function(){var n,i,r=this,t=r.constructor;return r.d?r.d[0]?(n=t.precision,i=t.rounding,t.precision=n+Math.max(r.e,r.sd())+7,t.rounding=1,r=function(n,e){var i,r,t;if(e.isZero())return e;r=e.d.length,r<32?t=(1/$(4,i=Math.ceil(r/3))).toString():(i=16,t="2.3283064365386962890625e-10");n.precision+=i,e=V(n,1,e.times(t),new n(1));for(var s=i;s--;){var o=e.times(e);e=o.times(o).minus(o).times(8).plus(1)}return n.precision-=i,e}(t,j(t,r)),t.precision=n,t.rounding=i,D(2==e||3==e?r.neg():r,n,i,!0)):new t(1):new t(NaN)},y.cubeRoot=y.cbrt=function(){var n,e,i,r,t,s,o,u,f,a,h=this,d=h.constructor;if(!h.isFinite()||h.isZero())return new d(h);for(c=!1,(s=h.s*p(h.s*h,1/3))&&Math.abs(s)!=1/0?r=new d(s.toString()):(i=M(h.d),(s=((n=h.e)-i.length+1)%3)&&(i+=1==s||-2==s?"0":"00"),s=p(i,1/3),n=g((n+1)/3)-(n%3==(n<0?-1:2)),(r=new d(i=s==1/0?"5e"+n:(i=s.toExponential()).slice(0,i.indexOf("e")+1)+n)).s=h.s),o=(n=d.precision)+3;;)if(a=(f=(u=r).times(u).times(u)).plus(h),r=A(a.plus(h).times(u),a.plus(f),o+2,1),M(u.d).slice(0,o)===(i=M(r.d)).slice(0,o)){if("9999"!=(i=i.slice(o-3,o+1))&&(t||"4999"!=i)){+i&&(+i.slice(1)||"5"!=i.charAt(0))||(D(r,n+1,1),e=!r.times(r).times(r).eq(h));break}if(!t&&(D(u,n+1,0),u.times(u).times(u).eq(h))){r=u;break}o+=4,t=1}return c=!0,D(r,n,d.rounding,e)},y.decimalPlaces=y.dp=function(){var n,e=this.d,i=NaN;if(e){if(i=7*((n=e.length-1)-g(this.e/7)),n=e[n])for(;n%10==0;n/=10)i--;i<0&&(i=0)}return i},y.dividedBy=y.div=function(n){return A(this,new this.constructor(n))},y.dividedToIntegerBy=y.divToInt=function(n){var e=this.constructor;return D(A(this,new e(n),0,1,1),e.precision,e.rounding)},y.equals=y.eq=function(n){return 0===this.cmp(n)},y.floor=function(){return D(new this.constructor(this),this.e+1,3)},y.greaterThan=y.gt=function(n){return this.cmp(n)>0},y.greaterThanOrEqualTo=y.gte=function(n){var e=this.cmp(n);return 1==e||0===e},y.hyperbolicCosine=y.cosh=function(){var n,e,i,r,t,s=this,o=s.constructor,u=new o(1);if(!s.isFinite())return new o(s.s?1/0:NaN);if(s.isZero())return u;i=o.precision,r=o.rounding,o.precision=i+Math.max(s.e,s.sd())+4,o.rounding=1,(t=s.d.length)<32?e=(1/$(4,n=Math.ceil(t/3))).toString():(n=16,e="2.3283064365386962890625e-10"),s=V(o,1,s.times(e),new o(1),!0);for(var c,f=n,a=new o(8);f--;)c=s.times(s),s=u.minus(c.times(a.minus(c.times(a))));return D(s,o.precision=i,o.rounding=r,!0)},y.hyperbolicSine=y.sinh=function(){var n,e,i,r,t=this,s=t.constructor;if(!t.isFinite()||t.isZero())return new s(t);if(e=s.precision,i=s.rounding,s.precision=e+Math.max(t.e,t.sd())+4,s.rounding=1,(r=t.d.length)<3)t=V(s,2,t,t,!0);else{n=(n=1.4*Math.sqrt(r))>16?16:0|n,t=V(s,2,t=t.times(1/$(5,n)),t,!0);for(var o,u=new s(5),c=new s(16),f=new s(20);n--;)o=t.times(t),t=t.times(u.plus(o.times(c.times(o).plus(f))))}return s.precision=e,s.rounding=i,D(t,e,i,!0)},y.hyperbolicTangent=y.tanh=function(){var n,e,i=this,r=i.constructor;return i.isFinite()?i.isZero()?new r(i):(n=r.precision,e=r.rounding,r.precision=n+7,r.rounding=1,A(i.sinh(),i.cosh(),r.precision=n,r.rounding=e)):new r(i.s)},y.inverseCosine=y.acos=function(){var n=this,e=n.constructor,i=n.abs().cmp(1),r=e.precision,t=e.rounding;return-1!==i?0===i?n.isNeg()?R(e,r,t):new e(0):new e(NaN):n.isZero()?R(e,r+4,t).times(.5):(e.precision=r+6,e.rounding=1,n=new e(1).minus(n).div(n.plus(1)).sqrt().atan(),e.precision=r,e.rounding=t,n.times(2))},y.inverseHyperbolicCosine=y.acosh=function(){var n,e,i=this,r=i.constructor;return i.lte(1)?new r(i.eq(1)?0:NaN):i.isFinite()?(n=r.precision,e=r.rounding,r.precision=n+Math.max(Math.abs(i.e),i.sd())+4,r.rounding=1,c=!1,i=i.times(i).minus(1).sqrt().plus(i),c=!0,r.precision=n,r.rounding=e,i.ln()):new r(i)},y.inverseHyperbolicSine=y.asinh=function(){var n,e,i=this,r=i.constructor;return!i.isFinite()||i.isZero()?new r(i):(n=r.precision,e=r.rounding,r.precision=n+2*Math.max(Math.abs(i.e),i.sd())+6,r.rounding=1,c=!1,i=i.times(i).plus(1).sqrt().plus(i),c=!0,r.precision=n,r.rounding=e,i.ln())},y.inverseHyperbolicTangent=y.atanh=function(){var n,e,i,r,t=this,s=t.constructor;return t.isFinite()?t.e>=0?new s(t.abs().eq(1)?t.s/0:t.isZero()?t:NaN):(n=s.precision,e=s.rounding,r=t.sd(),Math.max(r,n)<2*-t.e-1?D(new s(t),n,e,!0):(s.precision=i=r-t.e,t=A(t.plus(1),new s(1).minus(t),i+n,1),s.precision=n+4,s.rounding=1,t=t.ln(),s.precision=n,s.rounding=e,t.times(.5))):new s(NaN)},y.inverseSine=y.asin=function(){var n,e,i,r,t=this,s=t.constructor;return t.isZero()?new s(t):(e=t.abs().cmp(1),i=s.precision,r=s.rounding,-1!==e?0===e?((n=R(s,i+4,r).times(.5)).s=t.s,n):new s(NaN):(s.precision=i+6,s.rounding=1,t=t.div(new s(1).minus(t.times(t)).sqrt().plus(1)).atan(),s.precision=i,s.rounding=r,t.times(2)))},y.inverseTangent=y.atan=function(){var n,e,i,r,t,s,o,u,f,a=this,h=a.constructor,d=h.precision,l=h.rounding;if(a.isFinite()){if(a.isZero())return new h(a);if(a.abs().eq(1)&&d+4<=x)return(o=R(h,d+4,l).times(.25)).s=a.s,o}else{if(!a.s)return new h(NaN);if(d+4<=x)return(o=R(h,d+4,l).times(.5)).s=a.s,o}for(h.precision=u=d+10,h.rounding=1,n=i=Math.min(28,u/7+2|0);n;--n)a=a.div(a.times(a).plus(1).sqrt().plus(1));for(c=!1,e=Math.ceil(u/7),r=1,f=a.times(a),o=new h(a),t=a;-1!==n;)if(t=t.times(f),s=o.minus(t.div(r+=2)),t=t.times(f),void 0!==(o=s.plus(t.div(r+=2))).d[e])for(n=e;o.d[n]===s.d[n]&&n--;);return i&&(o=o.times(2<<i-1)),c=!0,D(o,h.precision=d,h.rounding=l,!0)},y.isFinite=function(){return!!this.d},y.isInteger=y.isInt=function(){return!!this.d&&g(this.e/7)>this.d.length-2},y.isNaN=function(){return!this.s},y.isNegative=y.isNeg=function(){return this.s<0},y.isPositive=y.isPos=function(){return this.s>0},y.isZero=function(){return!!this.d&&0===this.d[0]},y.lessThan=y.lt=function(n){return this.cmp(n)<0},y.lessThanOrEqualTo=y.lte=function(n){return this.cmp(n)<1},y.logarithm=y.log=function(n){var e,i,r,t,s,o,u,f,a=this,h=a.constructor,d=h.precision,l=h.rounding;if(null==n)n=new h(10),e=!0;else{if(i=(n=new h(n)).d,n.s<0||!i||!i[0]||n.eq(1))return new h(NaN);e=n.eq(10)}if(i=a.d,a.s<0||!i||!i[0]||a.eq(1))return new h(i&&!i[0]?-1/0:1!=a.s?NaN:i?0:1/0);if(e)if(i.length>1)s=!0;else{for(t=i[0];t%10==0;)t/=10;s=1!==t}if(c=!1,o=C(a,u=d+5),r=e?P(h,u+10):C(n,u),O((f=A(o,r,u,1)).d,t=d,l))do{if(o=C(a,u+=10),r=e?P(h,u+10):C(n,u),f=A(o,r,u,1),!s){+M(f.d).slice(t+1,t+15)+1==1e14&&(f=D(f,d+1,0));break}}while(O(f.d,t+=10,l));return c=!0,D(f,d,l)},y.minus=y.sub=function(n){var e,i,r,t,s,o,u,f,a,h,d,l,p=this,w=p.constructor;if(n=new w(n),!p.d||!n.d)return p.s&&n.s?p.d?n.s=-n.s:n=new w(n.d||p.s!==n.s?p:NaN):n=new w(NaN),n;if(p.s!=n.s)return n.s=-n.s,p.plus(n);if(a=p.d,l=n.d,u=w.precision,f=w.rounding,!a[0]||!l[0]){if(l[0])n.s=-n.s;else{if(!a[0])return new w(3===f?-0:0);n=new w(p)}return c?D(n,u,f):n}if(i=g(n.e/7),h=g(p.e/7),a=a.slice(),s=h-i){for((d=s<0)?(e=a,s=-s,o=l.length):(e=l,i=h,o=a.length),s>(r=Math.max(Math.ceil(u/7),o)+2)&&(s=r,e.length=1),e.reverse(),r=s;r--;)e.push(0);e.reverse()}else{for((d=(r=a.length)<(o=l.length))&&(o=r),r=0;r<o;r++)if(a[r]!=l[r]){d=a[r]<l[r];break}s=0}for(d&&(e=a,a=l,l=e,n.s=-n.s),o=a.length,r=l.length-o;r>0;--r)a[o++]=0;for(r=l.length;r>s;){if(a[--r]<l[r]){for(t=r;t&&0===a[--t];)a[t]=b-1;--a[t],a[r]+=b}a[r]-=l[r]}for(;0===a[--o];)a.pop();for(;0===a[0];a.shift())--i;return a[0]?(n.d=a,n.e=S(a,i),c?D(n,u,f):n):new w(3===f?-0:0)},y.modulo=y.mod=function(n){var e,i=this,r=i.constructor;return n=new r(n),!i.d||!n.s||n.d&&!n.d[0]?new r(NaN):!n.d||i.d&&!i.d[0]?D(new r(i),r.precision,r.rounding):(c=!1,9==r.modulo?(e=A(i,n.abs(),0,3,1)).s*=n.s:e=A(i,n,0,r.modulo,1),e=e.times(n),c=!0,i.minus(e))},y.naturalExponential=y.exp=function(){return I(this)},y.naturalLogarithm=y.ln=function(){return C(this)},y.negated=y.neg=function(){var n=new this.constructor(this);return n.s=-n.s,D(n)},y.plus=y.add=function(n){var e,i,r,t,s,o,u,f,a,h,d=this,l=d.constructor;if(n=new l(n),!d.d||!n.d)return d.s&&n.s?d.d||(n=new l(n.d||d.s===n.s?d:NaN)):n=new l(NaN),n;if(d.s!=n.s)return n.s=-n.s,d.minus(n);if(a=d.d,h=n.d,u=l.precision,f=l.rounding,!a[0]||!h[0])return h[0]||(n=new l(d)),c?D(n,u,f):n;if(s=g(d.e/7),r=g(n.e/7),a=a.slice(),t=s-r){for(t<0?(i=a,t=-t,o=h.length):(i=h,r=s,o=a.length),t>(o=(s=Math.ceil(u/7))>o?s+1:o+1)&&(t=o,i.length=1),i.reverse();t--;)i.push(0);i.reverse()}for((o=a.length)-(t=h.length)<0&&(t=o,i=h,h=a,a=i),e=0;t;)e=(a[--t]=a[t]+h[t]+e)/b|0,a[t]%=b;for(e&&(a.unshift(e),++r),o=a.length;0==a[--o];)a.pop();return n.d=a,n.e=S(a,r),c?D(n,u,f):n},y.precision=y.sd=function(n){var e,i=this;if(void 0!==n&&n!==!!n&&1!==n&&0!==n)throw Error(a+n);return i.d?(e=T(i.d),n&&i.e+1>e&&(e=i.e+1)):e=NaN,e},y.round=function(){var n=this,e=n.constructor;return D(new e(n),n.e+1,e.rounding)},y.sine=y.sin=function(){var n,i,r=this,t=r.constructor;return r.isFinite()?r.isZero()?new t(r):(n=t.precision,i=t.rounding,t.precision=n+Math.max(r.e,r.sd())+7,t.rounding=1,r=function(n,e){var i,r=e.d.length;if(r<3)return e.isZero()?e:V(n,2,e,e);i=(i=1.4*Math.sqrt(r))>16?16:0|i,e=e.times(1/$(5,i)),e=V(n,2,e,e);for(var t,s=new n(5),o=new n(16),u=new n(20);i--;)t=e.times(e),e=e.times(s.plus(t.times(o.times(t).minus(u))));return e}(t,j(t,r)),t.precision=n,t.rounding=i,D(e>2?r.neg():r,n,i,!0)):new t(NaN)},y.squareRoot=y.sqrt=function(){var n,e,i,r,t,s,o=this,u=o.d,f=o.e,a=o.s,h=o.constructor;if(1!==a||!u||!u[0])return new h(!a||a<0&&(!u||u[0])?NaN:u?o:1/0);for(c=!1,0==(a=Math.sqrt(+o))||a==1/0?(((e=M(u)).length+f)%2==0&&(e+="0"),a=Math.sqrt(e),f=g((f+1)/2)-(f<0||f%2),r=new h(e=a==1/0?"5e"+f:(e=a.toExponential()).slice(0,e.indexOf("e")+1)+f)):r=new h(a.toString()),i=(f=h.precision)+3;;)if(r=(s=r).plus(A(o,s,i+2,1)).times(.5),M(s.d).slice(0,i)===(e=M(r.d)).slice(0,i)){if("9999"!=(e=e.slice(i-3,i+1))&&(t||"4999"!=e)){+e&&(+e.slice(1)||"5"!=e.charAt(0))||(D(r,f+1,1),n=!r.times(r).eq(o));break}if(!t&&(D(s,f+1,0),s.times(s).eq(o))){r=s;break}i+=4,t=1}return c=!0,D(r,f,h.rounding,n)},y.tangent=y.tan=function(){var n,i,r=this,t=r.constructor;return r.isFinite()?r.isZero()?new t(r):(n=t.precision,i=t.rounding,t.precision=n+10,t.rounding=1,(r=r.sin()).s=1,r=A(r,new t(1).minus(r.times(r)).sqrt(),n+10,0),t.precision=n,t.rounding=i,D(2==e||4==e?r.neg():r,n,i,!0)):new t(NaN)},y.times=y.mul=function(n){var e,i,r,t,s,o,u,f,a,h=this,d=h.constructor,l=h.d,p=(n=new d(n)).d;if(n.s*=h.s,!(l&&l[0]&&p&&p[0]))return new d(!n.s||l&&!l[0]&&!p||p&&!p[0]&&!l?NaN:l&&p?0*n.s:n.s/0);for(i=g(h.e/7)+g(n.e/7),(f=l.length)<(a=p.length)&&(s=l,l=p,p=s,o=f,f=a,a=o),s=[],r=o=f+a;r--;)s.push(0);for(r=a;--r>=0;){for(e=0,t=f+r;t>r;)u=s[t]+p[r]*l[t-r-1]+e,s[t--]=u%b|0,e=u/b|0;s[t]=(s[t]+e)%b|0}for(;!s[--o];)s.pop();return e?++i:s.shift(),n.d=s,n.e=S(s,i),c?D(n,d.precision,d.rounding):n},y.toBinary=function(n,e){return W(this,2,n,e)},y.toDecimalPlaces=y.toDP=function(n,e){var i=this,t=i.constructor;return i=new t(i),void 0===n?i:(q(n,0,r),void 0===e?e=t.rounding:q(e,0,8),D(i,n+i.e+1,e))},y.toExponential=function(n,e){var i,t=this,s=t.constructor;return void 0===n?i=Z(t,!0):(q(n,0,r),void 0===e?e=s.rounding:q(e,0,8),i=Z(t=D(new s(t),n+1,e),!0,n+1)),t.isNeg()&&!t.isZero()?"-"+i:i},y.toFixed=function(n,e){var i,t,s=this,o=s.constructor;return void 0===n?i=Z(s):(q(n,0,r),void 0===e?e=o.rounding:q(e,0,8),i=Z(t=D(new o(s),n+s.e+1,e),!1,n+t.e+1)),s.isNeg()&&!s.isZero()?"-"+i:i},y.toFraction=function(n){var e,i,r,t,s,o,u,f,h,d,l,g,w=this,m=w.d,v=w.constructor;if(!m)return new v(w);if(h=i=new v(1),r=f=new v(0),o=(s=(e=new v(r)).e=T(m)-w.e-1)%7,e.d[0]=p(10,o<0?7+o:o),null==n)n=s>0?e:h;else{if(!(u=new v(n)).isInt()||u.lt(h))throw Error(a+u);n=u.gt(e)?s>0?e:h:u}for(c=!1,u=new v(M(m)),d=v.precision,v.precision=s=7*m.length*2;l=A(u,e,0,1,1),1!=(t=i.plus(l.times(r))).cmp(n);)i=r,r=t,t=h,h=f.plus(l.times(t)),f=t,t=e,e=u.minus(l.times(t)),u=t;return t=A(n.minus(i),r,0,1,1),f=f.plus(t.times(h)),i=i.plus(t.times(r)),f.s=h.s=w.s,g=A(h,r,s,1).minus(w).abs().cmp(A(f,i,s,1).minus(w).abs())<1?[h,r]:[f,i],v.precision=d,c=!0,g},y.toHexadecimal=y.toHex=function(n,e){return W(this,16,n,e)},y.toNearest=function(n,e){var i=this,r=i.constructor;if(i=new r(i),null==n){if(!i.d)return i;n=new r(1),e=r.rounding}else{if(n=new r(n),void 0===e?e=r.rounding:q(e,0,8),!i.d)return n.s?i:n;if(!n.d)return n.s&&(n.s=i.s),n}return n.d[0]?(c=!1,i=A(i,n,0,e,1).times(n),c=!0,D(i)):(n.s=i.s,i=n),i},y.toNumber=function(){return+this},y.toOctal=function(n,e){return W(this,8,n,e)},y.toPower=y.pow=function(n){var e,i,r,t,s,o,u=this,f=u.constructor,a=+(n=new f(n));if(!(u.d&&n.d&&u.d[0]&&n.d[0]))return new f(p(+u,a));if((u=new f(u)).eq(1))return u;if(r=f.precision,s=f.rounding,n.eq(1))return D(u,r,s);if((e=g(n.e/7))>=n.d.length-1&&(i=a<0?-a:a)<=9007199254740991)return t=L(f,u,i,r),n.s<0?new f(1).div(t):D(t,r,s);if((o=u.s)<0){if(e<n.d.length-1)return new f(NaN);if(1&n.d[e]||(o=1),0==u.e&&1==u.d[0]&&1==u.d.length)return u.s=o,u}return(e=0!=(i=p(+u,a))&&isFinite(i)?new f(i+"").e:g(a*(Math.log("0."+M(u.d))/Math.LN10+u.e+1)))>f.maxE+1||e<f.minE-1?new f(e>0?o/0:0):(c=!1,f.rounding=u.s=1,i=Math.min(12,(e+"").length),(t=I(n.times(C(u,r+i)),r)).d&&O((t=D(t,r+5,1)).d,r,s)&&(e=r+10,+M((t=D(I(n.times(C(u,e+i)),e),e+5,1)).d).slice(r+1,r+15)+1==1e14&&(t=D(t,r+1,0))),t.s=o,c=!0,f.rounding=s,D(t,r,s))},y.toPrecision=function(n,e){var i,t=this,s=t.constructor;return void 0===n?i=Z(t,t.e<=s.toExpNeg||t.e>=s.toExpPos):(q(n,1,r),void 0===e?e=s.rounding:q(e,0,8),i=Z(t=D(new s(t),n,e),n<=t.e||t.e<=s.toExpNeg,n)),t.isNeg()&&!t.isZero()?"-"+i:i},y.toSignificantDigits=y.toSD=function(n,e){var i=this.constructor;return void 0===n?(n=i.precision,e=i.rounding):(q(n,1,r),void 0===e?e=i.rounding:q(e,0,8)),D(new i(this),n,e)},y.toString=function(){var n=this,e=n.constructor,i=Z(n,n.e<=e.toExpNeg||n.e>=e.toExpPos);return n.isNeg()&&!n.isZero()?"-"+i:i},y.truncated=y.trunc=function(){return D(new this.constructor(this),this.e+1,1)},y.valueOf=y.toJSON=function(){var n=this,e=n.constructor,i=Z(n,n.e<=e.toExpNeg||n.e>=e.toExpPos);return n.isNeg()?"-"+i:i};var A=function(){function e(n,e,i){var r,t=0,s=n.length;for(n=n.slice();s--;)r=n[s]*e+t,n[s]=r%i|0,t=r/i|0;return t&&n.unshift(t),n}function i(n,e,i,r){var t,s;if(i!=r)s=i>r?1:-1;else for(t=s=0;t<i;t++)if(n[t]!=e[t]){s=n[t]>e[t]?1:-1;break}return s}function r(n,e,i,r){for(var t=0;i--;)n[i]-=t,t=n[i]<e[i]?1:0,n[i]=t*r+n[i]-e[i];for(;!n[0]&&n.length>1;)n.shift()}return function(t,s,o,u,c,f){var a,h,d,l,p,w,m,v,N,E,x,y,M,q,O,F,A,Z,S,P,R=t.constructor,T=t.s==s.s?1:-1,_=t.d,L=s.d;if(!(_&&_[0]&&L&&L[0]))return new R(t.s&&s.s&&(_?!L||_[0]!=L[0]:L)?_&&0==_[0]||!L?0*T:T/0:NaN);for(f?(p=1,h=t.e-s.e):(f=b,p=7,h=g(t.e/p)-g(s.e/p)),S=L.length,A=_.length,E=(N=new R(T)).d=[],d=0;L[d]==(_[d]||0);d++);if(L[d]>(_[d]||0)&&h--,null==o?(q=o=R.precision,u=R.rounding):q=c?o+(t.e-s.e)+1:o,q<0)E.push(1),w=!0;else{if(q=q/p+2|0,d=0,1==S){for(l=0,L=L[0],q++;(d<A||l)&&q--;d++)O=l*f+(_[d]||0),E[d]=O/L|0,l=O%L|0;w=l||d<A}else{for((l=f/(L[0]+1)|0)>1&&(L=e(L,l,f),_=e(_,l,f),S=L.length,A=_.length),F=S,y=(x=_.slice(0,S)).length;y<S;)x[y++]=0;(P=L.slice()).unshift(0),Z=L[0],L[1]>=f/2&&++Z;do{l=0,(a=i(L,x,S,y))<0?(M=x[0],S!=y&&(M=M*f+(x[1]||0)),(l=M/Z|0)>1?(l>=f&&(l=f-1),1==(a=i(m=e(L,l,f),x,v=m.length,y=x.length))&&(l--,r(m,S<v?P:L,v,f))):(0==l&&(a=l=1),m=L.slice()),(v=m.length)<y&&m.unshift(0),r(x,m,y,f),-1==a&&(a=i(L,x,S,y=x.length))<1&&(l++,r(x,S<y?P:L,y,f)),y=x.length):0===a&&(l++,x=[0]),E[d++]=l,a&&x[0]?x[y++]=_[F]||0:(x=[_[F]],y=1)}while((F++<A||void 0!==x[0])&&q--);w=void 0!==x[0]}E[0]||E.shift()}if(1==p)N.e=h,n=w;else{for(d=1,l=E[0];l>=10;l/=10)d++;N.e=d+h*p-1,D(N,c?o+N.e+1:o,u,w)}return N}}();function D(n,e,i,r){var t,s,o,u,f,a,h,d,l,g=n.constructor;n:if(null!=e){if(!(d=n.d))return n;for(t=1,u=d[0];u>=10;u/=10)t++;if((s=e-t)<0)s+=7,o=e,f=(h=d[l=0])/p(10,t-o-1)%10|0;else if((l=Math.ceil((s+1)/7))>=(u=d.length)){if(!r)break n;for(;u++<=l;)d.push(0);h=f=0,t=1,o=(s%=7)-7+1}else{for(h=u=d[l],t=1;u>=10;u/=10)t++;f=(o=(s%=7)-7+t)<0?0:h/p(10,t-o-1)%10|0}if(r=r||e<0||void 0!==d[l+1]||(o<0?h:h%p(10,t-o-1)),a=i<4?(f||r)&&(0==i||i==(n.s<0?3:2)):f>5||5==f&&(4==i||r||6==i&&(s>0?o>0?h/p(10,t-o):0:d[l-1])%10&1||i==(n.s<0?8:7)),e<1||!d[0])return d.length=0,a?(e-=n.e+1,d[0]=p(10,(7-e%7)%7),n.e=-e||0):d[0]=n.e=0,n;if(0==s?(d.length=l,u=1,l--):(d.length=l+1,u=p(10,7-s),d[l]=o>0?(h/p(10,t-o)%p(10,o)|0)*u:0),a)for(;;){if(0==l){for(s=1,o=d[0];o>=10;o/=10)s++;for(o=d[0]+=u,u=1;o>=10;o/=10)u++;s!=u&&(n.e++,d[0]==b&&(d[0]=1));break}if(d[l]+=u,d[l]!=b)break;d[l--]=0,u=1}for(s=d.length;0===d[--s];)d.pop()}return c&&(n.e>g.maxE?(n.d=null,n.e=NaN):n.e<g.minE&&(n.e=0,n.d=[0])),n}function Z(n,e,i){if(!n.isFinite())return H(n);var r,t=n.e,s=M(n.d),o=s.length;return e?(i&&(r=i-o)>0?s=s.charAt(0)+"."+s.slice(1)+_(r):o>1&&(s=s.charAt(0)+"."+s.slice(1)),s=s+(n.e<0?"e":"e+")+n.e):t<0?(s="0."+_(-t-1)+s,i&&(r=i-o)>0&&(s+=_(r))):t>=o?(s+=_(t+1-o),i&&(r=i-t-1)>0&&(s=s+"."+_(r))):((r=t+1)<o&&(s=s.slice(0,r)+"."+s.slice(r)),i&&(r=i-o)>0&&(t+1===o&&(s+="."),s+=_(r))),s}function S(n,e){var i=n[0];for(e*=7;i>=10;i/=10)e++;return e}function P(n,e,i){if(e>E)throw c=!0,i&&(n.precision=i),Error(h);return D(new n(s),e,1,!0)}function R(n,e,i){if(e>x)throw Error(h);return D(new n(o),e,i,!0)}function T(n){var e=n.length-1,i=7*e+1;if(e=n[e]){for(;e%10==0;e/=10)i--;for(e=n[0];e>=10;e/=10)i++}return i}function _(n){for(var e="";n--;)e+="0";return e}function L(n,e,i,r){var t,s=new n(1),o=Math.ceil(r/7+4);for(c=!1;;){if(i%2&&J((s=s.times(e)).d,o)&&(t=!0),0===(i=g(i/2))){i=s.d.length-1,t&&0===s.d[i]&&++s.d[i];break}J((e=e.times(e)).d,o)}return c=!0,s}function U(n){return 1&n.d[n.d.length-1]}function k(n,e,i){for(var r,t,s=new n(e[0]),o=0;++o<e.length;){if(!(t=new n(e[o])).s){s=t;break}((r=s.cmp(t))===i||0===r&&s.s===i)&&(s=t)}return s}function I(n,e){var i,r,t,s,o,u,f,a=0,h=0,d=0,l=n.constructor,g=l.rounding,w=l.precision;if(!n.d||!n.d[0]||n.e>17)return new l(n.d?n.d[0]?n.s<0?0:1/0:1:n.s?n.s<0?0:n:NaN);for(null==e?(c=!1,f=w):f=e,u=new l(.03125);n.e>-2;)n=n.times(u),d+=5;for(f+=r=Math.log(p(2,d))/Math.LN10*2+5|0,i=s=o=new l(1),l.precision=f;;){if(s=D(s.times(n),f,1),i=i.times(++h),M((u=o.plus(A(s,i,f,1))).d).slice(0,f)===M(o.d).slice(0,f)){for(t=d;t--;)o=D(o.times(o),f,1);if(null!=e)return l.precision=w,o;if(!(a<3&&O(o.d,f-r,g,a)))return D(o,l.precision=w,g,c=!0);l.precision=f+=10,i=s=u=new l(1),h=0,a++}o=u}}function C(n,e){var i,r,t,s,o,u,f,a,h,d,l,g=1,p=n,w=p.d,m=p.constructor,v=m.rounding,N=m.precision;if(p.s<0||!w||!w[0]||!p.e&&1==w[0]&&1==w.length)return new m(w&&!w[0]?-1/0:1!=p.s?NaN:w?0:p);if(null==e?(c=!1,h=N):h=e,m.precision=h+=10,r=(i=M(w)).charAt(0),!(Math.abs(s=p.e)<15e14))return a=P(m,h+2,N).times(s+""),p=C(new m(r+"."+i.slice(1)),h-10).plus(a),m.precision=N,null==e?D(p,N,v,c=!0):p;for(;r<7&&1!=r||1==r&&i.charAt(1)>3;)r=(i=M((p=p.times(n)).d)).charAt(0),g++;for(s=p.e,r>1?(p=new m("0."+i),s++):p=new m(r+"."+i.slice(1)),d=p,f=o=p=A(p.minus(1),p.plus(1),h,1),l=D(p.times(p),h,1),t=3;;){if(o=D(o.times(l),h,1),M((a=f.plus(A(o,new m(t),h,1))).d).slice(0,h)===M(f.d).slice(0,h)){if(f=f.times(2),0!==s&&(f=f.plus(P(m,h+2,N).times(s+""))),f=A(f,new m(g),h,1),null!=e)return m.precision=N,f;if(!O(f.d,h-10,v,u))return D(f,m.precision=N,v,c=!0);m.precision=h+=10,a=o=p=A(d.minus(1),d.plus(1),h,1),l=D(p.times(p),h,1),t=u=1}f=a,t+=2}}function H(n){return String(n.s*n.s/0)}function B(n,e){var i,r,t;for((i=e.indexOf("."))>-1&&(e=e.replace(".","")),(r=e.search(/e/i))>0?(i<0&&(i=r),i+=+e.slice(r+1),e=e.substring(0,r)):i<0&&(i=e.length),r=0;48===e.charCodeAt(r);r++);for(t=e.length;48===e.charCodeAt(t-1);--t);if(e=e.slice(r,t)){if(t-=r,n.e=i=i-r-1,n.d=[],r=(i+1)%7,i<0&&(r+=7),r<t){for(r&&n.d.push(+e.slice(0,r)),t-=7;r<t;)n.d.push(+e.slice(r,r+=7));r=7-(e=e.slice(r)).length}else r-=t;for(;r--;)e+="0";n.d.push(+e),c&&(n.e>n.constructor.maxE?(n.d=null,n.e=NaN):n.e<n.constructor.minE&&(n.e=0,n.d=[0]))}else n.e=0,n.d=[0];return n}function V(n,e,i,r,t){var s,o,u,f,a=n.precision,h=Math.ceil(a/7);for(c=!1,f=i.times(i),u=new n(r);;){if(o=A(u.times(f),new n(e++*e++),a,1),u=t?r.plus(o):r.minus(o),r=A(o.times(f),new n(e++*e++),a,1),void 0!==(o=u.plus(r)).d[h]){for(s=h;o.d[s]===u.d[s]&&s--;);if(-1==s)break}s=u,u=r,r=o,o=s}return c=!0,o.d.length=h+1,o}function $(n,e){for(var i=n;--e;)i*=n;return i}function j(n,i){var r,t=i.s<0,s=R(n,n.precision,1),o=s.times(.5);if((i=i.abs()).lte(o))return e=t?4:1,i;if((r=i.divToInt(s)).isZero())e=t?3:2;else{if((i=i.minus(r.times(s))).lte(o))return e=U(r)?t?2:3:t?4:1,i;e=U(r)?t?1:4:t?3:2}return i.minus(s).abs()}function W(e,i,s,o){var u,c,f,a,h,d,l,g,p,w=e.constructor,m=void 0!==s;if(m?(q(s,1,r),void 0===o?o=w.rounding:q(o,0,8)):(s=w.precision,o=w.rounding),e.isFinite()){for(m?(u=2,16==i?s=4*s-3:8==i&&(s=3*s-2)):u=i,(f=(l=Z(e)).indexOf("."))>=0&&(l=l.replace(".",""),(p=new w(1)).e=l.length-f,p.d=F(Z(p),10,u),p.e=p.d.length),c=h=(g=F(l,10,u)).length;0==g[--h];)g.pop();if(g[0]){if(f<0?c--:((e=new w(e)).d=g,e.e=c,g=(e=A(e,p,s,o,0,u)).d,c=e.e,d=n),f=g[s],a=u/2,d=d||void 0!==g[s+1],d=o<4?(void 0!==f||d)&&(0===o||o===(e.s<0?3:2)):f>a||f===a&&(4===o||d||6===o&&1&g[s-1]||o===(e.s<0?8:7)),g.length=s,d)for(;++g[--s]>u-1;)g[s]=0,s||(++c,g.unshift(1));for(h=g.length;!g[h-1];--h);for(f=0,l="";f<h;f++)l+=t.charAt(g[f]);if(m){if(h>1)if(16==i||8==i){for(f=16==i?4:3,--h;h%f;h++)l+="0";for(h=(g=F(l,u,i)).length;!g[h-1];--h);for(f=1,l="1.";f<h;f++)l+=t.charAt(g[f])}else l=l.charAt(0)+"."+l.slice(1);l=l+(c<0?"p":"p+")+c}else if(c<0){for(;++c;)l="0"+l;l="0."+l}else if(++c>h)for(c-=h;c--;)l+="0";else c<h&&(l=l.slice(0,c)+"."+l.slice(c))}else l=m?"0p+0":"0";l=(16==i?"0x":2==i?"0b":8==i?"0o":"")+l}else l=H(e);return e.s<0?"-"+l:l}function J(n,e){if(n.length>e)return n.length=e,!0}function z(n){return new this(n).abs()}function G(n){return new this(n).acos()}function K(n){return new this(n).acosh()}function Q(n,e){return new this(n).plus(e)}function X(n){return new this(n).asin()}function Y(n){return new this(n).asinh()}function nn(n){return new this(n).atan()}function en(n){return new this(n).atanh()}function rn(n,e){n=new this(n),e=new this(e);var i,r=this.precision,t=this.rounding,s=r+4;return n.s&&e.s?n.d||e.d?!e.d||n.isZero()?(i=e.s<0?R(this,r,t):new this(0)).s=n.s:!n.d||e.isZero()?(i=R(this,s,1).times(.5)).s=n.s:e.s<0?(this.precision=s,this.rounding=1,i=this.atan(A(n,e,s,1)),e=R(this,s,1),this.precision=r,this.rounding=t,i=n.s<0?i.minus(e):i.plus(e)):i=this.atan(A(n,e,s,1)):(i=R(this,s,1).times(e.s>0?.25:.75)).s=n.s:i=new this(NaN),i}function tn(n){return new this(n).cbrt()}function sn(n){return D(n=new this(n),n.e+1,2)}function on(n,e,i){return new this(n).clamp(e,i)}function un(n){if(!n||"object"!=typeof n)throw Error(f+"Object expected");var e,t,s,o=!0===n.defaults,c=["precision",1,r,"rounding",0,8,"toExpNeg",-i,0,"toExpPos",0,i,"maxE",0,i,"minE",-i,0,"modulo",0,9];for(e=0;e<c.length;e+=3)if(t=c[e],o&&(this[t]=u[t]),void 0!==(s=n[t])){if(!(g(s)===s&&s>=c[e+1]&&s<=c[e+2]))throw Error(a+t+": "+s);this[t]=s}if(t="crypto",o&&(this[t]=u[t]),void 0!==(s=n[t])){if(!0!==s&&!1!==s&&0!==s&&1!==s)throw Error(a+t+": "+s);if(s){if("undefined"==typeof crypto||!crypto||!crypto.getRandomValues&&!crypto.randomBytes)throw Error(d);this[t]=!0}else this[t]=!1}return this}function cn(n){return new this(n).cos()}function fn(n){return new this(n).cosh()}function an(n,e){return new this(n).div(e)}function hn(n){return new this(n).exp()}function dn(n){return D(n=new this(n),n.e+1,3)}function ln(){var n,e,i=new this(0);for(c=!1,n=0;n<arguments.length;)if((e=new this(arguments[n++])).d)i.d&&(i=i.plus(e.times(e)));else{if(e.s)return c=!0,new this(1/0);i=e}return c=!0,i.sqrt()}function gn(n){return n instanceof _n||n&&n.toStringTag===l||!1}function pn(n){return new this(n).ln()}function wn(n,e){return new this(n).log(e)}function mn(n){return new this(n).log(2)}function vn(n){return new this(n).log(10)}function Nn(){return k(this,arguments,-1)}function bn(){return k(this,arguments,1)}function En(n,e){return new this(n).mod(e)}function xn(n,e){return new this(n).mul(e)}function yn(n,e){return new this(n).pow(e)}function Mn(n){var e,i,t,s,o=0,u=new this(1),c=[];if(void 0===n?n=this.precision:q(n,1,r),t=Math.ceil(n/7),this.crypto)if(crypto.getRandomValues)for(e=crypto.getRandomValues(new Uint32Array(t));o<t;)(s=e[o])>=429e7?e[o]=crypto.getRandomValues(new Uint32Array(1))[0]:c[o++]=s%1e7;else{if(!crypto.randomBytes)throw Error(d);for(e=crypto.randomBytes(t*=4);o<t;)(s=e[o]+(e[o+1]<<8)+(e[o+2]<<16)+((127&e[o+3])<<24))>=214e7?crypto.randomBytes(4).copy(e,o):(c.push(s%1e7),o+=4);o=t/4}else for(;o<t;)c[o++]=1e7*Math.random()|0;for(n%=7,(t=c[--o])&&n&&(s=p(10,7-n),c[o]=(t/s|0)*s);0===c[o];o--)c.pop();if(o<0)i=0,c=[0];else{for(i=-1;0===c[0];i-=7)c.shift();for(t=1,s=c[0];s>=10;s/=10)t++;t<7&&(i-=7-t)}return u.e=i,u.d=c,u}function qn(n){return D(n=new this(n),n.e+1,this.rounding)}function On(n){return(n=new this(n)).d?n.d[0]?n.s:0*n.s:n.s||NaN}function Fn(n){return new this(n).sin()}function An(n){return new this(n).sinh()}function Dn(n){return new this(n).sqrt()}function Zn(n,e){return new this(n).sub(e)}function Sn(){var n=0,e=arguments,i=new this(e[n]);for(c=!1;i.s&&++n<e.length;)i=i.plus(e[n]);return c=!0,D(i,this.precision,this.rounding)}function Pn(n){return new this(n).tan()}function Rn(n){return new this(n).tanh()}function Tn(n){return D(n=new this(n),n.e+1,1)}y[Symbol.for("nodejs.util.inspect.custom")]=y.toString,y[Symbol.toStringTag]="Decimal";var _n=y.constructor=function n(e){var i,r,t;function s(n){var e,i,r,t=this;if(!(t instanceof s))return new s(n);if(t.constructor=s,gn(n))return t.s=n.s,void(c?!n.d||n.e>s.maxE?(t.e=NaN,t.d=null):n.e<s.minE?(t.e=0,t.d=[0]):(t.e=n.e,t.d=n.d.slice()):(t.e=n.e,t.d=n.d?n.d.slice():n.d));if("number"===(r=typeof n)){if(0===n)return t.s=1/n<0?-1:1,t.e=0,void(t.d=[0]);if(n<0?(n=-n,t.s=-1):t.s=1,n===~~n&&n<1e7){for(e=0,i=n;i>=10;i/=10)e++;return void(c?e>s.maxE?(t.e=NaN,t.d=null):e<s.minE?(t.e=0,t.d=[0]):(t.e=e,t.d=[n]):(t.e=e,t.d=[n]))}return 0*n!=0?(n||(t.s=NaN),t.e=NaN,void(t.d=null)):B(t,n.toString())}if("string"===r)return 45===(i=n.charCodeAt(0))?(n=n.slice(1),t.s=-1):(43===i&&(n=n.slice(1)),t.s=1),N.test(n)?B(t,n):function(n,e){var i,r,t,s,o,u,f,h,d;if(e.indexOf("_")>-1){if(e=e.replace(/(\d)_(?=\d)/g,"$1"),N.test(e))return B(n,e)}else if("Infinity"===e||"NaN"===e)return+e||(n.s=NaN),n.e=NaN,n.d=null,n;if(m.test(e))i=16,e=e.toLowerCase();else if(w.test(e))i=2;else{if(!v.test(e))throw Error(a+e);i=8}for((s=e.search(/p/i))>0?(f=+e.slice(s+1),e=e.substring(2,s)):e=e.slice(2),o=(s=e.indexOf("."))>=0,r=n.constructor,o&&(s=(u=(e=e.replace(".","")).length)-s,t=L(r,new r(i),s,2*s)),s=d=(h=F(e,i,b)).length-1;0===h[s];--s)h.pop();return s<0?new r(0*n.s):(n.e=S(h,d),n.d=h,c=!1,o&&(n=A(n,t,4*u)),f&&(n=n.times(Math.abs(f)<54?p(2,f):_n.pow(2,f))),c=!0,n)}(t,n);if("bigint"===r)return n<0?(n=-n,t.s=-1):t.s=1,B(t,n.toString());throw Error(a+n)}if(s.prototype=y,s.ROUND_UP=0,s.ROUND_DOWN=1,s.ROUND_CEIL=2,s.ROUND_FLOOR=3,s.ROUND_HALF_UP=4,s.ROUND_HALF_DOWN=5,s.ROUND_HALF_EVEN=6,s.ROUND_HALF_CEIL=7,s.ROUND_HALF_FLOOR=8,s.EUCLID=9,s.config=s.set=un,s.clone=n,s.isDecimal=gn,s.abs=z,s.acos=G,s.acosh=K,s.add=Q,s.asin=X,s.asinh=Y,s.atan=nn,s.atanh=en,s.atan2=rn,s.cbrt=tn,s.ceil=sn,s.clamp=on,s.cos=cn,s.cosh=fn,s.div=an,s.exp=hn,s.floor=dn,s.hypot=ln,s.ln=pn,s.log=wn,s.log10=vn,s.log2=mn,s.max=Nn,s.min=bn,s.mod=En,s.mul=xn,s.pow=yn,s.random=Mn,s.round=qn,s.sign=On,s.sin=Fn,s.sinh=An,s.sqrt=Dn,s.sub=Zn,s.sum=Sn,s.tan=Pn,s.tanh=Rn,s.trunc=Tn,void 0===e&&(e={}),e&&!0!==e.defaults)for(t=["precision","rounding","toExpNeg","toExpPos","maxE","minE","modulo","crypto"],i=0;i<t.length;)e.hasOwnProperty(r=t[i++])||(e[r]=this[r]);return s.config(e),s}(u);s=new _n(s),o=new _n(o);export{_n as D};
