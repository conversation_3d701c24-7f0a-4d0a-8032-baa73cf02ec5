import{a0 as a}from"./index-CIPK2z2P.js";const t=t=>a({url:"/api/user_media/get_statistics",method:"POST",data:{mtk_name:t}});function e(t){return a({url:"/api/user_media/get_list",method:"POST",data:t})}function d(t){return a({url:"/api/user_media/get_zd",method:"POST",data:{mtk_name:t}})}function i(t){return a({url:"/api/user_media/add_edit",method:"POST",data:t})}function r(t){return a({url:"/api/user_media/get_info",method:"POST",data:{id:t}})}function u(t){return a({url:"/api/user_media/del",method:"POST",data:{id:t}})}export{t as a,d as b,r as c,u as d,e as g,i as s};
