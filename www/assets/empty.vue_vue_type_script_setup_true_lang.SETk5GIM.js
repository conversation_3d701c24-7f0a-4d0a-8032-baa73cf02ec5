import{_ as t}from"./uv-empty.Xz_IG7Bm.js";import{d as s,u as e,v as a,o as p,i as m,w as n,e as r,a as o,h as y,b as u}from"./index-BBirLt11.js";const c=s({__name:"empty",props:{state:{type:String,default:"empty"}},setup(s){const{t:c}=e();return(e,i)=>{const d=o(u("uv-empty"),t),l=m;return p(),a(l,null,{default:n(()=>[r(d,{text:"empty"===s.state?y(c)("sys.emptyState"):y(c)("sys.searchEmpty"),icon:"empty"===s.state?y("/assets/emptyState-3dRGES0W.png"):y("/assets/searchState-DqIxdpqS.png")},null,8,["text","icon"])]),_:1})}}});export{c as _};
