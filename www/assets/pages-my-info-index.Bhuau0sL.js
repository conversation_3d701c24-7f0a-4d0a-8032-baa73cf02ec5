import{_ as e}from"./prompt-popup.vue_vue_type_script_setup_true_lang.CHEY8D5W.js";import{_ as a,u as s}from"./config.Dlnj0OY2.js";import{_ as t}from"./navbar.vue_vue_type_script_setup_true_lang.CSl8hyKC.js";import{d as o,k as r,u as n,r as i,c as l,e as u,h as p,w as c,ac as m,i as f,R as g,j as d,o as _,F as v,z as j,v as k,g as w,t as b}from"./index-CIPK2z2P.js";import{l as y}from"./login.DeOnoCbS.js";import{d as h}from"./debounce.Ce2HeGXN.js";import"./uv-icon.UcuauzO0.js";import"./uv-parse.wZPhbfSD.js";import"./uv-popup.ewhZSqs9.js";import"./uv-transition.tIadgx1N.js";import"./uv-status-bar.BkslDeIT.js";const C=o({__name:"index",setup(o){const C=r(),{t:I}=n(),x=i(s),T=i(),F=h(e=>{switch(e.title){case"account":d(e.path??"");break;case"userInfo.email":g({url:"/pages/my/info/email"});break;case"userInfo.password":g({url:"/pages/my/info/password"});break;case"userInfo.improve":g({url:"/pages/my/improve"});break;case"userInfo.promotionCode":g({url:"/pages/common/promotion-code"})}},300,{leading:!0,trailing:!0}),O=()=>{T.value.open()},R=h(async()=>{try{await y(),C.deleteToken()}catch(e){}},300,{leading:!0,trailing:!0});return x.value.forEach(e=>{var a;"language"===e.title&&(e.field=(null==(a=m.find(e=>e.value===C.userLanguage))?void 0:a.label)||"")}),(s,o)=>{const r=t,n=a,i=f,m=e;return _(),l("div",{class:"warp"},[u(r,{bgColor:"transparent",title:p(I)("userInfo.title")},null,8,["title"]),u(i,{class:"p-32"},{default:c(()=>[(_(!0),l(v,null,j(p(x),e=>(_(),k(i,{class:"user-info",key:e.title,onClick:a=>p(F)(e)},{default:c(()=>[u(n,{item:e},null,8,["item"])]),_:2},1032,["onClick"]))),128)),u(i,{class:"logOut w-686 text-blackThree p-32 leading-36 bg-white rounded-20 text-center text-28",onClick:O},{default:c(()=>[w(b(p(I)("userInfo.logOut")),1)]),_:1})]),_:1}),u(m,{ref_key:"warningRef",ref:T,title:`${p(I)("userInfo.logOut")} ?`,content:p(I)("sys.logoutTip"),showIcon:!1,tipType:"login",iconName:"warning-fill",iconColor:"#FF6B35",showCancel:!0,onConfirm:p(R)},null,8,["title","content","onConfirm"])])}}});export{C as default};
