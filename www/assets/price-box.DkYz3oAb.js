import{d as a,c as e,o as t,F as s,z as l,v as n,i as o,w as d,e as c,g as i,t as r,N as x}from"./index-BBirLt11.js";import{_}from"./uv-icon.Dp0oPivN.js";const p=_(a({__name:"price-box",props:{info:{type:Array,default:()=>[]}},setup:a=>(_,p)=>{const u=o;return t(),e("div",{class:"bg-white rounded-30 px-34"},[(t(!0),e(s,null,l(a.info,a=>(t(),n(u,{key:a,class:"py-32 relative box"},{default:d(()=>[c(u,{class:"title max-w-450 text-blackOne text-28 font-semibold leading-40 line-clamp-2"},{default:d(()=>[i(r(a.name),1)]),_:2},1024),c(u,{class:"text-greyOne text-24 mt-10 leading-34"},{default:d(()=>[i(r(a.data_zq.label)+": ",1),x("span",{class:"text-blackTwo"},r(a.data_zq.value),1)]),_:2},1024),c(u,{class:"text-greyOne text-24 mt-10 leading-34"},{default:d(()=>[i(r(a.js_zq.label)+": ",1),x("span",{class:"text-blackTwo"},r(a.js_zq.value),1)]),_:2},1024),c(u,{class:"absolute right-0 top-32"},{default:d(()=>[c(u,{class:"font-din text-48 leading-48 text-orange"},{default:d(()=>[i(r(a.qian),1)]),_:2},1024)]),_:2},1024)]),_:2},1024))),128))])}}),[["__scopeId","data-v-cc39753a"]]);export{p as _};
