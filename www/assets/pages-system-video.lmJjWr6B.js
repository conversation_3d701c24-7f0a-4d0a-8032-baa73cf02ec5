import{_ as e}from"./navbar.vue_vue_type_script_setup_true_lang.DC1OHhyC.js";import{v as t,o as n,i as a,d as i,r as o,am as s,a3 as r,c as l,N as d,x as c,e as h,h as p}from"./index-BBirLt11.js";import{_ as u}from"./uv-icon.Dp0oPivN.js";import"./uv-status-bar.CB0-bOg6.js";import"./debounce.Ce2HeGXN.js";const m={data:()=>({num:"",videoEl:null,loadingEl:null,delayFunc:null,renderProps:{}}),computed:{playerId(){return`DOM_VIDEO_PLAYER_${this.num}`},wrapperId(){return`video-wrapper-${this.num}`}},methods:{isApple(){const e=navigator.userAgent.toLowerCase();return-1!==e.indexOf("iphone")||-1!==e.indexOf("ipad")},async initVideoPlayer(e){if(this.delayFunc=null,await this.$nextTick(),!e)return;if(this.videoEl)return!this.isApple()&&this.loadingEl&&(this.loadingEl.style.display="block"),void(this.videoEl.src=e);const t=document.createElement("video");this.videoEl=t,this.listenVideoEvent();const{autoplay:n,muted:a,controls:i,controlsList:o,loop:s,playbackRate:r,objectFit:l,poster:d,preload:c}=this.renderProps;t.src=e,t.autoplay=n,t.controls=i,t.loop=s,t.muted=a,t.playbackRate=r,t.id=this.playerId,t.setAttribute("preload",c),t.setAttribute("playsinline",!0),t.setAttribute("webkit-playsinline",!0),o&&t.setAttribute("controlslist",o),t.setAttribute("disablePictureInPicture",!0),t.style.objectFit=l,d&&(t.poster=d),t.style.width="100%",t.style.height="100%";const h=document.getElementById(this.wrapperId);h.insertBefore(t,h.firstChild),this.createTrack(),this.createLoading()},createTrack(){this.videoEl.querySelectorAll("track").forEach(e=>{this.videoEl.removeChild(e)});const{trackList:e}=this.renderProps;e.length&&e.forEach(e=>{const{src:t,kind:n,label:a,srclang:i,default:o}=e,s=document.createElement("track");s.kind=n,s.label=a,s.srclang=i,s.default=o,s.src=t,this.videoEl.appendChild(s)})},createLoading(){const{isLoading:e}=this.renderProps;if(!this.isApple()&&e){const e=document.createElement("div");this.loadingEl=e,e.className="loading-wrapper",e.style.position="absolute",e.style.top="0",e.style.left="0",e.style.zIndex="1",e.style.width="100%",e.style.height="100%",e.style.backgroundColor="black",document.getElementById(this.wrapperId).appendChild(e);const t=document.createElement("div");t.className="loading",t.style.zIndex="2",t.style.position="absolute",t.style.top="50%",t.style.left="50%",t.style.marginTop="-15px",t.style.marginLeft="-15px",t.style.width="30px",t.style.height="30px",t.style.border="2px solid #FFF",t.style.borderTopColor="rgba(255, 255, 255, 0.2)",t.style.borderRightColor="rgba(255, 255, 255, 0.2)",t.style.borderBottomColor="rgba(255, 255, 255, 0.2)",t.style.borderRadius="100%",t.style.animation="circle infinite 0.75s linear",e.appendChild(t);const n=document.createElement("style"),a="\n          @keyframes circle {\n            0% {\n              transform: rotate(0);\n            }\n            100% {\n              transform: rotate(360deg);\n            }\n          }\n        ";n.type="text/css",n.styleSheet?n.styleSheet.cssText=a:n.appendChild(document.createTextNode(a)),document.head.appendChild(n)}},listenVideoEvent(){const e=()=>{this.$ownerInstance.callMethod("eventEmit",{event:"play"}),this.$ownerInstance.callMethod("setViewData",{key:"playing",value:!0}),this.loadingEl&&(this.loadingEl.style.display="none")};this.videoEl.removeEventListener("play",e),this.videoEl.addEventListener("play",e);const t=()=>{this.$ownerInstance.callMethod("eventEmit",{event:"pause"}),this.$ownerInstance.callMethod("setViewData",{key:"playing",value:!1})};this.videoEl.removeEventListener("pause",t),this.videoEl.addEventListener("pause",t);const n=()=>{this.$ownerInstance.callMethod("eventEmit",{event:"ended"}),this.$ownerInstance.callMethod("resetEventCommand")};this.videoEl.removeEventListener("ended",n),this.videoEl.addEventListener("ended",n);const a=()=>{this.$ownerInstance.callMethod("eventEmit",{event:"canplay"}),this.execDelayFunc()};this.videoEl.removeEventListener("canplay",a),this.videoEl.addEventListener("canplay",a);const i=e=>{this.loadingEl&&(this.loadingEl.style.display="block"),this.$ownerInstance.callMethod("eventEmit",{event:"error",data:e})};this.videoEl.removeEventListener("error",i),this.videoEl.addEventListener("error",i);const o=()=>{this.$ownerInstance.callMethod("eventEmit",{event:"loadedmetadata"});const e=this.videoEl.duration;this.$ownerInstance.callMethod("eventEmit",{event:"durationchange",data:e}),this.$ownerInstance.callMethod("setViewData",{key:"duration",value:e}),this.loadFirstFrame()};this.videoEl.removeEventListener("loadedmetadata",o),this.videoEl.addEventListener("loadedmetadata",o);const s=e=>{const t=e.target.currentTime;this.$ownerInstance.callMethod("eventEmit",{event:"timeupdate",data:t}),this.$ownerInstance.callMethod("setViewData",{key:"currentTime",value:t})};this.videoEl.removeEventListener("timeupdate",s),this.videoEl.addEventListener("timeupdate",s);const r=e=>{const t=e.target.playbackRate;this.$ownerInstance.callMethod("eventEmit",{event:"ratechange",data:t})};if(this.videoEl.removeEventListener("ratechange",r),this.videoEl.addEventListener("ratechange",r),this.isApple()){const e=()=>{let e=null;e="fullscreen"===this.videoEl.webkitPresentationMode,this.$ownerInstance.callMethod("eventEmit",{event:"fullscreenchange",data:e})};this.videoEl.removeEventListener("webkitpresentationmodechanged",e),this.videoEl.addEventListener("webkitpresentationmodechanged",e)}else{const e=()=>{let e=null;e=!!document.fullscreenElement,this.$ownerInstance.callMethod("eventEmit",{event:"fullscreenchange",data:e})};document.removeEventListener("fullscreenchange",e),document.addEventListener("fullscreenchange",e)}},loadFirstFrame(){let{autoplay:e,poster:t,muted:n}=this.renderProps;if(this.isApple()){if(t)return;this.videoEl.play(),e||this.videoEl.pause()}else{if(t)return;this.videoEl.muted=!0,setTimeout(()=>{this.videoEl.play(),this.videoEl.muted=n,e||setTimeout(()=>{this.videoEl.pause()},100)},10)}},triggerCommand(e){e&&(this.$ownerInstance.callMethod("resetEventCommand"),this.videoEl&&this.videoEl[e]())},triggerFunc(e){const{name:t,params:n}=e||{};t&&(this[t](n),this.$ownerInstance.callMethod("resetFunc"))},removeHandler(){this.videoEl&&(this.videoEl.pause(),this.videoEl.src="",this.$ownerInstance.callMethod("setViewData",{key:"videoSrc",value:""}),this.videoEl.load())},fullScreenHandler(){this.isApple()?this.videoEl.webkitEnterFullscreen():this.videoEl.requestFullscreen()},toSeekHandler({sec:e,isDelay:t}){const n=()=>{this.videoEl&&(this.videoEl.currentTime=e)};t?this.delayFunc=n:n()},execDelayFunc(){this.delayFunc&&this.delayFunc(),this.delayFunc=null},viewportChange(e){this.renderProps=e;const{autoplay:t,muted:n,controls:a,loop:i,playbackRate:o}=e;this.videoEl&&(this.videoEl.autoplay=t,this.videoEl.controls=a,this.videoEl.loop=i,this.videoEl.muted=n,this.videoEl.playbackRate=o,this.createTrack())},randomNumChange(e){this.num=e}}},v=e=>{e.$renderjs||(e.$renderjs=[]),e.$renderjs.push("domVideoPlayer"),e.mixins||(e.mixins=[]),e.mixins.push({beforeCreate(){this.domVideoPlayer=this},mounted(){this.$ownerInstance=this.$gcd(this,!0)}}),e.mixins.push(m)},y={props:{src:{type:String,default:""},autoplay:{type:Boolean,default:!1},loop:{type:Boolean,default:!1},controls:{type:Boolean,default:!1},controlsList:{type:String,default:""},objectFit:{type:String,default:"contain"},muted:{type:Boolean,default:!1},playbackRate:{type:Number,default:1},isLoading:{type:Boolean,default:!1},poster:{type:String,default:""},preload:{type:String,default:"auto"},trackList:{type:Array,default:()=>[]},isLog:{type:Boolean,default:!0},id:{type:String,default:""}},data:()=>({randomNum:Math.floor(1e8*Math.random()),videoSrc:"",eventCommand:null,renderFunc:{name:null,params:null},currentTime:0,duration:0,playing:!1}),mounted(){this.isLog},watch:{src:{handler(e){e&&setTimeout(()=>{this.videoSrc=e},0)},immediate:!0}},computed:{videoWrapperId(){return`video-wrapper-${this.randomNum}`},viewportProps(){return{autoplay:this.autoplay,muted:this.muted,controls:this.controls,controlsList:this.controlsList,loop:this.loop,objectFit:this.objectFit,poster:this.poster,isLoading:this.isLoading,playbackRate:this.playbackRate,trackList:this.trackList}}},methods:{videoWrapperClick(e){this.$emit("video-click",e)},eventEmit({event:e,data:t}){this.$emit(e,t)},setViewData({key:e,value:t}){e&&this.$set(this,e,t)},resetEventCommand(){this.eventCommand=null},play(){this.eventCommand="play"},pause(){this.eventCommand="pause"},resetFunc(){this.renderFunc={name:null,params:null}},remove(e){this.renderFunc={name:"removeHandler",params:e}},fullScreen(e){this.renderFunc={name:"fullScreenHandler",params:e}},toSeek(e,t=!1){this.renderFunc={name:"toSeekHandler",params:{sec:e,isDelay:t}}}}};v(y);const E=u(y,[["render",function(e,i,o,s,r,l){const d=a;return n(),t(d,{class:"player-wrapper",id:l.videoWrapperId,parentId:o.id,randomNum:r.randomNum,"change:randomNum":e.domVideoPlayer.randomNumChange,viewportProps:l.viewportProps,"change:viewportProps":e.domVideoPlayer.viewportChange,videoSrc:r.videoSrc,"change:videoSrc":e.domVideoPlayer.initVideoPlayer,command:r.eventCommand,"change:command":e.domVideoPlayer.triggerCommand,func:r.renderFunc,"change:func":e.domVideoPlayer.triggerFunc,onClick:l.videoWrapperClick},null,8,["id","parentId","randomNum","change:randomNum","viewportProps","change:viewportProps","videoSrc","change:videoSrc","command","change:command","func","change:func","onClick"])}],["__scopeId","data-v-b2594316"]]),g=u(i({__name:"video",setup(t){const a=o(""),i=o("");return s(()=>{const e=r(),t=e[e.length-1].options||{};t.video&&(a.value=decodeURIComponent(t.video)),t.poster&&(i.value=decodeURIComponent(t.poster))}),(t,o)=>{const s=e;return n(),l("div",{class:"video-page"},[d("div",{class:"head"},[h(s,{leftIconColor:"#FFF",bgColor:"transparent",scrollTextColor:"blackOne",isDarkMode:!0})]),a.value?(n(),l("div",{key:0,class:"video-container"},[h(p(E),{src:a.value,poster:i.value,autoplay:"",fullscreenchange:"",controls:"",objectFit:"contain"},null,8,["src","poster"])])):c("",!0)])}}}),[["__scopeId","data-v-08180ca2"]]);export{g as default};
