import{_ as a}from"./navbar.vue_vue_type_script_setup_true_lang.CSl8hyKC.js";import{_ as s}from"./uv-parse.wZPhbfSD.js";import{d as t,r,J as e,c as n,e as o,v as l,x as u,h as i,w as c,b0 as p,i as v,a as m,b as d,o as _,a1 as b}from"./index-CIPK2z2P.js";import"./uv-icon.UcuauzO0.js";import"./uv-status-bar.BkslDeIT.js";import"./debounce.Ce2HeGXN.js";const f=t({__name:"xy",setup(t){const f=r();return e(a=>{(null==a?void 0:a.mark)&&(async a=>{try{const{data:s}=await p("",a);f.value=s}catch(s){}})(a.mark)}),(t,r)=>{var e;const p=a,j=m(d("uv-parse"),s),k=v;return _(),n("div",null,[o(p,{"bg-color":"transparent",title:(null==(e=i(f))?void 0:e.name)||"",scrollTextColor:"blackOne",isDarkMode:!0},null,8,["title"]),i(f)?(_(),l(k,{key:0,class:"p-32"},{default:c(()=>{var a;return[o(j,{content:i(b)(null==(a=i(f))?void 0:a.info)},null,8,["content"])]}),_:1})):u("",!0)])}}});export{f as default};
