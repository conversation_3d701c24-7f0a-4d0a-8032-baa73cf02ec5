var t,o;import{_ as e}from"./uv-line.CNQanRFo.js";import{a,b as l,v as n,o as i,w as s,x as r,e as d,i as u,s as m,U as c,q as g,g as h,t as p}from"./index-BBirLt11.js";import{_ as f}from"./uv-loading-icon.G79bokgG.js";import{_ as y,m as v,a as S}from"./uv-icon.Dp0oPivN.js";const _=y({name:"uv-loadmore",mixins:[v,S,{props:{status:{type:String,default:"loadmore"},bgColor:{type:String,default:"transparent"},icon:{type:Boolean,default:!0},fontSize:{type:[String,Number],default:14},iconSize:{type:[String,Number],default:16},color:{type:String,default:"#606266"},loadingIcon:{type:String,default:"spinner"},loadmoreText:{type:String,default:"加载更多"},loadingText:{type:String,default:"正在加载..."},nomoreText:{type:String,default:"没有更多了"},isDot:{type:Boolean,default:!1},iconColor:{type:String,default:"#b7b7b7"},marginTop:{type:[String,Number],default:10},marginBottom:{type:[String,Number],default:10},height:{type:[String,Number],default:"auto"},line:{type:Boolean,default:!1},lineColor:{type:String,default:"#E6E8EB"},dashed:{type:Boolean,default:!1},...null==(o=null==(t=uni.$uv)?void 0:t.props)?void 0:o.loadmore}}],data:()=>({dotText:"●"}),computed:{loadTextStyle(){return{color:this.color,fontSize:this.$uv.addUnit(this.fontSize),lineHeight:this.$uv.addUnit(this.fontSize),backgroundColor:this.bgColor}},showText(){let t="";return t="loadmore"==this.status?this.loadmoreText:"loading"==this.status?this.loadingText:"nomore"==this.status&&this.isDot?this.dotText:this.nomoreText,t}},methods:{loadMore(){"loadmore"==this.status&&this.$emit("loadmore")}}},[["render",function(t,o,y,v,S,_){const x=a(l("uv-line"),e),T=a(l("uv-loading-icon"),f),b=u,C=c;return i(),n(b,{class:"uv-loadmore",style:g([{backgroundColor:t.bgColor,marginBottom:t.$uv.addUnit(t.marginBottom),marginTop:t.$uv.addUnit(t.marginTop),height:t.$uv.addUnit(t.height)},t.$uv.addStyle(t.customStyle)])},{default:s(()=>[t.line?(i(),n(x,{key:0,length:"140rpx",color:t.lineColor,hairline:!1,dashed:t.dashed},null,8,["color","dashed"])):r("",!0),d(b,{class:m(["loadmore"==t.status||"nomore"==t.status?"uv-more":"","uv-loadmore__content"])},{default:s(()=>["loading"===t.status&&t.icon?(i(),n(b,{key:0,class:"uv-loadmore__content__icon-wrap"},{default:s(()=>[d(T,{color:t.iconColor,size:t.iconSize,mode:t.loadingIcon},null,8,["color","size","mode"])]),_:1})):r("",!0),d(C,{class:m(["uv-line-1",["nomore"==t.status&&1==t.isDot?"uv-loadmore__content__dot-text":"uv-loadmore__content__text"]]),style:g([_.loadTextStyle]),onClick:_.loadMore},{default:s(()=>[h(p(_.showText),1)]),_:1},8,["style","class","onClick"])]),_:1},8,["class"]),t.line?(i(),n(x,{key:1,length:"140rpx",color:t.lineColor,hairline:!1,dashed:t.dashed},null,8,["color","dashed"])):r("",!0)]),_:1},8,["style"])}],["__scopeId","data-v-53282763"]]);export{_};
