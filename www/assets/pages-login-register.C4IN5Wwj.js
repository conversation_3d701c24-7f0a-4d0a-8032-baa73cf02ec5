import{_ as e}from"./xyPopup.vue_vue_type_script_setup_true_lang.B8-LxnmC.js";import{a,_ as l}from"./stepsIcon.3mQNdnVR.js";import{_ as t}from"./ali-popup.Dsc3VcwQ.js";import{_ as o}from"./navbar.vue_vue_type_script_setup_true_lang.CSl8hyKC.js";import{d as s,k as r,u as n,r as i,D as d,ak as u,am as c,H as p,aw as m,J as b,c as v,e as f,h as x,w as g,F as y,i as _,ae as k,ai as h,a2 as w,ax as T,a as F,b as j,o as O,v as V,x as P,z as C,U as I,s as z,g as U,t as B,f as S,ab as G,N as R,j as $,af as J}from"./index-CIPK2z2P.js";import{_ as L,a as D}from"./uv-steps.qNI4wMcC.js";import{_ as E}from"./uv-input.CdM7e6ra.js";import{g as N}from"./login.DeOnoCbS.js";import{V as A}from"./system.DZjCJFRG.js";import{d as H}from"./debounce.Ce2HeGXN.js";import{_ as K}from"./uv-icon.UcuauzO0.js";import"./uv-popup.ewhZSqs9.js";import"./uv-transition.tIadgx1N.js";import"./uv-status-bar.BkslDeIT.js";import"./uv-text.DpB-FlH4.js";const q=K(s({__name:"register",setup(s){const K=r(),{t:q}=n(),M=i(),Q=i([q("login.setTitle"),q("login.setTitle2")]),W=i(),X=i(0),Y=i(),Z=i(!1),ee=i(!1),ae=d({email:"",nickname:"",password:"",yzm:"",invite_code:""}),le=d({certifyId:"",deviceToken:"",data:""}),te=i(!1),oe=i(q("login.codeText")),se=i(null),re=i(!0),ne=i(!1),ie=i(),de=i(!1),ue=i(!1),ce=i(!1),pe=()=>{se.value&&(clearInterval(se.value),se.value=null),te.value=!1,oe.value=q("login.codeText")};u(()=>({...ae}),e=>{ue.value=X.value?!(!e.nickname||!e.password):!!(e.email&&e.yzm&&e.invite_code)},{deep:!0});const me=H(()=>{ae.email?te.value||ie.value.open((new Date).getTime()):k(q("sys.enter")+q("login.emailPlaceholder"))},500,{leading:!0,trailing:!0}),be=()=>{ee.value=!0,ge()},ve=e=>{const a=r();switch(e){case"service":$(`/pages/system/xy?mark=${a.appSystem.xy_mask[0]}`);break;case"privacy":$(`/pages/system/xy?mark=${a.appSystem.xy_mask[1]}`)}},fe=async e=>{if(e.certifyId&&e.deviceToken&&e.data){if(Object.assign(le,{certifyId:e.certifyId,deviceToken:e.deviceToken,data:e.data}),!te.value)try{const{msg:e}=await J({lx:A.REGISTER,email:ae.email,...le});k(e??""),ye()}catch(a){return void pe()}}else k(q("sys.loseEfficacy"))},xe=H(e=>{if(!e)return X.value--,void(ue.value=!0);if(X.value){if(!(ae.nickname&&ae.password&&ae.yzm&&ae.invite_code&&ae.email))return;if(!ee.value)return void M.value.open();ge()}else{if(!ae.email||!ae.invite_code||!ae.yzm)return;X.value++,ae.password&&ae.nickname||(ue.value=!1)}},300,{leading:!0,trailing:!0}),ge=H(async()=>{try{const e=JSON.parse(JSON.stringify(ae));e.password=h(ae.password);const{msg:a,data:l}=await N(e);k(a??""),Promise.allSettled([K.setToken(l.token),w()]).then(()=>{T()})}catch(e){return void pe()}},300,{leading:!0,trailing:!0}),ye=()=>{pe();let e=60;te.value=!0,se.value=setInterval(()=>{e--,oe.value=`${q("login.codeText")} ${e}s`,e<=0&&pe()},1e3)};return c(()=>{p(()=>{Y.value=`trigger_${(new Date).getTime()}`})}),m(()=>{pe(),ie.value&&(ie.value.isAli=!1)}),b(e=>{(null==e?void 0:e.invite_code)&&(Z.value=!0,ae.invite_code=e.invite_code)}),(s,r)=>{const n=o,i=S,d=I,u=F(j("uv-steps-item"),L),c=F(j("uv-steps"),D),p=_,m=F(j("uv-input"),E),b=t,k=a,h=e;return O(),v(y,null,[f(n,{"bg-color":"transparent",isLeft:!x(Z)},null,8,["isLeft"]),f(p,{class:"warp pl-60 pr-60"},{default:g(()=>[f(p,{class:"flex justify-center w-450 m-auto"},{default:g(()=>[f(c,{current:x(X),dot:""},{default:g(()=>[(O(!0),v(y,null,C(x(Q),(e,a)=>(O(),V(u,{key:e},{icon:g(()=>[a<=x(X)?(O(),V(i,{key:0,src:l,class:"w-24 h-24",mode:"aspectFill"})):P("",!0)]),title:g(()=>[f(d,{class:z(["text-24 leading-32",a<=x(X)?"text-blueOne":"text-blackTwo"])},{default:g(()=>[U(B(e),1)]),_:2},1032,["class"])]),_:2},1024))),128))]),_:1},8,["current"])]),_:1}),f(p,{class:"text-blackOne text-48 leading-60 mb-20 mt-60"},{default:g(()=>[U(B(x(q)("login.register")),1)]),_:1}),f(p,{class:"text-blackTwo text-26 leading-36"},{default:g(()=>[U(B(x(q)("login.registerTip")),1)]),_:1}),f(p,{class:"form mt-40"},{default:g(()=>[x(X)?(O(),V(p,{key:1,class:z([x(ce)?"border-blueOne":"border-borderGray","form-item h-100 rounded-20 relative border pl-30 pr-30 flex items-center box-border mb-24"])},{default:g(()=>[f(m,{placeholder:x(q)("login.namePlaceholder"),border:"none",modelValue:x(ae).nickname,"onUpdate:modelValue":r[3]||(r[3]=e=>x(ae).nickname=e),onFocus:r[4]||(r[4]=e=>ce.value=!0),onBlur:r[5]||(r[5]=e=>ce.value=!1)},null,8,["placeholder","modelValue"]),x(ae).nickname||x(ce)?(O(),V(p,{key:0,class:z([x(ce)?"text-blueOne":"text-blackTwo","tips absolute text-22 leading-30 pl-8 pr-8 -top-15 left-20 bg-[#F6F7F9]"])},{default:g(()=>[U(B(x(q)("login.namePlaceholder")),1)]),_:1},8,["class"])):P("",!0)]),_:1},8,["class"])):(O(),V(p,{key:0,class:z([x(W)?"border-blueOne":"border-borderGray","form-item h-100 relative rounded-20 border pl-30 pr-30 flex items-center box-border mb-24"])},{default:g(()=>[f(m,{placeholder:x(q)("login.emailPlaceholder"),border:"none",modelValue:x(ae).email,"onUpdate:modelValue":r[0]||(r[0]=e=>x(ae).email=e),onFocus:r[1]||(r[1]=e=>W.value=!0),onBlur:r[2]||(r[2]=e=>W.value=!1)},null,8,["placeholder","modelValue"]),x(ae).email||x(W)?(O(),V(p,{key:0,class:z([x(W)?"text-blueOne":"text-blackTwo","tips absolute text-22 leading-30 pl-8 pr-8 -top-15 left-20 bg-[#F6F7F9]"])},{default:g(()=>[U(B(x(q)("login.emailPlaceholder")),1)]),_:1},8,["class"])):P("",!0)]),_:1},8,["class"])),x(X)?(O(),V(p,{key:2,class:z([x(ne)?"border-blueOne":"border-borderGray","form-item h-100 rounded-20 relative border pl-30 pr-30 flex items-center box-border mb-24"])},{default:g(()=>[f(m,{placeholder:x(q)("login.passwordPlaceholder"),border:"none",type:x(re)?"password":"text",modelValue:x(ae).password,"onUpdate:modelValue":r[6]||(r[6]=e=>x(ae).password=e),onFocus:r[7]||(r[7]=e=>ne.value=!0),onBlur:r[8]||(r[8]=e=>ne.value=!1)},null,8,["placeholder","type","modelValue"]),f(d,{class:z(["iconfont !text-44",x(re)?"icon-a-kejianoff":"icon-a-kejianon"]),onClick:r[9]||(r[9]=e=>re.value=!x(re))},null,8,["class"]),x(ae).password||x(ne)?(O(),V(p,{key:0,class:z([x(ne)?"text-blueOne":"text-blackTwo","tips absolute text-22 leading-30 pl-8 pr-8 -top-15 left-20 bg-[#F6F7F9]"])},{default:g(()=>[U(B(x(q)("login.passwordPlaceholder")),1)]),_:1},8,["class"])):P("",!0)]),_:1},8,["class"])):(O(),V(p,{key:3,class:z([x(ne)?"border-blueOne":"border-borderGray","form-item relative h-100 rounded-20 border pl-30 pr-30 flex items-center box-border mb-24"])},{default:g(()=>[f(m,{placeholder:x(q)("login.code"),border:"none",maxlength:"6",modelValue:x(ae).yzm,"onUpdate:modelValue":r[10]||(r[10]=e=>x(ae).yzm=e),onFocus:r[11]||(r[11]=e=>ne.value=!0),onBlur:r[12]||(r[12]=e=>ne.value=!1)},null,8,["placeholder","modelValue"]),f(p,{class:"w-216 h-full absolute right-0 flex justify-center items-center"},{default:g(()=>[f(p,{class:z(["height-36 text-24 Code relative w-216 text-center border-l",x(te)?"text-blueTwo":"text-blueOne"]),onClick:r[13]||(r[13]=e=>x(me)())},{default:g(()=>[U(B(x(oe)),1)]),_:1},8,["class"])]),_:1}),x(ae).yzm||x(ne)?(O(),V(p,{key:0,class:z([x(ne)?"text-blueOne":"text-blackTwo","tips absolute text-22 text-blackTwo leading-30 pl-8 pr-8 -top-15 left-20 bg-[#F6F7F9]"])},{default:g(()=>[U(B(x(q)("login.code")),1)]),_:1},8,["class"])):P("",!0)]),_:1},8,["class"])),x(X)?P("",!0):(O(),V(p,{key:4,class:z([x(de)?"border-blueOne":"border-borderGray","form-item h-100 rounded-20 relative border pl-30 pr-30 flex items-center box-border mb-24"])},{default:g(()=>[f(m,{placeholder:x(q)("login.verificationCodePlactholder"),border:"none",type:"number",disabled:x(Z),modelValue:x(ae).invite_code,"onUpdate:modelValue":r[14]||(r[14]=e=>x(ae).invite_code=e),onFocus:r[15]||(r[15]=e=>de.value=!0),onBlur:r[16]||(r[16]=e=>de.value=!1)},null,8,["placeholder","disabled","modelValue"]),x(ae).invite_code||x(de)?(O(),V(p,{key:0,class:z([x(de)?"text-blueOne":"text-blackTwo","tips absolute text-22 text-blackTwo leading-30 pl-8 pr-8 -top-15 left-20 bg-[#F6F7F9]"])},{default:g(()=>[U(B(x(q)("login.verificationCodePlactholder")),1)]),_:1},8,["class"])):P("",!0)]),_:1},8,["class"]))]),_:1}),x(X)?(O(),V(p,{key:0,class:"text-blackTwo mt-4 flex"},{default:g(()=>[f(d,{class:z(["iconfont !text-32 !text-blueOne",x(ee)?"icon-a-Property1xieyigouxuan":"icon-a-Property1xieyi"]),onClick:r[17]||(r[17]=e=>ee.value=!x(ee))},null,8,["class"]),f(p,{class:"text-26 mt-4 ml-12 text-blackTwo",onClick:r[20]||(r[20]=G(e=>ee.value=!x(ee),["stop"]))},{default:g(()=>[R("span",null,B(x(q)("agreement.agree")),1),R("span",{class:"text-blueOne",onClick:r[18]||(r[18]=G(e=>ve("service"),["stop"]))},B(x(q)("agreement.service")),1),U(" "+B(x(q)("agreement.and"))+" ",1),R("span",{class:"text-blueOne",onClick:r[19]||(r[19]=G(e=>ve("privacy"),["stop"]))},B(x(q)("agreement.privacy")),1),U(". ")]),_:1})]),_:1})):P("",!0),f(b,{ref_key:"aliPopupRef",ref:ie,onCallBack:fe,code:x(Y)},null,8,["code"])]),_:1}),f(k,{current:x(X),isLogin:x(ue),onUpdate:x(xe)},null,8,["current","isLogin","onUpdate"]),f(h,{ref_key:"xyPopupRef",ref:M,onCallBack:be},null,512)],64)}}}),[["__scopeId","data-v-f4086819"]]);export{q as default};
