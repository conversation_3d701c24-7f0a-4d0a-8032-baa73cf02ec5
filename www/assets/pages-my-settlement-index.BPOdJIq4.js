import{_ as e}from"./empty.vue_vue_type_script_setup_true_lang.SETk5GIM.js";import{_ as t}from"./settlement-item.vue_vue_type_script_setup_true_lang.CqwaaTz7.js";import{_ as a}from"./navbar.vue_vue_type_script_setup_true_lang.DC1OHhyC.js";import{d as s,u as o,r as l,D as r,E as n,J as u,v as i,w as m,a5 as p,i as c,a as v,b as d,o as _,e as g,h as y,x as f,c as x,F as b,z as j}from"./index-BBirLt11.js";import{_ as h}from"./uv-load-more.SNzw0348.js";import{_ as k}from"./uv-skeletons.BJIINbO6.js";import{_ as T}from"./uv-picker.CEmVlaLq.js";import{D as w}from"./decimal.B1oHnkff.js";import"./uv-empty.Xz_IG7Bm.js";import"./uv-icon.Dp0oPivN.js";import"./uv-status-bar.CB0-bOg6.js";import"./debounce.Ce2HeGXN.js";import"./uv-line.CNQanRFo.js";import"./uv-loading-icon.G79bokgG.js";import"./uv-popup.BGprV-fU.js";import"./uv-transition.B3tXKydv.js";const C=[{type:"line",num:4,style:"width: 686rpx;marginTop: 20rpx;height: 174rpx;marginLeft:32rpx"}],O=s({__name:"index",setup(s){const{t:O}=o(),S=l(!0),D=l(),E=l(0),z=l([]),F=l(0),R=r({zt:0,page:1,limit:10}),J=l(),L=l([{name:O("sys.reviewStatusObj.all"),value:0},{name:O("sys.reviewStatusObj.pending"),value:1},{name:O("sys.reviewStatusObj.passed"),value:3},{name:O("sys.reviewStatusObj.rejected"),value:2}]),N=()=>{J.value.open()},A=e=>{const t=e.value[0];R.zt=t.value,S.value=!0,R.page=1,K()},K=async()=>{try{const{data:e,count:t}=await p(R);1===R.page?z.value=e||[]:z.value=z.value.concat(e||[]),F.value=t||0,R.limit*R.page>=F.value&&(D.value="nomore")}catch(e){}finally{S.value=!1}};return n(()=>{if(F.value<=w(R.page*R.limit).toNumber())return D.value="nomore";R.page++,K(),D.value="loading"}),u(()=>{K()}),(s,o)=>{const l=a,r=t,n=c,u=v(d("uv-load-more"),h),p=e,w=v(d("uv-skeletons"),k),F=v(d("uv-picker"),T);return _(),i(n,null,{default:m(()=>[g(l,{"bg-color":"transparent",title:y(O)("wallet.settlementRecords"),scrollTextColor:"blackOne",isDarkMode:!0,"right-text":y(O)("sys.filter"),rightTextColor:y(E)?"blueOne":"blackOne","right-icon":"shaixuanloudou","icon-color":y(E)?"rgb(1, 101, 255)":"",onRightClick:N},null,8,["title","right-text","rightTextColor","icon-color"]),g(n,{class:"py-32"},{default:m(()=>[g(w,{loading:y(S),skeleton:y(C)},{default:m(()=>[g(n,{class:"px-32 w-686 bg-white rounded-30 m-auto"},{default:m(()=>[(_(!0),x(b,null,j(y(z),e=>(_(),i(n,{class:"py-28 border-b border-b-[#E6E6E6] border-dashed last:border-b-0",key:e.id},{default:m(()=>[g(r,{item:e,columns:y(L)},null,8,["item","columns"])]),_:2},1024))),128)),y(z).length&&y(D)?(_(),i(u,{key:0,status:y(D),class:"p-32",loadingText:y(O)("sys.loading"),nomoreText:y(O)("sys.noData"),onLoadmore:K},null,8,["status","loadingText","nomoreText"])):f("",!0)]),_:1}),y(S)||y(z).length?f("",!0):(_(),i(p,{key:0,class:"mt-[30%]"}))]),_:1},8,["loading","skeleton"])]),_:1}),g(F,{ref_key:"picker",ref:J,keyName:"name",columns:[y(L)],confirmText:y(O)("sys.sure"),cancelText:y(O)("sys.cancel"),confirmColor:"#0165FF",cancelColor:"#727A86",onConfirm:A},null,8,["columns","confirmText","cancelText"])]),_:1})}}});export{O as default};
