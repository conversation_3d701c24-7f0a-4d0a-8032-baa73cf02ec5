import{d as s,u as a,l as e,r as t,c as l,e as r,q as c,h as o,w as i,i as n,a as u,b as f,o as h,N as p,f as d,j as m,t as _}from"./index-CIPK2z2P.js";import{_ as x}from"./uv-search.CD68DrLn.js";import{_ as v}from"./search.CpVgtt1a.js";import{_ as g}from"./uv-icon.UcuauzO0.js";const j=g(s({__name:"detailsSearch",setup(s){var g,j;const{t:b}=a(),w=e(),y=t(w.statusBarHeight||0),k=t();return null==(j=null==(g=k.value)?void 0:g.focus)||j.call(g),(s,a)=>{const e=n,t=d,g=u(f("uv-search"),x);return h(),l("div",null,[r(e,{class:"status-bar",style:c({height:o(y)+"px"})},null,8,["style"]),r(e,{class:"p-32 flex justify-between items-center"},{default:i(()=>[r(g,{inputAlign:"left",bgColor:"#fff",ref_key:"searchRef",ref:k,height:"88rpx",focus:!0,autofocus:"",placeholder:o(b)("search.placeholder"),showAction:!1},{prefix:i(()=>[r(t,{class:"w-48 h-48",src:v,mode:"aspectFill"})]),_:1},8,["placeholder"]),p("span",{class:"ml-10",onClick:a[0]||(a[0]=s=>o(m)("/pages/index/index","switchTab"))},_(o(b)("search.actionText")),1)]),_:1}),r(e)])}}}),[["__scopeId","data-v-1c51f60e"]]);export{j as default};
