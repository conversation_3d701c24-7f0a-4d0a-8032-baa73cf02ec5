import{_ as e}from"./xyPopup.vue_vue_type_script_setup_true_lang.B8-LxnmC.js";import{_ as a}from"./ali-popup.Dsc3VcwQ.js";import{d as l,u as t,D as o,k as s,r,ak as n,am as i,aw as c,c as u,e as d,w as p,F as m,i as f,ae as v,a as x,b as g,o as b,g as y,t as k,h as _,v as h,s as w,U as j,ab as T,N as C,j as O,af as I,ai as P,a2 as V,ax as S}from"./index-CIPK2z2P.js";import{_ as B}from"./uv-tabs.CWR5nOXF.js";import{_ as z}from"./uv-input.CdM7e6ra.js";import{V as G}from"./system.DZjCJFRG.js";import{a as $}from"./login.DeOnoCbS.js";import{d as F}from"./debounce.Ce2HeGXN.js";import{_ as N}from"./uv-icon.UcuauzO0.js";import"./uv-popup.ewhZSqs9.js";import"./uv-transition.tIadgx1N.js";import"./uv-status-bar.BkslDeIT.js";const U=N(l({__name:"index",setup(l){const{t:N}=t(),U=o({login_type:1}),A=s(),J=r(),R=r(),D=r(),W=r(!1),q=r(0),E=r(N("login.codeText")),K=r(!1),L=r(null),H=r(!0),M=r(!1),Q=r(!1),X=r(!1),Y=r(!1),Z=[{name:N("login.way2"),value:"phone"},{name:N("login.way1"),value:"email"}],ee=()=>{L.value&&(clearInterval(L.value),L.value=null),K.value=!1,E.value=N("login.codeText")};n(()=>U,()=>{ie()},{deep:!0});const ae=F(()=>{U.email?K.value||(J.value.open(),J.value.activateCaptcha()):v(N("sys.enter")+N("login.emailPlaceholder"))},500,{leading:!0,trailing:!0}),le=()=>{J.value&&J.value.isAli&&(J.value.isAli=!1),setTimeout(()=>{O("/pages/login/register")},100)},te=()=>{if(X.value=!0,!q.value)return J.value.open(),void J.value.activateCaptcha();re()},oe=e=>{const a=s();switch(e){case"service":O(`/pages/system/xy?mark=${a.appSystem.xy_mask[0]}`);break;case"privacy":O(`/pages/system/xy?mark=${a.appSystem.xy_mask[1]}`)}},se=async e=>{if(W.value=!1,e.certifyId&&e.deviceToken&&e.data)if(Object.assign(U,{certifyId:e.certifyId,deviceToken:e.deviceToken,data:e.data}),q.value)try{const{msg:e}=await I({lx:G.LOGIN,email:U.email,certifyId:U.certifyId,deviceToken:U.deviceToken,data:U.data});v(e??""),ne()}catch(a){return void ee()}else re();else v(N("sys.loseEfficacy"))},re=async()=>{var e;if(X.value)try{let e=JSON.parse(JSON.stringify(U));U.password&&(e={...U,password:P(U.password)});const{data:a,msg:l}=await $(e);v(l??""),Promise.allSettled([A.setToken(a.token),V()]).then(()=>{S()})}catch(a){return ee(),void(W.value=!1)}else null==(e=R.value)||e.open()},ne=()=>{ee();let e=60;K.value=!0,L.value=setInterval(()=>{e--,E.value=`${N("login.codeText")} ${e}s`,e<=0&&ee()},1e3)},ie=()=>{const e=0===q.value?!!U.email&&!!U.password:!!U.email&&!!U.yzm;Y.value=e},ce=F(()=>{var e;if(Y.value)if(q.value)re();else{if(!U.certifyId)return X.value?(J.value.open(),void J.value.activateCaptcha()):void(null==(e=R.value)||e.open());re()}},500,{leading:!0,trailing:!0});return i(()=>{A.token||(A.userInfo=""),J.value&&setTimeout(()=>{D.value=`trigger_${(new Date).getTime()}`},300)}),c(()=>{ee(),J.value&&(J.value.isAli=!1)}),(l,t)=>{const o=f,s=x(g("uv-tabs"),B),r=j,n=x(g("uv-input"),z),i=a,c=e;return b(),u(m,null,[d(o,{class:"warp flex flex-col h-full"},{default:p(()=>[d(o,{class:"text-blackOne text-60 leading-88 text-center mb-20 mt-104"},{default:p(()=>[y(k(_(N)("login.title")),1)]),_:1}),d(o,{class:"text-blackTwo text-26 leading-36 text-center mb-60"},{default:p(()=>[y(k(_(N)("login.tip"))+k(_(q)?_(N)("login.tip3"):_(N)("login.tip2")),1)]),_:1}),d(o,{class:"flex justify-center"},{default:p(()=>[d(s,{list:Z,current:_(q),activeStyle:{color:"#131726",fontWeight:"bold",fontSize:"28rpx"},inactiveStyle:{color:"#444B56"},lineColor:"#0165ff",lineWidth:"66rpx",onClick:t[0]||(t[0]=e=>{q.value=e.index,_(U).login_type=e.index+1,ie()})},null,8,["current"])]),_:1}),d(o,{class:"form pl-80 pr-80 mt-40"},{default:p(()=>[d(o,{class:w([_(M)?"border-blueOne":"border-borderGray","form-item h-100 rounded-20 border pl-30 pr-30 flex items-center box-border mb-24"])},{default:p(()=>[d(r,{class:"iconfont icon-a-Property1youxiang !text-44 mr-20"}),d(n,{placeholder:_(N)("login.emailPlaceholder"),border:"none",modelValue:_(U).email,"onUpdate:modelValue":t[1]||(t[1]=e=>_(U).email=e),onFocus:t[2]||(t[2]=e=>M.value=!0),onBlur:t[3]||(t[3]=e=>M.value=!1)},null,8,["placeholder","modelValue"]),d(o,{class:"shield-container"},{default:p(()=>[d(o,{class:"shield-wrapper"},{default:p(()=>[d(r,{class:"iconfont icon-anquanbaohuzhong !text-44 h-38 leading-44 text-formGreen"}),d(o,{class:"rotating-line"})]),_:1})]),_:1})]),_:1},8,["class"]),_(q)?(b(),h(o,{key:1,class:w([_(Q)?"border-blueOne":"border-borderGray","form-item relative h-100 rounded-20 border pl-30 pr-30 flex items-center box-border mb-24"])},{default:p(()=>[d(n,{placeholder:_(N)("login.code"),border:"none",maxlength:"6",modelValue:_(U).yzm,"onUpdate:modelValue":t[8]||(t[8]=e=>_(U).yzm=e),onFocus:t[9]||(t[9]=e=>Q.value=!0),onBlur:t[10]||(t[10]=e=>Q.value=!1)},null,8,["placeholder","modelValue"]),d(o,{class:"h-full absolute right-0 flex justify-center items-center"},{default:p(()=>[d(o,{class:w(["height-36 text-24 Code relative pl-30 pr-30 text-center border-l",_(K)?"text-blueTwo":"text-blueOne"]),onClick:t[11]||(t[11]=e=>_(ae)())},{default:p(()=>[y(k(_(E)),1)]),_:1},8,["class"])]),_:1})]),_:1},8,["class"])):(b(),h(o,{key:0,class:w([_(Q)?"border-blueOne":"border-borderGray","form-item h-100 rounded-20 border pl-30 pr-30 flex items-center box-border mb-24"])},{default:p(()=>[d(r,{class:"iconfont icon-a-Property1mima !text-44 mr-20"}),d(n,{placeholder:_(N)("login.passwordPlaceholder"),border:"none",type:_(H)?"password":"text",modelValue:_(U).password,"onUpdate:modelValue":t[4]||(t[4]=e=>_(U).password=e),onFocus:t[5]||(t[5]=e=>Q.value=!0),onBlur:t[6]||(t[6]=e=>Q.value=!1)},null,8,["placeholder","type","modelValue"]),d(r,{class:w(["iconfont !text-44",_(H)?"icon-a-kejianoff":"icon-a-kejianon"]),onClick:t[7]||(t[7]=e=>H.value=!_(H))},null,8,["class"])]),_:1},8,["class"]))]),_:1}),d(o,{class:"text-blackTwo pl-80 pr-80 flex"},{default:p(()=>[d(r,{class:w(["iconfont !text-32 !text-blueOne",_(X)?"icon-a-Property1xieyigouxuan":"icon-a-Property1xieyi"]),onClick:t[12]||(t[12]=e=>X.value=!_(X))},null,8,["class"]),d(o,{class:"text-26 ml-12 mt-4 reounded-32 text-blackTwo",onClick:t[15]||(t[15]=T(e=>X.value=!_(X),["stop"]))},{default:p(()=>[C("span",{class:"rounded-32 black"},k(_(N)("agreement.agree")),1),C("span",{class:"text-blueOne",onClick:t[13]||(t[13]=T(e=>oe("service"),["stop"]))},k(_(N)("agreement.service")),1),y(" "+k(_(N)("agreement.and"))+" ",1),C("span",{class:"text-blueOne",onClick:t[14]||(t[14]=T(e=>oe("privacy"),["stop"]))},k(_(N)("agreement.privacy")),1),y(". ")]),_:1})]),_:1}),d(o,{class:w(["btn mt-60 w-590 py-28 leading-44 rounded-20 bg-blueOne ml-80 mr-80 text-white text-32 flex items-center justify-center",_(Y)?"opacity-100":"opacity-50"]),onClick:t[16]||(t[16]=e=>_(ce)())},{default:p(()=>[y(k(_(N)("login.loginBtn")),1)]),_:1},8,["class"]),d(o,{class:"mt-40 text-center text-blackOne text-28 leading-40",onClick:t[17]||(t[17]=T(e=>_(O)("/pages/index/index","switchTab"),["stop"]))},{default:p(()=>[y(k(_(N)("login.tourist")),1)]),_:1}),d(i,{ref_key:"aliPopupRef",ref:J,onCallBack:se,code:_(D)},null,8,["code"]),d(o,{class:"fixed top-[90%] w-686 ml-32 text-center z-20 flex items-center justify-center text-blackTwo text-28 leading-40 mb-40"},{default:p(()=>[y(k(_(N)("login.prompt"))+" ",1),d(o,{class:"text-blueOne ml-10",onClick:le},{default:p(()=>[y(k(_(N)("login.register")),1)]),_:1})]),_:1})]),_:1}),d(c,{ref_key:"xyPopupRef",ref:R,onCallBack:te},null,512),d(o,{class:"login-bg-corner"})],64)}}}),[["__scopeId","data-v-10c600ce"]]);export{U as default};
