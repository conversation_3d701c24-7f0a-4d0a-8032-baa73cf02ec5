import{d as e,v as t,o as s,w as a,e as l,i as n,g as i,t as m,j as c}from"./index-CIPK2z2P.js";const x=e({__name:"settlement-item",props:{item:{},columns:{}},setup(e){const x=e,f=()=>{c(`/pages/my/settlement/details?id=${x.item.id}`)};return(e,c)=>{const d=n;return s(),t(d,{class:"flex",onClick:f},{default:a(()=>[l(d,{class:"flex-1 flex flex-col justify-between"},{default:a(()=>[l(d,{class:"text-blackOne text-28 leading-40 line-clamp-2 mb-4"},{default:a(()=>[i(m(e.item.txzh_type_name),1)]),_:1}),l(d,{class:"text-blackThree text-24 leading-36"},{default:a(()=>[i(m(e.item.tjsj),1)]),_:1})]),_:1}),l(d,{class:"w-fit ml-20 justify-center flex flex-col"},{default:a(()=>[l(d,{class:"font-din text-40 leading-40 text-right mb-16 text-orange"},{default:a(()=>[i(m(`${e.item.money}`),1)]),_:1}),l(d,{class:"text-blackThree text-24 leading-34 text-right"},{default:a(()=>[i(m(x.columns[x.item.zt??0].name),1)]),_:1})]),_:1})]),_:1})}}});export{x as _};
