const t=[{type:"line",num:4,style:["width:686rpx;marginBottom: 20rpx;height: 88rpx; marginLeft: 32rpx;","width:686rpx;marginBottom: 20rpx;height: 424rpx;marginLeft: 32rpx;","width:238rpx;marginBottom: 20rpx;height: 44rpx;marginLeft: 32rpx;","width:686rpx;marginBottom: 20rpx;height: 88rpx;marginLeft: 32rpx;"]},{type:"flex",children:[{type:"custom",num:2,style:"width:200rpx;height:298rpx;marginLeft: 42rpx;marginRight: 32rpx"},{type:"custom",num:2,style:"width:200rpx;height:298rpx;marginRight: 42rpx"},{type:"custom",num:2,style:"width:200rpx;height:298rpx;"}]},{type:"line",num:1,style:["width:686rpx;marginBottom: 20rpx;height: 108rpx;  marginTop: 20rpx;position: fixed;bottom: 0; left: 50%; transform: translateX(-50%); right: 0; z-index: 100; background-color: #fff; box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1); padding: 20rpx; display: flex; justify-content: space-between; align-items: center;"]}],r=[{type:"line",num:2,style:["width:686rpx;marginBottom: 20rpx;height: 88rpx; marginLeft: 32rpx;","width:686rpx;marginBottom: 20rpx;height: 88rpx;marginLeft: 32rpx;"]},{type:"flex",children:[{type:"custom",num:4,style:"width:200rpx;height:298rpx;marginLeft: 40rpx;marginRight: 32rpx"},{type:"custom",num:4,style:"width:200rpx;height:298rpx;marginRight: 42rpx"},{type:"custom",num:4,style:"width:200rpx;height:298rpx;"}]},{type:"line",num:1,style:["width:686rpx;marginBottom: 20rpx;height: 108rpx; marginLeft: 32rpx; marginTop: 20rpx;position: fixed;bottom: 0; left: 0; right: 0; z-index: 100; background-color: #fff; box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1); padding: 20rpx; display: flex; justify-content: space-between; align-items: center;"]}],p=[{type:"flex",style:"display: flex; justify-content: space-between;  padding: 20rpx; width: 686rpx;",children:[{type:"custom",num:2,style:"width:188rpx;height:64rpx;marginRight: 32rpx"},{type:"custom",num:2,style:"width:188rpx;height:64rpx;marginRight: 32rpx"},{type:"custom",num:1,style:"width:188rpx;height:64rpx;marginRight: 32rpx"}]}],e=[{type:"line",num:1,style:["width:686rpx;marginBottom: 20rpx;height: 88rpx; marginLeft: 32rpx; marginTop: 20rpx;"]},{type:"line",num:1,style:["width:686rpx;marginBottom: 20rpx;height: 688rpx; marginLeft: 32rpx; marginTop: 20rpx;"]}],i=[{type:"flex",num:1,style:"paddingLeft:20rpx;display:flex;flex-direction:column;",children:[{type:"line",num:1,style:"marginTop:32rpx;height:44rpx;width:300rpx;marginBottom: 24rpx;"}]},{type:"line",num:4,style:"width:710rpx;marginBottom: 20rpx;height: 168rpx; marginLeft: 20rpx;"}];export{r as a,e as l,i as m,t as s,p as t};
