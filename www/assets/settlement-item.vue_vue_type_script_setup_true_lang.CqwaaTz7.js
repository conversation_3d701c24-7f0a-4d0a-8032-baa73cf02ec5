import{d as t,v as e,o as s,w as a,e as l,i as n,g as i,t as m,s as c,j as x}from"./index-BBirLt11.js";const r=t({__name:"settlement-item",props:{item:{},columns:{}},setup(t){const r=t,d=()=>{x(`/pages/my/settlement/details?id=${r.item.id}`)};return(t,x)=>{const r=n;return s(),e(r,{class:"flex",onClick:d},{default:a(()=>[l(r,{class:"flex-1 flex flex-col justify-between"},{default:a(()=>[l(r,{class:"text-blackOne text-28 leading-40 line-clamp-2 mb-4"},{default:a(()=>[i(m(t.item.txzh_type_name),1)]),_:1}),l(r,{class:"text-blackThree text-24 leading-36"},{default:a(()=>[i(m(t.item.tjsj),1)]),_:1})]),_:1}),l(r,{class:"w-fit ml-20 justify-center flex flex-col"},{default:a(()=>[l(r,{class:c(["font-din text-40 leading-40 text-right mb-16","3"===String(t.item.zt)?"text-orange":"text-blackOne"])},{default:a(()=>[i(m(("3"===String(t.item.zt)?"+":"-")+t.item.money),1)]),_:1},8,["class"]),l(r,{class:"text-blackThree text-24 leading-34 text-right"},{default:a(()=>{var e;return[i(m(null==(e=t.columns[t.item.zt??0])?void 0:e.name),1)]}),_:1})]),_:1})]),_:1})}}});export{r as _};
