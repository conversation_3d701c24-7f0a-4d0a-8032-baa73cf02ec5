import{_ as e}from"./navbar.vue_vue_type_script_setup_true_lang.DC1OHhyC.js";import{d as t,u as a,r as l,J as s,c as d,e as n,h as c,w as r,an as u,i as x,o as i,v as f,x as o,g as _,t as m,f as b,U as g}from"./index-BBirLt11.js";import"./uv-icon.Dp0oPivN.js";import"./uv-status-bar.CB0-bOg6.js";import"./debounce.Ce2HeGXN.js";const h=t({__name:"details",setup(t){const{t:h}=a(),p=l({}),y=l([{name:h("sys.reviewStatusObj.all"),value:0},{name:h("sys.reviewStatusObj.pending"),value:1},{name:h("sys.reviewStatusObj.passed"),value:3},{name:h("sys.reviewStatusObj.rejected"),value:2}]);return s(e=>{(null==e?void 0:e.id)&&(async e=>{try{const{data:t}=await u(e);p.value=t||{}}catch(t){}})(e.id)}),(t,a)=>{const l=e,s=x,u=b,v=g;return i(),d("div",null,[n(l,{"bg-color":"transparent",title:c(h)("details.tit"),scrollTextColor:"blackOne",isDarkMode:!0},null,8,["title"]),n(s,{class:"p-32"},{default:r(()=>[n(s,{class:"bg-white rounded-30 shadow-md pb-32 px-32"},{default:r(()=>[n(s,{class:"py-40 border-b border-dashed"},{default:r(()=>[n(s,{class:"text-blackTwo leading-40 text-28 text-center"},{default:r(()=>[_(m(c(h)("bankCard.withdrawalAmount")),1)]),_:1}),n(s,{class:"text-blackOne text-60 leading-60 font-din mt-20 text-center"},{default:r(()=>[_(m(c(p).money),1)]),_:1}),"3"!==c(p).zt?(i(),f(s,{key:0,class:"flex mt-40"},{default:r(()=>[n(s,{class:"flex-1 text-blackThree leading-36 text-26"},{default:r(()=>[_(m(c(h)("bankCard.AmountReceived")),1)]),_:1}),n(s,{class:"flex-1 text-right leading-36 text-26"},{default:r(()=>[_(" $"+m(c(p).zs_money),1)]),_:1})]),_:1})):o("",!0),"3"!==c(p).zt?(i(),f(s,{key:1,class:"flex mt-20"},{default:r(()=>[n(s,{class:"flex-1 text-blackThree leading-36 text-26"},{default:r(()=>[_(m(c(h)("bankCard.HandlingFee")),1)]),_:1}),n(s,{class:"flex-1 text-right leading-36 text-26"},{default:r(()=>[_(" $"+m(c(p).sxf),1)]),_:1})]),_:1})):o("",!0)]),_:1}),n(s,{class:"flex mt-40 mb-20"},{default:r(()=>[n(s,{class:"flex-1 text-blackThree leading-36 text-26"},{default:r(()=>[_(m(c(h)("sys.status")),1)]),_:1}),n(s,{class:"flex-1 text-right leading-36 text-26"},{default:r(()=>[_(m(c(y)[c(p).zt??0].name),1)]),_:1})]),_:1}),c(p).fail_reason?(i(),f(s,{key:0,class:"p-28 bg-[#F6F7F9] rounded-20"},{default:r(()=>[_(m(c(p).fail_reason),1)]),_:1})):o("",!0),n(s,{class:"mt-20 flex"},{default:r(()=>[n(s,{class:"flex-1 text-blackThree leading-36 text-26"},{default:r(()=>[_(m(c(h)("sys.time")),1)]),_:1}),n(s,{class:"flex-1 text-right leading-36 text-26"},{default:r(()=>[_(m(c(p).tjsj),1)]),_:1})]),_:1}),n(s,{class:"mt-20 flex"},{default:r(()=>[n(s,{class:"flex-1 text-blackThree leading-36 text-26"},{default:r(()=>[_(m(c(h)("bankCard.paymentMethod")),1)]),_:1}),n(s,{class:"flex-1 text-right leading-36 text-26 flex justify-end"},{default:r(()=>[n(u,{class:"w-36 h-36 mr-8",src:c(p).txzh_type_logo,mode:"scaleToFill"},null,8,["src"]),n(v,{class:"max-w-200 line-clamp-1"},{default:r(()=>[_(m(c(p).txzh_type_name),1)]),_:1})]),_:1})]),_:1}),n(s,{class:"mt-20 flex"},{default:r(()=>[n(s,{class:"flex-1 text-blackThree leading-36 text-26"},{default:r(()=>[_(m(c(h)("tab.account")),1)]),_:1}),n(s,{class:"flex-1 text-right leading-36 text-26"},{default:r(()=>[_(m(c(p).txzh_account),1)]),_:1})]),_:1})]),_:1})]),_:1})])}}});export{h as default};
