import{c as e,o as t,e as a,w as l,i as s,g as i,F as n,z as u,d,u as c,r,m as o,al as f,l as b,am as x,H as m,a as p,b as _,v as g,h,s as y,t as k,j as w,f as F}from"./index-CIPK2z2P.js";import{_ as S}from"./uv-icon.UcuauzO0.js";import{a as T,_ as v}from"./navbar.vue_vue_type_script_setup_true_lang.CSl8hyKC.js";import{_ as j}from"./uv-sticky.CXYYNICz.js";import"./uv-status-bar.BkslDeIT.js";import"./debounce.Ce2HeGXN.js";const O=S({},[["render",function(d,c){const r=s;return t(),e("div",null,[a(r,{class:"p-32 bg-white rounded-30 mb-20"},{default:l(()=>[a(r,{class:"pb-32 border-b border-dashed flex"},{default:l(()=>[a(r,{class:"flex-1 pr-20"},{default:l(()=>[a(r,{class:"line-clamp-2 text-blackOne leading-42 font-semibold text-30 mb-16"},{default:l(()=>[i(" Bilibi Shu ")]),_:1}),a(r,{class:"flex flex-wrap"},{default:l(()=>[a(r,{class:"text-blackThree mb-8 bg-[#F6F7F9] leading-28 text-22 py-4 px-8 rounded-8 mr-8"},{default:l(()=>[i("186***9619")]),_:1}),a(r,{class:"text-orange mb-8 bg-[#FFE9E1] leading-28 text-22 py-4 px-8 rounded-8 mr-8"},{default:l(()=>[i(" PingFang SC-Regular ")]),_:1}),a(r,{class:"mb-8 text-blueOne bg-[#E7EEFF] leading-28 text-22 py-4 px-8 rounded-8 mr-8"},{default:l(()=>[i(" PingFang SC-Regular ")]),_:1})]),_:1})]),_:1}),a(r,{class:""},{default:l(()=>[a(r,{class:"text-right font-din text-40 leading-40"},{default:l(()=>[i("$1,001.50")]),_:1}),a(r,{class:"mt-14 text-blackTwo text-24 leading-36 text-right"},{default:l(()=>[i("total")]),_:1})]),_:1})]),_:1}),a(r,{class:"pt-32"},{default:l(()=>[(t(),e(n,null,u(4,e=>a(r,{class:"mb-20 flex justify-between last:mb-0",key:e},{default:l(()=>[a(r,{class:"text-blackThree leading-36 text-26 pr-20 flex-1"},{default:l(()=>[i("Subordinate Subsidy")]),_:1}),a(r,{class:"text-blackOne w-fit max-w-[30%] leading-36 text-26 text-right"},{default:l(()=>[i("$3232.00")]),_:1})]),_:2},1024)),64))]),_:1})]),_:1})])}]]),B=d({__name:"details-teambox",setup(d){const{t:F}=c(),S=r(0),v=r(!1),B=r(0);let C=!1;o(e=>{C||(C=!0,setTimeout(()=>{C=!1},50),E())});const q=()=>{w("/pages/my/income/detailsSearch")},E=()=>{f().select("#sticky-header").boundingClientRect(e=>{if(e&&!Array.isArray(e)){const t=e,a=(b().statusBarHeight||0)+44,l=(t.top||0)-a;B.value=l,v.value=l<=0}}).exec()};return x(()=>{b(),S.value=0,m(()=>{setTimeout(()=>{E()},100)})}),(d,c)=>{const r=s,o=T,f=p(_("uv-sticky"),j),b=O;return t(),g(r,null,{default:l(()=>[a(f,{"offset-top":h(S),"z-index":99},{default:l(()=>[a(r,{class:y(["py-20 px-32",h(v)?"bg-white shadow-md rounded-10":""]),id:"sticky-header"},{default:l(()=>[a(r,{class:"flex justify-between items-center"},{default:l(()=>[a(r,{class:"font-semibold"},{default:l(()=>[i(k(h(F)("income.teamDate")),1)]),_:1}),a(r,{class:y([h(v)?"bg-[#F6F7F9]":"bg-white","py-8 px-12 rounded-10 text-blackTwo text-24 leading-36 flex items-center"])},{default:l(()=>[a(o,{name:"paixu",size:"24rpx",class:"mr-8"}),i(" Default Sorting")]),_:1},8,["class"])]),_:1}),a(r,{class:"mt-8 mb-24 text-blackThree text-24 leading-36"},{default:l(()=>[i(k(h(F)("income.detailTeamTip")),1)]),_:1}),a(r,{class:y(["rounded-20 py-20 flex items-center justify-center",h(v)?"bg-[#F6F7F9]":"bg-white"]),onClick:q},{default:l(()=>[a(o,{name:"sousuokuangsousuo"}),a(r,{class:"ml-12 text-28 line-clamp-1 text-blackThree leading-36"},{default:l(()=>[i(k(h(F)("income.searchPlaceholder")),1)]),_:1})]),_:1},8,["class"])]),_:1},8,["class"])]),_:1},8,["offset-top"]),a(r,{class:"px-32 pb-20"},{default:l(()=>[(t(),e(n,null,u(4,e=>a(r,{key:e},{default:l(()=>[a(b,{item:e},null,8,["item"])]),_:2},1024)),64))]),_:1})]),_:1})}}});const C=S({},[["render",function(d,c){const r=F,o=s;return t(),g(o,null,{default:l(()=>[a(o,{class:"pt-20 px-32"},{default:l(()=>[a(o,{class:"flex bg-white rounded-30 py-26 px-32 items-center"},{default:l(()=>[a(r,{class:"w-48 h-48 rounded-10 mr-10",src:"/assets/logo-CWdWNtVm.png",mode:"aspectFill"}),a(o,{class:"flex-1 line-clamp-1 text-blackOne leading-40 text-28 font-semibold"},{default:l(()=>[i(" Bilibi ShuqitehuiBilibi ShuqitehuiBilibi ShuqitehuiBilibi ShuqitehuiBilibi Shuqitehui ")]),_:1})]),_:1}),a(o,{class:"px-32 bg-white mt-6 rounded-30"},{default:l(()=>[a(o,{class:"py-32"},{default:l(()=>[a(o,{class:"flex justify-between items-center"},{default:l(()=>[a(o,{class:"text-blackOne leading-36 font-semibold text-28 flex-1 pr-20"},{default:l(()=>[i("Total Income")]),_:1}),a(o,{class:"text-blackOne leading-48 font-din text-48"},{default:l(()=>[i(" $5002.32 ")]),_:1})]),_:1})]),_:1}),(t(),e(n,null,u(6,(e,t)=>a(o,{key:e},{default:l(()=>[a(o,{class:y({"pt-32 border-t border-dashed":t%3==0})},{default:l(()=>[a(o,{class:y(["mb-20 flex justify-between",{"pb-32 !mb-0":(t+1)%3==0||5===t}])},{default:l(()=>[a(o,{class:"text-blackThree leading-36 text-26 pr-20 flex-1"},{default:l(()=>[i("Subordinate Subsidy")]),_:1}),a(o,{class:"text-blackOne w-fit max-w-[30%] leading-36 text-26 text-right"},{default:l(()=>[i("$3232.00")]),_:1})]),_:2},1032,["class"])]),_:2},1032,["class"])]),_:2},1024)),64))]),_:1})]),_:1})]),_:1})}]]),q=d({__name:"details",setup(l){const{t:s}=c();return(l,i)=>{const n=v,u=C,d=B;return t(),e("div",null,[a(n,{"bg-color":"transparent",title:h(s)("navTitle.incomeDetails"),scrollTextColor:"blackOne",isDarkMode:!0},null,8,["title"]),a(u),a(d)])}}});export{q as default};
