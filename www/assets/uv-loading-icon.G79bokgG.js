var t,e;import{v as o,x as r,o as i,w as n,i as a,q as l,s,c as d,F as u,z as c,U as f,g,t as p,a3 as h}from"./index-BBirLt11.js";import{_ as m,m as v,a as y}from"./uv-icon.Dp0oPivN.js";function b(t,e=!0){if((t=String(t).toLowerCase())&&/^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/.test(t)){if(4===t.length){let e="#";for(let o=1;o<4;o+=1)e+=t.slice(o,o+1).concat(t.slice(o,o+1));t=e}const o=[];for(let e=1;e<7;e+=2)o.push(parseInt(`0x${t.slice(e,e+2)}`));return e?`rgb(${o[0]},${o[1]},${o[2]})`:o}if(/^(rgb|RGB)/.test(t)){return t.replace(/(?:\(|\)|rgb|RGB)*/g,"").split(",").map(t=>Number(t))}return t}function S(t){const e=t;if(/^(rgb|RGB)/.test(e)){const t=e.replace(/(?:\(|\)|rgb|RGB)*/g,"").split(",");let o="#";for(let e=0;e<t.length;e++){let r=Number(t[e]).toString(16);r=1==String(r).length?`0${r}`:r,"0"===r&&(r+=r),o+=r}return 7!==o.length&&(o=e),o}if(!/^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/.test(e))return e;{const t=e.replace(/#/,"").split("");if(6===t.length)return e;if(3===t.length){let e="#";for(let o=0;o<t.length;o+=1)e+=t[o]+t[o];return e}}}const $=m({name:"uv-loading-icon",mixins:[v,y,{props:{show:{type:Boolean,default:!0},color:{type:String,default:"#909193"},textColor:{type:String,default:"#909193"},vertical:{type:Boolean,default:!1},mode:{type:String,default:"spinner"},size:{type:[String,Number],default:24},textSize:{type:[String,Number],default:15},textStyle:{type:Object,default:()=>({})},text:{type:[String,Number],default:""},timingFunction:{type:String,default:"linear"},duration:{type:[String,Number],default:1200},inactiveColor:{type:String,default:""},...null==(e=null==(t=uni.$uv)?void 0:t.props)?void 0:e.loadingIcon}}],data:()=>({array12:Array.from({length:12}),aniAngel:360,webviewHide:!1,loading:!1}),computed:{otherBorderColor(){const t=function(t="rgb(0, 0, 0)",e="rgb(255, 255, 255)",o=10){const r=b(t,!1),i=r[0],n=r[1],a=r[2],l=b(e,!1),s=(l[0]-i)/o,d=(l[1]-n)/o,u=(l[2]-a)/o,c=[];for(let f=0;f<o;f++){let r=S(`rgb(${Math.round(s*f+i)},${Math.round(d*f+n)},${Math.round(u*f+a)})`);0===f&&(r=S(t)),f===o-1&&(r=S(e)),c.push(r)}return c}(this.color,"#ffffff",100)[80];return"circle"===this.mode?this.inactiveColor?this.inactiveColor:t:"transparent"}},watch:{show(t){}},mounted(){this.init()},methods:{init(){setTimeout(()=>{},20)},addEventListenerToWebview(){const t=h(),e=t[t.length-1].$getAppWebview();e.addEventListener("hide",()=>{this.webviewHide=!0}),e.addEventListener("show",()=>{this.webviewHide=!1})}}},[["render",function(t,e,h,m,v,y){const b=a,S=f;return t.show?(i(),o(b,{key:0,class:s(["uv-loading-icon",[t.vertical&&"uv-loading-icon--vertical"]]),style:l([t.$uv.addStyle(t.customStyle)])},{default:n(()=>[v.webviewHide?r("",!0):(i(),o(b,{key:0,class:s(["uv-loading-icon__spinner",[`uv-loading-icon__spinner--${t.mode}`]]),ref:"ani",style:l({color:t.color,width:t.$uv.addUnit(t.size),height:t.$uv.addUnit(t.size),borderTopColor:t.color,borderBottomColor:y.otherBorderColor,borderLeftColor:y.otherBorderColor,borderRightColor:y.otherBorderColor,"animation-duration":`${t.duration}ms`,"animation-timing-function":"semicircle"===t.mode||"circle"===t.mode?t.timingFunction:""})},{default:n(()=>["spinner"===t.mode?(i(!0),d(u,{key:0},c(v.array12,(t,e)=>(i(),o(b,{key:e,class:"uv-loading-icon__dot"}))),128)):r("",!0)]),_:1},8,["class","style"])),t.text?(i(),o(S,{key:1,class:"uv-loading-icon__text",style:l([{fontSize:t.$uv.addUnit(t.textSize),color:t.textColor},t.$uv.addStyle(t.textStyle)])},{default:n(()=>[g(p(t.text),1)]),_:1},8,["style"])):r("",!0)]),_:1},8,["style","class"])):r("",!0)}],["__scopeId","data-v-d9c6b36a"]]);export{$ as _};
