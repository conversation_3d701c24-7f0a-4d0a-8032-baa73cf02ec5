import{_ as a}from"./detail-bottom.DXyPlEgb.js";import{_ as s}from"./content-item.CV7Kewyk.js";import{_ as t}from"./navbar.vue_vue_type_script_setup_true_lang.DC1OHhyC.js";import{d as e,u as o,l as i,r as l,D as r,J as n,v as u,w as m,h as p,aD as c,a as d,b as _,o as v,e as g,i as f,x as h,j as x}from"./index-BBirLt11.js";import{_ as j}from"./uv-search.CiAqqsGD.js";import{_ as y}from"./uv-sticky.SyKwVUE-.js";import{_ as b}from"./uv-load-more.SNzw0348.js";import{_ as k}from"./uv-skeletons.BJIINbO6.js";import{a as w}from"./config.CwGRXwA6.js";import{_ as T}from"./uv-icon.Dp0oPivN.js";import"./uv-popup.BGprV-fU.js";import"./uv-transition.B3tXKydv.js";import"./uv-status-bar.CB0-bOg6.js";import"./empty.vue_vue_type_script_setup_true_lang.SETk5GIM.js";import"./uv-empty.Xz_IG7Bm.js";import"./debounce.Ce2HeGXN.js";import"./uv-line.CNQanRFo.js";import"./uv-loading-icon.G79bokgG.js";const B=T(e({__name:"search",setup(e){const{t:T}=o(),B=i(),D=l(B.statusBarHeight||0),H=l(!0),C=l([]),I=l(0),J=r({xm_id:"",page:1,limit:12,name:""}),M=async()=>{try{const{data:a,count:s}=await c(J);a&&a.length&&(1===J.page?C.value=a:C.value=C.value.concat(a)),I.value=s??0}catch(a){}finally{H.value=!1}},N=a=>{J.name=a,J.page=1,C.value=[],M()},R=()=>{J.limit*J.page>I.value||(J.page+=1,M())},S=()=>{x(`/pages/details/form?id=${J.xm_id}`)};return n(a=>{(null==a?void 0:a.id)&&(H.value=!0,J.xm_id=a.id,M())}),(e,o)=>{const i=t,l=d(_("uv-search"),j),r=f,n=d(_("uv-sticky"),y),c=s,x=d(_("uv-load-more"),b),B=a,M=d(_("uv-skeletons"),k);return v(),u(M,{loading:p(H),skeleton:p(w)},{default:m(()=>[g(r,{class:"warp"},{default:m(()=>[g(i,{title:p(T)("details.content")},null,8,["title"]),g(r,{class:"box"},{default:m(()=>[g(n,{customNavHeight:`${p(D)+44}px`},{default:m(()=>[g(r,{class:"w-750 px-30 pt-20 bg-white"},{default:m(()=>[g(l,{shape:"square",showAction:!1,height:"88rpx",boxStyle:{background:"#fff",border:"1px solid #e5e5e5",borderRadius:"20rpx"},placeholder:p(T)("sys.searchTip"),searchIcon:"/src/static/image/home/<USER>",bgColor:"#fff",onChange:N},null,8,["placeholder"])]),_:1})]),_:1},8,["customNavHeight"]),g(r,{class:"env-bottom"},{default:m(()=>[g(c,{class:"px-32",status:!0,xm_id:p(J).xm_id,all:!0,list:p(C),onLoadMore:R},null,8,["xm_id","list"]),p(C).length?(v(),u(x,{key:0,status:p(I)>p(C).length?"loading":"noMore",class:"p-32",loadingText:p(T)("sys.loading"),nomoreText:p(T)("sys.noData")},null,8,["status","loadingText","nomoreText"])):h("",!0)]),_:1}),g(B,{info:[{name:p(T)("details.promoteBtn")}],onBack:S},null,8,["info"])]),_:1})]),_:1})]),_:1},8,["loading","skeleton"])}}}),[["__scopeId","data-v-c691106c"]]);export{B as default};
