var t,s;import{v as i,o as e,w as o,e as n,i as h,q as c,A as d,aM as r}from"./index-CIPK2z2P.js";import{_ as u,m as a,a as l}from"./uv-icon.UcuauzO0.js";const y=u({name:"uv-sticky",mixins:[a,l,{props:{offsetTop:{type:[String,Number],default:0},customNavHeight:{type:[String,Number],default:44},disabled:{type:Boolean,default:!1},bgColor:{type:String,default:"transparent"},zIndex:{type:[String,Number],default:""},index:{type:[String,Number],default:""},...null==(s=null==(t=uni.$uv)?void 0:t.props)?void 0:s.sticky}}],data:()=>({cssSticky:!1,stickyTop:0,elId:"",left:0,width:"auto",height:"auto",fixed:!1}),computed:{style(){const t={};return this.disabled?t.position="static":this.cssSticky?(t.position="sticky",t.zIndex=this.uZindex,t.top=this.$uv.addUnit(this.stickyTop)):t.height=this.fixed?this.height+"px":"auto",t.backgroundColor=this.bgColor,this.$uv.deepMerge(this.$uv.addStyle(this.customStyle),t)},stickyContent(){const t={};return this.cssSticky||(t.position=this.fixed?"fixed":"static",t.top=this.stickyTop+"px",t.left=this.left+"px",t.width="auto"==this.width?"auto":this.width+"px",t.zIndex=this.uZindex),t},uZindex(){return this.zIndex?this.zIndex:970}},created(){this.elId=this.$uv.guid()},mounted(){this.init()},methods:{init(){this.getStickyTop(),this.checkSupportCssSticky(),this.cssSticky||!this.disabled&&this.initObserveContent()},initObserveContent(){this.$uvGetRect("#"+this.elId).then(t=>{this.height=t.height,this.left=t.left,this.width=t.width,this.$nextTick(()=>{this.observeContent()})})},observeContent(){this.disconnectObserver("contentObserver");const t=r({thresholds:[.95,.98,1]});t.relativeToViewport({top:-this.stickyTop}),t.observe(`#${this.elId}`,t=>{this.setFixed(t.boundingClientRect.top)}),this.contentObserver=t},setFixed(t){const s=t<=this.stickyTop;this.fixed=s},disconnectObserver(t){const s=this[t];s&&s.disconnect()},getStickyTop(){this.stickyTop=this.$uv.getPx(this.offsetTop)+this.$uv.getPx(this.customNavHeight)},async checkSupportCssSticky(){this.checkCssStickyForH5()&&(this.cssSticky=!0),"android"===this.$uv.os()&&Number(this.$uv.sys().system)>8&&(this.cssSticky=!0),"ios"===this.$uv.os()&&(this.cssSticky=!0)},checkComputedStyle(){},checkCssStickyForH5(){const t=["","-webkit-","-ms-","-moz-","-o-"],s=t.length,i=document.createElement("div");for(let e=0;e<s;e++)if(i.style.position=t[e]+"sticky",""!==i.style.position)return!0;return!1}},unmounted(){this.disconnectObserver("contentObserver")}},[["render",function(t,s,r,u,a,l){const y=h;return e(),i(y,{class:"uv-sticky",id:a.elId,style:c([l.style])},{default:o(()=>[n(y,{style:c([l.stickyContent]),class:"uv-sticky__content"},{default:o(()=>[d(t.$slots,"default",{},void 0,!0)]),_:3},8,["style"])]),_:3},8,["id","style"])}],["__scopeId","data-v-df7be8db"]]);export{y as _};
