var e,t,l,a,i,s;import{a as n,b as o,v as r,o as c,w as d,e as u,x as p,i as h,q as v,s as m,A as y,U as f,g,t as b,N as _,aF as k}from"./index-BBirLt11.js";import{_ as S,m as $,a as x,b as w}from"./uv-icon.Dp0oPivN.js";import{_ as B}from"./uv-line.CNQanRFo.js";const D=S({name:"uv-cell",emits:["click"],mixins:[$,x,{props:{title:{type:[String,Number],default:""},label:{type:[String,Number],default:""},value:{type:[String,Number],default:""},icon:{type:String,default:""},disabled:{type:Boolean,default:!1},border:{type:Boolean,default:!0},center:{type:Boolean,default:!0},url:{type:String,default:""},linkType:{type:String,default:"navigateTo"},clickable:{type:Boolean,default:!1},isLink:{type:Boolean,default:!1},required:{type:Boolean,default:!1},rightIcon:{type:String,default:"arrow-right"},arrowDirection:{type:String,default:""},iconStyle:{type:[Object,String],default:()=>({})},rightIconStyle:{type:[Object,String],default:()=>({})},titleStyle:{type:[Object,String],default:()=>({})},size:{type:String,default:""},stop:{type:Boolean,default:!0},name:{type:[Number,String],default:""},cellStyle:{type:[Object,String],default:()=>{}},...null==(t=null==(e=uni.$uv)?void 0:e.props)?void 0:t.cell}}],computed:{titleTextStyle(){return this.$uv.addStyle(this.titleStyle)}},methods:{clickHandler(e){this.disabled||(this.$emit("click",{name:this.name}),this.openPage(),this.stop&&this.preventEvent(e))}}},[["render",function(e,t,l,a,i,s){const _=n(o("uv-icon"),w),k=h,S=f,$=n(o("uv-line"),B);return c(),r(k,{class:m(["uv-cell",[e.customClass]]),style:v([e.$uv.addStyle(e.customStyle)]),"hover-class":e.disabled||!e.clickable&&!e.isLink?"":"uv-cell--clickable","hover-stay-time":250,onClick:s.clickHandler},{default:d(()=>[u(k,{class:m(["uv-cell__body",[e.center&&"uv-cell--center","large"===e.size&&"uv-cell__body--large"]]),style:v([e.cellStyle])},{default:d(()=>[u(k,{class:"uv-cell__body__content"},{default:d(()=>[u(k,{class:"uv-cell__left-icon-wrap"},{default:d(()=>[y(e.$slots,"icon",{},()=>[e.icon?(c(),r(_,{key:0,name:e.icon,"custom-style":e.iconStyle,size:"large"===e.size?22:18},null,8,["name","custom-style","size"])):p("",!0)],!0)]),_:3}),u(k,{class:"uv-cell__title"},{default:d(()=>[y(e.$slots,"title",{},()=>[e.title?(c(),r(S,{key:0,class:m(["uv-cell__title-text",[e.disabled&&"uv-cell--disabled","large"===e.size&&"uv-cell__title-text--large"]]),style:v([s.titleTextStyle])},{default:d(()=>[g(b(e.title),1)]),_:1},8,["style","class"])):p("",!0)],!0),y(e.$slots,"label",{},()=>[e.label?(c(),r(S,{key:0,class:m(["uv-cell__label",[e.disabled&&"uv-cell--disabled","large"===e.size&&"uv-cell__label--large"]])},{default:d(()=>[g(b(e.label),1)]),_:1},8,["class"])):p("",!0)],!0)]),_:3})]),_:3}),y(e.$slots,"value",{},()=>[e.$uv.test.empty(e.value)?p("",!0):(c(),r(S,{key:0,class:m(["uv-cell__value",[e.disabled&&"uv-cell--disabled","large"===e.size&&"uv-cell__value--large"]])},{default:d(()=>[g(b(e.value),1)]),_:1},8,["class"]))],!0),e.$slots["right-icon"]||e.isLink?(c(),r(k,{key:0,class:m(["uv-cell__right-icon-wrap",[`uv-cell__right-icon-wrap--${e.arrowDirection}`]])},{default:d(()=>[e.$slots["right-icon"]?y(e.$slots,"right-icon",{key:0},void 0,!0):(c(),r(_,{key:1,name:e.rightIcon,"custom-style":e.rightIconStyle,color:e.disabled?"#c8c9cc":"info",size:"large"===e.size?18:16},null,8,["name","custom-style","color","size"]))]),_:3},8,["class"])):p("",!0)]),_:3},8,["class","style"]),e.border?(c(),r($,{key:0})):p("",!0)]),_:3},8,["class","style","hover-class","onClick"])}],["__scopeId","data-v-1976e35c"]]);const I=S({name:"uv-collapse-item",mixins:[$,x,{props:{title:{type:String,default:""},value:{type:String,default:""},label:{type:String,default:""},disabled:{type:Boolean,default:!1},isLink:{type:Boolean,default:!0},clickable:{type:Boolean,default:!0},border:{type:Boolean,default:!0},align:{type:String,default:"left"},name:{type:[String,Number],default:""},icon:{type:String,default:""},duration:{type:Number,default:300},...null==(a=null==(l=uni.$uv)?void 0:l.props)?void 0:a.collapseItem}}],data:()=>({elId:"",animationData:{},expanded:!1,showBorder:!1,animating:!1,parentData:{accordion:!1,border:!1}}),watch:{expanded(e){clearTimeout(this.timer),this.timer=null,this.timer=setTimeout(()=>{this.showBorder=e},e?10:290)}},created(){this.elId=this.$uv.guid()},mounted(){this.init()},methods:{init(){if(this.updateParentData(),!this.parent)return this.$uv.error("uv-collapse-item必须要搭配uv-collapse组件使用");const{value:e,accordion:t,children:l=[]}=this.parent;if(t){if(this.$uv.test.array(e))return this.$uv.error("手风琴模式下，uv-collapse组件的value参数不能为数组");this.expanded=this.name==e}else{if(!this.$uv.test.array(e)&&null!==e)return this.$uv.error("非手风琴模式下，uv-collapse组件的value参数必须为数组");this.expanded=(e||[]).some(e=>e==this.name)}this.$nextTick(function(){this.setContentAnimate()})},updateParentData(){this.getParentData("uv-collapse")},async setContentAnimate(){const e=await this.queryRect(),t=this.expanded?e.height:0;this.animating=!0;const l=k({timingFunction:"ease-in-out"});l.height(t).step({duration:this.duration}).step(),this.animationData=l.export(),this.$uv.sleep(this.duration).then(()=>{this.animating=!1})},clickHandler(){this.disabled&&this.animating||this.parent&&this.parent.onChange(this)},queryRect(){return new Promise(e=>{this.$uvGetRect(`#${this.elId}`).then(t=>{e(t)})})}}},[["render",function(e,t,l,a,i,s){const v=n(o("uv-cell"),D),m=h,f=n(o("uv-line"),B);return c(),r(m,{class:"uv-collapse-item"},{default:d(()=>[u(v,{title:e.title,value:e.value,label:e.label,icon:e.icon,isLink:e.isLink,clickable:e.clickable,border:i.parentData.border&&i.showBorder,onClick:s.clickHandler,arrowDirection:i.expanded?"up":"down",disabled:e.disabled},{default:d(()=>[_("template",{slot:"title"},[y(e.$slots,"title",{},void 0,!0)]),_("template",{slot:"icon"},[y(e.$slots,"icon",{},void 0,!0)]),_("template",{slot:"value"},[y(e.$slots,"value",{},void 0,!0)]),_("template",{slot:"right-icon"},[y(e.$slots,"right-icon",{},void 0,!0)])]),_:3},8,["title","value","label","icon","isLink","clickable","border","onClick","arrowDirection","disabled"]),u(m,{class:"uv-collapse-item__content",animation:i.animationData,ref:"animation"},{default:d(()=>[u(m,{class:"uv-collapse-item__content__text content-class",id:i.elId,ref:i.elId},{default:d(()=>[y(e.$slots,"default",{},void 0,!0)]),_:3},8,["id"])]),_:3},8,["animation"]),i.parentData.border?(c(),r(f,{key:0})):p("",!0)]),_:3})}],["__scopeId","data-v-dd71d52c"]]);const z=S({name:"uv-collapse",mixins:[$,x,{props:{value:{type:[String,Number,Array,null],default:null},accordion:{type:Boolean,default:!1},border:{type:Boolean,default:!0},...null==(s=null==(i=uni.$uv)?void 0:i.props)?void 0:s.collapse}}],watch:{needInit(){this.init()},parentData(){this.children.length&&this.children.map(e=>{"function"==typeof e.updateParentData&&e.updateParentData()})}},created(){this.children=[]},computed:{needInit(){return[this.accordion,this.value]}},methods:{init(){this.children.map(e=>{e.init()})},onChange(e){let t=[];this.children.map((l,a)=>{this.accordion?(l.expanded=l===e&&!e.expanded,l.setContentAnimate()):l===e&&(l.expanded=!l.expanded,l.setContentAnimate()),t.push({name:l.name||a,status:l.expanded?"open":"close"})}),this.$emit("change",t),this.$emit(e.expanded?"open":"close",e.name)}}},[["render",function(e,t,l,a,i,s){const u=n(o("uv-line"),B),v=h;return c(),r(v,{class:"uv-collapse"},{default:d(()=>[e.border?(c(),r(u,{key:0})):p("",!0),y(e.$slots,"default")]),_:3})}]]);export{I as _,z as a};
