import{_ as t}from"./prompt-popup.vue_vue_type_script_setup_true_lang.DNXGzWqX.js";import{_ as e}from"./submitButton.0smgT9Uf.js";import{_ as s,a as i}from"./navbar.vue_vue_type_script_setup_true_lang.DC1OHhyC.js";import{_ as a}from"./promotion-item.Byf1_4lC.js";import{d as l,u as o,D as n,r,n as d,$ as u,H as c,J as p,c as m,e as _,h as f,w as v,a as b,b as h,ae as y,k as g,R as j,j as x,I as k,o as z,i as w,g as I,t as A,F as E,z as S,v as O,x as C,aE as T}from"./index-BBirLt11.js";import{_ as $}from"./uv-skeletons.BJIINbO6.js";import{P as J,B as R}from"./system.DZjCJFRG.js";import{f as U,e as N,c as P,h as B}from"./details.uFiKUvam.js";import{s as L}from"./skeleton.BDseN9kT.js";import{d as q}from"./debounce.Ce2HeGXN.js";import{_ as D}from"./uv-icon.Dp0oPivN.js";import"./uv-parse.CpsDxc7n.js";import"./uv-popup.BGprV-fU.js";import"./uv-transition.B3tXKydv.js";import"./uv-status-bar.CB0-bOg6.js";import"./empty.vue_vue_type_script_setup_true_lang.SETk5GIM.js";import"./uv-empty.Xz_IG7Bm.js";import"./uv-input.Q1TIxLPl.js";import"./uv-radio-group.CF_hqWfL.js";import"./uv-load-more.SNzw0348.js";import"./uv-line.CNQanRFo.js";import"./uv-loading-icon.G79bokgG.js";import"./uv-datetime-picker.C3YPVc3n.js";import"./uv-picker.CEmVlaLq.js";import"./uploadImag.CANC8dMl.js";import"./media.D7Io5yin.js";import"./decimal.B1oHnkff.js";const F=D(l({__name:"voucherForm",setup(l){const{t:D}=o(),F=n({}),H=n([]),K=r(!0),M=r(),Q=r(""),V=r(""),W=r(),X=r(""),Y=r("warn_tip"),Z=r(!0),G=r([]),tt=r(0),et=n({xm_id:"",page:1,limit:5,kssj:"",jssj:"",keyword:"",zt:J.PASSED}),st=async(t,e)=>{try{const{data:s}=await U(t);H.length=0,H[0]=JSON.parse(JSON.stringify(s)),Object.assign(F,s),K.value=!1,e&&await it({id:t,detailId:e})}catch(s){K.value=!1}},it=async t=>{try{const{data:e}=await N(t.detailId,t.id);H[0].zd_list.forEach((t,s)=>{const i=e.post_data.find(e=>e.bs===t.bs);i&&(t.submit_value=i.value,t.child.length&&t.child.forEach(t=>{i.value===t.id&&(t.child&&t.child.length?t.child.forEach(t=>{H[0].zd_list.splice(s+1,0,t)}):t.submit_value=i.value)}))})}catch(e){K.value=!1}},at=(t,e)=>{const s=JSON.parse(JSON.stringify(t));s.zd_list.forEach(t=>{"1"===t.is_unique&&(t.submit_value="")}),H.splice(e+1,0,s),c(()=>{T({selector:`#item-${e} > .copy`,duration:300})})},lt=()=>{x(`/pages/details/vouncherHistory?id=${Q.value}`)},ot=async()=>{try{const{data:t,count:e}=await P(et);t&&t.length?(1===et.page?G.value=t:G.value=G.value.concat(t),tt.value=e??0):(G.value=[],tt.value=0)}catch(t){}},nt=t=>{Object.assign(et,t),ot()},rt=(t,e,s,i,a)=>{if(2===s){const e=t.child.find(t=>t.id===a);e&&e.child&&e.child.forEach(t=>{const e=H[i].zd_list.findIndex(e=>e.id===t.id);-1!==e&&H[i].zd_list.splice(e,1)})}if(3===s){const e=H[i].zd_list.findIndex(e=>e.id===t.id);if(H[i].zd_list.some(t=>Array.isArray(t)))H[i].zd_list.forEach(e=>{Array.isArray(e)&&e.push(t)});else{const s=H[i].zd_list[e];t.submit_value="",H[i].zd_list[e]=[s,t]}return}if(4===s){const e=H[i].zd_list.findIndex(e=>Array.isArray(e)&&e.some(e=>e.id===t.id));if(-1!==e&&Array.isArray(H[i].zd_list[e])){const t=H[i].zd_list[e],s=parseInt(a,10);s>0&&s<t.length&&t.splice(s,1),1===t.length&&(H[i].zd_list[e]=t[0])}return}if(e.length){const s=H[i].zd_list.findIndex(e=>e.id===t.id);if(-1!==s){e.every(t=>H[i].zd_list.some(e=>e.id===t.id))||H[i].zd_list.splice(s+1,0,...e)}}},dt=t=>{switch(t){case"tj_tip":F.open_bb_tj_tip=R.IS_FALSE,setTimeout(()=>{ut()},500);break;case"warn_tip":W.value&&W.value.close()}},ut=q(async()=>{try{for(const i of H)for(const t of i.zd_list)if("1"===t.is_required&&!t.submit_value)throw y(D("sys.pleaseEnter")+t.name),new Error("validation_failed");const t=[];for(const i of H){const e={},s=[];for(const t of i.zd_list)Array.isArray(t)?t.forEach((t,i)=>{if(t.submit_value)if(0===i)e[t.bs]=t.submit_value;else{const e={[t.bs]:t.submit_value};s.push(e)}}):t.submit_value&&(e[t.bs]=t.submit_value);s.forEach(t=>{for(const e of i.zd_list)!Array.isArray(e)&&e.submit_value&&(t[e.bs]=e.submit_value)}),t.push(e),t.push(...s)}const{code:e,msg:s}=await B({xm_id:Q.value,pz_data:t});if(1!==e)return-1005===e||-1004===e?(g().setRedirectUrl(""),g().setToken(""),void j({url:"/pages/login/index"})):(X.value=s??"",Y.value="warn_tip",Z.value=!1,void setTimeout(()=>{W.value&&"function"==typeof W.value.open&&W.value.open()},100));x(`/pages/details/tips?id=${Q.value}&type=1`)}catch(t){return void t.message}},300,{leading:!0,trailing:!0});return d(()=>{u("promotionSuccess"),c(()=>{k("promotionSuccess",()=>{et.page=1,ot()})})}),p(t=>(null==t?void 0:t.id)&&!(null==t?void 0:t.detailId)?(Q.value=t.id,st(t.id),et.xm_id=t.id,void ot()):(null==t?void 0:t.id)&&(null==t?void 0:t.detailId)?(Q.value=t.id,et.xm_id=t.id,V.value=t.detailId,void Promise.allSettled([st(t.id,t.detailId),ot()]).then(()=>{K.value=!1})):void(K.value=!1)),(l,o)=>{const n=s,r=w,d=a,u=i,p=e,y=b(h("uv-skeletons"),$),g=t;return z(),m("div",null,[_(n,{"bg-color":"transparent",title:f(D)("navTitle.voucher"),scrollTextColor:"blackOne",isDarkMode:!0,"right-icon":"dingdanjilu",onRightClick:lt},null,8,["title"]),_(y,{loading:f(K),skeleton:f(L)},{default:v(()=>[_(r,{class:"form p-32"},{default:v(()=>[_(r,{class:"w-686 text-blackOne text-32 leading-44 font-semibold"},{default:v(()=>[I(A(`【${f(H)[0].name}】`+f(D)("navTitle.voucher")),1)]),_:1}),_(r,null,{default:v(()=>[(z(!0),m(E,null,S(f(H),(t,e)=>(z(),O(r,{class:"w-686 bg-white p-32 mt-24 rounded-30",key:`${t}-${e}`,id:`item-${e}`},{default:v(()=>[(z(!0),m(E,null,S(t.zd_list,t=>(z(),O(r,{key:t.id},{default:v(()=>[_(d,{ref_for:!0,ref_key:"promotionItemRef",ref:M,obj:t,xmId:f(Q),bbList:f(G),"bb-total":f(tt),lx:"pz_data",detailId:f(V),value:t.submit_value,"onUpdate:value":e=>t.submit_value=e,index:e,"onUpdate:list":rt,"onUpdate:bbList":nt},null,8,["obj","xmId","bbList","bb-total","detailId","value","onUpdate:value","index"])]),_:2},1024))),128)),_(r,{class:"copy flex justify-center items-center mt-24"},{default:v(()=>[_(r,{class:"flex-1 text flex items-center justify-center"},{default:v(()=>[_(u,{name:"fuzhi",size:"44rpx"}),_(r,{class:"ml-8 leading-40 text-blueOne text-28",onClick:s=>at(t,e)},{default:v(()=>[I(A(f(D)("promotion.copy")),1)]),_:2},1032,["onClick"])]),_:2},1024),f(H).length>1?(z(),O(r,{key:0,onClick:t=>((t,e)=>{H.length>1&&(H.splice(e,1),c(()=>{T({selector:"#item-"+(e-1),duration:300})}))})(0,e),class:"flex-1 flex items-center justify-center text-blackTwo text-28"},{default:v(()=>[I(A(f(D)("sys.delete")),1)]),_:2},1032,["onClick"])):C("",!0)]),_:2},1024)]),_:2},1032,["id"]))),128))]),_:1}),_(r,{class:"env-bottom"},{default:v(()=>[_(r,{class:"w-686 mt-50 bg-white h-88 border border-blueOne rounded-20 flex justify-center items-center"},{default:v(()=>[_(r,{class:"copy flex justify-center items-center"},{default:v(()=>[_(u,{name:"fuzhi",size:"44rpx"}),_(r,{class:"ml-8 leading-40 text-blueOne text-28",onClick:o[0]||(o[0]=t=>at(f(F),f(H).length-1))},{default:v(()=>[I(A(f(D)("promotion.add")),1)]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),_(p,{onBack:f(ut)},null,8,["onBack"])]),_:1},8,["loading","skeleton"]),_(g,{ref_key:"promptPopupRef",ref:W,title:f(D)("sys.tip"),content:f(X),type:f(Y),contentType:"html",showCancel:f(Z),"cancel-text":f(D)("sys.thinkAgain"),onConfirm:dt},null,8,["title","content","type","showCancel","cancel-text"])])}}}),[["__scopeId","data-v-c3d56e44"]]);export{F as default};
