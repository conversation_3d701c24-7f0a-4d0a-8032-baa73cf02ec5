import{d as e,c as t,o as a,e as s,a_ as l,s as n,w as o,i as r,F as i,z as f,v as c,g as d,t as u,h as b,P as m}from"./index-BBirLt11.js";import{_ as x}from"./uv-icon.Dp0oPivN.js";const g=x(e({__name:"detail-bottom",props:{info:{type:Array,default:()=>[]},bg:{type:String,default:"bg-white"},id:{type:String,default:""},leftTextColor:{type:String,default:"blueOne"},leftTextBg:{type:String,default:"white"}},emits:["back"],setup(e,{emit:x}){const g=x;return(x,p)=>{const _=r,y=l;return a(),t("div",null,[s(y,{class:n(["flex item-center justify-center items-center py-20 fixed bottom-0 w-750 footerBtn",e.bg])},{default:o(()=>[s(_,{class:"flex w-686"},{default:o(()=>[(a(!0),t(i,null,f(e.info,t=>(a(),c(_,{key:t.name,onClick:e=>(e=>{g("back",null==e?void 0:e.bs)})(t),class:n(["text-center text-30 leading-88 rounded-20 h-88 last:mr-0","white"!==t.color?"bg-blueOne flex-1 text-white mr-20":`bg-${e.leftTextBg} text-${e.leftTextColor}  border-blueOne flex-1`])},{default:o(()=>[d(u(b(m)(t.name)),1)]),_:2},1032,["onClick","class"]))),128))]),_:1})]),_:1},8,["class"])])}}}),[["__scopeId","data-v-81c161b7"]]);export{g as _};
