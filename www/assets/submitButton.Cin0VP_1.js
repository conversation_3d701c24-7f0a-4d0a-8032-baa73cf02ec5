import{d as t,u as e,v as s,w as a,s as o,i,o as n,e as r,g as u,t as c,h as l}from"./index-CIPK2z2P.js";import{_ as f}from"./uv-icon.UcuauzO0.js";const d=f(t({__name:"submitButton",props:{title:{type:String,default:"sys.confirm"},status:{type:Boolean,default:!0},bg:{type:String,default:"bg-white"}},emits:["back"],setup(t,{emit:f}){const{t:d}=e(),b=f,m=()=>{b("back")};return(e,f)=>{const b=i;return n(),s(b,{class:o(["w-750 footerBtn fixed bottom-0 flex justify-center items-center px-32 py-20",t.bg])},{default:a(()=>[r(b,{onClick:m,class:o([t.status?"bg-blueOne":"bg-blueTwo","w-686 rounded-20 leading-88 flex justify-center items-center text-white text-30 bg"])},{default:a(()=>[u(c(l(d)(t.title)),1)]),_:1},8,["class"])]),_:1},8,["class"])}}}),[["__scopeId","data-v-c9183892"]]);export{d as _};
