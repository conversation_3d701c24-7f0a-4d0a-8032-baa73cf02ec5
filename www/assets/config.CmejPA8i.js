import{d as e,u as t,k as i,r as a,ak as s,a as n,b as l,v as o,o as c,w as r,e as d,i as u,g as f,t as m,h as p,x as g,U as h,s as x,_}from"./index-BBirLt11.js";import{b,_ as v}from"./uv-icon.Dp0oPivN.js";const I=v(e({__name:"user-box",props:{item:{type:Object,required:!0}},setup(e){const{t:v}=t(),I=i().userInfo,w=e,k=a(w.item);s(()=>w.item,e=>{k.value=e},{deep:!0});return(t,a)=>{const s=u,w=h,y=n(l("uv-icon"),b);return c(),o(s,{class:"bg-white w-686 p-32 mb-20 rounded-20 flex justify-between"},{default:r(()=>[d(s,{class:"left text-blackOne text-28 leading-36"},{default:r(()=>[f(m(p(v)(`userInfo.${e.item.title}`)),1)]),_:1}),d(s,{class:"right text-blackTwo text-28 leading-36 flex items-center"},{default:r(()=>[d(s,{class:"max-w-350 line-clamp-1"},{default:r(()=>[f(m("version"!==p(k).title?p(I)[p(k).field]:`v${p(i)().appSystem.app_version}`),1)]),_:1}),d(w,{class:x([p(k).icon?`iconfont ${p(k).icon}`:"iconfont","!text-44 !text-blueOne"]),onClick:a[0]||(a[0]=e=>{"icon-fuzhi"===p(k).icon&&_(I[k.value.field])})},null,8,["class"]),p(k).rightIcon?(c(),o(y,{key:0,name:"arrow-right",class:"ml-8",size:"28rpx",color:"#727A86"})):g("",!0)]),_:1})]),_:1})}}}),[["__scopeId","data-v-85b5cb9c"]]),w=[{title:"id",field:"uid"},{title:"name",field:"nickname"},{title:"inviteCode",field:"invite_code",icon:"icon-fuzhi"},{title:"account",path:"/pages/my/info/security",rightIcon:!0},{title:"version",field:"USD",rightIcon:!1}],k=[{title:"changeEmail",field:"<EMAIL>",rightIcon:!0},{title:"changePassword",field:"",rightIcon:!0},{title:"deleteAccount",field:"",rightIcon:!0}];export{I as _,k as a,w as u};
