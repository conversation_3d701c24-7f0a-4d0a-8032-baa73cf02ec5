import{_ as e}from"./empty.vue_vue_type_script_setup_true_lang.SETk5GIM.js";import{_ as t}from"./prompt-popup.vue_vue_type_script_setup_true_lang.DNXGzWqX.js";import{a,_ as s}from"./navbar.vue_vue_type_script_setup_true_lang.DC1OHhyC.js";import{d as l,u as o,r as n,aq as i,v as r,o as u,w as m,e as p,i as c,x as d,f,g as _,t as v,h as k,D as y,n as g,$ as x,I as b,E as j,c as T,a as h,b as C,F as w,z as D,j as z}from"./index-BBirLt11.js";import{d as R,a as U}from"./bank.DK3OlB0r.js";import{d as F}from"./debounce.Ce2HeGXN.js";import{_ as I}from"./uv-load-more.SNzw0348.js";import{_ as N}from"./uv-skeletons.BJIINbO6.js";import{s as O}from"./config.B61nRb73.js";import{D as P}from"./decimal.B1oHnkff.js";import"./uv-empty.Xz_IG7Bm.js";import"./uv-icon.Dp0oPivN.js";import"./uv-parse.CpsDxc7n.js";import"./uv-popup.BGprV-fU.js";import"./uv-transition.B3tXKydv.js";import"./uv-status-bar.CB0-bOg6.js";import"./uv-line.CNQanRFo.js";import"./uv-loading-icon.G79bokgG.js";const q=l({__name:"bankCardItem",props:{item:{},type:{type:Boolean}},emits:["update:look"],setup(e,{emit:s}){const l=e,y=s,{t:g}=o(),x=n(),b=n(),j=n(l.item.look),T=()=>{j.value=!j.value,y("update:look",j.value)},h=()=>{x.value=g("sys.deleteTip3")+l.item.type_name+g("sys.deleteTip2"),b.value.open()},C=F(async()=>{try{await R(l.item.id),i("back:bank")}catch(e){}},300,{leading:!0,trailing:!0});return(e,s)=>{const l=f,o=c,n=a,i=t;return u(),r(o,null,{default:m(()=>[p(o,{class:"flex items-center justify-between mb-28"},{default:m(()=>[p(o,{class:"flex items-center"},{default:m(()=>[p(l,{class:"w-48 h-48 mr-20",src:e.item.type_logo,mode:"aspectFill"},null,8,["src"]),p(o,{class:"max-w-426 line-clamp-1 font-semibold text-28 leading-40"},{default:m(()=>[_(v(e.item.type_name),1)]),_:1})]),_:1}),e.type?(u(),r(o,{key:0,class:"flex items-center"},{default:m(()=>[p(n,{onClick:T,name:e.item.look?"a-kejianon":"a-kejianoff",size:"44rpx"},null,8,["name"]),p(n,{class:"ml-20",name:"dalajitong",fill:"#333",size:"44rpx",onClick:h})]),_:1})):d("",!0)]),_:1}),p(o,{class:"flex text-40 text-blackOne font-din leading-40 h-40"},{default:m(()=>[_(v(e.item.look?e.item.y_account:e.item.account),1)]),_:1}),p(i,{ref_key:"promptPopupRef",ref:b,title:k(g)("sys.deleteTip"),confirmType:"outline",content:k(x),confirmText:k(g)("sys.delete"),"cancel-text":k(g)("sys.return"),showCancel:!0,onConfirm:k(C)},null,8,["title","content","confirmText","cancel-text","onConfirm"])]),_:1})}}}),A=l({__name:"index",setup(t){const{t:a}=o(),l=n(!0),i=n("loading"),d=n(0),f=y({page:1,limit:10}),_=n([]),v=()=>{z("/pages/my/bankCard/add")},R=async()=>{try{const{data:e,count:t}=await U(f);1===f.page?_.value=e||[]:_.value=_.value.concat(e||[]),d.value=t||0,f.limit*f.page>=d.value?i.value="nomore":i.value="",d.value=t??0}catch(e){}finally{l.value=!1}};return R(),g(()=>{x("back:bank"),b("back:bank",()=>{f.page=1,l.value=!0,R()})}),j(()=>{if(d.value<=P(f.page*f.limit).toNumber())return i.value="nomore";f.page++,R(),i.value="loading"}),(t,o)=>{const n=s,d=q,f=c,y=h(C("uv-load-more"),I),g=e,x=h(C("uv-skeletons"),N);return u(),T("div",null,[p(n,{"bg-color":"transparent",title:k(a)("navTitle.settlement"),scrollTextColor:"blackOne",isDarkMode:!0,"right-text":"+ Add",onRightClick:v},null,8,["title"]),p(f,{class:"p-32"},{default:m(()=>[p(x,{loading:k(l),skeleton:k(O)},{default:m(()=>[k(_).length?(u(),r(f,{key:0},{default:m(()=>[(u(!0),T(w,null,D(k(_),e=>(u(),r(f,{class:"bg-white rounded-30 p-32 mb-20 last:mb-0",key:e.id},{default:m(()=>[p(d,{item:e,type:!0,"onUpdate:look":t=>(e=>{e.look=!e.look})(e)},null,8,["item","onUpdate:look"])]),_:2},1024))),128)),p(y,{status:k(i),class:"p-32",loadingText:k(a)("sys.loading"),nomoreText:k(a)("sys.noData")},null,8,["status","loadingText","nomoreText"])]),_:1})):(u(),r(g,{key:1,class:"mt-[30%]"}))]),_:1},8,["loading","skeleton"])]),_:1})])}}});export{A as default};
