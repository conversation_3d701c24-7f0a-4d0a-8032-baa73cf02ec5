import{_ as e}from"./uploadImag.CANC8dMl.js";import{d as t,c as a,o as s,e as l,v as d,x as n,i as r,w as i,g as u,t as o,h as c,P as m,s as p,F as _,z as E}from"./index-BBirLt11.js";import{F as x,P as f}from"./system.DZjCJFRG.js";import{_ as b}from"./uv-icon.Dp0oPivN.js";const g=b(t({__name:"promotionCodeInfo",props:{item:{type:Object,default:()=>({})}},setup(t){const b=e=>{switch(e){case f.PENDING:return"text-[#FFB006] bg-[#FFF8E7]";case f.NOT_PASSED:return"text-orange bg-[#FFEDE7]";case f.PASSED:return"text-[#06BF53] bg-[#E6F9EE]";default:return"text-[#444B56] bg-[#EDEDEF]"}},g=e=>{switch(e){case f.PENDING:return"Pending";case f.NOT_PASSED:return"Not Approved";case f.PASSED:return"Approved";default:return"Unknown"}};return(y,F)=>{const D=r,S=e;return s(),a("div",null,[l(D,{class:"pb-32 flex justify-between border-b broder-[#E8E8EA] border-dashed"},{default:i(()=>[l(D,{class:"leading-36 text-28 text-blackOne"},{default:i(()=>[u(o(c(m)(t.item.xm_name)),1)]),_:1}),l(D,{class:p(["text-22 leading-28 text-center py-4 px-8 rounded-8 w-fit",b(t.item.zt)])},{default:i(()=>[u(o(t.item.zt_text?t.item.zt_text:g(t.item.zt)),1)]),_:1},8,["class"])]),_:1}),l(D,{class:"pt-32 pb-0"},{default:i(()=>[(s(!0),a(_,null,E(t.item.post_data,e=>(s(),d(D,{key:e.bs},{default:i(()=>[l(D,{class:"flex last:mb-0"},{default:i(()=>[l(D,{class:"text-26 leading-32 text-blackThree w-220 mr-20"},{default:i(()=>[u(o(c(m)(e.name)),1)]),_:2},1024),l(D,{class:"text-22 text-blackOne flex-1 mb-24"},{default:i(()=>[l(D,{class:"text-26 leading-36 text-blackOne"},{default:i(()=>[e.type!==c(x).IMAGE&&e.type!==c(x).FILE?(s(),d(D,{key:0},{default:i(()=>[u(o(c(m)(e.value_display)),1)]),_:2},1024)):(s(),d(S,{key:1,img:e.value_display,"onUpdate:img":t=>e.value_display=t,value:e.value_display,lx:e.type,delIcon:!1,count:1,accept:e.type===c(x).IMAGE?"image":e.type===c(x).VIDEO?"video":"file"},null,8,["img","onUpdate:img","value","lx","accept"]))]),_:2},1024)]),_:2},1024)]),_:2},1024)]),_:2},1024))),128))]),_:1}),!t.item.u_bz||t.item.zt!==c(f).PASSED&&t.item.zt!==c(f).NOT_PASSED?n("",!0):(s(),d(D,{key:0,class:p(["mt-40 w-622 p-28 rounded-20 text-26 leading-40",t.item.zt===c(f).PASSED?"text-[#06BF53] bg-[#E6F9EE]":"text-orange bg-[#FFEDE7]"])},{default:i(()=>[u(o(t.item.u_bz),1)]),_:1},8,["class"]))])}}}),[["__scopeId","data-v-ad99a1e2"]]);export{g as _};
