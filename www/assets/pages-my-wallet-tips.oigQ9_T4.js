import{d as t,u as e,v as s,w as a,i as l,o as n,e as o,h as c,f as i,g as r,t as u,j as m}from"./index-BBirLt11.js";import{_ as d}from"./success.njPbLiaa.js";import{_ as f}from"./navbar.vue_vue_type_script_setup_true_lang.DC1OHhyC.js";import{_ as p}from"./uv-icon.Dp0oPivN.js";import"./uv-status-bar.CB0-bOg6.js";import"./debounce.Ce2HeGXN.js";const _=p(t({__name:"tips",setup(t){const{t:p}=e(),_=()=>{m("/pages/my/index","switchTab")};return(t,e)=>{const m=i,b=l;return n(),s(b,{class:"container"},{default:a(()=>[o(f,{title:c(p)("wallet.settle"),showBack:!0},null,8,["title"]),o(b,{class:"content-container flex flex-col items-center justify-center"},{default:a(()=>[o(m,{class:"w-136 h-136 mt-140 mb-20",src:d,mode:"aspectFill"}),o(b,{class:"title w-492 leading-50 text-36 font-semibold m-auto mb-20 text-center text-blackOne"},{default:a(()=>[r(u(c(p)("wallet.tip1")),1)]),_:1}),o(b,{class:"hint m-auto leading-48 text-28 text-blackTwo w-622 text-center"},{default:a(()=>[r(u(c(p)("wallet.tip2")),1)]),_:1}),o(b,{class:"button-area"},{default:a(()=>[o(b,{class:"primary-button h-88 bg-blueOne px-162 w-fit m-auto mt-40 rounded-20 text-white leading-88 text-30",onClick:_},{default:a(()=>[r(u(c(p)("sys.back")),1)]),_:1})]),_:1})]),_:1})]),_:1})}}}),[["__scopeId","data-v-ff335f10"]]);export{_ as default};
