import{_ as e}from"./navbar.vue_vue_type_script_setup_true_lang.CSl8hyKC.js";import{d as t,u as a,r as s,J as l,c as d,e as n,h as r,w as c,an as u,i,o as x,v as f,x as o,g as _,t as m,f as b,U as p}from"./index-CIPK2z2P.js";import"./uv-icon.UcuauzO0.js";import"./uv-status-bar.BkslDeIT.js";import"./debounce.Ce2HeGXN.js";const g=t({__name:"details",setup(t){const{t:g}=a(),h=s({}),v=s([{name:g("sys.reviewStatusObj.all"),value:0},{name:g("sys.reviewStatusObj.pending"),value:1},{name:g("sys.reviewStatusObj.passed"),value:3},{name:g("sys.reviewStatusObj.rejected"),value:2}]);return l(e=>{(null==e?void 0:e.id)&&(async e=>{try{const{data:t}=await u(e);h.value=t||{}}catch(t){}})(e.id)}),(t,a)=>{const s=e,l=i,u=b,y=p;return x(),d("div",null,[n(s,{"bg-color":"transparent",title:r(g)("details.tit"),scrollTextColor:"blackOne",isDarkMode:!0},null,8,["title"]),n(l,{class:"p-32"},{default:c(()=>[n(l,{class:"bg-white rounded-30 shadow-md pb-32 px-32"},{default:c(()=>[n(l,{class:"py-40 border-b border-dashed"},{default:c(()=>[n(l,{class:"text-blackTwo leading-40 text-28 text-center"},{default:c(()=>[_("Account in arrear")]),_:1}),n(l,{class:"text-blackOne text-60 leading-60 font-din mt-20 text-center"},{default:c(()=>[_(m(r(h).money),1)]),_:1})]),_:1}),n(l,{class:"flex mt-40 mb-20"},{default:c(()=>[n(l,{class:"flex-1 text-blackThree leading-36 text-26"},{default:c(()=>[_(m(r(g)("sys.status")),1)]),_:1}),n(l,{class:"flex-1 text-right leading-36 text-26"},{default:c(()=>[_(m(r(v)[r(h).zt??0].name),1)]),_:1})]),_:1}),r(h).fail_reason?(x(),f(l,{key:0,class:"p-28 bg-[#F6F7F9] rounded-20"},{default:c(()=>[_(m(r(h).fail_reason),1)]),_:1})):o("",!0),n(l,{class:"mt-20 flex"},{default:c(()=>[n(l,{class:"flex-1 text-blackThree leading-36 text-26"},{default:c(()=>[_(m(r(g)("sys.time")),1)]),_:1}),n(l,{class:"flex-1 text-right leading-36 text-26"},{default:c(()=>[_(m(r(h).tjsj),1)]),_:1})]),_:1}),n(l,{class:"mt-20 flex"},{default:c(()=>[n(l,{class:"flex-1 text-blackThree leading-36 text-26"},{default:c(()=>[_(m(r(g)("bankCard.paymentMethod")),1)]),_:1}),n(l,{class:"flex-1 text-right leading-36 text-26 flex justify-end"},{default:c(()=>[n(u,{class:"w-36 h-36 mr-8",src:r(h).txzh_type_logo,mode:"scaleToFill"},null,8,["src"]),n(y,{class:"max-w-200 line-clamp-1"},{default:c(()=>[_(m(r(h).txzh_type_name),1)]),_:1})]),_:1})]),_:1}),n(l,{class:"mt-20 flex"},{default:c(()=>[n(l,{class:"flex-1 text-blackThree leading-36 text-26"},{default:c(()=>[_(m(r(g)("tab.account")),1)]),_:1}),n(l,{class:"flex-1 text-right leading-36 text-26"},{default:c(()=>[_(m(r(h).txzh_account),1)]),_:1})]),_:1})]),_:1})]),_:1})])}}});export{g as default};
