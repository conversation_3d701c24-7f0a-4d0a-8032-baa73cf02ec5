import{_ as t,a as e}from"./navbar.vue_vue_type_script_setup_true_lang.DC1OHhyC.js";import{d as a,u as l,D as s,r as d,J as n,v as o,w as i,i as r,o as c,e as u,c as f,g as x,t as _,h as m,F as b,z as p}from"./index-BBirLt11.js";import{e as g}from"./details.uFiKUvam.js";import"./uv-icon.Dp0oPivN.js";import"./uv-status-bar.CB0-bOg6.js";import"./debounce.Ce2HeGXN.js";const k=a({__name:"promotionCodeDetails",setup(a){const{t:k}=l(),v=s({xm_id:"",id:""}),O=d({post_data:[]});return n(t=>{(null==t?void 0:t.id)&&(null==t?void 0:t.xm_id)&&(Object.assign(v,{xm_id:t.xm_id,id:t.id}),(async()=>{try{const{data:t}=await g(v.id,v.xm_id);O.value=t??{post_data:[]}}catch(t){}})())}),(a,l)=>{const s=t,d=r,n=e;return c(),o(d,null,{default:i(()=>[u(s,{title:a.$t("navTitle.promotionDetails"),"bg-color":"transparent",scrollTextColor:"blackOne",isDarkMode:!0},null,8,["title"]),u(d,{class:"p-32 bg-white"},{default:i(()=>[u(d,{class:"pb-32 flex justify-between"},{default:i(()=>[u(d,{class:"leading-36 text-28 text-blackOne"},{default:i(()=>[x("ZhangXiang")]),_:1}),u(d,{class:"text-22 leading-28 text-greenOne text-center py-4 px-8 rounded-8 w-fit"},{default:i(()=>[x("Approved")]),_:1})]),_:1}),u(d,{class:"bg-[#F6F7F9] p-32 rounded-30 mb-32"},{default:i(()=>[u(d,{class:"pb-32 border-b border-[#E8E8EA] border-dashed"},{default:i(()=>[u(d,{class:"text-blackTwo leading-32 text-24"},{default:i(()=>[x(_(m(k)("promotion.Income"))+" ($)",1)]),_:1}),u(d,{class:"mt-12 text-blackOne font-din text-48 leading-48"},{default:i(()=>[x("502.32")]),_:1})]),_:1}),u(d,{class:"flex pt-32"},{default:i(()=>[u(d,{class:"flex-1"},{default:i(()=>[u(d,{class:"text-blackTwo leading-32 text-24"},{default:i(()=>[x(_(m(k)("promotion.Total"))+" (Order) ",1)]),_:1}),u(d,{class:"mt-12 text-blackOne font-din text-48 leading-48"},{default:i(()=>[x("502.32")]),_:1})]),_:1}),u(d,{class:"flex-1"},{default:i(()=>[u(d,{class:"text-blackTwo leading-32 text-24"},{default:i(()=>[x(_(m(k)("promotion.Deals"))+" (Order) ",1)]),_:1}),u(d,{class:"mt-12 text-blackOne font-din text-48 leading-48"},{default:i(()=>[x("502.32")]),_:1})]),_:1})]),_:1})]),_:1}),(c(!0),f(b,null,p(m(O).post_data,t=>(c(),o(d,{class:"flex mb-24 last:mb-0",key:t.bs},{default:i(()=>[u(d,{class:"text-26 leading-32 text-blackThree w-220 mr-20"},{default:i(()=>[x(_(t.name),1)]),_:2},1024),u(d,{class:"text-22 text-blackOne"},{default:i(()=>[u(d,{class:"text-26 leading-36 text-blackOne"},{default:i(()=>[x(_(t.value_display),1)]),_:2},1024)]),_:2},1024)]),_:2},1024))),128))]),_:1}),u(d,{class:"p-32 py-34"},{default:i(()=>[u(d,{class:"flex justify-between items-center mb-24"},{default:i(()=>[u(d,{class:"text-blackOne font-semibold text-32 leading-44"},{default:i(()=>[x(_(m(k)("promotion.orderRecords")),1)]),_:1}),u(d,{class:"h-60 flex items-center relative filter ml-30"},{default:i(()=>[u(n,{name:"shaixuanloudou"}),x(" "+_(m(k)("sys.filter")),1)]),_:1})]),_:1})]),_:1})]),_:1})}}});export{k as default};
