import{_ as e}from"./empty.vue_vue_type_script_setup_true_lang.SETk5GIM.js";import{_ as a}from"./media-item.vue_vue_type_script_setup_true_lang.CFbV5CFx.js";import{_ as t}from"./navbar.vue_vue_type_script_setup_true_lang.DC1OHhyC.js";import{_ as s}from"./uv-tabs.DzY9siFk.js";import{d as l,u as i,r as o,D as n,E as r,m as u,n as m,c as d,e as c,h as p,w as v,a as _,b as g,o as f,v as y,i as b,s as x,F as j,z as k,g as h,t as w,j as T}from"./index-BBirLt11.js";import{_ as C}from"./uv-sticky.SyKwVUE-.js";import{_ as S}from"./uv-load-more.SNzw0348.js";import{_ as D}from"./uv-skeletons.BJIINbO6.js";import{g as W,a as z}from"./media.D7Io5yin.js";import{s as N}from"./config.CV-betc9.js";import{D as O}from"./decimal.B1oHnkff.js";import"./uv-empty.Xz_IG7Bm.js";import"./uv-icon.Dp0oPivN.js";import"./uv-status-bar.CB0-bOg6.js";import"./debounce.Ce2HeGXN.js";import"./uv-line.CNQanRFo.js";import"./uv-loading-icon.G79bokgG.js";const P=l({__name:"index",setup(l){const{t:P}=i(),q=o(!0),A=o([]),B=o(""),E=o(0),F=o(0),K=n({mtpt_id:"",page:1,limit:10}),L=o(0),M=o([]),Q=async()=>{try{const{data:e,count:a}=await W(K);1===K.page?A.value=e||[]:A.value=A.value.concat(e||[]),L.value=a||0,K.limit*K.page>=L.value&&(B.value="nomore")}catch(e){}},R=()=>{T("/pages/my/media/add")},U=()=>{T("/pages/my/index","switchTab")};r(()=>{if(L.value<=O(K.page*K.limit).toNumber())return B.value="nomore";K.page++,Q(),B.value="loading"}),u(e=>{F.value=e.scrollTop});const V=async()=>{try{const{data:e}=await z();return e&&e.length?(M.value=e.map(e=>({name:e.name,value:e.mtpt_id,badge:{value:e.num||0}})),M.value.unshift({name:P("notice.all"),value:"",badge:{value:0}}),e||[]):void(M.value=[])}catch(e){return[]}};return m(()=>{K.mtpt_id="",K.page=1,A.value.length||(q.value=!0),Promise.allSettled([Q(),V()]).finally(()=>{q.value=!1})}),(l,i)=>{const o=t,n=_(g("uv-tabs"),s),r=b,u=_(g("uv-sticky"),C),m=a,W=_(g("uv-load-more"),S),z=e,O=_(g("uv-skeletons"),D);return f(),d("div",null,[c(o,{"bg-color":"transparent",title:p(P)("navTitle.media"),scrollTextColor:"blackOne",isDarkMode:!0,back:!1,"right-text":"+ Add",onLeftClick:U,onRightClick:R},null,8,["title"]),c(O,{loading:p(q),skeleton:p(N)},{default:v(()=>[c(u,null,{default:v(()=>[c(r,{class:x(["pb-20 pl-30",{"bg-white":p(F)>30}])},{default:v(()=>[c(n,{list:p(M),current:p(E),activeStyle:{color:"#131726",fontWeight:"bold",fontSize:"28rpx"},inactiveStyle:{color:"#444B56"},lineColor:"#0165ff",lineWidth:"66rpx",onClick:i[0]||(i[0]=e=>{p(K).mtpt_id=e.value,E.value=e.index,p(K).page=1,Q()})},null,8,["list","current"])]),_:1},8,["class"])]),_:1}),p(A).length?(f(),y(r,{key:0,class:"p-20"},{default:v(()=>[(f(!0),d(j,null,k(p(A),e=>(f(),y(r,{class:"px-30 py-40 bg-white rounded-24 mb-20 last:mb-0 flex items-center",key:e.id,onClick:a=>{return t=e.id,void T(`/pages/my/media/add?id=${t}`);var t}},{default:v(()=>[c(m,{item:e,select:!1},null,8,["item"])]),_:2},1032,["onClick"]))),128)),c(W,{status:p(B),class:"p-32",loadingText:p(P)("sys.loading"),nomoreText:p(P)("sys.noData")},null,8,["status","loadingText","nomoreText"])]),_:1})):(f(),y(r,{key:1,class:"flex flex-col items-center"},{default:v(()=>[c(z,{class:"mt-[30%]"}),c(r,{class:"bg-blueOne w-400 py-22 text-center leading-44 text-28 text-white px-50 mt-60 rounded-20",onClick:R},{default:v(()=>[h(w(p(P)("sys.addNow")),1)]),_:1})]),_:1}))]),_:1},8,["loading","skeleton"])])}}});export{P as default};
