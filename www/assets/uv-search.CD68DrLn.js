var e,t;import{a as o,b as a,v as l,o as s,w as c,e as i,i as r,q as n,x as u,A as d,aP as h,U as p,ab as y,s as f,g as m,t as g,aQ as b}from"./index-CIPK2z2P.js";import{_,m as k,a as v,b as S}from"./uv-icon.UcuauzO0.js";const w=_({name:"uv-search",emits:["click","input","change","clear","search","custom","focus","blur","clickIcon","update:modelValue"],mixins:[k,v,{props:{value:{type:[String,Number],default:""},modelValue:{type:[String,Number],default:""},shape:{type:String,default:"round"},bgColor:{type:String,default:"#f2f2f2"},placeholder:{type:String,default:"请输入关键字"},clearabled:{type:Boolean,default:!0},focus:{type:Boolean,default:!1},showAction:{type:Boolean,default:!0},actionStyle:{type:Object,default:()=>({})},actionText:{type:String,default:"搜索"},inputAlign:{type:String,default:"left"},inputStyle:{type:Object,default:()=>({})},disabled:{type:Boolean,default:!1},borderColor:{type:String,default:"transparent"},searchIconColor:{type:String,default:"#909399"},color:{type:String,default:"#606266"},placeholderColor:{type:String,default:"#909399"},searchIcon:{type:String,default:"search"},searchIconSize:{type:[Number,String],default:22},margin:{type:String,default:"0"},animation:{type:Boolean,default:!1},maxlength:{type:[String,Number],default:-1},height:{type:[String,Number],default:32},label:{type:[String,Number,null],default:null},boxStyle:{type:[String,Object],default:()=>({})},...null==(t=null==(e=uni.$uv)?void 0:e.props)?void 0:t.search}}],data(){return{keyword:"",showClear:!1,show:!1,focused:this.focus}},created(){this.keyword=this.modelValue},watch:{value(e){this.keyword=e},modelValue(e){this.keyword=e}},computed:{showActionBtn(){return!this.animation&&this.showAction}},methods:{keywordChange(){this.$emit("input",this.keyword),this.$emit("update:modelValue",this.keyword),this.$emit("change",this.keyword)},inputChange(e){this.keyword=e.detail.value,this.keywordChange()},clear(){this.keyword="",this.$nextTick(()=>{this.$emit("clear")}),this.keywordChange()},search(e){this.$emit("search",e.detail.value);try{b()}catch(t){}},custom(){this.$emit("custom",this.keyword);try{b()}catch(e){}},getFocus(){this.focused=!0,this.animation&&this.showAction&&(this.show=!0),this.$emit("focus",this.keyword)},blur(){setTimeout(()=>{this.focused=!1},100),this.show=!1,this.$emit("blur",this.keyword)},clickHandler(){this.disabled&&this.$emit("click")},clickIcon(){this.$emit("clickIcon")}}},[["render",function(e,t,b,_,k,v){const w=r,C=o(a("uv-icon"),S),x=h,$=p;return s(),l(w,{class:"uv-search",onClick:v.clickHandler,style:n([{margin:e.margin},e.$uv.addStyle(e.customStyle)])},{default:c(()=>[i(w,{class:"uv-search__content",style:n([{backgroundColor:e.bgColor,borderRadius:"round"==e.shape?"100px":"4px",borderColor:e.borderColor},e.$uv.addStyle(e.boxStyle)])},{default:c(()=>[e.disabled?(s(),l(w,{key:0,class:"uv-search__content__disabled"})):u("",!0),d(e.$slots,"prefix",{},()=>[i(w,{class:"uv-search__content__icon"},{default:c(()=>[i(C,{onClick:v.clickIcon,size:e.searchIconSize,name:e.searchIcon,color:e.searchIconColor?e.searchIconColor:e.color},null,8,["onClick","size","name","color"])]),_:1})],!0),i(x,{"confirm-type":"search",onBlur:v.blur,value:k.keyword,onConfirm:v.search,onInput:v.inputChange,disabled:e.disabled,onFocus:v.getFocus,focus:e.focus,maxlength:e.maxlength,"placeholder-class":"uv-search__content__input--placeholder",placeholder:e.placeholder,"placeholder-style":`color: ${e.placeholderColor}`,class:"uv-search__content__input",type:"text",style:n([{textAlign:e.inputAlign,color:e.color,backgroundColor:e.bgColor,height:e.$uv.addUnit(e.height)},e.inputStyle])},null,8,["onBlur","value","onConfirm","onInput","disabled","onFocus","focus","maxlength","placeholder","placeholder-style","style"]),k.keyword&&e.clearabled&&k.focused?(s(),l(w,{key:1,class:"uv-search__content__icon uv-search__content__close",onClick:v.clear},{default:c(()=>[i(C,{name:"close",size:"11",color:"#ffffff",customStyle:"line-height: 12px"})]),_:1},8,["onClick"])):u("",!0),d(e.$slots,"suffix",{},void 0,!0)]),_:3},8,["style"]),i($,{style:n([e.actionStyle]),class:f(["uv-search__action",[(v.showActionBtn||k.show)&&"uv-search__action--active"]]),onClick:y(v.custom,["stop","prevent"])},{default:c(()=>[m(g(e.actionText),1)]),_:1},8,["style","class","onClick"])]),_:3},8,["onClick","style"])}],["__scopeId","data-v-b3baced2"]]);export{w as _};
