import{_ as e}from"./submitButton.Cin0VP_1.js";import{_ as t}from"./navbar.vue_vue_type_script_setup_true_lang.CSl8hyKC.js";import{d as a,k as l,u as s,r as n,D as u,J as o,c as i,e as r,h as d,w as c,i as p,a2 as f,j as m,a as _,b,o as x,g,t as v,f as y,a7 as j,H as w}from"./index-CIPK2z2P.js";import{b as k,_ as h}from"./uv-icon.UcuauzO0.js";import{_ as O}from"./uv-input.CdM7e6ra.js";import{g as z,w as I}from"./bank._E669YjS.js";import{S}from"./select-popup.B1Gjkpfl.js";import{d as T}from"./debounce.Ce2HeGXN.js";import"./uv-status-bar.BkslDeIT.js";import"./empty.vue_vue_type_script_setup_true_lang.BXZPGwCi.js";import"./uv-empty.BH_ZJrMJ.js";import"./uv-skeletons.D1UL33yi.js";import"./uv-popup.ewhZSqs9.js";import"./uv-transition.tIadgx1N.js";import"./decimal.B1oHnkff.js";const V=h(a({__name:"fund",setup(a){const h=l().userInfo,{t:V}=s(),$=n(),A=u({}),B=n(),C=n(),D=async()=>{try{const{data:e}=await z($.value);Object.assign(A,e)}catch(e){}},q=e=>{$.value=e,D()},J=()=>{w(()=>{C.value.open()})},P=T(async()=>{if(B.value||!(B.value<=0))try{await f(),await I($.value,B.value),m("/pages/my/wallet/tips")}catch(e){}},300,{leading:!0,trailing:!0});return o(e=>{(null==e?void 0:e.id)&&($.value=e.id,D())}),(a,l)=>{const s=t,n=p,u=y,o=_(b("uv-icon"),k),f=_(b("uv-input"),O),m=e;return x(),i("div",null,[r(s,{"bg-color":"transparent",title:d(V)("wallet.settle"),scrollTextColor:"blackOne",isDarkMode:!0},null,8,["title"]),r(n,{class:"p-32"},{default:c(()=>[r(n,{class:"leading-44 font-semibold text-blackOne text-32 mb-24"},{default:c(()=>[g(v(d(V)("fund.settlementAccount")),1)]),_:1}),r(n,{class:"bg-white rounded-30 p-32 flex justify-between mb-32",onClick:J},{default:c(()=>[r(n,{class:"flex items-center flex-1"},{default:c(()=>[r(u,{class:"w-48 h-48",src:d(A).type_logo,mode:"scaleToFill"},null,8,["src"]),r(n,{class:"ml-20 text-28 leading-40 text-blackOne font-semibold line-clamp-1 flex-1"},{default:c(()=>[g(v(d(A).type_name),1)]),_:1})]),_:1}),r(n,{class:"font-din text-32 font-semibold text-blackOne ml-20 flex"},{default:c(()=>[g(v(d(A).account)+" ",1),r(o,{name:"arrow-right",class:"ml-8",size:"28rpx",color:"#727A86"})]),_:1})]),_:1}),r(n,{class:"leading-44 font-semibold text-blackOne text-32 mb-24"},{default:c(()=>[g(v(d(V)("fund.paymentDetails")),1)]),_:1}),r(n,{class:"bg-white rounded-30 px-32"},{default:c(()=>[r(n,{class:"py-40"},{default:c(()=>[r(n,{class:"flex items-center"},{default:c(()=>[r(n,{class:"leading-68 text-60 font-din mr-20"},{default:c(()=>[g("$")]),_:1}),r(f,{type:"digit",modelValue:d(B),"onUpdate:modelValue":l[0]||(l[0]=e=>j(B)?B.value=e:null),fontSize:"50rpx",placeholder:`${d(V)("fund.Input")}`,border:"none"},null,8,["modelValue","placeholder"])]),_:1})]),_:1}),r(n,{class:"py-28 border-1 border-t flex justify-between"},{default:c(()=>[r(n,{class:"text-blackTwo text-26 leading-36"},{default:c(()=>[g(v(d(V)("fund.balance"))+" : "+v(`$${d(h).zqian}`),1)]),_:1}),r(n,{class:"text-blueOne text-26 leading-36 ml-20 cursor-pointer",onClick:l[1]||(l[1]=e=>B.value=d(h).zqian)},{default:c(()=>[g(v(d(V)("fund.withdrawAll")),1)]),_:1})]),_:1})]),_:1}),r(n,{class:"mt-40 mb-10 font-semibold text-26 leading-32 text-greyOne"},{default:c(()=>[g(v(d(V)("fund.withdrawalInstructions")),1)]),_:1}),r(n,{class:"text-26 leading-32 text-greyOne mb-10"},{default:c(()=>[g(v(d(V)("fund.tip1")),1)]),_:1}),r(n,{class:"text-26 leading-32 text-greyOne mb-10"},{default:c(()=>[g(v(d(V)("fund.tip2")),1)]),_:1}),r(n,{class:"text-26 leading-32 text-greyOne mb-10"},{default:c(()=>[g(v("1"===d(A).type?d(V)("fund.tip4"):d(V)("fund.tip3")),1)]),_:1})]),_:1}),r(m,{status:d(B)>0,title:"sys.save",onBack:d(P),bg:"bg-transparent"},null,8,["status","title","onBack"]),r(S,{ref_key:"selectPopup",ref:C,jump:!1,"onSelect:bank":q},null,512)])}}}),[["__scopeId","data-v-16e4cb58"]]);export{V as default};
