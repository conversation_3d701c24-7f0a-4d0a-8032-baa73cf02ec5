import{_ as e,a as t}from"./stepsIcon.BOs4boEW.js";import{d as a,ao as r,u as l,ap as s,a as o,b as u,v as i,o as n,w as p,e as c,x as d,i as m,g as y,t as f,h as v,ab as b,j as x,c as _,F as k,z as h,U as g,s as w,f as j,r as U,D as L,n as O,I as T,J as z,a7 as B}from"./index-BBirLt11.js";import{b as F,_ as I}from"./uv-icon.Dp0oPivN.js";import{_ as M}from"./uv-input.Q1TIxLPl.js";import{_ as A,a as C}from"./uv-steps.stmGVDDL.js";import{_ as V}from"./navbar.vue_vue_type_script_setup_true_lang.DC1OHhyC.js";import{c as D}from"./country.DPybxyM6.js";import"./uv-text.D1tdgrqN.js";import"./uv-status-bar.CB0-bOg6.js";import"./debounce.Ce2HeGXN.js";const E=[{title:"city",type:1,placeholder:"请选择城市",key:"country"},{title:"mobile",type:2,placeholder:"Mobile",key:"phone"},{title:"group",type:1,placeholder:"Please select",key:"group"},{title:"expertise",type:1,placeholder:"请选择专长",key:"expertise"},{title:"behavior",type:1,placeholder:"请选择行为",key:"behavior"}],S=a({__name:"form-item",props:r({item:{type:Object,required:!0}},{value:{},valueModifiers:{},root:{},rootModifiers:{}}),emits:["update:value","update:root"],setup(e){const{t:t}=l(),a=s(e,"value"),r=s(e,"root");return(l,s)=>{const _=m,k=o(u("uv-icon"),F),h=o(u("uv-input"),M);return n(),i(_,null,{default:p(()=>[c(_,{class:"text-24 leading-34 text-blackTwo mb-12"},{default:p(()=>[y(f(v(t)(`improve.${e.item.title}`)),1)]),_:1}),3!==e.item.type?(n(),i(_,{key:0,onClick:s[2]||(s[2]=t=>(e=>{switch(e.title){case"city":x("/pages/my/improve/country?back=to");break;case"phone":return}})(e.item)),class:"w-622 h-88 bg-[#f6f7f9] rounded-20 mb-24 px-28 py-24 flex items-center justify-between"},{default:p(()=>[c(_,{class:"flex items-center"},{default:p(()=>[2===e.item.type?(n(),i(_,{key:0,class:"pr-28 text-blackOne text-28 flex border-red border-r-2 h-40 mr-28",onClick:s[0]||(s[0]=b(()=>{},["stop"]))},{default:p(()=>[c(_,{class:"min-w-86 mr-12 text-center"},{default:p(()=>{var e,t;return[y(f((null==(e=r.value)?void 0:e.root)+(null==(t=r.value)?void 0:t.suffixes[0])),1)]}),_:1}),c(k,{name:"arrow-down",size:"20rpx",color:"#9BA7B5"})]),_:1})):d("",!0),c(h,{type:2!==e.item.type?"text":"number",fontSize:"28rpx",color:"#131726",modelValue:a.value,"onUpdate:modelValue":s[1]||(s[1]=e=>a.value=e),readonly:2!==e.item.type,placeholder:1===e.item.type?v(t)("improve.placeholder"):2===e.item.type?v(t)(`improve.${e.item.title}`):"",class:"text-24 text-blackTwo leading-34 w-auto",border:"none"},null,8,["type","modelValue","readonly","placeholder"])]),_:1}),1===e.item.type?(n(),i(k,{key:0,name:"arrow-right",size:"28rpx",color:"#9BA7B5"})):d("",!0)]),_:1})):d("",!0)]),_:1})}}}),$=I({__name:"steps",props:{current:{type:Number,default:0},setList:{type:Array,default:()=>[]}},setup:t=>(a,r)=>{const l=j,s=g,v=o(u("uv-steps-item"),A),b=o(u("uv-steps"),C),x=m;return n(),_("div",null,[c(x,{class:"warp pl-60 pr-60"},{default:p(()=>[c(x,{class:"flex justify-center m-auto"},{default:p(()=>[c(b,{current:t.current,dot:""},{default:p(()=>[(n(!0),_(k,null,h(t.setList,(a,r)=>(n(),i(v,{key:a},{icon:p(()=>[r<=t.current?(n(),i(l,{key:0,src:e,class:"w-24 h-24",mode:"aspectFill"})):d("",!0)]),title:p(()=>[c(s,{class:w(["text-24 leading-32",r<=t.current?"text-blueOne":"text-blackTwo"])},{default:p(()=>[y(f(a),1)]),_:2},1032,["class"])]),_:2},1024))),128))]),_:1},8,["current"])]),_:1})]),_:1})])}},[["__scopeId","data-v-feffd3f5"]]),q=a({__name:"index",setup(e){const{t:a}=l(),r=U(0),s=L({country:"",phone:"",group:"",expertise:"",behavior:""}),o=U();return O(()=>{T("country-selected",e=>{e&&(s.country=e.name||"",o.value=(null==e?void 0:e.idd)||[{root:"",suffixes:[]}])})}),z(e=>{if(null==e?void 0:e.country){const t=D.find(t=>t.name===e.country);t&&(s.country=t.name,o.value=t.idd)}}),(e,l)=>{const u=V,d=m,b=$,x=S,g=t;return n(),_(k,null,[c(d,null,{default:p(()=>[c(u,{"bg-color":"transparent",title:v(a)("improve.title"),scrollTextColor:"blackOne",isDarkMode:!0},null,8,["title"]),c(d,{class:"w-686 m-auto mt-20 px-32 py-16 bg-[#E9EFF9] text-blueOne text-24 leading-34 rounded-20"},{default:p(()=>[y(f(v(a)("improve.tips")),1)]),_:1}),c(b,{class:"my-40",current:v(r),setList:[v(a)("improve.steps1"),v(a)("improve.steps2")]},null,8,["current","setList"]),c(d,{class:"pl-32 text-black font-semibold text-32 leading-44"},{default:p(()=>[y(f(v(a)("improve.briefIntroduction")),1)]),_:1}),c(d,{class:"p-32 pb-40 rounded-30 bg-white w-686 m-auto mt-20"},{default:p(()=>[(n(!0),_(k,null,h(v(E),e=>(n(),i(d,{key:e.type},{default:p(()=>[c(x,{item:e,value:v(s)[e.key],"onUpdate:value":t=>v(s)[e.key]=t,root:v(o),"onUpdate:root":l[0]||(l[0]=e=>B(o)?o.value=e:null)},null,8,["item","value","onUpdate:value","root"])]),_:2},1024))),128))]),_:1})]),_:1}),c(g,{current:v(r),isLogin:!1,text:"sure",onUpdate:l[1]||(l[1]=e=>r.value=e)},null,8,["current"])],64)}}});export{q as default};
