var t,e;import{_ as a}from"./empty.vue_vue_type_script_setup_true_lang.SETk5GIM.js";import{d as s,a as i,b as l,v as r,o as n,w as o,e as u,a9 as d,i as p,g as c,t as h,h as m,P as v,a1 as _,aa as g,A as y,x as f,q as w,s as b,c as x,F as k,z as j,a0 as $,u as S,r as T,D as N,m as W,E as H,n as U,H as C,j as O,K as A}from"./index-BBirLt11.js";import{_ as z}from"./uv-parse.CpsDxc7n.js";import{_ as L,m as E,a as B}from"./uv-icon.Dp0oPivN.js";import{_ as I}from"./navbar.vue_vue_type_script_setup_true_lang.DC1OHhyC.js";import{_ as D}from"./uv-tabs.DzY9siFk.js";import{_ as R}from"./uv-sticky.SyKwVUE-.js";import{_ as M}from"./uv-load-more.SNzw0348.js";import{g as P}from"./details.uFiKUvam.js";import{N as q}from"./system.DZjCJFRG.js";import{D as F}from"./decimal.B1oHnkff.js";import"./uv-empty.Xz_IG7Bm.js";import"./uv-status-bar.CB0-bOg6.js";import"./debounce.Ce2HeGXN.js";import"./uv-line.CNQanRFo.js";import"./uv-loading-icon.G79bokgG.js";const J=L(s({__name:"notice-item",props:{item:{type:Object,required:!0},code:{type:Number,default:1}},setup:t=>(e,a)=>{const s=p,y=i(l("uv-parse"),z);return n(),r(s,{class:"p-32 bg-white rounded-30 relative"},{default:o(()=>[u(s,{class:"text-blackOne text-28 leading-40 font-semibold"},{default:o(()=>[c(h(m(v)(t.item.title)),1)]),_:1}),u(s,{class:"text-blackTwo text-24 leading-34 my-20 line-clamp-2"},{default:o(()=>[u(y,{content:m(_)(t.item.nr?m(v)(t.item.nr):m(v)(t.item.ms))},null,8,["content"])]),_:1}),u(s,{class:"text-blackThree leading-34 text-24"},{default:o(()=>[c(h(t.item.tjsj?m(v)(t.item.tjsj):m(v)(t.item.publish_sj)),1)]),_:1}),d(u(s,{class:"absolute right-20 rounded-20 top-20 w-16 h-16 bg-orange rounded-full flex items-center justify-center"},null,512),[[g,!t.item.is_read]])]),_:1})}}),[["__scopeId","data-v-d7a02790"]]);const K=L({name:"uv-skeleton",mixins:[E,B,{props:{loading:{type:Boolean,default:!0},animate:{type:Boolean,default:!0},rows:{type:[String,Number],default:0},rowsWidth:{type:[String,Number,Array],default:"100%"},rowsHeight:{type:[String,Number,Array],default:18},rowsLeft:{type:[String,Number,Array],default:0},title:{type:Boolean,default:!0},titleWidth:{type:[String,Number],default:"50%"},titleHeight:{type:[String,Number],default:18},avatar:{type:Boolean,default:!1},avatarSize:{type:[String,Number],default:32},avatarShape:{type:String,default:"circle"},...null==(e=null==(t=uni.$uv)?void 0:t.props)?void 0:e.skeleton}}],data:()=>({width:0}),watch:{loading(){this.getComponentWidth()}},computed:{rowsArray(){/%$/.test(this.rowsHeight)&&this.$uv.error("rowsHeight参数不支持百分比单位");const t=[];for(let e=0;e<this.rows;e++){let a={},s=this.$uv.test.array(this.rowsWidth)?this.rowsWidth[e]||(e===this.row-1?"70%":"100%"):e===this.rows-1?"70%":this.rowsWidth,i=this.$uv.test.array(this.rowsHeight)?this.rowsHeight[e]||"18px":this.rowsHeight,l=this.$uv.test.array(this.rowsLeft)?this.rowsLeft[e]||0:this.rowsLeft;a.marginTop=this.title||0!==e?this.title&&0===e?"20px":"12px":0,/%$/.test(s)?a.width=this.$uv.addUnit(this.width*parseInt(s)/100):a.width=this.$uv.addUnit(s),a.height=this.$uv.addUnit(i),a.marginLeft=this.$uv.addUnit(l),t.push(a)}return t},uTitleWidth(){let t=0;return t=/%$/.test(this.titleWidth)?this.$uv.addUnit(this.width*parseInt(this.titleWidth)/100):this.$uv.addUnit(this.titleWidth),this.$uv.addUnit(t)}},mounted(){this.init()},methods:{init(){this.getComponentWidth()},async setNvueAnimation(){},async getComponentWidth(){await this.$uv.sleep(20),this.$uvGetRect(".uv-skeleton__wrapper__content").then(t=>{this.width=t.width})}}},[["render",function(t,e,a,s,i,l){const d=p;return n(),r(d,{class:"uv-skeleton"},{default:o(()=>[t.loading?(n(),r(d,{key:0,class:"uv-skeleton__wrapper",ref:"uv-skeleton__wrapper",style:{display:"flex","flex-direction":"row"}},{default:o(()=>[t.avatar?(n(),r(d,{key:0,class:b(["uv-skeleton__wrapper__avatar",[`uv-skeleton__wrapper__avatar--${t.avatarShape}`,t.animate&&"animate"]]),style:w({height:t.$uv.addUnit(t.avatarSize),width:t.$uv.addUnit(t.avatarSize)})},null,8,["class","style"])):f("",!0),u(d,{class:"uv-skeleton__wrapper__content",ref:"uv-skeleton__wrapper__content",style:{flex:"1"}},{default:o(()=>[t.title?(n(),r(d,{key:0,class:b(["uv-skeleton__wrapper__content__title",[t.animate&&"animate"]]),style:w({width:l.uTitleWidth,height:t.$uv.addUnit(t.titleHeight)})},null,8,["style","class"])):f("",!0),(n(!0),x(k,null,j(l.rowsArray,(e,a)=>(n(),r(d,{class:b(["uv-skeleton__wrapper__content__rows",[t.animate&&"animate"]]),key:a,style:w({width:e.width,height:e.height,marginTop:e.marginTop,marginLeft:e.marginLeft})},null,8,["class","style"]))),128))]),_:1},512)]),_:1},512)):y(t.$slots,"default",{key:1},void 0,!0)]),_:3})}],["__scopeId","data-v-997a7d7e"]]),X=s({__name:"index",setup(t){const{t:e}=S(),s=T(!0),d=T([]),v=T(!0),_=T(1),g=T("loading"),y=T([]),w=N({type:1,page:1,limit:10}),z=T(0),L=T(0),E=T(0);W(t=>{L.value=t.scrollTop});const B=t=>{t.index&&(t.badge.value=0),d.value=[],w.page=1,z.value=0,s.value=!0,v.value=!t.index,1===t.index?w.type=2:Object.assign(w,JSON.parse(null==t?void 0:t.search_json)),X()},X=async()=>{try{const{data:t,count:e}=await(t=>{const e=2===(null==t?void 0:t.type)?{page:t.page,limit:t.limit}:{page:t.page,limit:t.limit,type:t.type},a=2!==t.type?"/api/site_notice/get_list":"/api/site_notice/get_znx_list";return $({url:a,method:"POST",data:e})})(w);t&&t.length&&(1===w.page?d.value=t:d.value=d.value.concat(t)),E.value=e??0,F(w.page*w.limit).toNumber()>=E.value?g.value="noMore":g.value=""}catch(t){}finally{s.value=!1}};return H(()=>{F(w.page*w.limit).toNumber()<E.value?(g.value="loading",w.page+=1,X()):g.value="noMore"}),U(()=>{C(()=>{(async()=>{y.value=[];try{const{data:t}=await A();null==t||t.forEach((t,e)=>{y.value.push({...t,badge:{value:0==e?t.child[1].count:t.count}})}),_.value=0,X()}catch(t){}})()})}),(t,$)=>{const S=I,T=i(l("uv-tabs"),D),N=p,W=i(l("uv-sticky"),R),H=J,U=i(l("uv-load-more"),M),C=a,A=i(l("uv-skeleton"),K);return n(),x(k,null,[u(S,{"bg-color":"transparent",title:m(e)("navTitle.notifications"),scrollTextColor:"blackOne",isDarkMode:!0},null,8,["title"]),u(N,null,{default:o(()=>[u(W,null,{default:o(()=>[u(N,{class:b(["px-32",m(L)?"bg-white pb-32  shadow-md":"pb-0"])},{default:o(()=>[u(T,{list:m(y),activeStyle:{color:"#131726",fontWeight:"bold",fontSize:"28rpx"},itemStyle:{height:"72rpx"},lineColor:"#0165ff",inactiveStyle:{color:"#444B56"},current:m(_),onClick:B},null,8,["list","current"]),m(v)&&m(y).length?(n(),r(N,{key:0,class:"flex mt-20"},{default:o(()=>[(n(!0),x(k,null,j(m(y)[0].child,(t,e)=>(n(),r(N,{class:b(["mr-16 py-14 border px-20 border-box text-26 text-blackTwo rounded-20 border-transparent",{"!border-blueOne !text-blueOne ":e===m(z),"bg-white":m(L)||e===m(z)||!m(L)&&e!==m(z),"!bg-[#f5f5f5]":m(L)&&e!==m(z)}]),key:t.name,onClick:a=>((t,e)=>{e&&(y.value[0].badge.value=0),z.value=e,d.value=[],w.page=1,s.value=!0,Object.assign(w,JSON.parse(t.search_json)),X()})(t,e)},{default:o(()=>[c(h(t.name),1)]),_:2},1032,["class","onClick"]))),128))]),_:1})):f("",!0)]),_:1},8,["class"])]),_:1}),u(N,{class:"mt-32 px-32"},{default:o(()=>[u(A,{loading:m(s),rows:"4",rowsHeight:"200rpx"},{default:o(()=>[(n(!0),x(k,null,j(m(d),t=>(n(),r(N,{key:t.id,class:"mb-20 last:mb-0"},{default:o(()=>[u(H,{code:t.type,item:t,onClick:e=>(async t=>{switch(t.is_read=1,t.tz_type){case q.RICH_TEXT:case q.TABLE:O(`/pages/my/notice/detail?id=${t.id}&zt=4`);break;case q.FUND:await P(t.id),O(`/pages/details/index?id=${t.xm_id}`);break;case q.THREE_PARTIES:O(`/pages/my/notice/project?id=${t.xm_id}`);break;default:O(`/pages/my/notice/detail?id=${t.id}&code=${t.code}`)}})(t)},null,8,["code","item","onClick"])]),_:2},1024))),128)),m(d).length&&m(w).page>1?(n(),r(U,{key:0,status:m(g),class:"p-32",loadingText:m(e)("sys.loading"),nomoreText:m(e)("sys.noData")},null,8,["status","loadingText","nomoreText"])):f("",!0),m(s)||m(d).length?f("",!0):(n(),r(C,{key:1,class:"mt-[30%]"}))]),_:1},8,["loading"])]),_:1})]),_:1})],64)}}});export{X as default};
