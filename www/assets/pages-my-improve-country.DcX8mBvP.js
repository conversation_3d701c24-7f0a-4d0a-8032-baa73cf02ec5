var t,e,i,s;import{v as o,o as n,w as a,e as r,U as l,q as u,g as c,t as d,s as h,i as v,A as p,a as m,b as x,a8 as f,ab as g,c as _,F as y,z as I,d as $,r as b,ak as L,J as T,h as w,j as H,aq as k,l as S,u as C,am as R,H as N,al as V,av as A}from"./index-CIPK2z2P.js";import{_ as j,m as z,a as U}from"./uv-icon.UcuauzO0.js";import{_ as M}from"./uv-transition.tIadgx1N.js";import{c as P}from"./country.DPybxyM6.js";import{_ as D}from"./uv-search.CD68DrLn.js";import{_ as F}from"./uv-sticky.CXYYNICz.js";import{_ as B}from"./navbar.vue_vue_type_script_setup_true_lang.CSl8hyKC.js";import"./uv-status-bar.BkslDeIT.js";import"./debounce.Ce2HeGXN.js";const E=j({name:"uv-index-anchor",mixins:[z,U,{props:{text:{type:[String,Number],default:""},color:{type:String,default:"#606266"},size:{type:[String,Number],default:14},bgColor:{type:String,default:"#dedede"},height:{type:[String,Number],default:32},...null==(e=null==(t=uni.$uv)?void 0:t.props)?void 0:e.indexAnchor}}],data:()=>({parentData:{sticky:!0}}),created(){this.init()},methods:{init(){const t=this.$uv.$parent.call(this,"uv-index-list");if(!t)return this.$uv.error("uv-index-anchor必须要搭配uv-index-list组件使用");this.parentData.sticky=t.sticky,t.anchors.push(this);const e=this.$uv.$parent.call(this,"uv-index-item");if(!e)return this.$uv.error("uv-index-anchor必须要搭配uv-index-item组件使用");e.id=this.text.charCodeAt(0)}}},[["render",function(t,e,i,s,p,m){const x=l,f=v;return n(),o(f,{class:h(["uv-index-anchor",{"uv-index-anchor-sticky":p.parentData.sticky}]),ref:`uv-index-anchor-${t.text}`,style:u({height:t.$uv.addUnit(t.height),backgroundColor:t.bgColor})},{default:a(()=>[r(x,{class:"uv-index-anchor__text",style:u({fontSize:t.$uv.addUnit(t.size),color:t.color})},{default:a(()=>[c(d(t.text),1)]),_:1},8,["style"])]),_:1},8,["class","style"])}],["__scopeId","data-v-2a56b74b"]]);const q=j({name:"uv-index-item",mixins:[z,U],data:()=>({top:0,height:0,id:""}),created(){this.anchor={}},mounted(){this.init()},methods:{init(){if(this.getParentData("uv-index-list"),!this.parent)return this.$uv.error("uv-index-item必须要搭配uv-index-list组件使用");this.$uv.sleep().then(()=>{this.getIndexItemRect().then(t=>{this.top=Math.ceil(t.top),this.height=Math.ceil(t.height)})})},getIndexItemRect(){return new Promise(t=>{this.$uvGetRect(".uv-index-item").then(e=>{t(e)})})}}},[["render",function(t,e,i,s,r,l){const u=v;return n(),o(u,{class:h(["uv-index-item",[`uv-index-item-${r.id}`]]),id:`uv-index-item-${r.id}`},{default:a(()=>[p(t.$slots,"default",{},void 0,!0)]),_:3},8,["id","class"])}],["__scopeId","data-v-8d47cb6a"]]),G={props:{inactiveColor:{type:String,default:"#606266"},activeColor:{type:String,default:"#5677fc"},indexList:{type:Array,default:()=>[]},sticky:{type:Boolean,default:!0},customNavHeight:{type:[String,Number],default:0},...null==(s=null==(i=uni.$uv)?void 0:i.props)?void 0:s.indexList}};const O=j({name:"uv-index-list",mixins:[z,U,G],data:()=>({activeIndex:-1,touchmoveIndex:1,letterInfo:{height:0,itemHeight:0,top:0},indicatorHeight:50,touching:!1,scrollTop:0,scrollViewHeight:0,sys:"",scrolling:!1,scrollIntoView:"",hasHeight:0,timer:0,disTap:!1}),computed:{uIndexList(){return this.indexList.length?this.indexList:(()=>{const t=[],e="A".charCodeAt(0);for(let i=0;i<26;i++)t.push(String.fromCharCode(e+i));return t})()},indicatorTop(){const{top:t,itemHeight:e}=this.letterInfo;return Math.floor(t+e*this.activeIndex+e/2-this.indicatorHeight/2)-8}},watch:{uIndexList:{immediate:!0,handler(){this.$uv.sleep().then(()=>{this.setIndexListLetterInfo()})}}},created(){this.sys=this.$uv.sys(),this.children=[],this.anchors=[],this.init()},mounted(){this.setIndexListLetterInfo()},methods:{init(){this.scrollViewHeight=this.sys.windowHeight-this.$uv.getPx(this.customNavHeight)},touchStart(t){const e=t.changedTouches[0];if(!e||this.disTap)return;this.touching=!0;const{pageY:i}=e,s=this.getIndexListLetter(i);this.setValueForTouch(s)},touchMove(t){let e=t.changedTouches[0];if(!e||this.disTap)return;this.touching||(this.touching=!0);const{pageY:i}=e,s=this.getIndexListLetter(i);this.setValueForTouch(s)},touchEnd(t){this.$uv.sleep(300).then(()=>{this.touching=!1}),this.$emit("select",this.activeIndex)},getIndexListLetterRect(){return new Promise(t=>{this.$uvGetRect(".uv-index-list__letter").then(e=>{t(e)})})},setIndexListLetterInfo(){this.getIndexListLetterRect().then(t=>{const{top:e,height:i}=t,s=this.$uv.sys().windowHeight;let o=0;o=0==this.customNavHeight?this.$uv.sys().windowTop:this.$uv.getPx(this.customNavHeight),this.letterInfo={height:i,top:(s-i)/2+o/2,itemHeight:Math.floor(i/this.uIndexList.length)}})},getIndexListLetter(t){const{top:e,height:i,itemHeight:s}=this.letterInfo;return(t+=this.$uv.sys().windowTop)<e?0:t>=e+i?this.uIndexList.length-1:Math.floor((t-e)/s)},setValueForTouch(t){t!==this.activeIndex&&(this.activeIndex=t,this.scrollIntoView=`uv-index-item-${this.uIndexList[t].charCodeAt(0)}`)},getHeaderRect(){return new Promise(t=>{dom.getComponentRect(this.$refs.header,e=>{t(e.size)})})},async scrollHandler(t){if(this.touching||this.scrolling)return;this.scrolling=!0,this.disTap=!0,clearTimeout(this.timer),this.timer=setTimeout(()=>{this.disTap=!1},200),this.$uv.sleep(30).then(()=>{this.scrolling=!1});let e=0;const i=this.children.length;let s=this.children;this.anchors;const o=this.$uv.getPx(this.customNavHeight);e=t.detail.scrollTop;for(let n=0;n<i;n++){const t=s[n],a=s[n+1];if(e+o<=s[0].top||e>=s[i-1].top+s[i-1].height){this.activeIndex=-1;break}if(!a){this.activeIndex=i-1;break}if(e+o>t.top&&e+o<a.top){this.activeIndex=n;break}}}}},[["render",function(t,e,i,s,$,b){const L=v,T=f,w=l,H=m(x("uv-transition"),M);return n(),o(L,{class:"uv-index-list"},{default:a(()=>[r(T,{scrollTop:$.scrollTop,scrollIntoView:$.scrollIntoView,"offset-accuracy":1,style:u({maxHeight:t.$uv.addUnit($.scrollViewHeight,"px")}),"scroll-y":"",onScroll:b.scrollHandler,ref:"uvList"},{default:a(()=>[r(L,null,{default:a(()=>[p(t.$slots,"header",{},void 0,!0)]),_:3}),p(t.$slots,"default",{},void 0,!0),r(L,null,{default:a(()=>[p(t.$slots,"footer",{},void 0,!0)]),_:3})]),_:3},8,["scrollTop","scrollIntoView","style","onScroll"]),r(L,{class:"uv-index-list__letter",ref:"uv-index-list__letter",style:u({top:t.$uv.addUnit($.letterInfo.top||100,"px")}),onTouchstart:b.touchStart,onTouchmove:g(b.touchMove,["stop","prevent"]),onTouchend:g(b.touchEnd,["stop","prevent"]),onTouchcancel:g(b.touchEnd,["stop","prevent"])},{default:a(()=>[(n(!0),_(y,null,I(b.uIndexList,(e,i)=>(n(),o(L,{class:"uv-index-list__letter__item",key:i,style:u({backgroundColor:$.activeIndex===i?t.activeColor:"transparent"})},{default:a(()=>[r(w,{class:"uv-index-list__letter__item__index",style:u({color:$.activeIndex===i?"#fff":t.inactiveColor})},{default:a(()=>[c(d(e),1)]),_:2},1032,["style"])]),_:2},1032,["style"]))),128))]),_:1},8,["style","onTouchstart","onTouchmove","onTouchend","onTouchcancel"]),r(H,{mode:"fade",show:$.touching,customStyle:{position:"fixed",right:"40px",top:t.$uv.addUnit(b.indicatorTop,"px"),zIndex:2}},{default:a(()=>[r(L,{class:"uv-index-list__indicator__box"},{default:a(()=>[r(L,{class:h(["uv-index-list__indicator",["uv-index-list__indicator--show"]]),style:u({height:t.$uv.addUnit($.indicatorHeight,"px"),width:t.$uv.addUnit($.indicatorHeight,"px")})},{default:a(()=>[r(w,{class:"uv-index-list__indicator__text"},{default:a(()=>[c(d(b.uIndexList[$.activeIndex]),1)]),_:1})]),_:1},8,["style"])]),_:1})]),_:1},8,["show","customStyle"])]),_:3})}],["__scopeId","data-v-4fd3abb5"]]),W=j($({__name:"countrys",props:{val:{type:String,default:""}},emits:["call-back"],setup(t,{emit:e}){const i=t,s=["A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"],u=e,h=b(""),p=b([]);p.value=s.map(t=>P.filter(e=>e.name.startsWith(t))).filter(t=>t.length>0);L(()=>i.val,t=>{const e=t.toLowerCase(),i=P.filter(t=>t.name.toLowerCase().includes(e));p.value=s.map(t=>i.filter(e=>{var i;return(null==(i=e.name[0])?void 0:i.toLowerCase())===t.toLowerCase()})).filter(t=>t.length>0),f.value=s.filter(t=>i.some(e=>{var i;return(null==(i=e.name[0])?void 0:i.toLowerCase())===t.toLowerCase()}))});const f=b([]);return f.value=s.filter(t=>P.some(e=>e.name.startsWith(t))),T(t=>{(null==t?void 0:t.back)&&(h.value=t.back)}),(t,e)=>{const i=m(x("uv-index-anchor"),E),s=v,g=l,$=m(x("uv-index-item"),q),b=m(x("uv-index-list"),O);return n(),_("div",{id:"countrys"},[r(b,{indexList:w(f),customNavHeight:"142.296875px"},{default:a(()=>[(n(!0),_(y,null,I(w(p),(t,e)=>(n(),o($,{key:e},{default:a(()=>[r(i,{text:w(f)[e]},null,8,["text"]),(n(!0),_(y,null,I(t,(t,e)=>(n(),o(s,{class:"list pl-32",key:e,onClick:e=>{return u("call-back",i=t),void(h.value?(H("","back"),k("country-selected",i)):H(`/pages/my/improve/index?country=${i.name}`,"redirect"));var i}},{default:a(()=>[r(s,{class:"list__item flex items-center"},{default:a(()=>[r(s,{class:"!text-48"},{default:a(()=>[c(d(t.flag),1)]),_:2},1024),r(g,{class:"ml-20"},{default:a(()=>{var e;return[c(d(t.idd.root+(null==(e=t.idd)?void 0:e.suffixes[0])),1)]}),_:2},1024),r(s,{class:"ml-20"},{default:a(()=>[c(d(t.name),1)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onClick"]))),128))]),_:2},1024))),128))]),_:1},8,["indexList"])])}}}),[["__scopeId","data-v-2807fc9a"]]),Y=$({__name:"all-search",props:{codeTxt:{type:String,default:""},searchIconSize:{type:String,default:"34px"}},emits:["update:modelValue"],setup(t,{emit:e}){const i=S(),s=b(i.statusBarHeight||0),{t:l}=C(),u=b(""),c=e;return(e,i)=>{const d=m(x("uv-search"),D),h=v,p=m(x("uv-sticky"),F);return n(),o(p,{customNavHeight:`${w(s)+44}px`},{default:a(()=>[r(h,{class:"w-750 px-30 py-20 bg-white"},{default:a(()=>[r(d,{shape:"square",showAction:!1,height:"88rpx",boxStyle:{background:"#fff",border:"1px solid #e5e5e5",borderRadius:"20rpx"},searchIconSize:t.searchIconSize,placeholder:w(l)(t.codeTxt),"model:value":w(u),onInput:i[0]||(i[0]=t=>c("update:modelValue",t)),searchIcon:"/src/static/image/home/<USER>",bgColor:"#fff"},null,8,["searchIconSize","placeholder","model:value"])]),_:1})]),_:1},8,["customNavHeight"])}}}),J=$({__name:"country",setup(t){const e=S(),i=b(),s=b(e.statusBarHeight||0),l=b(""),u=b(0),{t:h}=C();return R(()=>{N(()=>{var t;V().in(null==(t=A())?void 0:t.proxy).select("#allSearchRef").boundingClientRect(t=>{var e;t&&(Array.isArray(t)?u.value=(null==(e=t[0])?void 0:e.height)||0:u.value=t.height||0)}).exec()})}),(t,e)=>{const p=B,f=Y,g=v,_=m(x("uv-sticky"),F),y=W;return n(),o(g,{class:"bg-white min-h-[100vh]"},{default:a(()=>[r(p,{"bg-color":"transparent",title:w(h)("improve.countryTitle"),scrollTextColor:"blackOne",isDarkMode:!0},null,8,["title"]),r(f,{ref_key:"allSearchRef",ref:i,id:"allSearchRef",codeTxt:w(h)("improve.findCountry"),"onUpdate:modelValue":e[0]||(e[0]=t=>l.value=t)},null,8,["codeTxt"]),r(_,{customNavHeight:`${w(s)+112.53125+w(u)}px`},{default:a(()=>[r(g,{class:"px-32 bg-white text-blackOne text-28 leading-40 font-semibold"},{default:a(()=>[c(d(w(l).length?w(h)("improve.searchResults"):w(h)("improve.currentLocation")),1)]),_:1})]),_:1},8,["customNavHeight"]),r(y,{val:w(l)},null,8,["val"])]),_:1})}}});export{J as default};
