var e,t,s,i;import{v as a,x as r,o as l,w as o,g as n,t as u,q as d,s as h,U as c,a as p,b,e as v,i as _,A as g,a8 as f,c as m,F as y,z as w}from"./index-CIPK2z2P.js";import{_ as S,m as $,a as x}from"./uv-icon.UcuauzO0.js";const C={props:{isDot:{type:Boolean,default:!1},value:{type:[Number,String],default:""},show:{type:Boolean,default:!0},max:{type:[Number,String],default:999},type:{type:[String,void 0,null],default:"error"},showZero:{type:Boolean,default:!1},bgColor:{type:[String,null],default:null},color:{type:[String,null],default:null},shape:{type:[String,void 0,null],default:"circle"},numberType:{type:[String,void 0,null],default:"overflow"},offset:{type:Array,default:()=>[]},inverted:{type:Boolean,default:!1},absolute:{type:Boolean,default:!1},...null==(t=null==(e=uni.$uv)?void 0:e.props)?void 0:t.badge}};const B=S({name:"uv-badge",mixins:[$,x,C],computed:{boxStyle:()=>({}),badgeStyle(){const e={};if(this.color&&(e.color=this.color),this.bgColor&&!this.inverted&&(e.backgroundColor=this.bgColor),this.absolute&&(e.position="absolute",this.offset.length)){const t=this.offset[0],s=this.offset[1]||t;e.top=this.$uv.addUnit(t),e.right=this.$uv.addUnit(s)}return e},showValue(){switch(this.numberType){case"overflow":return Number(this.value)>Number(this.max)?this.max+"+":this.value;case"ellipsis":return Number(this.value)>Number(this.max)?"...":this.value;case"limit":return Number(this.value)>999?Number(this.value)>=9999?Math.floor(this.value/1e4*100)/100+"w":Math.floor(this.value/1e3*100)/100+"k":this.value;default:return Number(this.value)}},propsType(){return this.type||"error"}}},[["render",function(e,t,s,i,p,b){const v=c;return e.show&&(0!==Number(e.value)||e.showZero||e.isDot)?(l(),a(v,{key:0,class:h([[e.isDot?"uv-badge--dot":"uv-badge--not-dot",e.inverted&&"uv-badge--inverted","horn"===e.shape&&"uv-badge--horn",`uv-badge--${b.propsType}${e.inverted?"--inverted":""}`],"uv-badge"]),style:d([e.$uv.addStyle(e.customStyle),b.badgeStyle])},{default:o(()=>[n(u(e.isDot?"":b.showValue),1)]),_:1},8,["class","style"])):r("",!0)}],["__scopeId","data-v-79f791a7"]]);const T=S({name:"uv-tabs",emits:["click","change"],mixins:[$,x,{props:{duration:{type:Number,default:300},list:{type:Array,default:()=>[]},lineColor:{type:String,default:"#3c9cff"},activeStyle:{type:[String,Object],default:()=>({color:"#303133"})},inactiveStyle:{type:[String,Object],default:()=>({color:"#606266"})},lineWidth:{type:[String,Number],default:20},lineHeight:{type:[String,Number],default:3},lineBgSize:{type:String,default:"cover"},itemStyle:{type:[String,Object],default:()=>({height:"44px"})},scrollable:{type:Boolean,default:!0},current:{type:[Number,String],default:0},keyName:{type:String,default:"name"},...null==(i=null==(s=uni.$uv)?void 0:s.props)?void 0:i.tabs}}],data:()=>({firstTime:!0,scrollLeft:0,scrollViewWidth:0,lineOffsetLeft:0,tabsRect:{left:0},innerCurrent:0,moving:!1}),watch:{current:{immediate:!0,handler(e,t){e!==this.innerCurrent&&(this.innerCurrent=e,this.$nextTick(()=>{this.resize()}))}},list(){this.$nextTick(()=>{this.resize()})}},computed:{textStyle(){return e=>{const t={},s=e==this.innerCurrent?this.$uv.addStyle(this.activeStyle):this.$uv.addStyle(this.inactiveStyle);return this.list[e].disabled&&(t.color="#c8c9cc"),this.$uv.deepMerge(s,t)}},propsBadge:()=>C},async mounted(){this.init()},methods:{setLineLeft(){const e=this.list[this.innerCurrent];if(!e)return;let t=this.list.slice(0,this.innerCurrent).reduce((e,t)=>e+t.rect.width,0),s=this.$uv.getPx(this.lineWidth);this.$uv.test.number(this.lineWidth)&&this.$uv.unit&&(s=this.$uv.getPx(`${this.lineWidth}${this.$uv.unit}`)),this.lineOffsetLeft=t+(e.rect.width-s)/2,this.firstTime&&setTimeout(()=>{this.firstTime=!1},20)},animation(e,t=0){},clickHandler(e,t){this.$emit("click",{...e,index:t}),e.disabled||(this.innerCurrent!=t&&this.$emit("change",{...e,index:t}),this.innerCurrent=t,this.$nextTick(()=>{this.resize()}))},init(){this.$uv.sleep().then(()=>{this.resize()})},setScrollLeft(){const e=this.list[this.innerCurrent],t=this.list.slice(0,this.innerCurrent).reduce((e,t)=>e+t.rect.width,0),s=this.$uv.sys().windowWidth;let i=t-(this.tabsRect.width-e.rect.width)/2-(s-this.tabsRect.right)/2+this.tabsRect.left/2;i=Math.min(i,this.scrollViewWidth-this.tabsRect.width),this.scrollLeft=Math.max(0,i)},resize(){0!==this.list.length&&Promise.all([this.getTabsRect(),this.getAllItemRect()]).then(([e,t=[]])=>{this.tabsRect=e,this.scrollViewWidth=0,t.map((e,t)=>{this.scrollViewWidth+=e.width,this.list[t].rect=e}),this.setLineLeft(),this.setScrollLeft()})},getTabsRect(){return new Promise(e=>{this.queryRect("uv-tabs__wrapper__scroll-view").then(t=>e(t))})},getAllItemRect(){return new Promise(e=>{const t=this.list.map((e,t)=>this.queryRect(`uv-tabs__wrapper__nav__item-${t}`,!0));Promise.all(t).then(t=>e(t))})},queryRect(e,t){return new Promise(t=>{this.$uvGetRect(`.${e}`).then(e=>{t(e)})})}}},[["render",function(e,t,s,i,r,S){const $=c,x=p(b("uv-badge"),B),C=_,T=f;return l(),a(C,{class:"uv-tabs",style:d([e.$uv.addStyle(e.customStyle)])},{default:o(()=>[v(C,{class:"uv-tabs__wrapper"},{default:o(()=>[g(e.$slots,"left",{},void 0,!0),v(C,{class:"uv-tabs__wrapper__scroll-view-wrapper"},{default:o(()=>[v(T,{"scroll-x":e.scrollable,"scroll-left":r.scrollLeft,"scroll-with-animation":"",class:"uv-tabs__wrapper__scroll-view","show-scrollbar":!1,ref:"uv-tabs__wrapper__scroll-view"},{default:o(()=>[v(C,{class:"uv-tabs__wrapper__nav",ref:"uv-tabs__wrapper__nav",style:d({flex:e.scrollable?"":1})},{default:o(()=>[(l(!0),m(y,null,w(e.list,(t,s)=>(l(),a(C,{class:h(["uv-tabs__wrapper__nav__item",[`uv-tabs__wrapper__nav__item-${s}`,t.disabled&&"uv-tabs__wrapper__nav__item--disabled"]]),key:s,onClick:e=>S.clickHandler(t,s),ref_for:!0,ref:`uv-tabs__wrapper__nav__item-${s}`,style:d([{flex:e.scrollable?"":1},e.$uv.addStyle(e.itemStyle)])},{default:o(()=>[v($,{class:h([[t.disabled&&"uv-tabs__wrapper__nav__item__text--disabled"],"uv-tabs__wrapper__nav__item__text"]),style:d([S.textStyle(s)])},{default:o(()=>[n(u(t[e.keyName]),1)]),_:2},1032,["class","style"]),v(x,{show:!(!t.badge||!(t.badge.show||t.badge.isDot||t.badge.value)),isDot:t.badge&&t.badge.isDot||S.propsBadge.isDot,value:t.badge&&t.badge.value||S.propsBadge.value,max:t.badge&&t.badge.max||S.propsBadge.max,type:t.badge&&t.badge.type||S.propsBadge.type,showZero:t.badge&&t.badge.showZero||S.propsBadge.showZero,bgColor:t.badge&&t.badge.bgColor||S.propsBadge.bgColor,color:t.badge&&t.badge.color||S.propsBadge.color,shape:t.badge&&t.badge.shape||S.propsBadge.shape,numberType:t.badge&&t.badge.numberType||S.propsBadge.numberType,inverted:t.badge&&t.badge.inverted||S.propsBadge.inverted,customStyle:"margin-left: 4px;"},null,8,["show","isDot","value","max","type","showZero","bgColor","color","shape","numberType","inverted"])]),_:2},1032,["onClick","style","class"]))),128)),v(C,{class:"uv-tabs__wrapper__nav__line",ref:"uv-tabs__wrapper__nav__line",style:d([{width:e.$uv.addUnit(e.lineWidth),transform:`translate(${r.lineOffsetLeft}px)`,transitionDuration:`${r.firstTime?0:e.duration}ms`,height:r.firstTime?0:e.$uv.addUnit(e.lineHeight),background:e.lineColor,backgroundSize:e.lineBgSize}])},null,8,["style"])]),_:1},8,["style"])]),_:1},8,["scroll-x","scroll-left"])]),_:1}),g(e.$slots,"right",{},void 0,!0)]),_:3})]),_:3},8,["style"])}],["__scopeId","data-v-02da80e0"]]);export{T as _};
