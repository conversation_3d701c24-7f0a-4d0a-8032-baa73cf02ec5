var e,t;import{_ as a}from"./prompt-popup.vue_vue_type_script_setup_true_lang.CHEY8D5W.js";import{_ as l}from"./submitButton.Cin0VP_1.js";import{_ as o}from"./empty.vue_vue_type_script_setup_true_lang.BXZPGwCi.js";import{d as n,u as i,r as s,ae as u,H as r,a as d,b as c,v as p,o as m,w as f,e as y,i as b,g as h,t as g,h as v,a8 as x,x as _,a7 as j,c as S,F as k,z as T,f as I,ab as E,j as F,aV as C,q as $,U as V,s as B,ao as w,ap as N,D,aD as M,am as L,ak as A,P,a1 as U}from"./index-CIPK2z2P.js";import{_ as R}from"./uv-input.CdM7e6ra.js";import{b as O,_ as K,m as z,a as H}from"./uv-icon.UcuauzO0.js";import{_ as q,a as Y}from"./uv-radio-group.CHWPt9Cy.js";import{_ as J}from"./uv-load-more.DsJkRw2i.js";import{_ as G}from"./uv-popup.ewhZSqs9.js";import{d as Q}from"./debounce.Ce2HeGXN.js";import{_ as W,a as X}from"./uv-datetime-picker.CJSFIHl6.js";import{_ as Z}from"./uploadImag.CizL1aRL.js";import{a as ee}from"./navbar.vue_vue_type_script_setup_true_lang.CSl8hyKC.js";import{_ as te}from"./uv-parse.wZPhbfSD.js";import{_ as ae}from"./uv-picker.C6UOrbqb.js";import{P as le,F as oe,B as ne}from"./system.DZjCJFRG.js";import{c as ie}from"./media.Ds2RueMI.js";import{D as se}from"./decimal.B1oHnkff.js";const ue=K(n({__name:"neirongku-popup",props:{list:{type:Array,default:()=>[]},nrShow:{type:Boolean,default:!0},id:{type:String,default:""},title:{type:String,default:""},total:{type:Number,default:0},detailId:{type:String,default:""},value:{type:String,default:""},index:{type:Number,default:0}},emits:["select","scrollToLower","backValue"],setup(e,{expose:t,emit:a}){const{t:n}=i(),C=a,$=e,V=s(),B=s(""),w=()=>{$.total>$.list.length&&C("scrollToLower")},N=e=>{C("select",e)},D=Q(()=>{B.value?C("backValue",B.value):u(n("sys.pleaseSelect"))},300,{leading:!0,trailing:!0});return t({open(){r(()=>{$.value&&(B.value=$.value),V.value.open()})},close(){r(()=>{V.value.close()})}}),(t,a)=>{const i=b,s=d(c("uv-input"),R),u=I,r=d(c("uv-icon"),O),C=d(c("uv-radio"),q),M=d(c("uv-radio-group"),Y),L=d(c("uv-load-more"),J),A=x,P=o,U=l,K=d(c("uv-popup"),G);return m(),p(K,{ref_key:"popup",ref:V,mode:"bottom",closeable:"",overlayStyle:"background: rgba(0, 0, 0, 0.3);",round:"15"},{default:f(()=>[y(i,{class:"bg-[#F6F7F9] env-bottom"},{default:f(()=>[y(i,{class:"text-black font-semibold pt-26 pl-32"},{default:f(()=>[h(g(v(n)("sys.pleaseSelect")),1)]),_:1}),y(i,{class:"p-32 pt-42"},{default:f(()=>[y(s,{customStyle:{width:"686rpx",height:"88rpx",borderRadius:"20rpx",backgroundColor:"#FFFFFF"},onInput:N,clearable:"",placeholder:v(n)("sys.searchTip"),prefixIcon:"search",prefixIconStyle:"font-size: 22px;color: #909399"},null,8,["placeholder"])]),_:1}),e.nrShow?(m(),p(i,{key:0,class:"p-32 pt-0"},{default:f(()=>[y(A,{"scroll-y":"true","scroll-left":"120",class:"h-[50vh]",onScrolltolower:w},{default:f(()=>[y(M,{modelValue:v(B),"onUpdate:modelValue":a[0]||(a[0]=e=>j(B)?B.value=e:null),placement:"column",iconPlacement:"right",customStyle:{margin:"0"}},{default:f(()=>[(m(!0),S(k,null,T(e.list,e=>(m(),p(i,{key:e.id,class:"p-32 bg-white rounded-20 mb-20 last:mb-0"},{default:f(()=>[y(C,{name:e.id,class:"flex items-center mb-20"},{default:f(()=>[y(i,{class:"max-w-582 flex"},{default:f(()=>[y(u,{class:"w-176 h-260 rounded-20 mr-20",src:e.cover,mode:"aspectFill"},null,8,["src"]),y(i,null,{default:f(()=>[y(i,{class:"w-388 leading-36 text-28 font-semibold text-blackOne line-clamp-2"},{default:f(()=>[h(g(e.name),1)]),_:2},1024),y(i,{class:"w-388 line-clamp-4 text-24 leading-29 text-blackTwo mt-20"},{default:f(()=>[h(g(e.intro),1)]),_:2},1024),y(i,{class:"flex items-center text-blueOne text-24 leading-30 mt-12",onClick:E(t=>{return a=e.id,void F(`/pages/details/shortPlayInfo?id=${a}&xm_id=${$.id}&type=select&index=${$.index}`);var a},["stop"])},{default:f(()=>[h(g(v(n)("promotion.details"))+" ",1),y(r,{color:"#0165FF",size:"24rpx",class:"mt-2",name:"arrow-right"})]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1024)]),_:2},1032,["name"])]),_:2},1024))),128))]),_:1},8,["modelValue"]),e.list.length?(m(),p(L,{key:0,status:e.total>e.list.length?"loading":"noMore",class:"p-32",loadingText:v(n)("sys.loading"),nomoreText:v(n)("sys.noData")},null,8,["status","loadingText","nomoreText"])):_("",!0)]),_:1})]),_:1})):(m(),p(i,{key:1,class:"bg-[#F6F7F9] py-100 flex items-center flex-col"},{default:f(()=>[y(P)]),_:1}))]),_:1}),y(U,{onBack:v(D)},null,8,["onBack"])]),_:1},512)}}}),[["__scopeId","data-v-69f17060"]]);const re=K({name:"uv-textarea",mixins:[z,H,{props:{value:{type:[String,Number],default:""},modelValue:{type:[String,Number],default:""},placeholder:{type:[String,Number],default:""},placeholderClass:{type:String,default:"textarea-placeholder"},placeholderStyle:{type:[String,Object],default:"color: #c0c4cc"},height:{type:[String,Number],default:70},confirmType:{type:String,default:"return"},disabled:{type:Boolean,default:!1},count:{type:Boolean,default:!1},focus:{type:Boolean,default:!1},autoHeight:{type:Boolean,default:!1},fixed:{type:Boolean,default:!1},cursorSpacing:{type:Number,default:0},cursor:{type:[String,Number],default:""},showConfirmBar:{type:Boolean,default:!0},selectionStart:{type:Number,default:-1},selectionEnd:{type:Number,default:-1},adjustPosition:{type:Boolean,default:!0},disableDefaultPadding:{type:Boolean,default:!1},holdKeyboard:{type:Boolean,default:!1},maxlength:{type:[String,Number],default:140},border:{type:String,default:"surround"},formatter:{type:[Function,null],default:null},ignoreCompositionEvent:{type:Boolean,default:!0},confirmHold:{type:Boolean,default:!1},textStyle:{type:[Object,String],default:()=>{}},countStyle:{type:[Object,String],default:()=>{}},...null==(t=null==(e=uni.$uv)?void 0:e.props)?void 0:t.textarea}}],data:()=>({innerValue:"",focused:!1,innerFormatter:e=>e}),created(){this.innerValue=this.modelValue},watch:{value(e){this.innerValue=e},modelValue(e){this.innerValue=e}},computed:{textareaClass(){let e=[],{border:t,disabled:a}=this;return"surround"===t&&(e=e.concat(["uv-border","uv-textarea--radius"])),"bottom"===t&&(e=e.concat(["uv-border-bottom","uv-textarea--no-radius"])),a&&e.push("uv-textarea--disabled"),e.join(" ")},textareaStyle(){return this.$uv.deepMerge({},this.$uv.addStyle(this.customStyle))},maxlen(){return this.maxlength<0?this.maxlength<0?-1:140:this.maxlength},getCount(){try{return this.innerValue.length>this.maxlen?this.maxlen:this.innerValue.length}catch(e){return 0}}},methods:{setFormatter(e){this.innerFormatter=e},onFocus(e){this.$emit("focus",e)},onBlur(e){this.$emit("blur",e),this.$uv.formValidate(this,"blur")},onLinechange(e){this.$emit("linechange",e)},onInput(e){let{value:t=""}=e.detail||{};const a=(this.formatter||this.innerFormatter)(t);this.innerValue=t,this.$nextTick(()=>{this.innerValue=a,this.valueChange()})},valueChange(){const e=this.innerValue;this.$nextTick(()=>{this.$emit("input",e),this.$emit("update:modelValue",e),this.$emit("change",e),this.$uv.formValidate(this,"change")})},onConfirm(e){this.$emit("confirm",e)},onKeyboardheightchange(e){this.$emit("keyboardheightchange",e)}}},[["render",function(e,t,a,l,o,n){const i=C,s=V,u=b;return m(),p(u,{class:B(["uv-textarea",n.textareaClass]),style:$([n.textareaStyle])},{default:f(()=>[y(i,{class:"uv-textarea__field",value:o.innerValue,style:$([{height:e.autoHeight?"auto":e.$uv.addUnit(e.height)},e.$uv.addStyle(e.textStyle)]),placeholder:e.placeholder,"placeholder-style":e.$uv.addStyle(e.placeholderStyle,"string"),"placeholder-class":e.placeholderClass,disabled:e.disabled,focus:e.focus,autoHeight:e.autoHeight,fixed:e.fixed,cursorSpacing:e.cursorSpacing,cursor:e.cursor,showConfirmBar:e.showConfirmBar,selectionStart:e.selectionStart,selectionEnd:e.selectionEnd,adjustPosition:e.adjustPosition,disableDefaultPadding:e.disableDefaultPadding,holdKeyboard:e.holdKeyboard,maxlength:n.maxlen,confirmType:e.confirmType,ignoreCompositionEvent:e.ignoreCompositionEvent,"confirm-hold":e.confirmHold,onFocus:n.onFocus,onBlur:n.onBlur,onLinechange:n.onLinechange,onInput:n.onInput,onConfirm:n.onConfirm,onKeyboardheightchange:n.onKeyboardheightchange},null,8,["value","style","placeholder","placeholder-style","placeholder-class","disabled","focus","autoHeight","fixed","cursorSpacing","cursor","showConfirmBar","selectionStart","selectionEnd","adjustPosition","disableDefaultPadding","holdKeyboard","maxlength","confirmType","ignoreCompositionEvent","confirm-hold","onFocus","onBlur","onLinechange","onInput","onConfirm","onKeyboardheightchange"]),e.count&&-1!=n.maxlen?(m(),p(s,{key:0,class:"uv-textarea__count",style:$([{"background-color":e.disabled?"transparent":"#fff"},e.$uv.addStyle(e.countStyle)])},{default:f(()=>[h(g(n.getCount)+"/"+g(n.maxlen),1)]),_:1},8,["style"])):_("",!0)]),_:1},8,["class","style"])}],["__scopeId","data-v-b56fd13a"]]),de=K(n({__name:"promotion-item",props:w({obj:{type:Object,default:()=>({})},index:{type:Number,default:0},xmId:{type:String,default:""},detailId:{type:String,default:""},fromTheMedia:{type:String,default:""},lx:{type:String,default:""},bbList:{type:Array,default:()=>[]},bbTotal:{type:Number,default:0}},{value:{},valueModifiers:{}}),emits:w(["update:list","update:bbList","updateItem:list"],["update:value"]),setup(e,{expose:t,emit:l}){const{t:o}=i(),n=l,u=s([]),x=s(),I=s(),E=s(""),C=Number(new Date),$=s([]),V=s(0),B=s(""),w=s(),K=s(),z=s(),H=s(!0),q=N(e,"value"),Y=e,J=D({xm_id:"",page:1,limit:5,kssj:"",jssj:"",keyword:"",zt:le.PASSED}),G=D({xm_id:"",page:1,limit:1e3,name:""});J.xm_id=Y.xmId,G.xm_id=Y.xmId;const de=Q(()=>{const e=Y.obj;if(e.type===oe.SELECT||e.type===oe.MEDIA&&"myMedia"===Y.fromTheMedia)return u.value=Y.obj.child,void x.value.open();e.type===oe.DATE&&I.value.open(),e.type===oe.BB_ID&&w.value.open(),e.type===oe.CONTENT_LIBRARY&&(G.page=1,G.name="",K.value.open(),fe()),e.type===oe.MEDIA&&F(`/pages/details/media?id=${Y.xmId}&index=${Y.index}&bs=${Y.obj.bs}&defaultId=${q.value?q.value:""}&rules=${Y.obj.rules}`)},300,{leading:!0,trailing:!0}),ce=Q(()=>{se(J.page*J.limit).toNumber()<Y.bbTotal&&(J.page++,n("update:bbList",J))},300,{leading:!0,trailing:!0}),pe=e=>{J.keyword=e,J.page=1,n("update:bbList",J)},me=e=>{q.value=e,w.value.close()},fe=async()=>{try{const{data:e,count:t}=await M(G);e&&e.length&&t?(H.value=!0,1===G.page?$.value=e:$.value=$.value.concat(e),V.value=t??0,Ie(Y.obj.value)):H.value=!1}catch(e){}},ye=e=>Array.isArray(e)&&e.length>0,be=e=>{G.name=e,G.page=1,fe()},he=Q(()=>{G.page*G.limit<V.value&&(G.page++,fe())},300,{leading:!0,trailing:!0}),ge=e=>{q.value=e,G.name="",K.value.close()},ve=(e,t)=>{const a=t.find(t=>t.id===e);return a?a.name:""},xe=e=>{q.value=ke(e.value),I.value.close()},_e=e=>{const t=e.value[0];"myMedia"!==Y.fromTheMedia?(t.child.length&&t.id!==q.value&&t.child.length&&n("update:list",Y.obj,t.child,1,Y.index),q.value&&q.value!==t.id&&t.child.length&&n("update:list",Y.obj,t.child,2,Y.index,q.value)):t.child&&n("update:list",t.child,t),q.value=t.id,E.value=t.name},je=Q(()=>{Y.obj.tips&&z.value.open()},300,{leading:!0,trailing:!0}),Se=()=>{z.value.close()},ke=e=>{if(!e)return"";const t=new Date(e);return`${t.getFullYear()}-${(t.getMonth()+1).toString().padStart(2,"0")}-${t.getDate().toString().padStart(2,"0")}`},Te=e=>{const t=Y.bbList.find(t=>t.id===e);return t?t.uniq_bb_value:""},Ie=e=>{const t=$.value.find(t=>t.id===e);return t?t.name:""},Ee=async e=>{try{if(!e)return void(B.value="");const{data:t}=await ie(e??"");q.value=t.id,B.value=t.xsmc}catch(t){B.value=""}},Fe=(e,t)=>{if(e.is_multi===ne.IS_TRUE&&e.type===oe.INPUT){const a={...JSON.parse(JSON.stringify(e)),id:t?`${e.id}_${Date.now()}`:e.id,submit_value:t?"":e.submit_value};n("updateItem:list",a,q.value,3,Y.index)}};L(()=>{Y.obj.type===oe.CONTENT_LIBRARY&&fe(),Y.obj.type===oe.MEDIA&&"myMedia"!==Y.fromTheMedia&&q.value&&Ee(q.value)}),A(()=>q.value,e=>{Y.obj.type===oe.MEDIA&&"myMedia"!==Y.fromTheMedia&&(e?Ee(e):B.value="")});return t({getMediaLabel:Ee,closeNr:()=>{r(()=>{K.value.close()})}}),(t,l)=>{const i=b,s=d(c("uv-icon"),O),r=d(c("uv-input"),R),E=ee,F=d(c("uv-textarea"),re),N=Z,D=d(c("uv-parse"),te),M=d(c("uv-picker"),ae),L=d(c("uv-datetime-picker"),W),A=X,G=ue,Q=a;return m(),S(k,null,[y(i,{class:"mb-24"},{default:f(()=>[y(i,{class:"text-24 leading-34 text-[#444B56] flex items-center"},{default:f(()=>[e.obj.is_required===v(ne).IS_TRUE?(m(),p(i,{key:0,class:"text-orange"},{default:f(()=>[h("*")]),_:1})):_("",!0),h(" "+g(v(P)(ye(e.obj)?e.obj[0].name:e.obj.name))+" ",1),e.obj.help?(m(),p(s,{key:1,class:"ml-5",color:"#0165FF",size:"24rpx",name:"question-circle",onClick:v(je)},null,8,["onClick"])):_("",!0)]),_:1}),e.obj.type!==v(oe).INPUT&&e.obj.type!==v(oe).LINK||ye(e.obj)?_("",!0):(m(),p(i,{key:0,class:"mt-12 bg-[#F6F7F9] px-28 py-20 rounded-20 flex"},{default:f(()=>[y(r,{placeholder:`${v(o)("sys.pleaseEnter")} ${v(P)(e.obj.name)}`,border:"none",maxlength:"0"!=e.obj.len_max?e.obj.len_max:-1,modelValue:q.value,"onUpdate:modelValue":l[0]||(l[0]=e=>q.value=e)},null,8,["placeholder","maxlength","modelValue"]),e.obj.is_multi===v(ne).IS_TRUE&&e.obj.type===v(oe).INPUT?(m(),p(E,{key:0,class:"ml-20",name:"xinzeng",onClick:l[1]||(l[1]=e=>Fe(Y.obj,0))})):_("",!0)]),_:1})),ye(e.obj)?(m(),p(i,{key:1},{default:f(()=>[(m(!0),S(k,null,T(e.obj,(e,t)=>(m(),p(i,{class:"mt-12 bg-[#F6F7F9] px-28 py-24 rounded-20 flex",key:e.id},{default:f(()=>[y(r,{placeholder:`${v(o)("sys.pleaseEnter")} ${v(P)(e.name)}`,border:"none",maxlength:"0"!=e.len_max?e.len_max:-1,modelValue:e.submit_value,"onUpdate:modelValue":t=>e.submit_value=t},null,8,["placeholder","maxlength","modelValue","onUpdate:modelValue"]),e.is_multi===v(ne).IS_TRUE&&e.type===v(oe).INPUT?(m(),p(E,{key:0,class:"ml-20",name:t?"shanchu":"xinzeng",onClick:a=>((e,t,a)=>{0===t?Fe(e,a):n("updateItem:list",e,q.value,4,Y.index,t)})(e,Number(t),1)},null,8,["name","onClick"])):_("",!0)]),_:2},1024))),128))]),_:1})):_("",!0),e.obj.type===v(oe).TEXTAREA?(m(),p(i,{key:2,class:"mt-12 bg-[#F6F7F9] rounded-20"},{default:f(()=>[y(F,{placeholder:`${v(o)("sys.pleaseEnter")} ${e.obj.name}`,border:"none",maxlength:e.obj.len_max,modelValue:q.value,"onUpdate:modelValue":l[2]||(l[2]=e=>q.value=e)},null,8,["placeholder","maxlength","modelValue"])]),_:1})):_("",!0),e.obj.type===v(oe).SELECT||e.obj.type===v(oe).DATE||e.obj.type===v(oe).MEDIA||e.obj.type===v(oe).CONTENT_LIBRARY||e.obj.type===v(oe).BB_ID||e.obj.type===v(oe).MEDIA?(m(),p(i,{key:3,class:"mt-12 bg-[#F6F7F9] px-28 py-24 rounded-20 flex items-center justify-between",onClick:v(de)},{default:f(()=>[q.value?_("",!0):(m(),p(r,{key:0,placeholder:`${v(o)("sys.pleaseSelect")} ${v(P)(e.obj.name)}`,border:"none",disabled:""},null,8,["placeholder"])),e.obj.type===v(oe).SELECT||e.obj.type===v(oe).MEDIA&&"myMedia"===Y.fromTheMedia?(m(),p(i,{key:1},{default:f(()=>[h(g(q.value?ve(q.value,e.obj.child):""),1)]),_:1})):_("",!0),e.obj.type===v(oe).DATE?(m(),p(i,{key:2},{default:f(()=>[h(g(ke(q.value)),1)]),_:1})):_("",!0),e.obj.type===v(oe).BB_ID&&Y.bbList.length?(m(),p(i,{key:3},{default:f(()=>[h(g(Te(q.value)),1)]),_:1})):_("",!0),e.obj.type===v(oe).CONTENT_LIBRARY&&v($).length?(m(),p(i,{key:4,class:"line-clamp-1"},{default:f(()=>[h(g(Ie(q.value)),1)]),_:1})):_("",!0),q.value&&e.obj.type===v(oe).MEDIA&&"myMedia"!==Y.fromTheMedia?(m(),p(i,{key:5,class:"line-clamp-1"},{default:f(()=>[h(g(v(B)),1)]),_:1})):_("",!0),y(s,{name:"arrow-right",class:"ml-8",size:"28rpx",color:"#727A86"})]),_:1},8,["onClick"])):_("",!0),e.obj.type===v(oe).IMAGE||e.obj.type===v(oe).FILE||e.obj.type===v(oe).VIDEO?(m(),p(i,{key:4,class:"mt-12 rounded-20"},{default:f(()=>[y(N,{img:q.value,"onUpdate:img":l[3]||(l[3]=e=>q.value=e),value:q.value,lx:e.obj.type,form:Y.lx,count:1,accept:e.obj.type===v(oe).IMAGE?"image":e.obj.type===v(oe).VIDEO?"video":"file"},null,8,["img","value","lx","form","accept"])]),_:1})):_("",!0),e.obj.tips?(m(),p(i,{key:5,class:"mt-12 text-24 leading-34 text-blackThree"},{default:f(()=>[y(D,{content:v(U)(e.obj.tips)},null,8,["content"])]),_:1})):_("",!0)]),_:1}),y(M,{ref_key:"picker",ref:x,keyName:"name",columns:[v(u)],confirmText:v(o)("sys.sure"),cancelText:v(o)("sys.cancel"),confirmColor:"#0165FF",cancelColor:"#727A86",onConfirm:_e},null,8,["columns","confirmText","cancelText"]),y(L,{ref_key:"datetimePicker",ref:I,confirmText:v(o)("sys.sure"),cancelText:v(o)("sys.cancel"),minDate:15875248e5,modelValue:v(C),"onUpdate:modelValue":l[4]||(l[4]=e=>j(C)?C.value=e:null),mode:"date",onConfirm:xe},null,8,["confirmText","cancelText","modelValue"]),y(A,{ref_key:"reportPopupRef",ref:w,list:e.bbList,id:v(J).xm_id,total:e.bbTotal,title:e.obj.name,onSelect:pe,onScrollToLower:v(ce),onBackValue:me},null,8,["list","id","total","title","onScrollToLower"]),y(G,{ref_key:"neirongkuPopupRef",ref:K,list:v($),id:v(J).xm_id,detailId:e.detailId,nrShow:v(H),value:q.value,total:v(V),title:e.obj.name,index:Y.index,onSelect:be,onScrollToLower:v(he),onBackValue:ge},null,8,["list","id","detailId","nrShow","value","total","title","index","onScrollToLower"]),y(Q,{ref_key:"promptPopupRef",ref:z,title:v(o)("sys.tip"),content:e.obj.help,contentType:"html",onCancel:Se},null,8,["title","content"])],64)}}}),[["__scopeId","data-v-d6cb999e"]]);export{de as _};
