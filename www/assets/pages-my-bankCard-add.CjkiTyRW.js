import{d as e,u as l,r as a,k as t,a as s,b as o,c as n,o as c,e as r,w as u,i,g as d,t as p,h as m,F as f,z as b,v as k,f as v,x as y,D as _,ae as x,aq as g,a3 as h,ar as j,as as w,s as F}from"./index-CIPK2z2P.js";import{_ as C}from"./uv-popup.ewhZSqs9.js";import{_ as T,b as $}from"./uv-icon.UcuauzO0.js";import{_ as S}from"./submitButton.Cin0VP_1.js";import{_ as A}from"./navbar.vue_vue_type_script_setup_true_lang.CSl8hyKC.js";import{_ as B}from"./uv-input.CdM7e6ra.js";import{_ as E}from"./uv-skeletons.D1UL33yi.js";import{_ as V}from"./uv-picker.C6UOrbqb.js";import{b as z}from"./bank._E669YjS.js";import{i as O,a as D}from"./config.B61nRb73.js";import{d as I}from"./debounce.Ce2HeGXN.js";import"./uv-transition.tIadgx1N.js";import"./uv-status-bar.BkslDeIT.js";import"./uv-loading-icon.Bi7ZFsTo.js";const M=T(e({__name:"select-bank",emits:["back"],setup(e,{expose:_,emit:x}){const{t:g}=l(),h=a(),j=t().appSystem.settlement_account_back_type,w=x;return _({open:()=>{h.value.open()},close:()=>{h.value.close()}}),(e,l)=>{const a=i,t=v,_=s(o("uv-popup"),C);return c(),n("div",null,[r(_,{ref_key:"popup",ref:h,mode:"bottom",overlayStyle:"background: rgba(0, 0, 0, 0.3);",round:"15",closeable:""},{default:u(()=>[r(a,{class:"w-750 min-h-800 pt-26 pl-32 pr-32 box-border bg-[#F6F7F9]"},{default:u(()=>[r(a,{class:"text-black font-semibold"},{default:u(()=>[d(p(m(g)("bankCard.selectSettlementAccount")),1)]),_:1}),(c(!0),n(f,null,b(m(j),e=>(c(),k(a,{key:e.value,onClick:l=>(e=>{w("back",e),h.value.close()})(e)},{default:u(()=>[r(a,{class:"bg-white p-32 mt-30 rounded-20 flex items-center"},{default:u(()=>[r(t,{class:"w-80 h-80 mr-32",src:e.logo,mode:"scaleToFill"},null,8,["src"]),r(a,{class:"flex"},{default:u(()=>[d(p(e.name)+" ",1),1!==e.value?(c(),k(a,{key:0,class:"bg ml-20 rounded-10 text-24 text-white px-12 flex items-center justify-center"},{default:u(()=>[d(p("0"+m(g)("bankCard.handlingFee")),1)]),_:1})):y("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onClick"]))),128))]),_:1})]),_:1},512)])}}}),[["__scopeId","data-v-bf74900f"]]),R=T(e({__name:"add",setup(e){const C=a(!1),T=a([]),R=a(""),U=a(),q=a({logo:"",name:"",value:0}),J=a(),K=a(""),L=_({type:""}),{t:N}=l(),P=a(),Y="bankCard.paymentMethod",G=e=>{const{value:l}=e;L[U.value]=l[0].value,R.value=l[0].name},H=I(()=>{if(!L.type)return void x(`${N("sys.pleaseSelect")} ${N(Y)}`);const e=O.filter(e=>e.type.includes(L.type));if(L.account.length<13)x(`${N("bankCard.accountLength")}`);else{for(const l of e)if(!L[l.value])return void x(`${N("sys.pleaseEnter")} ${N(l.label)}`);Q()}},300,{leading:!0,trailing:!0}),Q=async()=>{try{const{msg:e}=await z(L);x(e??""),g("back:bank");const l=h();2===l.length?j({delta:1}):1===l.length?w({url:"/pages/index/index"}):j({delta:1})}catch(e){}},W=()=>{P.value&&P.value.open()},X=e=>{L.type=e.value.toString(),Object.keys(L).forEach(e=>{"type"!==e&&(L[e]="")}),q.value=e};return(e,l)=>{const a=A,_=i,x=s(o("uv-input"),B),g=v,h=s(o("uv-icon"),$),j=S,w=s(o("uv-skeletons"),E),z=M,I=s(o("uv-picker"),V);return c(),n("div",null,[r(a,{"bg-color":"transparent",title:m(N)("navTitle.addSettlementAccount"),scrollTextColor:"blackOne",isDarkMode:!0},null,8,["title"]),r(w,{loading:m(C),skeleton:m(D)},{default:u(()=>[r(_,{class:"p-32 env-bottom"},{default:u(()=>[r(_,{class:"w-full bg-white p-32 rounded-30"},{default:u(()=>[r(_,{class:"leading-34 text-24 text-blackTwo font-semibold"},{default:u(()=>[d(p(m(N)(Y)),1)]),_:1}),r(_,{onClick:W,class:"mt-12 bg-[#F6F7F9] px-28 py-24 rounded-20 flex items-center justify-between"},{default:u(()=>[m(L).type?(c(),k(_,{key:1,class:"flex items-center"},{default:u(()=>[r(g,{class:"w-48 h-48 mr-20",src:m(q).logo,mode:"scaleToFill"},null,8,["src"]),r(_,{class:"text-28 leading-40 text-blackOne"},{default:u(()=>[d(p(m(q).name),1)]),_:1})]),_:1})):(c(),k(x,{key:0,placeholder:`${m(N)("sys.pleaseEnter")} ${m(N)(Y)}`,border:"none",disabled:""},null,8,["placeholder"])),r(h,{name:"arrow-right",class:"ml-8",size:"28rpx",color:"#727A86"})]),_:1}),m(L).type?(c(),k(_,{key:0},{default:u(()=>[(c(!0),n(f,null,b(m(O),e=>(c(),k(_,{class:"mt-20",key:e.label},{default:u(()=>[e.type.includes(m(L).type)?(c(),k(_,{key:0},{default:u(()=>[r(_,{class:"leading-34 text-24 text-blackTwo font-semibold"},{default:u(()=>[d(p(m(N)(e.label)),1)]),_:2},1024),r(_,{class:"mt-12 bg-[#F6F7F9] px-28 py-24 rounded-20 flex items-center justify-between",onClick:l=>(e=>{"select"===e.lx&&(U.value=e.value,T.value=t().appSystem.settlement_account_type,J.value.open())})(e)},{default:u(()=>["select"!==e.lx||"select"===e.lx&&!m(L)[e.value]?(c(),k(x,{key:0,placeholder:`${m(N)("sys.pleaseEnter")} ${m(N)(e.label)}`,border:"none",disabled:"select"===e.lx,modelValue:m(L)[e.value],"onUpdate:modelValue":l=>m(L)[e.value]=l},null,8,["placeholder","disabled","modelValue","onUpdate:modelValue"])):(c(),k(_,{key:1},{default:u(()=>[d(p(m(R)),1)]),_:1})),"select"===e.lx?(c(),k(h,{key:2,name:"arrow-right",class:"ml-8",size:"28rpx",color:"#727A86"})):y("",!0)]),_:2},1032,["onClick"])]),_:2},1024)):y("",!0)]),_:2},1024))),128))]),_:1})):y("",!0)]),_:1})]),_:1}),r(j,{title:m(K)?"sys.modify":"sys.save",onBack:m(H),class:F(m(K)?"submitbutton":"1233"),bg:"bg-transparent"},null,8,["title","onBack","class"])]),_:1},8,["loading","skeleton"]),r(z,{ref_key:"selectBankRef",ref:P,onBack:X},null,512),r(I,{ref_key:"picker",ref:J,keyName:"name",columns:[m(T)],confirmText:m(N)("sys.sure"),cancelText:m(N)("sys.cancel"),confirmColor:"#0165FF",cancelColor:"#727A86",onConfirm:G},null,8,["columns","confirmText","cancelText"])])}}}),[["__scopeId","data-v-f097e5b5"]]);export{R as default};
