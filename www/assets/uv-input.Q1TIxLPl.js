var e,t;import{_ as n,m as o,a as i,b as l}from"./uv-icon.Dp0oPivN.js";import{a,b as s,v as r,o as u,w as d,e as c,i as p,x as f,A as h,aP as y,q as m,s as g}from"./index-BBirLt11.js";const S=n({name:"uv-input",mixins:[o,i,{props:{value:{type:[String,Number],default:""},modelValue:{type:[String,Number],default:""},type:{type:String,default:"text"},disabled:{type:Boolean,default:!1},disabledColor:{type:String,default:"#f5f7fa"},clearable:{type:Boolean,default:!1},password:{type:Boolean,default:!1},maxlength:{type:[String,Number],default:-1},placeholder:{type:String,default:null},placeholderClass:{type:String,default:"input-placeholder"},placeholderStyle:{type:[String,Object],default:"color: #c0c4cc"},confirmType:{type:String,default:"done"},confirmHold:{type:Boolean,default:!1},holdKeyboard:{type:Boolean,default:!1},focus:{type:Boolean,default:!1},autoBlur:{type:Boolean,default:!1},cursor:{type:[String,Number],default:-1},cursorSpacing:{type:[String,Number],default:30},selectionStart:{type:[String,Number],default:-1},selectionEnd:{type:[String,Number],default:-1},adjustPosition:{type:Boolean,default:!0},inputAlign:{type:String,default:"left"},fontSize:{type:[String,Number],default:"14px"},color:{type:String,default:"#303133"},prefixIcon:{type:String,default:""},prefixIconStyle:{type:[String,Object],default:""},suffixIcon:{type:String,default:""},suffixIconStyle:{type:[String,Object],default:""},border:{type:String,default:"surround"},readonly:{type:Boolean,default:!1},shape:{type:String,default:"square"},formatter:{type:[Function,null],default:null},ignoreCompositionEvent:{type:Boolean,default:!0},...null==(t=null==(e=uni.$uv)?void 0:e.props)?void 0:t.input}}],data:()=>({innerValue:"",focused:!1,innerFormatter:e=>e}),created(){this.innerValue=this.modelValue},watch:{value(e){this.innerValue=e},modelValue(e){this.innerValue=e}},computed:{isShowClear(){const{clearable:e,readonly:t,focused:n,innerValue:o}=this;return!!e&&!t&&!!n&&""!==o},inputClass(){let e=[],{border:t,disabled:n,shape:o}=this;return"surround"===t&&(e=e.concat(["uv-border","uv-input--radius"])),e.push(`uv-input--${o}`),"bottom"===t&&(e=e.concat(["uv-border-bottom","uv-input--no-radius"])),e.join(" ")},wrapperStyle(){const e={};return this.disabled&&(e.backgroundColor=this.disabledColor),"none"===this.border?e.padding="0":(e.paddingTop="6px",e.paddingBottom="6px",e.paddingLeft="9px",e.paddingRight="9px"),this.$uv.deepMerge(e,this.$uv.addStyle(this.customStyle))},inputStyle(){const e={color:this.color,fontSize:this.$uv.addUnit(this.fontSize),textAlign:this.inputAlign};return(this.disabled||this.readonly)&&(e["pointer-events"]="none"),e}},methods:{setFormatter(e){this.innerFormatter=e},onInput(e){let{value:t=""}=e.detail||{};const n=(this.formatter||this.innerFormatter)(t);this.innerValue=t,this.$nextTick(()=>{this.innerValue=n,this.valueChange()})},onBlur(e){this.$emit("blur",e.detail.value),this.$uv.sleep(100).then(()=>{this.focused=!1}),this.$uv.formValidate(this,"blur")},onFocus(e){this.focused=!0,this.$emit("focus")},onConfirm(e){this.$emit("confirm",this.innerValue)},onkeyboardheightchange(e){this.$emit("keyboardheightchange",e)},valueChange(){this.isClear&&(this.innerValue="");const e=this.innerValue;this.$nextTick(()=>{this.$emit("input",e),this.$emit("update:modelValue",e),this.$emit("change",e),this.$uv.formValidate(this,"change")})},onClear(){this.innerValue="",this.isClear=!0,this.$uv.sleep(100).then(e=>{this.isClear=!1}),this.$nextTick(()=>{this.$emit("clear"),this.valueChange()})},clickHandler(){}}},[["render",function(e,t,n,o,i,S){const b=a(s("uv-icon"),l),v=p,_=y;return u(),r(v,{class:g(["uv-input",S.inputClass]),style:m([S.wrapperStyle])},{default:d(()=>[c(v,{class:"uv-input__content"},{default:d(()=>[c(v,{class:"uv-input__content__prefix-icon"},{default:d(()=>[h(e.$slots,"prefix",{},()=>[e.prefixIcon?(u(),r(b,{key:0,name:e.prefixIcon,size:"18",customStyle:e.prefixIconStyle},null,8,["name","customStyle"])):f("",!0)],!0)]),_:3}),c(v,{class:"uv-input__content__field-wrapper",onClick:S.clickHandler},{default:d(()=>[c(_,{class:"uv-input__content__field-wrapper__field",style:m([S.inputStyle]),type:e.type,focus:e.focus,cursor:e.cursor,value:i.innerValue,"auto-blur":e.autoBlur,disabled:e.disabled||e.readonly,maxlength:e.maxlength,placeholder:e.placeholder,"placeholder-style":e.placeholderStyle,"placeholder-class":e.placeholderClass,"confirm-type":e.confirmType,"confirm-hold":e.confirmHold,"hold-keyboard":e.holdKeyboard,"cursor-spacing":e.cursorSpacing,"adjust-position":e.adjustPosition,"selection-end":e.selectionEnd,"selection-start":e.selectionStart,password:e.password||"password"===e.type||void 0,ignoreCompositionEvent:e.ignoreCompositionEvent,onInput:S.onInput,onBlur:S.onBlur,onFocus:S.onFocus,onConfirm:S.onConfirm,onKeyboardheightchange:S.onkeyboardheightchange},null,8,["style","type","focus","cursor","value","auto-blur","disabled","maxlength","placeholder","placeholder-style","placeholder-class","confirm-type","confirm-hold","hold-keyboard","cursor-spacing","adjust-position","selection-end","selection-start","password","ignoreCompositionEvent","onInput","onBlur","onFocus","onConfirm","onKeyboardheightchange"])]),_:1},8,["onClick"]),S.isShowClear?(u(),r(v,{key:0,class:"uv-input__content__clear",onClick:S.onClear},{default:d(()=>[c(b,{name:"close",size:"11",color:"#ffffff",customStyle:"line-height: 12px"})]),_:1},8,["onClick"])):f("",!0),c(v,{class:"uv-input__content__subfix-icon"},{default:d(()=>[h(e.$slots,"suffix",{},()=>[e.suffixIcon?(u(),r(b,{key:0,name:e.suffixIcon,size:"18",customStyle:e.suffixIconStyle},null,8,["name","customStyle"])):f("",!0)],!0)]),_:3})]),_:3})]),_:3},8,["class","style"])}],["__scopeId","data-v-f5be3788"]]);export{S as _};
