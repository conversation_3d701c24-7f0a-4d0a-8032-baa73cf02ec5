function __vite__mapDeps(indexes) {
  if (!__vite__mapDeps.viteFileDeps) {
    __vite__mapDeps.viteFileDeps = ["assets/pages-index-index.DrimU85T.js","assets/uv-popup.ewhZSqs9.js","assets/uv-transition.tIadgx1N.js","assets/uv-icon.UcuauzO0.js","assets/uv-icon-DG-p_dje.css","assets/uv-status-bar.BkslDeIT.js","assets/uv-status-bar-T-GoYQUa.css","assets/uv-popup-CasUWi47.css","assets/set-language.BfCwdxCn.js","assets/debounce.Ce2HeGXN.js","assets/set-language-Mh4csRT3.css","assets/config.DrO4s-Uw.js","assets/empty.vue_vue_type_script_setup_true_lang.BXZPGwCi.js","assets/uv-empty.BH_ZJrMJ.js","assets/uv-empty-DugdxaDo.css","assets/uv-load-more.DsJkRw2i.js","assets/uv-line.CaGHsg1_.js","assets/uv-line-buInAe3I.css","assets/uv-loading-icon.Bi7ZFsTo.js","assets/uv-loading-icon-DFAUUgof.css","assets/uv-load-more-CqUHv_8t.css","assets/config-CBvgWFj0.css","assets/prompt-popup.vue_vue_type_script_setup_true_lang.CHEY8D5W.js","assets/uv-parse.wZPhbfSD.js","assets/uv-parse-DKAFE8xa.css","assets/search.CpVgtt1a.js","assets/uv-skeletons.D1UL33yi.js","assets/uv-skeletons-DHrIcNY_.css","assets/decimal.B1oHnkff.js","assets/index-S4M__34r.css","assets/pages-search-index.B6yUP09N.js","assets/uv-search.CD68DrLn.js","assets/uv-search-GKpsrU2F.css","assets/index-L0ZaHRAG.css","assets/pages-team-index.BB0SFNjK.js","assets/uv-collapse.BPpVsG_y.js","assets/uv-collapse-ZtCNOoth.css","assets/index-vLW7f-aF.css","assets/pages-my-index.DaQeUbl0.js","assets/config.DJhcrVV7.js","assets/index-D7yFp_nU.css","assets/pages-my-wallet-index.DP5H9dBN.js","assets/settlement-item.vue_vue_type_script_setup_true_lang.B9YEvUiw.js","assets/navbar.vue_vue_type_script_setup_true_lang.CSl8hyKC.js","assets/navbar-B-C532NF.css","assets/select-popup.B1Gjkpfl.js","assets/bank._E669YjS.js","assets/select-popup-Bq6Yzx6o.css","assets/index-DCR5MB7X.css","assets/pages-my-wallet-fund.Do4Ow9L1.js","assets/submitButton.Cin0VP_1.js","assets/submitButton-DzwpLLqu.css","assets/uv-input.CdM7e6ra.js","assets/uv-input-BkfBp6Mh.css","assets/fund-fHGArRoW.css","assets/pages-my-wallet-tips.D-6_2IF1.js","assets/success.njPbLiaa.js","assets/tips-CF_z3dwK.css","assets/pages-my-notice-index.Dd87FZ8y.js","assets/uv-tabs.CWR5nOXF.js","assets/uv-tabs-DHpZhbv3.css","assets/uv-sticky.CXYYNICz.js","assets/uv-sticky-CHdWoJ2w.css","assets/details.B83uqO-n.js","assets/system.DZjCJFRG.js","assets/index-ChKMppPo.css","assets/pages-my-notice-detail.CLrmZBc7.js","assets/price-box.MCDlGFhF.js","assets/price-box-Czjwyr33.css","assets/pages-my-filings-index.CoHdojQ0.js","assets/pages-my-info-index.Bhuau0sL.js","assets/config.Dlnj0OY2.js","assets/config-CoFplyh1.css","assets/login.DeOnoCbS.js","assets/pages-my-info-security.FDz5YJr_.js","assets/ali-popup.Dsc3VcwQ.js","assets/ali-popup-CcoyTy4j.css","assets/uv-steps.qNI4wMcC.js","assets/uv-text.DpB-FlH4.js","assets/uv-text-BcpTomo7.css","assets/uv-steps-AP9y4JFt.css","assets/security-B2cAMia0.css","assets/pages-my-settlement-index.1TnO0ru9.js","assets/uv-picker.C6UOrbqb.js","assets/uv-picker-D8G9K39U.css","assets/pages-my-income-index.BZpUl0fm.js","assets/pages-my-income-details.D4C0U5tN.js","assets/pages-my-income-detailsSearch.B_X4DLl9.js","assets/detailsSearch-CwQPMekD.css","assets/pages-my-settlement-details.DTvJ0iMR.js","assets/pages-my-orderHistory-index.C8qHhe8G.js","assets/filter-popup.vue_vue_type_script_setup_true_lang.hyGhGyJf.js","assets/uv-datetime-picker.CJSFIHl6.js","assets/uv-radio-group.CHWPt9Cy.js","assets/uv-radio-group-CKQ2MXgd.css","assets/uv-datetime-picker-D9Ommnai.css","assets/pages-my-improve-index.BC2uRMdL.js","assets/stepsIcon.3mQNdnVR.js","assets/country.DPybxyM6.js","assets/index-EgewbmBR.css","assets/pages-my-bankCard-index.3EoR0nFR.js","assets/config.B61nRb73.js","assets/pages-my-bankCard-add.CjkiTyRW.js","assets/add-C8OpYwrf.css","assets/pages-my-media-index.BvCkRSzm.js","assets/media-item.vue_vue_type_script_setup_true_lang.C8dLc39B.js","assets/media.Ds2RueMI.js","assets/config.CV-betc9.js","assets/pages-my-fund-index.DGCQdzm7.js","assets/pages-my-media-add.fnrpn6P-.js","assets/promotion-item.D9gP7SLW.js","assets/uploadImag.CizL1aRL.js","assets/uploadImag-CFoEyEy3.css","assets/promotion-item-BcWVAOHp.css","assets/add-Dd8t-XJC.css","assets/pages-my-media-tips.BGdMetpK.js","assets/tips-CSHfz3xj.css","assets/pages-my-improve-country.DcX8mBvP.js","assets/country-ByFgji9J.css","assets/pages-login-index.nHXU7vgH.js","assets/xyPopup.vue_vue_type_script_setup_true_lang.B8-LxnmC.js","assets/index-BDR9eqF2.css","assets/pages-login-register.C4IN5Wwj.js","assets/register-CNwse-0a.css","assets/pages-details-index.knM1iBbO.js","assets/detail-bottom.CMEFRwuj.js","assets/detail-bottom-B9dqn_Ou.css","assets/content-item.a2Jnsz7f.js","assets/content-item-D_jf8qIy.css","assets/config.CwGRXwA6.js","assets/index-Ck_2kBbJ.css","assets/pages-details-promotionRecord.C7LGOuoP.js","assets/promotionCodeInfo.BJy-mwMn.js","assets/promotionCodeInfo-Biyw3Oqq.css","assets/uv-read-more.0DxKa8S8.js","assets/uv-read-more-BIKQ0ykt.css","assets/promotionRecord-KOlxkINs.css","assets/pages-details-form.CGwn_X86.js","assets/skeleton.BDseN9kT.js","assets/form-J2BqtV2Q.css","assets/pages-details-media.DWsO6Wv6.js","assets/pages-details-voucherForm.CIzotMYU.js","assets/voucherForm-Dp4RqdhG.css","assets/pages-details-vouncherHistory.sC9wAjX7.js","assets/vouncherHistory--B8wkFUT.css","assets/pages-details-tips.BIMTiqK6.js","assets/tips-BcfHjDqo.css","assets/pages-details-promotionCode.BEFqVWKi.js","assets/promotionCode-CTdhiLV-.css","assets/pages-details-promotionCodeDetails.DFKKPOYw.js","assets/pages-details-search.pK1wvTW_.js","assets/search-scIjtgFX.css","assets/pages-details-shortPlayInfo.D9DI-TtR.js","assets/shortPlayInfo-xKVopEZM.css","assets/pages-common-promotion-code.BSsEUspS.js","assets/promotion-code-U8SLHNY5.css","assets/pages-system-xy.BrSceaeY.js","assets/pages-system-video.D8XYjyMg.js","assets/video-BWrVXVLk.css","assets/pages-system-404.CGU7uJrc.js"]
  }
  return indexes.map((i) => __vite__mapDeps.viteFileDeps[i])
}
var e=Object.defineProperty,t=(t,n,o)=>(((t,n,o)=>{n in t?e(t,n,{enumerable:!0,configurable:!0,writable:!0,value:o}):t[n]=o})(t,"symbol"!=typeof n?n+"":n,o),o);!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver(e=>{for(const n of e)if("childList"===n.type)for(const e of n.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)}).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();const n={},o=function(e,t,o){let r=Promise.resolve();if(t&&t.length>0){document.getElementsByTagName("link");const e=document.querySelector("meta[property=csp-nonce]"),o=(null==e?void 0:e.nonce)||(null==e?void 0:e.getAttribute("nonce"));r=Promise.all(t.map(e=>{if((e=function(e){return"/"+e}(e))in n)return;n[e]=!0;const t=e.endsWith(".css"),r=t?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${e}"]${r}`))return;const i=document.createElement("link");return i.rel=t?"stylesheet":"modulepreload",t||(i.as="script",i.crossOrigin=""),i.href=e,o&&i.setAttribute("nonce",o),document.head.appendChild(i),t?new Promise((t,n)=>{i.addEventListener("load",t),i.addEventListener("error",()=>n(new Error(`Unable to preload CSS for ${e}`)))}):void 0}))}return r.then(()=>e()).catch(e=>{const t=new Event("vite:preloadError",{cancelable:!0});if(t.payload=e,window.dispatchEvent(t),!t.defaultPrevented)throw e})};
/**
* @vue/shared v3.4.21
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/
function r(e,t){const n=new Set(e.split(","));return e=>n.has(e)}const i={},s=[],a=()=>{},l=()=>!1,c=e=>111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),u=e=>e.startsWith("onUpdate:"),d=Object.assign,f=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},p=Object.prototype.hasOwnProperty,h=(e,t)=>p.call(e,t),m=Array.isArray,g=e=>"[object Map]"===S(e),v=e=>"[object Set]"===S(e),y=e=>"function"==typeof e,b=e=>"string"==typeof e,_=e=>"symbol"==typeof e,w=e=>null!==e&&"object"==typeof e,x=e=>(w(e)||y(e))&&y(e.then)&&y(e.catch),T=Object.prototype.toString,S=e=>T.call(e),k=e=>"[object Object]"===S(e),E=e=>b(e)&&"NaN"!==e&&"-"!==e[0]&&""+parseInt(e,10)===e,C=r(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),P=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},L=/-(\w)/g,O=P(e=>e.replace(L,(e,t)=>t?t.toUpperCase():"")),I=/\B([A-Z])/g,A=P(e=>e.replace(I,"-$1").toLowerCase()),M=P(e=>e.charAt(0).toUpperCase()+e.slice(1)),R=P(e=>e?`on${M(e)}`:""),$=(e,t)=>!Object.is(e,t),N=(e,t)=>{for(let n=0;n<e.length;n++)e[n](t)},D=(e,t,n)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,value:n})},F=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let B;const j=()=>B||(B="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{});function V(e){if(m(e)){const t={};for(let n=0;n<e.length;n++){const o=e[n],r=b(o)?U(o):V(o);if(r)for(const e in r)t[e]=r[e]}return t}if(b(e)||w(e))return e}const W=/;(?![^(]*\))/g,H=/:([^]+)/,z=/\/\*[^]*?\*\//g;function U(e){const t={};return e.replace(z,"").split(W).forEach(e=>{if(e){const n=e.split(H);n.length>1&&(t[n[0].trim()]=n[1].trim())}}),t}function q(e){let t="";if(b(e))t=e;else if(m(e))for(let n=0;n<e.length;n++){const o=q(e[n]);o&&(t+=o+" ")}else if(w(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const Y=r("itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly");function X(e){return!!e||""===e}const G=e=>b(e)?e:null==e?"":m(e)||w(e)&&(e.toString===T||!y(e.toString))?JSON.stringify(e,K,2):String(e),K=(e,t)=>t&&t.__v_isRef?K(e,t.value):g(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((e,[t,n],o)=>(e[J(t,o)+" =>"]=n,e),{})}:v(t)?{[`Set(${t.size})`]:[...t.values()].map(e=>J(e))}:_(t)?J(t):!w(t)||m(t)||k(t)?t:String(t),J=(e,t="")=>{var n;return _(e)?`Symbol(${null!=(n=e.description)?n:t})`:e},Z=["ad","ad-content-page","ad-draw","audio","button","camera","canvas","checkbox","checkbox-group","cover-image","cover-view","editor","form","functional-page-navigator","icon","image","input","label","live-player","live-pusher","map","movable-area","movable-view","navigator","official-account","open-data","picker","picker-view","picker-view-column","progress","radio","radio-group","rich-text","scroll-view","slider","swiper","swiper-item","switch","text","textarea","video","view","web-view","location-picker","location-view"].map(e=>"uni-"+e),Q=["list-view","list-item","sticky-section","sticky-header","cloud-db-element"].map(e=>"uni-"+e),ee=["list-item"].map(e=>"uni-"+e);function te(e){if(-1!==ee.indexOf(e))return!1;const t="uni-"+e.replace("v-uni-","");return-1!==Z.indexOf(t)||-1!==Q.indexOf(t)}const ne="\n",oe="UNI_LOCALE",re=["%","%"],ie=/^([a-z-]+:)?\/\//i,se=/^data:.*,.*/,ae="onShow",le="onHide",ce="onLaunch",ue="onError",de="onThemeChange",fe="onPageNotFound",pe="onUnhandledRejection",he="onLoad",me="onUnload",ge="onInit",ve="onSaveExitState",ye="onResize",be="onBackPress",_e="onPageScroll",we="onTabItemTap",xe="onReachBottom",Te="onPullDownRefresh",Se="onShareTimeline",ke="onShareChat",Ee="onAddToFavorites",Ce="onShareAppMessage",Pe="onNavigationBarButtonTap",Le="onNavigationBarSearchInputClicked",Oe="onNavigationBarSearchInputChanged",Ie="onNavigationBarSearchInputConfirmed",Ae="onNavigationBarSearchInputFocusChanged",Me="onAppEnterForeground",Re="onAppEnterBackground";function $e(e){return 0===e.indexOf("/")}function Ne(e){return $e(e)?e:"/"+e}function De(e){return $e(e)?e.slice(1):e}function Fe(e,t=null){let n;return(...o)=>(e&&(n=e.apply(t,o),e=null),n)}let Be;function je(){return Be||(Be=function(){if("undefined"!=typeof globalThis)return globalThis;if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;function e(){return this}return void 0!==e()?e():new Function("return this")()}(),Be)}function Ve(e){return e&&(e.appContext?e.proxy:e)}function We(e){if(!e)return;let t=e.type.name;for(;t&&te(A(t));)t=(e=e.parent).type.name;return e.proxy}function He(e){return 1===e.nodeType}function ze(e){const t=je();if(t&&t.UTSJSONObject&&e instanceof t.UTSJSONObject){const n={};return t.UTSJSONObject.keys(e).forEach(t=>{n[t]=e[t]}),V(n)}if(e instanceof Map){const t={};return e.forEach((e,n)=>{t[n]=e}),V(t)}if(b(e))return U(e);if(m(e)){const t={};for(let n=0;n<e.length;n++){const o=e[n],r=b(o)?U(o):ze(o);if(r)for(const e in r)t[e]=r[e]}return t}return V(e)}function Ue(e){let t="";const n=je();if(n&&n.UTSJSONObject&&e instanceof n.UTSJSONObject)n.UTSJSONObject.keys(e).forEach(n=>{e[n]&&(t+=n+" ")});else if(e instanceof Map)e.forEach((e,n)=>{e&&(t+=n+" ")});else if(m(e))for(let o=0;o<e.length;o++){const n=Ue(e[o]);n&&(t+=n+" ")}else t=q(e);return t.trim()}function qe(e){return O(e.substring(5))}const Ye=Fe(e=>{e=e||(e=>e.tagName.startsWith("UNI-"));const t=HTMLElement.prototype,n=t.setAttribute;t.setAttribute=function(t,o){if(t.startsWith("data-")&&e(this)){(this.__uniDataset||(this.__uniDataset={}))[qe(t)]=o}n.call(this,t,o)};const o=t.removeAttribute;t.removeAttribute=function(t){this.__uniDataset&&t.startsWith("data-")&&e(this)&&delete this.__uniDataset[qe(t)],o.call(this,t)}});function Xe(e){return d({},e.dataset,e.__uniDataset)}const Ge=new RegExp("\"[^\"]+\"|'[^']+'|url\\([^)]+\\)|(\\d*\\.?\\d+)[r|u]px","g");function Ke(e){return{passive:e}}function Je(e){const{id:t,offsetTop:n,offsetLeft:o}=e;return{id:t,dataset:Xe(e),offsetTop:n,offsetLeft:o}}function Ze(e){try{return decodeURIComponent(""+e)}catch(t){}return""+e}function Qe(e={}){const t={};return Object.keys(e).forEach(n=>{try{t[n]=Ze(e[n])}catch(o){t[n]=e[n]}}),t}const et=/\+/g;function tt(e){const t={};if(""===e||"?"===e)return t;const n=("?"===e[0]?e.slice(1):e).split("&");for(let o=0;o<n.length;++o){const e=n[o].replace(et," ");let r=e.indexOf("="),i=Ze(r<0?e:e.slice(0,r)),s=r<0?null:Ze(e.slice(r+1));if(i in t){let e=t[i];m(e)||(e=t[i]=[e]),e.push(s)}else t[i]=s}return t}function nt(e,t,{clearTimeout:n,setTimeout:o}){let r;const i=function(){n(r);r=o(()=>e.apply(this,arguments),t)};return i.cancel=function(){n(r)},i}class ot{constructor(e,t){this.id=e,this.listener={},this.emitCache=[],t&&Object.keys(t).forEach(e=>{this.on(e,t[e])})}emit(e,...t){const n=this.listener[e];if(!n)return this.emitCache.push({eventName:e,args:t});n.forEach(e=>{e.fn.apply(e.fn,t)}),this.listener[e]=n.filter(e=>"once"!==e.type)}on(e,t){this._addListener(e,"on",t),this._clearCache(e)}once(e,t){this._addListener(e,"once",t),this._clearCache(e)}off(e,t){const n=this.listener[e];if(n)if(t)for(let o=0;o<n.length;)n[o].fn===t&&(n.splice(o,1),o--),o++;else delete this.listener[e]}_clearCache(e){for(let t=0;t<this.emitCache.length;t++){const n=this.emitCache[t],o=e?n.eventName===e?e:null:n.eventName;if(!o)continue;"number"!=typeof this.emit.apply(this,[o,...n.args])?(this.emitCache.splice(t,1),t--):this.emitCache.pop()}}_addListener(e,t,n){(this.listener[e]||(this.listener[e]=[])).push({fn:n,type:t})}}const rt=[ge,he,ae,le,me,be,_e,we,xe,Te,Se,Ce,ke,Ee,ve,Pe,Le,Oe,Ie,Ae];const it=[ae,le,ce,ue,de,fe,pe,"onExit",ge,he,"onReady",me,ye,be,_e,we,xe,Te,Se,Ee,Ce,ke,ve,Pe,Le,Oe,Ie,Ae];const st=[];const at=Fe((e,t)=>t(e)),lt=function(){};lt.prototype={_id:1,on:function(e,t,n){var o=this.e||(this.e={});return(o[e]||(o[e]=[])).push({fn:t,ctx:n,_id:this._id}),this._id++},once:function(e,t,n){var o=this;function r(){o.off(e,r),t.apply(n,arguments)}return r._=t,this.on(e,r,n)},emit:function(e){for(var t=[].slice.call(arguments,1),n=((this.e||(this.e={}))[e]||[]).slice(),o=0,r=n.length;o<r;o++)n[o].fn.apply(n[o].ctx,t);return this},off:function(e,t){var n=this.e||(this.e={}),o=n[e],r=[];if(o&&t){for(var i=o.length-1;i>=0;i--)if(o[i].fn===t||o[i].fn._===t||o[i]._id===t){o.splice(i,1);break}r=o}return r.length?n[e]=r:delete n[e],this}};var ct=lt;const ut={black:"rgba(0,0,0,0.4)",white:"rgba(255,255,255,0.4)"};function dt(e,t,n){if(b(t)&&t.startsWith("@")){let r=e[t.replace("@","")]||t;switch(n){case"titleColor":r="black"===r?"#000000":"#ffffff";break;case"borderStyle":r=(o=r)&&o in ut?ut[o]:o}return r}var o;return t}function ft(e,t={},n="light"){const o=t[n],r={};return void 0!==o&&e?(Object.keys(e).forEach(i=>{const s=e[i];r[i]=k(s)?ft(s,t,n):m(s)?s.map(e=>k(e)?ft(e,t,n):dt(o,e)):dt(o,s,i)}),r):e}
/**
* @dcloudio/uni-h5-vue v3.4.21
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let pt,ht;class mt{constructor(e=!1){this.detached=e,this._active=!0,this.effects=[],this.cleanups=[],this.parent=pt,!e&&pt&&(this.index=(pt.scopes||(pt.scopes=[])).push(this)-1)}get active(){return this._active}run(e){if(this._active){const t=pt;try{return pt=this,e()}finally{pt=t}}}on(){pt=this}off(){pt=this.parent}stop(e){if(this._active){let t,n;for(t=0,n=this.effects.length;t<n;t++)this.effects[t].stop();for(t=0,n=this.cleanups.length;t<n;t++)this.cleanups[t]();if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].stop(!0);if(!this.detached&&this.parent&&!e){const e=this.parent.scopes.pop();e&&e!==this&&(this.parent.scopes[this.index]=e,e.index=this.index)}this.parent=void 0,this._active=!1}}}function gt(e){return new mt(e)}function vt(){return pt}class yt{constructor(e,t,n,o){this.fn=e,this.trigger=t,this.scheduler=n,this.active=!0,this.deps=[],this._dirtyLevel=4,this._trackId=0,this._runnings=0,this._shouldSchedule=!1,this._depsLength=0,function(e,t=pt){t&&t.active&&t.effects.push(e)}(this,o)}get dirty(){if(2===this._dirtyLevel||3===this._dirtyLevel){this._dirtyLevel=1,Et();for(let e=0;e<this._depsLength;e++){const t=this.deps[e];if(t.computed&&(bt(t.computed),this._dirtyLevel>=4))break}1===this._dirtyLevel&&(this._dirtyLevel=0),Ct()}return this._dirtyLevel>=4}set dirty(e){this._dirtyLevel=e?4:0}run(){if(this._dirtyLevel=0,!this.active)return this.fn();let e=Tt,t=ht;try{return Tt=!0,ht=this,this._runnings++,_t(this),this.fn()}finally{wt(this),this._runnings--,ht=t,Tt=e}}stop(){var e;this.active&&(_t(this),wt(this),null==(e=this.onStop)||e.call(this),this.active=!1)}}function bt(e){return e.value}function _t(e){e._trackId++,e._depsLength=0}function wt(e){if(e.deps.length>e._depsLength){for(let t=e._depsLength;t<e.deps.length;t++)xt(e.deps[t],e);e.deps.length=e._depsLength}}function xt(e,t){const n=e.get(t);void 0!==n&&t._trackId!==n&&(e.delete(t),0===e.size&&e.cleanup())}let Tt=!0,St=0;const kt=[];function Et(){kt.push(Tt),Tt=!1}function Ct(){const e=kt.pop();Tt=void 0===e||e}function Pt(){St++}function Lt(){for(St--;!St&&It.length;)It.shift()()}function Ot(e,t,n){if(t.get(e)!==e._trackId){t.set(e,e._trackId);const n=e.deps[e._depsLength];n!==t?(n&&xt(n,e),e.deps[e._depsLength++]=t):e._depsLength++}}const It=[];function At(e,t,n){Pt();for(const o of e.keys()){let n;o._dirtyLevel<t&&(null!=n?n:n=e.get(o)===o._trackId)&&(o._shouldSchedule||(o._shouldSchedule=0===o._dirtyLevel),o._dirtyLevel=t),o._shouldSchedule&&(null!=n?n:n=e.get(o)===o._trackId)&&(o.trigger(),o._runnings&&!o.allowRecurse||2===o._dirtyLevel||(o._shouldSchedule=!1,o.scheduler&&It.push(o.scheduler)))}Lt()}const Mt=(e,t)=>{const n=new Map;return n.cleanup=e,n.computed=t,n},Rt=new WeakMap,$t=Symbol(""),Nt=Symbol("");function Dt(e,t,n){if(Tt&&ht){let t=Rt.get(e);t||Rt.set(e,t=new Map);let o=t.get(n);o||t.set(n,o=Mt(()=>t.delete(n))),Ot(ht,o)}}function Ft(e,t,n,o,r,i){const s=Rt.get(e);if(!s)return;let a=[];if("clear"===t)a=[...s.values()];else if("length"===n&&m(e)){const e=Number(o);s.forEach((t,n)=>{("length"===n||!_(n)&&n>=e)&&a.push(t)})}else switch(void 0!==n&&a.push(s.get(n)),t){case"add":m(e)?E(n)&&a.push(s.get("length")):(a.push(s.get($t)),g(e)&&a.push(s.get(Nt)));break;case"delete":m(e)||(a.push(s.get($t)),g(e)&&a.push(s.get(Nt)));break;case"set":g(e)&&a.push(s.get($t))}Pt();for(const l of a)l&&At(l,4);Lt()}const Bt=r("__proto__,__v_isRef,__isVue"),jt=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>"arguments"!==e&&"caller"!==e).map(e=>Symbol[e]).filter(_)),Vt=Wt();function Wt(){const e={};return["includes","indexOf","lastIndexOf"].forEach(t=>{e[t]=function(...e){const n=In(this);for(let t=0,r=this.length;t<r;t++)Dt(n,0,t+"");const o=n[t](...e);return-1===o||!1===o?n[t](...e.map(In)):o}}),["push","pop","shift","unshift","splice"].forEach(t=>{e[t]=function(...e){Et(),Pt();const n=In(this)[t].apply(this,e);return Lt(),Ct(),n}}),e}function Ht(e){const t=In(this);return Dt(t,0,e),t.hasOwnProperty(e)}class zt{constructor(e=!1,t=!1){this._isReadonly=e,this._isShallow=t}get(e,t,n){const o=this._isReadonly,r=this._isShallow;if("__v_isReactive"===t)return!o;if("__v_isReadonly"===t)return o;if("__v_isShallow"===t)return r;if("__v_raw"===t)return n===(o?r?wn:_n:r?bn:yn).get(e)||Object.getPrototypeOf(e)===Object.getPrototypeOf(n)?e:void 0;const i=m(e);if(!o){if(i&&h(Vt,t))return Reflect.get(Vt,t,n);if("hasOwnProperty"===t)return Ht}const s=Reflect.get(e,t,n);return(_(t)?jt.has(t):Bt(t))?s:(o||Dt(e,0,t),r?s:Fn(s)?i&&E(t)?s:s.value:w(s)?o?kn(s):Tn(s):s)}}class Ut extends zt{constructor(e=!1){super(!1,e)}set(e,t,n,o){let r=e[t];if(!this._isShallow){const t=Pn(r);if(Ln(n)||Pn(n)||(r=In(r),n=In(n)),!m(e)&&Fn(r)&&!Fn(n))return!t&&(r.value=n,!0)}const i=m(e)&&E(t)?Number(t)<e.length:h(e,t),s=Reflect.set(e,t,n,o);return e===In(o)&&(i?$(n,r)&&Ft(e,"set",t,n):Ft(e,"add",t,n)),s}deleteProperty(e,t){const n=h(e,t);e[t];const o=Reflect.deleteProperty(e,t);return o&&n&&Ft(e,"delete",t,void 0),o}has(e,t){const n=Reflect.has(e,t);return _(t)&&jt.has(t)||Dt(e,0,t),n}ownKeys(e){return Dt(e,0,m(e)?"length":$t),Reflect.ownKeys(e)}}class qt extends zt{constructor(e=!1){super(!0,e)}set(e,t){return!0}deleteProperty(e,t){return!0}}const Yt=new Ut,Xt=new qt,Gt=new Ut(!0),Kt=e=>e,Jt=e=>Reflect.getPrototypeOf(e);function Zt(e,t,n=!1,o=!1){const r=In(e=e.__v_raw),i=In(t);n||($(t,i)&&Dt(r,0,t),Dt(r,0,i));const{has:s}=Jt(r),a=o?Kt:n?Rn:Mn;return s.call(r,t)?a(e.get(t)):s.call(r,i)?a(e.get(i)):void(e!==r&&e.get(t))}function Qt(e,t=!1){const n=this.__v_raw,o=In(n),r=In(e);return t||($(e,r)&&Dt(o,0,e),Dt(o,0,r)),e===r?n.has(e):n.has(e)||n.has(r)}function en(e,t=!1){return e=e.__v_raw,!t&&Dt(In(e),0,$t),Reflect.get(e,"size",e)}function tn(e){e=In(e);const t=In(this);return Jt(t).has.call(t,e)||(t.add(e),Ft(t,"add",e,e)),this}function nn(e,t){t=In(t);const n=In(this),{has:o,get:r}=Jt(n);let i=o.call(n,e);i||(e=In(e),i=o.call(n,e));const s=r.call(n,e);return n.set(e,t),i?$(t,s)&&Ft(n,"set",e,t):Ft(n,"add",e,t),this}function on(e){const t=In(this),{has:n,get:o}=Jt(t);let r=n.call(t,e);r||(e=In(e),r=n.call(t,e)),o&&o.call(t,e);const i=t.delete(e);return r&&Ft(t,"delete",e,void 0),i}function rn(){const e=In(this),t=0!==e.size,n=e.clear();return t&&Ft(e,"clear",void 0,void 0),n}function sn(e,t){return function(n,o){const r=this,i=r.__v_raw,s=In(i),a=t?Kt:e?Rn:Mn;return!e&&Dt(s,0,$t),i.forEach((e,t)=>n.call(o,a(e),a(t),r))}}function an(e,t,n){return function(...o){const r=this.__v_raw,i=In(r),s=g(i),a="entries"===e||e===Symbol.iterator&&s,l="keys"===e&&s,c=r[e](...o),u=n?Kt:t?Rn:Mn;return!t&&Dt(i,0,l?Nt:$t),{next(){const{value:e,done:t}=c.next();return t?{value:e,done:t}:{value:a?[u(e[0]),u(e[1])]:u(e),done:t}},[Symbol.iterator](){return this}}}}function ln(e){return function(...t){return"delete"!==e&&("clear"===e?void 0:this)}}function cn(){const e={get(e){return Zt(this,e)},get size(){return en(this)},has:Qt,add:tn,set:nn,delete:on,clear:rn,forEach:sn(!1,!1)},t={get(e){return Zt(this,e,!1,!0)},get size(){return en(this)},has:Qt,add:tn,set:nn,delete:on,clear:rn,forEach:sn(!1,!0)},n={get(e){return Zt(this,e,!0)},get size(){return en(this,!0)},has(e){return Qt.call(this,e,!0)},add:ln("add"),set:ln("set"),delete:ln("delete"),clear:ln("clear"),forEach:sn(!0,!1)},o={get(e){return Zt(this,e,!0,!0)},get size(){return en(this,!0)},has(e){return Qt.call(this,e,!0)},add:ln("add"),set:ln("set"),delete:ln("delete"),clear:ln("clear"),forEach:sn(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach(r=>{e[r]=an(r,!1,!1),n[r]=an(r,!0,!1),t[r]=an(r,!1,!0),o[r]=an(r,!0,!0)}),[e,n,t,o]}const[un,dn,fn,pn]=cn();function hn(e,t){const n=t?e?pn:fn:e?dn:un;return(t,o,r)=>"__v_isReactive"===o?!e:"__v_isReadonly"===o?e:"__v_raw"===o?t:Reflect.get(h(n,o)&&o in t?n:t,o,r)}const mn={get:hn(!1,!1)},gn={get:hn(!1,!0)},vn={get:hn(!0,!1)},yn=new WeakMap,bn=new WeakMap,_n=new WeakMap,wn=new WeakMap;function xn(e){return e.__v_skip||!Object.isExtensible(e)?0:function(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}((e=>S(e).slice(8,-1))(e))}function Tn(e){return Pn(e)?e:En(e,!1,Yt,mn,yn)}function Sn(e){return En(e,!1,Gt,gn,bn)}function kn(e){return En(e,!0,Xt,vn,_n)}function En(e,t,n,o,r){if(!w(e))return e;if(e.__v_raw&&(!t||!e.__v_isReactive))return e;const i=r.get(e);if(i)return i;const s=xn(e);if(0===s)return e;const a=new Proxy(e,2===s?o:n);return r.set(e,a),a}function Cn(e){return Pn(e)?Cn(e.__v_raw):!(!e||!e.__v_isReactive)}function Pn(e){return!(!e||!e.__v_isReadonly)}function Ln(e){return!(!e||!e.__v_isShallow)}function On(e){return Cn(e)||Pn(e)}function In(e){const t=e&&e.__v_raw;return t?In(t):e}function An(e){return Object.isExtensible(e)&&D(e,"__v_skip",!0),e}const Mn=e=>w(e)?Tn(e):e,Rn=e=>w(e)?kn(e):e;class $n{constructor(e,t,n,o){this.getter=e,this._setter=t,this.dep=void 0,this.__v_isRef=!0,this.__v_isReadonly=!1,this.effect=new yt(()=>e(this._value),()=>Dn(this,2===this.effect._dirtyLevel?2:3)),this.effect.computed=this,this.effect.active=this._cacheable=!o,this.__v_isReadonly=n}get value(){const e=In(this);return e._cacheable&&!e.effect.dirty||!$(e._value,e._value=e.effect.run())||Dn(e,4),Nn(e),e.effect._dirtyLevel>=2&&Dn(e,2),e._value}set value(e){this._setter(e)}get _dirty(){return this.effect.dirty}set _dirty(e){this.effect.dirty=e}}function Nn(e){var t;Tt&&ht&&(e=In(e),Ot(ht,null!=(t=e.dep)?t:e.dep=Mt(()=>e.dep=void 0,e instanceof $n?e:void 0)))}function Dn(e,t=4,n){const o=(e=In(e)).dep;o&&At(o,t)}function Fn(e){return!(!e||!0!==e.__v_isRef)}function Bn(e){return Vn(e,!1)}function jn(e){return Vn(e,!0)}function Vn(e,t){return Fn(e)?e:new Wn(e,t)}class Wn{constructor(e,t){this.__v_isShallow=t,this.dep=void 0,this.__v_isRef=!0,this._rawValue=t?e:In(e),this._value=t?e:Mn(e)}get value(){return Nn(this),this._value}set value(e){const t=this.__v_isShallow||Ln(e)||Pn(e);e=t?e:In(e),$(e,this._rawValue)&&(this._rawValue=e,this._value=t?e:Mn(e),Dn(this,4))}}function Hn(e){return Fn(e)?e.value:e}const zn={get:(e,t,n)=>Hn(Reflect.get(e,t,n)),set:(e,t,n,o)=>{const r=e[t];return Fn(r)&&!Fn(n)?(r.value=n,!0):Reflect.set(e,t,n,o)}};function Un(e){return Cn(e)?e:new Proxy(e,zn)}class qn{constructor(e){this.dep=void 0,this.__v_isRef=!0;const{get:t,set:n}=e(()=>Nn(this),()=>Dn(this));this._get=t,this._set=n}get value(){return this._get()}set value(e){this._set(e)}}class Yn{constructor(e,t,n){this._object=e,this._key=t,this._defaultValue=n,this.__v_isRef=!0}get value(){const e=this._object[this._key];return void 0===e?this._defaultValue:e}set value(e){this._object[this._key]=e}get dep(){return e=In(this._object),t=this._key,null==(n=Rt.get(e))?void 0:n.get(t);var e,t,n}}class Xn{constructor(e){this._getter=e,this.__v_isRef=!0,this.__v_isReadonly=!0}get value(){return this._getter()}}function Gn(e,t,n){return Fn(e)?e:y(e)?new Xn(e):w(e)&&arguments.length>1?Kn(e,t,n):Bn(e)}function Kn(e,t,n){const o=e[t];return Fn(o)?o:new Yn(e,t,n)}function Jn(e,t,n,o){try{return o?e(...o):e()}catch(r){Qn(r,t,n)}}function Zn(e,t,n,o){if(y(e)){const r=Jn(e,t,n,o);return r&&x(r)&&r.catch(e=>{Qn(e,t,n)}),r}const r=[];for(let i=0;i<e.length;i++)r.push(Zn(e[i],t,n,o));return r}function Qn(e,t,n,o=!0){const r=t?t.vnode:null;if(t){let o=t.parent;const r=t.proxy,i=`https://vuejs.org/error-reference/#runtime-${n}`;for(;o;){const t=o.ec;if(t)for(let n=0;n<t.length;n++)if(!1===t[n](e,r,i))return;o=o.parent}const s=t.appContext.config.errorHandler;if(s)return void Jn(s,null,10,[e,r,i])}eo(e,n,r,o)}function eo(e,t,n,o=!0){}let to=!1,no=!1;const oo=[];let ro=0;const io=[];let so=null,ao=0;const lo=Promise.resolve();let co=null;function uo(e){const t=co||lo;return e?t.then(this?e.bind(this):e):t}function fo(e){oo.length&&oo.includes(e,to&&e.allowRecurse?ro+1:ro)||(null==e.id?oo.push(e):oo.splice(function(e){let t=ro+1,n=oo.length;for(;t<n;){const o=t+n>>>1,r=oo[o],i=go(r);i<e||i===e&&r.pre?t=o+1:n=o}return t}(e.id),0,e),po())}function po(){to||no||(no=!0,co=lo.then(yo))}function ho(e,t,n=(to?ro+1:0)){for(;n<oo.length;n++){const t=oo[n];if(t&&t.pre){if(e&&t.id!==e.uid)continue;oo.splice(n,1),n--,t()}}}function mo(e){if(io.length){const e=[...new Set(io)].sort((e,t)=>go(e)-go(t));if(io.length=0,so)return void so.push(...e);for(so=e,ao=0;ao<so.length;ao++)so[ao]();so=null,ao=0}}const go=e=>null==e.id?1/0:e.id,vo=(e,t)=>{const n=go(e)-go(t);if(0===n){if(e.pre&&!t.pre)return-1;if(t.pre&&!e.pre)return 1}return n};function yo(e){no=!1,to=!0,oo.sort(vo);try{for(ro=0;ro<oo.length;ro++){const e=oo[ro];e&&!1!==e.active&&Jn(e,null,14)}}finally{ro=0,oo.length=0,mo(),to=!1,co=null,(oo.length||io.length)&&yo()}}function bo(e,t,...n){if(e.isUnmounted)return;const o=e.vnode.props||i;let r=n;const s=t.startsWith("update:"),a=s&&t.slice(7);if(a&&a in o){const e=`${"modelValue"===a?"model":a}Modifiers`,{number:t,trim:s}=o[e]||i;s&&(r=n.map(e=>b(e)?e.trim():e)),t&&(r=n.map(F))}let l,c=o[l=R(t)]||o[l=R(O(t))];!c&&s&&(c=o[l=R(A(t))]),c&&Zn(c,e,6,_o(e,c,r));const u=o[l+"Once"];if(u){if(e.emitted){if(e.emitted[l])return}else e.emitted={};e.emitted[l]=!0,Zn(u,e,6,_o(e,u,r))}}function _o(e,t,n){if(1!==n.length)return n;if(y(t)){if(t.length<2)return n}else if(!t.find(e=>e.length>=2))return n;const o=n[0];if(o&&h(o,"type")&&h(o,"timeStamp")&&h(o,"target")&&h(o,"currentTarget")&&h(o,"detail")){const t=e.proxy,o=t.$gcd(t,!0);o&&n.push(o)}return n}function wo(e,t,n=!1){const o=t.emitsCache,r=o.get(e);if(void 0!==r)return r;const i=e.emits;let s={},a=!1;if(!y(e)){const o=e=>{const n=wo(e,t,!0);n&&(a=!0,d(s,n))};!n&&t.mixins.length&&t.mixins.forEach(o),e.extends&&o(e.extends),e.mixins&&e.mixins.forEach(o)}return i||a?(m(i)?i.forEach(e=>s[e]=null):d(s,i),w(e)&&o.set(e,s),s):(w(e)&&o.set(e,null),null)}function xo(e,t){return!(!e||!c(t))&&(t=t.slice(2).replace(/Once$/,""),h(e,t[0].toLowerCase()+t.slice(1))||h(e,A(t))||h(e,t))}let To=null,So=null;function ko(e){const t=To;return To=e,So=e&&e.type.__scopeId||null,t}function Eo(e,t=To,n){if(!t)return e;if(e._n)return e;const o=(...n)=>{o._d&&Ri(-1);const r=ko(t);let i;try{i=e(...n)}finally{ko(r),o._d&&Ri(1)}return i};return o._n=!0,o._c=!0,o._d=!0,o}function Co(e){const{type:t,vnode:n,proxy:o,withProxy:r,props:i,propsOptions:[s],slots:a,attrs:l,emit:c,render:d,renderCache:f,data:p,setupState:h,ctx:m,inheritAttrs:g}=e;let v,y;const b=ko(e);try{if(4&n.shapeFlag){const e=r||o,t=e;v=Xi(d.call(t,e,f,i,h,p,m)),y=l}else{const e=t;0,v=Xi(e.length>1?e(i,{attrs:l,slots:a,emit:c}):e(i,null)),y=t.props?l:Po(l)}}catch(w){Oi.length=0,Qn(w,e,1),v=zi(Pi)}let _=v;if(y&&!1!==g){const e=Object.keys(y),{shapeFlag:t}=_;e.length&&7&t&&(s&&e.some(u)&&(y=Lo(y,s)),_=Ui(_,y))}return n.dirs&&(_=Ui(_),_.dirs=_.dirs?_.dirs.concat(n.dirs):n.dirs),n.transition&&(_.transition=n.transition),v=_,ko(b),v}const Po=e=>{let t;for(const n in e)("class"===n||"style"===n||c(n))&&((t||(t={}))[n]=e[n]);return t},Lo=(e,t)=>{const n={};for(const o in e)u(o)&&o.slice(9)in t||(n[o]=e[o]);return n};function Oo(e,t,n){const o=Object.keys(t);if(o.length!==Object.keys(e).length)return!0;for(let r=0;r<o.length;r++){const i=o[r];if(t[i]!==e[i]&&!xo(n,i))return!0}return!1}const Io="components";function Ao(e,t){return $o(Io,e,!0,t)||e}const Mo=Symbol.for("v-ndc");function Ro(e){return b(e)?$o(Io,e,!1)||e:e||Mo}function $o(e,t,n=!0,o=!1){const r=To||ts;if(r){const n=r.type;{const e=fs(n,!1);if(e&&(e===t||e===O(t)||e===M(O(t))))return n}const i=No(r[e]||n[e],t)||No(r.appContext[e],t);return!i&&o?n:i}}function No(e,t){return e&&(e[t]||e[O(t)]||e[M(O(t))])}const Do=e=>e.__isSuspense;const Fo=Symbol.for("v-scx");function Bo(e,t){return Wo(e,null,t)}const jo={};function Vo(e,t,n){return Wo(e,t,n)}function Wo(e,t,{immediate:n,deep:o,flush:r,once:s,onTrack:l,onTrigger:c}=i){if(t&&s){const e=t;t=(...t)=>{e(...t),E()}}const u=ts,d=e=>!0===o?e:Uo(e,!1===o?1:void 0);let p,h,g=!1,v=!1;if(Fn(e)?(p=()=>e.value,g=Ln(e)):Cn(e)?(p=()=>d(e),g=!0):m(e)?(v=!0,g=e.some(e=>Cn(e)||Ln(e)),p=()=>e.map(e=>Fn(e)?e.value:Cn(e)?d(e):y(e)?Jn(e,u,2):void 0)):p=y(e)?t?()=>Jn(e,u,2):()=>(h&&h(),Zn(e,u,3,[_])):a,t&&o){const e=p;p=()=>Uo(e())}let b,_=e=>{h=S.onStop=()=>{Jn(e,u,4),h=S.onStop=void 0}};if(ls){if(_=a,t?n&&Zn(t,u,3,[p(),v?[]:void 0,_]):p(),"sync"!==r)return a;{const e=si(Fo);b=e.__watcherHandles||(e.__watcherHandles=[])}}let w=v?new Array(e.length).fill(jo):jo;const x=()=>{if(S.active&&S.dirty)if(t){const e=S.run();(o||g||(v?e.some((e,t)=>$(e,w[t])):$(e,w)))&&(h&&h(),Zn(t,u,3,[e,w===jo?void 0:v&&w[0]===jo?[]:w,_]),w=e)}else S.run()};let T;x.allowRecurse=!!t,"sync"===r?T=x:"post"===r?T=()=>_i(x,u&&u.suspense):(x.pre=!0,u&&(x.id=u.uid),T=()=>fo(x));const S=new yt(p,a,T),k=vt(),E=()=>{S.stop(),k&&f(k.effects,S)};return t?n?x():w=S.run():"post"===r?_i(S.run.bind(S),u&&u.suspense):S.run(),b&&b.push(E),E}function Ho(e,t,n){const o=this.proxy,r=b(e)?e.includes(".")?zo(o,e):()=>o[e]:e.bind(o,o);let i;y(t)?i=t:(i=t.handler,n=t);const s=is(this),a=Wo(r,i.bind(o),n);return s(),a}function zo(e,t){const n=t.split(".");return()=>{let t=e;for(let e=0;e<n.length&&t;e++)t=t[n[e]];return t}}function Uo(e,t,n=0,o){if(!w(e)||e.__v_skip)return e;if(t&&t>0){if(n>=t)return e;n++}if((o=o||new Set).has(e))return e;if(o.add(e),Fn(e))Uo(e.value,t,n,o);else if(m(e))for(let r=0;r<e.length;r++)Uo(e[r],t,n,o);else if(v(e)||g(e))e.forEach(e=>{Uo(e,t,n,o)});else if(k(e))for(const r in e)Uo(e[r],t,n,o);return e}function qo(e,t){if(null===To)return e;const n=ds(To)||To.proxy,o=e.dirs||(e.dirs=[]);for(let r=0;r<t.length;r++){let[e,s,a,l=i]=t[r];e&&(y(e)&&(e={mounted:e,updated:e}),e.deep&&Uo(s),o.push({dir:e,instance:n,value:s,oldValue:void 0,arg:a,modifiers:l}))}return e}function Yo(e,t,n,o){const r=e.dirs,i=t&&t.dirs;for(let s=0;s<r.length;s++){const a=r[s];i&&(a.oldValue=i[s].value);let l=a.dir[o];l&&(Et(),Zn(l,n,8,[e.el,a,e,t]),Ct())}}const Xo=Symbol("_leaveCb"),Go=Symbol("_enterCb");const Ko=[Function,Array],Jo={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:Ko,onEnter:Ko,onAfterEnter:Ko,onEnterCancelled:Ko,onBeforeLeave:Ko,onLeave:Ko,onAfterLeave:Ko,onLeaveCancelled:Ko,onBeforeAppear:Ko,onAppear:Ko,onAfterAppear:Ko,onAppearCancelled:Ko},Zo={name:"BaseTransition",props:Jo,setup(e,{slots:t}){const n=ns(),o=function(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return kr(()=>{e.isMounted=!0}),Pr(()=>{e.isUnmounting=!0}),e}();return()=>{const r=t.default&&rr(t.default(),!0);if(!r||!r.length)return;let i=r[0];if(r.length>1)for(const e of r)if(e.type!==Pi){i=e;break}const s=In(e),{mode:a}=s;if(o.isLeaving)return tr(i);const l=nr(i);if(!l)return tr(i);const c=er(l,s,o,n);or(l,c);const u=n.subTree,d=u&&nr(u);if(d&&d.type!==Pi&&!Bi(l,d)){const e=er(d,s,o,n);if(or(d,e),"out-in"===a)return o.isLeaving=!0,e.afterLeave=()=>{o.isLeaving=!1,!1!==n.update.active&&(n.effect.dirty=!0,n.update())},tr(i);"in-out"===a&&l.type!==Pi&&(e.delayLeave=(e,t,n)=>{Qo(o,d)[String(d.key)]=d,e[Xo]=()=>{t(),e[Xo]=void 0,delete c.delayedLeave},c.delayedLeave=n})}return i}}};function Qo(e,t){const{leavingVNodes:n}=e;let o=n.get(t.type);return o||(o=Object.create(null),n.set(t.type,o)),o}function er(e,t,n,o){const{appear:r,mode:i,persisted:s=!1,onBeforeEnter:a,onEnter:l,onAfterEnter:c,onEnterCancelled:u,onBeforeLeave:d,onLeave:f,onAfterLeave:p,onLeaveCancelled:h,onBeforeAppear:g,onAppear:v,onAfterAppear:y,onAppearCancelled:b}=t,_=String(e.key),w=Qo(n,e),x=(e,t)=>{e&&Zn(e,o,9,t)},T=(e,t)=>{const n=t[1];x(e,t),m(e)?e.every(e=>e.length<=1)&&n():e.length<=1&&n()},S={mode:i,persisted:s,beforeEnter(t){let o=a;if(!n.isMounted){if(!r)return;o=g||a}t[Xo]&&t[Xo](!0);const i=w[_];i&&Bi(e,i)&&i.el[Xo]&&i.el[Xo](),x(o,[t])},enter(e){let t=l,o=c,i=u;if(!n.isMounted){if(!r)return;t=v||l,o=y||c,i=b||u}let s=!1;const a=e[Go]=t=>{s||(s=!0,x(t?i:o,[e]),S.delayedLeave&&S.delayedLeave(),e[Go]=void 0)};t?T(t,[e,a]):a()},leave(t,o){const r=String(e.key);if(t[Go]&&t[Go](!0),n.isUnmounting)return o();x(d,[t]);let i=!1;const s=t[Xo]=n=>{i||(i=!0,o(),x(n?h:p,[t]),t[Xo]=void 0,w[r]===e&&delete w[r])};w[r]=e,f?T(f,[t,s]):s()},clone:e=>er(e,t,n,o)};return S}function tr(e){if(cr(e))return(e=Ui(e)).children=null,e}function nr(e){return cr(e)?e.children?e.children[0]:void 0:e}function or(e,t){6&e.shapeFlag&&e.component?or(e.component.subTree,t):128&e.shapeFlag?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function rr(e,t=!1,n){let o=[],r=0;for(let i=0;i<e.length;i++){let s=e[i];const a=null==n?s.key:String(n)+String(null!=s.key?s.key:i);s.type===Ei?(128&s.patchFlag&&r++,o=o.concat(rr(s.children,t,a))):(t||s.type!==Pi)&&o.push(null!=a?Ui(s,{key:a}):s)}if(r>1)for(let i=0;i<o.length;i++)o[i].patchFlag=-2;return o}
/*! #__NO_SIDE_EFFECTS__ */function ir(e,t){return y(e)?(()=>d({name:e.name},t,{setup:e}))():e}const sr=e=>!!e.type.__asyncLoader;
/*! #__NO_SIDE_EFFECTS__ */function ar(e){y(e)&&(e={loader:e});const{loader:t,loadingComponent:n,errorComponent:o,delay:r=200,timeout:i,suspensible:s=!0,onError:a}=e;let l,c=null,u=0;const d=()=>{let e;return c||(e=c=t().catch(e=>{if(e=e instanceof Error?e:new Error(String(e)),a)return new Promise((t,n)=>{a(e,()=>t((u++,c=null,d())),()=>n(e),u+1)});throw e}).then(t=>e!==c&&c?c:(t&&(t.__esModule||"Module"===t[Symbol.toStringTag])&&(t=t.default),l=t,t)))};return ir({name:"AsyncComponentWrapper",__asyncLoader:d,get __asyncResolved(){return l},setup(){const e=ts;if(l)return()=>lr(l,e);const t=t=>{c=null,Qn(t,e,13,!o)};if(s&&e.suspense||ls)return d().then(t=>()=>lr(t,e)).catch(e=>(t(e),()=>o?zi(o,{error:e}):null));const a=Bn(!1),u=Bn(),f=Bn(!!r);return r&&setTimeout(()=>{f.value=!1},r),null!=i&&setTimeout(()=>{if(!a.value&&!u.value){const e=new Error(`Async component timed out after ${i}ms.`);t(e),u.value=e}},i),d().then(()=>{a.value=!0,e.parent&&cr(e.parent.vnode)&&(e.parent.effect.dirty=!0,fo(e.parent.update))}).catch(e=>{t(e),u.value=e}),()=>a.value&&l?lr(l,e):u.value&&o?zi(o,{error:u.value}):n&&!f.value?zi(n):void 0}})}function lr(e,t){const{ref:n,props:o,children:r,ce:i}=t.vnode,s=zi(e,o,r);return s.ref=n,s.ce=i,delete t.vnode.ce,s}const cr=e=>e.type.__isKeepAlive;class ur{constructor(e){this.max=e,this._cache=new Map,this._keys=new Set,this._max=parseInt(e,10)}get(e){const{_cache:t,_keys:n,_max:o}=this,r=t.get(e);if(r)n.delete(e),n.add(e);else if(n.add(e),o&&n.size>o){const e=n.values().next().value;this.pruneCacheEntry(t.get(e)),this.delete(e)}return r}set(e,t){this._cache.set(e,t)}delete(e){this._cache.delete(e),this._keys.delete(e)}forEach(e,t){this._cache.forEach(e.bind(t))}}const dr={name:"KeepAlive",__isKeepAlive:!0,props:{include:[String,RegExp,Array],exclude:[String,RegExp,Array],max:[String,Number],matchBy:{type:String,default:"name"},cache:Object},setup(e,{slots:t}){const n=ns(),o=n.ctx;if(!o.renderer)return()=>{const e=t.default&&t.default();return e&&1===e.length?e[0]:e};const r=e.cache||new ur(e.max);r.pruneCacheEntry=s;let i=null;function s(t){var o;!i||!Bi(t,i)||"key"===e.matchBy&&t.key!==i.key?(yr(o=t),u(o,n,a,!0)):i&&yr(i)}const a=n.suspense,{renderer:{p:l,m:c,um:u,o:{createElement:d}}}=o,f=d("div");function p(t){r.forEach((n,o)=>{const i=_r(n,e.matchBy);!i||t&&t(i)||(r.delete(o),s(n))})}o.activate=(e,t,n,o,r)=>{const i=e.component;if(i.ba){const e=i.isDeactivated;i.isDeactivated=!1,N(i.ba),i.isDeactivated=e}c(e,t,n,0,a),l(i.vnode,e,t,n,i,a,o,e.slotScopeIds,r),_i(()=>{i.isDeactivated=!1,i.a&&N(i.a);const t=e.props&&e.props.onVnodeMounted;t&&Zi(t,i.parent,e)},a)},o.deactivate=e=>{const t=e.component;t.bda&&wr(t.bda),c(e,f,null,1,a),_i(()=>{t.bda&&t.bda.forEach(e=>e.__called=!1),t.da&&N(t.da);const n=e.props&&e.props.onVnodeUnmounted;n&&Zi(n,t.parent,e),t.isDeactivated=!0},a)},Vo(()=>[e.include,e.exclude,e.matchBy],([e,t])=>{e&&p(t=>pr(e,t)),t&&p(e=>!pr(t,e))},{flush:"post",deep:!0});let h=null;const m=()=>{null!=h&&r.set(h,br(n.subTree))};return kr(m),Cr(m),Pr(()=>{r.forEach((t,o)=>{r.delete(o),s(t);const{subTree:i,suspense:a}=n,l=br(i);if(t.type===l.type&&("key"!==e.matchBy||t.key===l.key)){l.component.bda&&N(l.component.bda),yr(l);const e=l.component.da;return void(e&&_i(e,a))}})}),()=>{if(h=null,!t.default)return null;const n=t.default(),o=n[0];if(n.length>1)return i=null,n;if(!Fi(o)||!(4&o.shapeFlag)&&!Do(o.type))return i=null,o;let s=br(o);const a=s.type,l=_r(s,e.matchBy),{include:c,exclude:u}=e;if(c&&(!l||!pr(c,l))||u&&l&&pr(u,l))return i=s,o;const d=null==s.key?a:s.key,f=r.get(d);return s.el&&(s=Ui(s),Do(o.type)&&(o.ssContent=s)),h=d,f&&(s.el=f.el,s.component=f.component,s.transition&&or(s,s.transition),s.shapeFlag|=512),s.shapeFlag|=256,i=s,Do(o.type)?o:s}}},fr=dr;function pr(e,t){return m(e)?e.some(e=>pr(e,t)):b(e)?e.split(",").includes(t):"[object RegExp]"===S(e)&&e.test(t)}function hr(e,t){gr(e,"a",t)}function mr(e,t){gr(e,"da",t)}function gr(e,t,n=ts){const o=e.__wdc||(e.__wdc=()=>{let t=n;for(;t;){if(t.isDeactivated)return;t=t.parent}return e()});if(o.__called=!1,xr(t,o,n),n){let e=n.parent;for(;e&&e.parent;)cr(e.parent.vnode)&&vr(o,t,n,e),e=e.parent}}function vr(e,t,n,o){const r=xr(t,e,o,!0);Lr(()=>{f(o[t],r)},n)}function yr(e){e.shapeFlag&=-257,e.shapeFlag&=-513}function br(e){return Do(e.type)?e.ssContent:e}function _r(e,t){if("name"===t){const t=e.type;return fs(sr(e)?t.__asyncResolved||{}:t)}return String(e.key)}function wr(e){for(let t=0;t<e.length;t++){const n=e[t];n.__called||(n(),n.__called=!0)}}function xr(e,t,n=ts,o=!1){if(n){if(r=e,rt.indexOf(r)>-1&&n.$pageInstance){if(n.type.__reserved)return;if(n!==n.$pageInstance&&(n=n.$pageInstance,function(e){return[he,ae].indexOf(e)>-1}(e))){const o=n.proxy;Zn(t.bind(o),n,e,he===e?[o.$page.options]:[])}}const i=n[e]||(n[e]=[]),s=t.__weh||(t.__weh=(...o)=>{if(n.isUnmounted)return;Et();const r=is(n),i=Zn(t,n,e,o);return r(),Ct(),i});return o?i.unshift(s):i.push(s),s}var r}const Tr=e=>(t,n=ts)=>(!ls||"sp"===e)&&xr(e,(...e)=>t(...e),n),Sr=Tr("bm"),kr=Tr("m"),Er=Tr("bu"),Cr=Tr("u"),Pr=Tr("bum"),Lr=Tr("um"),Or=Tr("sp"),Ir=Tr("rtg"),Ar=Tr("rtc");function Mr(e,t=ts){xr("ec",e,t)}function Rr(e,t,n,o){let r;const i=n;if(m(e)||b(e)){r=new Array(e.length);for(let n=0,o=e.length;n<o;n++)r[n]=t(e[n],n,void 0,i)}else if("number"==typeof e){r=new Array(e);for(let n=0;n<e;n++)r[n]=t(n+1,n,void 0,i)}else if(w(e))if(e[Symbol.iterator])r=Array.from(e,(e,n)=>t(e,n,void 0,i));else{const n=Object.keys(e);r=new Array(n.length);for(let o=0,s=n.length;o<s;o++){const s=n[o];r[o]=t(e[s],s,o,i)}}else r=[];return r}function $r(e,t,n={},o,r){if(To.isCE||To.parent&&sr(To.parent)&&To.parent.isCE)return"default"!==t&&(n.name=t),zi("slot",n,o&&o());let i=e[t];i&&i._c&&(i._d=!1),Ai();const s=i&&Nr(i(n)),a=Di(Ei,{key:n.key||s&&s.key||`_${t}`},s||(o?o():[]),s&&1===e._?64:-2);return!r&&a.scopeId&&(a.slotScopeIds=[a.scopeId+"-s"]),i&&i._c&&(i._d=!0),a}function Nr(e){return e.some(e=>!Fi(e)||e.type!==Pi&&!(e.type===Ei&&!Nr(e.children)))?e:null}const Dr=e=>{if(!e)return null;if(as(e)){return ds(e)||e.proxy}return Dr(e.parent)},Fr=d(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Dr(e.parent),$root:e=>Dr(e.root),$emit:e=>e.emit,$options:e=>Yr(e),$forceUpdate:e=>e.f||(e.f=(e=>function(){e.effect.dirty=!0,fo(e.update)})(e)),$nextTick:e=>e.n||(e.n=uo.bind(e.proxy)),$watch:e=>Ho.bind(e)}),Br=(e,t)=>e!==i&&!e.__isScriptSetup&&h(e,t),jr={get({_:e},t){const{ctx:n,setupState:o,data:r,props:s,accessCache:a,type:l,appContext:c}=e;let u;if("$"!==t[0]){const l=a[t];if(void 0!==l)switch(l){case 1:return o[t];case 2:return r[t];case 4:return n[t];case 3:return s[t]}else{if(Br(o,t))return a[t]=1,o[t];if(r!==i&&h(r,t))return a[t]=2,r[t];if((u=e.propsOptions[0])&&h(u,t))return a[t]=3,s[t];if(n!==i&&h(n,t))return a[t]=4,n[t];Hr&&(a[t]=0)}}const d=Fr[t];let f,p;return d?("$attrs"===t&&Dt(e,0,t),d(e)):(f=l.__cssModules)&&(f=f[t])?f:n!==i&&h(n,t)?(a[t]=4,n[t]):(p=c.config.globalProperties,h(p,t)?p[t]:void 0)},set({_:e},t,n){const{data:o,setupState:r,ctx:s}=e;return Br(r,t)?(r[t]=n,!0):o!==i&&h(o,t)?(o[t]=n,!0):!h(e.props,t)&&(("$"!==t[0]||!(t.slice(1)in e))&&(s[t]=n,!0))},has({_:{data:e,setupState:t,accessCache:n,ctx:o,appContext:r,propsOptions:s}},a){let l;return!!n[a]||e!==i&&h(e,a)||Br(t,a)||(l=s[0])&&h(l,a)||h(o,a)||h(Fr,a)||h(r.config.globalProperties,a)},defineProperty(e,t,n){return null!=n.get?e._.accessCache[t]=0:h(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function Vr(e){return m(e)?e.reduce((e,t)=>(e[t]=null,e),{}):e}function Wr(e,t){return e&&t?m(e)&&m(t)?e.concat(t):d({},Vr(e),Vr(t)):e||t}let Hr=!0;function zr(e){const t=Yr(e),n=e.proxy,o=e.ctx;Hr=!1,t.beforeCreate&&Ur(t.beforeCreate,e,"bc");const{data:r,computed:i,methods:s,watch:l,provide:c,inject:u,created:d,beforeMount:f,mounted:p,beforeUpdate:h,updated:g,activated:v,deactivated:b,beforeDestroy:_,beforeUnmount:x,destroyed:T,unmounted:S,render:k,renderTracked:E,renderTriggered:C,errorCaptured:P,serverPrefetch:L,expose:O,inheritAttrs:I,components:A,directives:M,filters:R}=t;if(u&&function(e,t){m(e)&&(e=Jr(e));for(const n in e){const o=e[n];let r;r=w(o)?"default"in o?si(o.from||n,o.default,!0):si(o.from||n):si(o),Fn(r)?Object.defineProperty(t,n,{enumerable:!0,configurable:!0,get:()=>r.value,set:e=>r.value=e}):t[n]=r}}(u,o,null),s)for(const a in s){const e=s[a];y(e)&&(o[a]=e.bind(n))}if(r){const t=r.call(n,n);w(t)&&(e.data=Tn(t))}if(Hr=!0,i)for(const m in i){const e=i[m],t=y(e)?e.bind(n,n):y(e.get)?e.get.bind(n,n):a,r=!y(e)&&y(e.set)?e.set.bind(n):a,s=ps({get:t,set:r});Object.defineProperty(o,m,{enumerable:!0,configurable:!0,get:()=>s.value,set:e=>s.value=e})}if(l)for(const a in l)qr(l[a],o,n,a);if(c){const e=y(c)?c.call(n):c;Reflect.ownKeys(e).forEach(t=>{ii(t,e[t])})}function $(e,t){m(t)?t.forEach(t=>e(t.bind(n))):t&&e(t.bind(n))}if(d&&Ur(d,e,"c"),$(Sr,f),$(kr,p),$(Er,h),$(Cr,g),$(hr,v),$(mr,b),$(Mr,P),$(Ar,E),$(Ir,C),$(Pr,x),$(Lr,S),$(Or,L),m(O))if(O.length){const t=e.exposed||(e.exposed={});O.forEach(e=>{Object.defineProperty(t,e,{get:()=>n[e],set:t=>n[e]=t})})}else e.exposed||(e.exposed={});k&&e.render===a&&(e.render=k),null!=I&&(e.inheritAttrs=I),A&&(e.components=A),M&&(e.directives=M);const N=e.appContext.config.globalProperties.$applyOptions;N&&N(t,e,n)}function Ur(e,t,n){Zn(m(e)?e.map(e=>e.bind(t.proxy)):e.bind(t.proxy),t,n)}function qr(e,t,n,o){const r=o.includes(".")?zo(n,o):()=>n[o];if(b(e)){const n=t[e];y(n)&&Vo(r,n)}else if(y(e))Vo(r,e.bind(n));else if(w(e))if(m(e))e.forEach(e=>qr(e,t,n,o));else{const o=y(e.handler)?e.handler.bind(n):t[e.handler];y(o)&&Vo(r,o,e)}}function Yr(e){const t=e.type,{mixins:n,extends:o}=t,{mixins:r,optionsCache:i,config:{optionMergeStrategies:s}}=e.appContext,a=i.get(t);let l;return a?l=a:r.length||n||o?(l={},r.length&&r.forEach(e=>Xr(l,e,s,!0)),Xr(l,t,s)):l=t,w(t)&&i.set(t,l),l}function Xr(e,t,n,o=!1){const{mixins:r,extends:i}=t;i&&Xr(e,i,n,!0),r&&r.forEach(t=>Xr(e,t,n,!0));for(const s in t)if(o&&"expose"===s);else{const o=Gr[s]||n&&n[s];e[s]=o?o(e[s],t[s]):t[s]}return e}const Gr={data:Kr,props:ei,emits:ei,methods:Qr,computed:Qr,beforeCreate:Zr,created:Zr,beforeMount:Zr,mounted:Zr,beforeUpdate:Zr,updated:Zr,beforeDestroy:Zr,beforeUnmount:Zr,destroyed:Zr,unmounted:Zr,activated:Zr,deactivated:Zr,errorCaptured:Zr,serverPrefetch:Zr,components:Qr,directives:Qr,watch:function(e,t){if(!e)return t;if(!t)return e;const n=d(Object.create(null),e);for(const o in t)n[o]=Zr(e[o],t[o]);return n},provide:Kr,inject:function(e,t){return Qr(Jr(e),Jr(t))}};function Kr(e,t){return t?e?function(){return d(y(e)?e.call(this,this):e,y(t)?t.call(this,this):t)}:t:e}function Jr(e){if(m(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function Zr(e,t){return e?[...new Set([].concat(e,t))]:t}function Qr(e,t){return e?d(Object.create(null),e,t):t}function ei(e,t){return e?m(e)&&m(t)?[...new Set([...e,...t])]:d(Object.create(null),Vr(e),Vr(null!=t?t:{})):t}function ti(){return{app:null,config:{isNativeTag:l,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let ni=0;function oi(e,t){return function(t,n=null){y(t)||(t=d({},t)),null==n||w(n)||(n=null);const o=ti(),r=new WeakSet;let i=!1;const s=o.app={_uid:ni++,_component:t,_props:n,_container:null,_context:o,_instance:null,version:gs,get config(){return o.config},set config(e){},use:(e,...t)=>(r.has(e)||(e&&y(e.install)?(r.add(e),e.install(s,...t)):y(e)&&(r.add(e),e(s,...t))),s),mixin:e=>(o.mixins.includes(e)||o.mixins.push(e),s),component:(e,t)=>t?(o.components[e]=t,s):o.components[e],directive:(e,t)=>t?(o.directives[e]=t,s):o.directives[e],mount(r,a,l){if(!i){const a=zi(t,n);return a.appContext=o,!0===l?l="svg":!1===l&&(l=void 0),e(a,r,l),i=!0,s._container=r,r.__vue_app__=s,s._instance=a.component,ds(a.component)||a.component.proxy}},unmount(){i&&(e(null,s._container),delete s._container.__vue_app__)},provide:(e,t)=>(o.provides[e]=t,s),runWithContext(e){const t=ri;ri=s;try{return e()}finally{ri=t}}};return s}}let ri=null;function ii(e,t){if(ts){let n=ts.provides;const o=ts.parent&&ts.parent.provides;o===n&&(n=ts.provides=Object.create(o)),n[e]=t,"app"===ts.type.mpType&&ts.appContext.app.provide(e,t)}else;}function si(e,t,n=!1){const o=ts||To;if(o||ri){const r=o?null==o.parent?o.vnode.appContext&&o.vnode.appContext.provides:o.parent.provides:ri._context.provides;if(r&&e in r)return r[e];if(arguments.length>1)return n&&y(t)?t.call(o&&o.proxy):t}}function ai(e,t,n,o){const[r,s]=e.propsOptions;let a,l=!1;if(t)for(let i in t){if(C(i))continue;const c=t[i];let u;r&&h(r,u=O(i))?s&&s.includes(u)?(a||(a={}))[u]=c:n[u]=c:xo(e.emitsOptions,i)||i in o&&c===o[i]||(o[i]=c,l=!0)}if(s){const t=In(n),o=a||i;for(let i=0;i<s.length;i++){const a=s[i];n[a]=li(r,t,a,o[a],e,!h(o,a))}}return l}function li(e,t,n,o,r,i){const s=e[n];if(null!=s){const e=h(s,"default");if(e&&void 0===o){const e=s.default;if(s.type!==Function&&!s.skipFactory&&y(e)){const{propsDefaults:i}=r;if(n in i)o=i[n];else{const s=is(r);o=i[n]=e.call(null,t),s()}}else o=e}s[0]&&(i&&!e?o=!1:!s[1]||""!==o&&o!==A(n)||(o=!0))}return o}function ci(e,t,n=!1){const o=t.propsCache,r=o.get(e);if(r)return r;const a=e.props,l={},c=[];let u=!1;if(!y(e)){const o=e=>{u=!0;const[n,o]=ci(e,t,!0);d(l,n),o&&c.push(...o)};!n&&t.mixins.length&&t.mixins.forEach(o),e.extends&&o(e.extends),e.mixins&&e.mixins.forEach(o)}if(!a&&!u)return w(e)&&o.set(e,s),s;if(m(a))for(let s=0;s<a.length;s++){const e=O(a[s]);ui(e)&&(l[e]=i)}else if(a)for(const i in a){const e=O(i);if(ui(e)){const t=a[i],n=l[e]=m(t)||y(t)?{type:t}:d({},t);if(n){const t=pi(Boolean,n.type),o=pi(String,n.type);n[0]=t>-1,n[1]=o<0||t<o,(t>-1||h(n,"default"))&&c.push(e)}}}const f=[l,c];return w(e)&&o.set(e,f),f}function ui(e){return"$"!==e[0]&&!C(e)}function di(e){if(null===e)return"null";if("function"==typeof e)return e.name||"";if("object"==typeof e){return e.constructor&&e.constructor.name||""}return""}function fi(e,t){return di(e)===di(t)}function pi(e,t){return m(t)?t.findIndex(t=>fi(t,e)):y(t)&&fi(t,e)?0:-1}const hi=e=>"_"===e[0]||"$stable"===e,mi=e=>m(e)?e.map(Xi):[Xi(e)],gi=(e,t,n)=>{if(t._n)return t;const o=Eo((...e)=>mi(t(...e)),n);return o._c=!1,o},vi=(e,t,n)=>{const o=e._ctx;for(const r in e){if(hi(r))continue;const n=e[r];if(y(n))t[r]=gi(0,n,o);else if(null!=n){const e=mi(n);t[r]=()=>e}}},yi=(e,t)=>{const n=mi(t);e.slots.default=()=>n};function bi(e,t,n,o,r=!1){if(m(e))return void e.forEach((e,i)=>bi(e,t&&(m(t)?t[i]:t),n,o,r));if(sr(o)&&!r)return;const s=4&o.shapeFlag?ds(o.component)||o.component.proxy:o.el,a=r?null:s,{i:l,r:c}=e,u=t&&t.r,d=l.refs===i?l.refs={}:l.refs,p=l.setupState;if(null!=u&&u!==c&&(b(u)?(d[u]=null,h(p,u)&&(p[u]=null)):Fn(u)&&(u.value=null)),y(c))Jn(c,l,12,[a,d]);else{const t=b(c),o=Fn(c);if(t||o){const i=()=>{if(e.f){const n=t?h(p,c)?p[c]:d[c]:c.value;r?m(n)&&f(n,s):m(n)?n.includes(s)||n.push(s):t?(d[c]=[s],h(p,c)&&(p[c]=d[c])):(c.value=[s],e.k&&(d[e.k]=c.value))}else t?(d[c]=a,h(p,c)&&(p[c]=a)):o&&(c.value=a,e.k&&(d[e.k]=a))};a?(i.id=-1,_i(i,n)):i()}}}const _i=function(e,t){var n;t&&t.pendingBranch?m(e)?t.effects.push(...e):t.effects.push(e):(m(n=e)?io.push(...n):so&&so.includes(n,n.allowRecurse?ao+1:ao)||io.push(n),po())};function wi(e){return function(e){j().__VUE__=!0;const{insert:t,remove:n,patchProp:o,forcePatchProp:r,createElement:l,createText:c,createComment:u,setText:f,setElementText:p,parentNode:m,nextSibling:g,setScopeId:v=a,insertStaticContent:y}=e,b=(e,t,n,o=null,r=null,i=null,s=void 0,a=null,l=!!t.dynamicChildren)=>{if(e===t)return;e&&!Bi(e,t)&&(o=ee(e),G(e,r,i,!0),e=null),-2===t.patchFlag&&(l=!1,t.dynamicChildren=null);const{type:c,ref:u,shapeFlag:d}=t;switch(c){case Ci:_(e,t,n,o);break;case Pi:w(e,t,n,o);break;case Li:null==e&&T(t,n,o,s);break;case Ei:F(e,t,n,o,r,i,s,a,l);break;default:1&d?E(e,t,n,o,r,i,s,a,l):6&d?B(e,t,n,o,r,i,s,a,l):(64&d||128&d)&&c.process(e,t,n,o,r,i,s,a,l,oe)}null!=u&&r&&bi(u,e&&e.ref,i,t||e,!t)},_=(e,n,o,r)=>{if(null==e)t(n.el=c(n.children),o,r);else{const t=n.el=e.el;n.children!==e.children&&f(t,n.children)}},w=(e,n,o,r)=>{null==e?t(n.el=u(n.children||""),o,r):n.el=e.el},T=(e,t,n,o)=>{[e.el,e.anchor]=y(e.children,t,n,o,e.el,e.anchor)},S=({el:e,anchor:n},o,r)=>{let i;for(;e&&e!==n;)i=g(e),t(e,o,r),e=i;t(n,o,r)},k=({el:e,anchor:t})=>{let o;for(;e&&e!==t;)o=g(e),n(e),e=o;n(t)},E=(e,t,n,o,r,i,s,a,l)=>{"svg"===t.type?s="svg":"math"===t.type&&(s="mathml"),null==e?P(t,n,o,r,i,s,a,l):M(e,t,r,i,s,a,l)},P=(e,n,r,i,s,a,c,u)=>{let d,f;const{props:h,shapeFlag:m,transition:g,dirs:v}=e;if(d=e.el=l(e.type,a,h&&h.is,h),8&m?p(d,e.children):16&m&&I(e.children,d,null,i,s,xi(e,a),c,u),v&&Yo(e,null,i,"created"),L(d,e,e.scopeId,c,i),h){for(const t in h)"value"===t||C(t)||o(d,t,null,h[t],a,e.children,i,s,Q);"value"in h&&o(d,"value",null,h.value,a),(f=h.onVnodeBeforeMount)&&Zi(f,i,e)}Object.defineProperty(d,"__vueParentComponent",{value:i,enumerable:!1}),v&&Yo(e,null,i,"beforeMount");const y=function(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}(s,g);y&&g.beforeEnter(d),t(d,n,r),((f=h&&h.onVnodeMounted)||y||v)&&_i(()=>{f&&Zi(f,i,e),y&&g.enter(d),v&&Yo(e,null,i,"mounted")},s)},L=(e,t,n,o,r)=>{if(n&&v(e,n),o)for(let i=0;i<o.length;i++)v(e,o[i]);if(r){if(t===r.subTree){const t=r.vnode;L(e,t,t.scopeId,t.slotScopeIds,r.parent)}}},I=(e,t,n,o,r,i,s,a,l=0)=>{for(let c=l;c<e.length;c++){const l=e[c]=a?Gi(e[c]):Xi(e[c]);b(null,l,t,n,o,r,i,s,a)}},M=(e,t,n,s,a,l,c)=>{const u=t.el=e.el;let{patchFlag:d,dynamicChildren:f,dirs:h}=t;d|=16&e.patchFlag;const m=e.props||i,g=t.props||i;let v;if(n&&Ti(n,!1),(v=g.onVnodeBeforeUpdate)&&Zi(v,n,t,e),h&&Yo(t,e,n,"beforeUpdate"),n&&Ti(n,!0),f?R(e.dynamicChildren,f,u,n,s,xi(t,a),l):c||U(e,t,u,null,n,s,xi(t,a),l,!1),d>0){if(16&d)$(u,t,m,g,n,s,a);else if(2&d&&m.class!==g.class&&o(u,"class",null,g.class,a),4&d&&o(u,"style",m.style,g.style,a),8&d){const i=t.dynamicProps;for(let t=0;t<i.length;t++){const l=i[t],c=m[l],d=g[l];(d!==c||"value"===l||r&&r(u,l))&&o(u,l,c,d,a,e.children,n,s,Q)}}1&d&&e.children!==t.children&&p(u,t.children)}else c||null!=f||$(u,t,m,g,n,s,a);((v=g.onVnodeUpdated)||h)&&_i(()=>{v&&Zi(v,n,t,e),h&&Yo(t,e,n,"updated")},s)},R=(e,t,n,o,r,i,s)=>{for(let a=0;a<t.length;a++){const l=e[a],c=t[a],u=l.el&&(l.type===Ei||!Bi(l,c)||70&l.shapeFlag)?m(l.el):n;b(l,c,u,null,o,r,i,s,!0)}},$=(e,t,n,s,a,l,c)=>{if(n!==s){if(n!==i)for(const r in n)C(r)||r in s||o(e,r,n[r],null,c,t.children,a,l,Q);for(const i in s){if(C(i))continue;const u=s[i],d=n[i];(u!==d&&"value"!==i||r&&r(e,i))&&o(e,i,d,u,c,t.children,a,l,Q)}"value"in s&&o(e,"value",n.value,s.value,c)}},F=(e,n,o,r,i,s,a,l,u)=>{const d=n.el=e?e.el:c(""),f=n.anchor=e?e.anchor:c("");let{patchFlag:p,dynamicChildren:h,slotScopeIds:m}=n;m&&(l=l?l.concat(m):m),null==e?(t(d,o,r),t(f,o,r),I(n.children||[],o,f,i,s,a,l,u)):p>0&&64&p&&h&&e.dynamicChildren?(R(e.dynamicChildren,h,o,i,s,a,l),(null!=n.key||i&&n===i.subTree)&&Si(e,n,!0)):U(e,n,o,f,i,s,a,l,u)},B=(e,t,n,o,r,i,s,a,l)=>{t.slotScopeIds=a,null==e?512&t.shapeFlag?r.ctx.activate(t,n,o,s,l):V(t,n,o,r,i,s,l):W(e,t,l)},V=(e,t,n,o,r,s,a)=>{const l=e.component=function(e,t,n){const o=e.type,r=(t?t.appContext:e.appContext)||Qi,s={uid:es++,vnode:e,type:o,parent:t,appContext:r,root:null,next:null,subTree:null,effect:null,update:null,scope:new mt(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(r.provides),accessCache:null,renderCache:[],components:null,directives:null,propsOptions:ci(o,r),emitsOptions:wo(o,r),emit:null,emitted:null,propsDefaults:i,inheritAttrs:o.inheritAttrs,ctx:i,data:i,props:i,attrs:i,slots:i,refs:i,setupState:i,setupContext:null,attrsProxy:null,slotsProxy:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,bda:null,da:null,ba:null,a:null,rtg:null,rtc:null,ec:null,sp:null};s.ctx={_:s},s.root=t?t.root:s,s.emit=bo.bind(null,s),s.$pageInstance=t&&t.$pageInstance,e.ce&&e.ce(s);return s}(e,o,r);if(cr(e)&&(l.ctx.renderer=oe),function(e,t=!1){t&&rs(t);const{props:n,children:o}=e.vnode,r=as(e);(function(e,t,n,o=!1){const r={},i={};D(i,ji,1),e.propsDefaults=Object.create(null),ai(e,t,r,i);for(const s in e.propsOptions[0])s in r||(r[s]=void 0);n?e.props=o?r:Sn(r):e.type.props?e.props=r:e.props=i,e.attrs=i})(e,n,r,t),((e,t)=>{if(32&e.vnode.shapeFlag){const n=t._;n?(e.slots=In(t),D(t,"_",n)):vi(t,e.slots={})}else e.slots={},t&&yi(e,t);D(e.slots,ji,1)})(e,o);const i=r?function(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=An(new Proxy(e.ctx,jr));const{setup:o}=n;if(o){const n=e.setupContext=o.length>1?function(e){const t=t=>{e.exposed=t||{}};return{get attrs(){return function(e){return e.attrsProxy||(e.attrsProxy=new Proxy(e.attrs,{get:(t,n)=>(Dt(e,0,"$attrs"),t[n])}))}(e)},slots:e.slots,emit:e.emit,expose:t}}(e):null,r=is(e);Et();const i=Jn(o,e,0,[e.props,n]);if(Ct(),r(),x(i)){if(i.then(ss,ss),t)return i.then(t=>{cs(e,t)}).catch(t=>{Qn(t,e,0)});e.asyncDep=i}else cs(e,i)}else us(e)}(e,t):void 0;t&&rs(!1)}(l),l.asyncDep){if(r&&r.registerDep(l,H),!e.el){const e=l.subTree=zi(Pi);w(null,e,t,n)}}else H(l,e,t,n,r,s,a)},W=(e,t,n)=>{const o=t.component=e.component;if(function(e,t,n){const{props:o,children:r,component:i}=e,{props:s,children:a,patchFlag:l}=t,c=i.emitsOptions;if(t.dirs||t.transition)return!0;if(!(n&&l>=0))return!(!r&&!a||a&&a.$stable)||o!==s&&(o?!s||Oo(o,s,c):!!s);if(1024&l)return!0;if(16&l)return o?Oo(o,s,c):!!s;if(8&l){const e=t.dynamicProps;for(let t=0;t<e.length;t++){const n=e[t];if(s[n]!==o[n]&&!xo(c,n))return!0}}return!1}(e,t,n)){if(o.asyncDep&&!o.asyncResolved)return void z(o,t,n);o.next=t,function(e){const t=oo.indexOf(e);t>ro&&oo.splice(t,1)}(o.update),o.effect.dirty=!0,o.update()}else t.el=e.el,o.vnode=t},H=(e,t,n,o,r,i,s)=>{const l=()=>{if(e.isMounted){let{next:t,bu:n,u:o,parent:a,vnode:c}=e;{const n=ki(e);if(n)return t&&(t.el=c.el,z(e,t,s)),void n.asyncDep.then(()=>{e.isUnmounted||l()})}let u,d=t;Ti(e,!1),t?(t.el=c.el,z(e,t,s)):t=c,n&&N(n),(u=t.props&&t.props.onVnodeBeforeUpdate)&&Zi(u,a,t,c),Ti(e,!0);const f=Co(e),p=e.subTree;e.subTree=f,b(p,f,m(p.el),ee(p),e,r,i),t.el=f.el,null===d&&function({vnode:e,parent:t},n){for(;t;){const o=t.subTree;if(o.suspense&&o.suspense.activeBranch===e&&(o.el=e.el),o!==e)break;(e=t.vnode).el=n,t=t.parent}}(e,f.el),o&&_i(o,r),(u=t.props&&t.props.onVnodeUpdated)&&_i(()=>Zi(u,a,t,c),r)}else{let s;const{el:a,props:l}=t,{bm:c,m:u,parent:d}=e,f=sr(t);Ti(e,!1),c&&N(c),!f&&(s=l&&l.onVnodeBeforeMount)&&Zi(s,d,t),Ti(e,!0);{const s=e.subTree=Co(e);b(null,s,n,o,e,r,i),t.el=s.el}if(u&&_i(u,r),!f&&(s=l&&l.onVnodeMounted)){const e=t;_i(()=>Zi(s,d,e),r)}(256&t.shapeFlag||d&&sr(d.vnode)&&256&d.vnode.shapeFlag)&&(e.ba&&wr(e.ba),e.a&&_i(e.a,r)),e.isMounted=!0,t=n=o=null}},c=e.effect=new yt(l,a,()=>fo(u),e.scope),u=e.update=()=>{c.dirty&&c.run()};u.id=e.uid,Ti(e,!0),u()},z=(e,t,n)=>{t.component=e;const o=e.vnode.props;e.vnode=t,e.next=null,function(e,t,n,o){const{props:r,attrs:i,vnode:{patchFlag:s}}=e,a=In(r),[l]=e.propsOptions;let c=!1;if(!(o||s>0)||16&s){let o;ai(e,t,r,i)&&(c=!0);for(const i in a)t&&(h(t,i)||(o=A(i))!==i&&h(t,o))||(l?!n||void 0===n[i]&&void 0===n[o]||(r[i]=li(l,a,i,void 0,e,!0)):delete r[i]);if(i!==a)for(const e in i)t&&h(t,e)||(delete i[e],c=!0)}else if(8&s){const n=e.vnode.dynamicProps;for(let o=0;o<n.length;o++){let s=n[o];if(xo(e.emitsOptions,s))continue;const u=t[s];if(l)if(h(i,s))u!==i[s]&&(i[s]=u,c=!0);else{const t=O(s);r[t]=li(l,a,t,u,e,!1)}else u!==i[s]&&(i[s]=u,c=!0)}}c&&Ft(e,"set","$attrs")}(e,t.props,o,n),((e,t,n)=>{const{vnode:o,slots:r}=e;let s=!0,a=i;if(32&o.shapeFlag){const e=t._;e?n&&1===e?s=!1:(d(r,t),n||1!==e||delete r._):(s=!t.$stable,vi(t,r)),a=t}else t&&(yi(e,t),a={default:1});if(s)for(const i in r)hi(i)||null!=a[i]||delete r[i]})(e,t.children,n),Et(),ho(e),Ct()},U=(e,t,n,o,r,i,s,a,l=!1)=>{const c=e&&e.children,u=e?e.shapeFlag:0,d=t.children,{patchFlag:f,shapeFlag:h}=t;if(f>0){if(128&f)return void Y(c,d,n,o,r,i,s,a,l);if(256&f)return void q(c,d,n,o,r,i,s,a,l)}8&h?(16&u&&Q(c,r,i),d!==c&&p(n,d)):16&u?16&h?Y(c,d,n,o,r,i,s,a,l):Q(c,r,i,!0):(8&u&&p(n,""),16&h&&I(d,n,o,r,i,s,a,l))},q=(e,t,n,o,r,i,a,l,c)=>{t=t||s;const u=(e=e||s).length,d=t.length,f=Math.min(u,d);let p;for(p=0;p<f;p++){const o=t[p]=c?Gi(t[p]):Xi(t[p]);b(e[p],o,n,null,r,i,a,l,c)}u>d?Q(e,r,i,!0,!1,f):I(t,n,o,r,i,a,l,c,f)},Y=(e,t,n,o,r,i,a,l,c)=>{let u=0;const d=t.length;let f=e.length-1,p=d-1;for(;u<=f&&u<=p;){const o=e[u],s=t[u]=c?Gi(t[u]):Xi(t[u]);if(!Bi(o,s))break;b(o,s,n,null,r,i,a,l,c),u++}for(;u<=f&&u<=p;){const o=e[f],s=t[p]=c?Gi(t[p]):Xi(t[p]);if(!Bi(o,s))break;b(o,s,n,null,r,i,a,l,c),f--,p--}if(u>f){if(u<=p){const e=p+1,s=e<d?t[e].el:o;for(;u<=p;)b(null,t[u]=c?Gi(t[u]):Xi(t[u]),n,s,r,i,a,l,c),u++}}else if(u>p)for(;u<=f;)G(e[u],r,i,!0),u++;else{const h=u,m=u,g=new Map;for(u=m;u<=p;u++){const e=t[u]=c?Gi(t[u]):Xi(t[u]);null!=e.key&&g.set(e.key,u)}let v,y=0;const _=p-m+1;let w=!1,x=0;const T=new Array(_);for(u=0;u<_;u++)T[u]=0;for(u=h;u<=f;u++){const o=e[u];if(y>=_){G(o,r,i,!0);continue}let s;if(null!=o.key)s=g.get(o.key);else for(v=m;v<=p;v++)if(0===T[v-m]&&Bi(o,t[v])){s=v;break}void 0===s?G(o,r,i,!0):(T[s-m]=u+1,s>=x?x=s:w=!0,b(o,t[s],n,null,r,i,a,l,c),y++)}const S=w?function(e){const t=e.slice(),n=[0];let o,r,i,s,a;const l=e.length;for(o=0;o<l;o++){const l=e[o];if(0!==l){if(r=n[n.length-1],e[r]<l){t[o]=r,n.push(o);continue}for(i=0,s=n.length-1;i<s;)a=i+s>>1,e[n[a]]<l?i=a+1:s=a;l<e[n[i]]&&(i>0&&(t[o]=n[i-1]),n[i]=o)}}i=n.length,s=n[i-1];for(;i-- >0;)n[i]=s,s=t[s];return n}(T):s;for(v=S.length-1,u=_-1;u>=0;u--){const e=m+u,s=t[e],f=e+1<d?t[e+1].el:o;0===T[u]?b(null,s,n,f,r,i,a,l,c):w&&(v<0||u!==S[v]?X(s,n,f,2):v--)}}},X=(e,n,o,r,i=null)=>{const{el:s,type:a,transition:l,children:c,shapeFlag:u}=e;if(6&u)return void X(e.component.subTree,n,o,r);if(128&u)return void e.suspense.move(n,o,r);if(64&u)return void a.move(e,n,o,oe);if(a===Ei){t(s,n,o);for(let e=0;e<c.length;e++)X(c[e],n,o,r);return void t(e.anchor,n,o)}if(a===Li)return void S(e,n,o);if(2!==r&&1&u&&l)if(0===r)l.beforeEnter(s),t(s,n,o),_i(()=>l.enter(s),i);else{const{leave:e,delayLeave:r,afterLeave:i}=l,a=()=>t(s,n,o),c=()=>{e(s,()=>{a(),i&&i()})};r?r(s,a,c):c()}else t(s,n,o)},G=(e,t,n,o=!1,r=!1)=>{const{type:i,props:s,ref:a,children:l,dynamicChildren:c,shapeFlag:u,patchFlag:d,dirs:f}=e;if(null!=a&&bi(a,null,n,e,!0),256&u)return void t.ctx.deactivate(e);const p=1&u&&f,h=!sr(e);let m;if(h&&(m=s&&s.onVnodeBeforeUnmount)&&Zi(m,t,e),6&u)Z(e.component,n,o);else{if(128&u)return void e.suspense.unmount(n,o);p&&Yo(e,null,t,"beforeUnmount"),64&u?e.type.remove(e,t,n,r,oe,o):c&&(i!==Ei||d>0&&64&d)?Q(c,t,n,!1,!0):(i===Ei&&384&d||!r&&16&u)&&Q(l,t,n),o&&K(e)}(h&&(m=s&&s.onVnodeUnmounted)||p)&&_i(()=>{m&&Zi(m,t,e),p&&Yo(e,null,t,"unmounted")},n)},K=e=>{const{type:t,el:o,anchor:r,transition:i}=e;if(t===Ei)return void J(o,r);if(t===Li)return void k(e);const s=()=>{n(o),i&&!i.persisted&&i.afterLeave&&i.afterLeave()};if(1&e.shapeFlag&&i&&!i.persisted){const{leave:t,delayLeave:n}=i,r=()=>t(o,s);n?n(e.el,s,r):r()}else s()},J=(e,t)=>{let o;for(;e!==t;)o=g(e),n(e),e=o;n(t)},Z=(e,t,n)=>{const{bum:o,scope:r,update:i,subTree:s,um:a}=e;o&&N(o),r.stop(),i&&(i.active=!1,G(s,e,t,n)),a&&_i(a,t),_i(()=>{e.isUnmounted=!0},t),t&&t.pendingBranch&&!t.isUnmounted&&e.asyncDep&&!e.asyncResolved&&e.suspenseId===t.pendingId&&(t.deps--,0===t.deps&&t.resolve())},Q=(e,t,n,o=!1,r=!1,i=0)=>{for(let s=i;s<e.length;s++)G(e[s],t,n,o,r)},ee=e=>6&e.shapeFlag?ee(e.component.subTree):128&e.shapeFlag?e.suspense.next():g(e.anchor||e.el);let te=!1;const ne=(e,t,n)=>{null==e?t._vnode&&G(t._vnode,null,null,!0):b(t._vnode||null,e,t,null,null,null,n),te||(te=!0,ho(),mo(),te=!1),t._vnode=e},oe={p:b,um:G,m:X,r:K,mt:V,mc:I,pc:U,pbc:R,n:ee,o:e};let re;return{render:ne,hydrate:re,createApp:oi(ne)}}(e)}function xi({type:e,props:t},n){return"svg"===n&&"foreignObject"===e||"mathml"===n&&"annotation-xml"===e&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function Ti({effect:e,update:t},n){e.allowRecurse=t.allowRecurse=n}function Si(e,t,n=!1){const o=e.children,r=t.children;if(m(o)&&m(r))for(let i=0;i<o.length;i++){const e=o[i];let t=r[i];1&t.shapeFlag&&!t.dynamicChildren&&((t.patchFlag<=0||32===t.patchFlag)&&(t=r[i]=Gi(r[i]),t.el=e.el),n||Si(e,t)),t.type===Ci&&(t.el=e.el)}}function ki(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:ki(t)}const Ei=Symbol.for("v-fgt"),Ci=Symbol.for("v-txt"),Pi=Symbol.for("v-cmt"),Li=Symbol.for("v-stc"),Oi=[];let Ii=null;function Ai(e=!1){Oi.push(Ii=e?null:[])}let Mi=1;function Ri(e){Mi+=e}function $i(e){return e.dynamicChildren=Mi>0?Ii||s:null,Oi.pop(),Ii=Oi[Oi.length-1]||null,Mi>0&&Ii&&Ii.push(e),e}function Ni(e,t,n,o,r,i){return $i(Hi(e,t,n,o,r,i,!0))}function Di(e,t,n,o,r){return $i(zi(e,t,n,o,r,!0))}function Fi(e){return!!e&&!0===e.__v_isVNode}function Bi(e,t){return e.type===t.type&&e.key===t.key}const ji="__vInternal",Vi=({key:e})=>null!=e?e:null,Wi=({ref:e,ref_key:t,ref_for:n})=>("number"==typeof e&&(e=""+e),null!=e?b(e)||Fn(e)||y(e)?{i:To,r:e,k:t,f:!!n}:e:null);function Hi(e,t=null,n=null,o=0,r=null,i=(e===Ei?0:1),s=!1,a=!1){const l={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Vi(t),ref:t&&Wi(t),scopeId:So,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetAnchor:null,staticCount:0,shapeFlag:i,patchFlag:o,dynamicProps:r,dynamicChildren:null,appContext:null,ctx:To};return a?(Ki(l,n),128&i&&e.normalize(l)):n&&(l.shapeFlag|=b(n)?8:16),Mi>0&&!s&&Ii&&(l.patchFlag>0||6&i)&&32!==l.patchFlag&&Ii.push(l),l}const zi=function(e,t=null,n=null,o=0,r=null,i=!1){e&&e!==Mo||(e=Pi);if(Fi(e)){const o=Ui(e,t,!0);return n&&Ki(o,n),Mi>0&&!i&&Ii&&(6&o.shapeFlag?Ii[Ii.indexOf(e)]=o:Ii.push(o)),o.patchFlag|=-2,o}s=e,y(s)&&"__vccOpts"in s&&(e=e.__vccOpts);var s;if(t){t=function(e){return e?On(e)||ji in e?d({},e):e:null}(t);let{class:e,style:n}=t;e&&!b(e)&&(t.class=Ue(e)),w(n)&&(On(n)&&!m(n)&&(n=d({},n)),t.style=ze(n))}const a=b(e)?1:Do(e)?128:(e=>e.__isTeleport)(e)?64:w(e)?4:y(e)?2:0;return Hi(e,t,n,o,r,a,i,!0)};function Ui(e,t,n=!1){const{props:o,ref:r,patchFlag:i,children:s}=e,a=t?Ji(o||{},t):o;return{__v_isVNode:!0,__v_skip:!0,type:e.type,props:a,key:a&&Vi(a),ref:t&&t.ref?n&&r?m(r)?r.concat(Wi(t)):[r,Wi(t)]:Wi(t):r,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:s,target:e.target,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Ei?-1===i?16:16|i:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:e.transition,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Ui(e.ssContent),ssFallback:e.ssFallback&&Ui(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce}}function qi(e=" ",t=0){return zi(Ci,null,e,t)}function Yi(e="",t=!1){return t?(Ai(),Di(Pi,null,e)):zi(Pi,null,e)}function Xi(e){return null==e||"boolean"==typeof e?zi(Pi):m(e)?zi(Ei,null,e.slice()):"object"==typeof e?Gi(e):zi(Ci,null,String(e))}function Gi(e){return null===e.el&&-1!==e.patchFlag||e.memo?e:Ui(e)}function Ki(e,t){let n=0;const{shapeFlag:o}=e;if(null==t)t=null;else if(m(t))n=16;else if("object"==typeof t){if(65&o){const n=t.default;return void(n&&(n._c&&(n._d=!1),Ki(e,n()),n._c&&(n._d=!0)))}{n=32;const o=t._;o||ji in t?3===o&&To&&(1===To.slots._?t._=1:(t._=2,e.patchFlag|=1024)):t._ctx=To}}else y(t)?(t={default:t,_ctx:To},n=32):(t=String(t),64&o?(n=16,t=[qi(t)]):n=8);e.children=t,e.shapeFlag|=n}function Ji(...e){const t={};for(let n=0;n<e.length;n++){const o=e[n];for(const e in o)if("class"===e)t.class!==o.class&&(t.class=Ue([t.class,o.class]));else if("style"===e)t.style=ze([t.style,o.style]);else if(c(e)){const n=t[e],r=o[e];!r||n===r||m(n)&&n.includes(r)||(t[e]=n?[].concat(n,r):r)}else""!==e&&(t[e]=o[e])}return t}function Zi(e,t,n,o=null){Zn(e,t,7,[n,o])}const Qi=ti();let es=0;let ts=null;const ns=()=>ts||To;let os,rs;{const e=j(),t=(t,n)=>{let o;return(o=e[t])||(o=e[t]=[]),o.push(n),e=>{o.length>1?o.forEach(t=>t(e)):o[0](e)}};os=t("__VUE_INSTANCE_SETTERS__",e=>ts=e),rs=t("__VUE_SSR_SETTERS__",e=>ls=e)}const is=e=>{const t=ts;return os(e),e.scope.on(),()=>{e.scope.off(),os(t)}},ss=()=>{ts&&ts.scope.off(),os(null)};function as(e){return 4&e.vnode.shapeFlag}let ls=!1;function cs(e,t,n){y(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:w(t)&&(e.setupState=Un(t)),us(e)}function us(e,t,n){const o=e.type;e.render||(e.render=o.render||a);{const t=is(e);Et();try{zr(e)}finally{Ct(),t()}}}function ds(e){if(e.exposed)return e.exposeProxy||(e.exposeProxy=new Proxy(Un(An(e.exposed)),{get:(t,n)=>n in t?t[n]:n in Fr?Fr[n](e):void 0,has:(e,t)=>t in e||t in Fr}))}function fs(e,t=!0){return y(e)?e.displayName||e.name:e.name||t&&e.__name}const ps=(e,t)=>{const n=function(e,t,n=!1){let o,r;const i=y(e);return i?(o=e,r=a):(o=e.get,r=e.set),new $n(o,r,i||!r,n)}(e,0,ls);return n};function hs(e,t,n=i){const o=ns(),r=O(t),s=A(t),a=new qn((i,a)=>{let l;return Wo(()=>{const n=e[t];$(l,n)&&(l=n,a())},null,{flush:"sync"}),{get:()=>(i(),n.get?n.get(l):l),set(e){const i=o.vnode.props;i&&(t in i||r in i||s in i)&&(`onUpdate:${t}`in i||`onUpdate:${r}`in i||`onUpdate:${s}`in i)||!$(e,l)||(l=e,a()),o.emit(`update:${t}`,n.set?n.set(e):e)}}});const l="modelValue"===t?"modelModifiers":`${t}Modifiers`;return a[Symbol.iterator]=()=>{let t=0;return{next:()=>t<2?{value:t++?e[l]||{}:a,done:!1}:{done:!0}}},a}function ms(e,t,n){const o=arguments.length;return 2===o?w(t)&&!m(t)?Fi(t)?zi(e,null,[t]):zi(e,t):zi(e,null,t):(o>3?n=Array.prototype.slice.call(arguments,2):3===o&&Fi(n)&&(n=[n]),zi(e,t,n))}const gs="3.4.21",vs="undefined"!=typeof document?document:null,ys=vs&&vs.createElement("template"),bs={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,o)=>{const r="svg"===t?vs.createElementNS("http://www.w3.org/2000/svg",e):"mathml"===t?vs.createElementNS("http://www.w3.org/1998/Math/MathML",e):n?vs.createElement(e,{is:n}):vs.createElement(e);return"select"===e&&o&&null!=o.multiple&&r.setAttribute("multiple",o.multiple),r},createText:e=>vs.createTextNode(e),createComment:e=>vs.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>vs.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,o,r,i){const s=n?n.previousSibling:t.lastChild;if(r&&(r===i||r.nextSibling))for(;t.insertBefore(r.cloneNode(!0),n),r!==i&&(r=r.nextSibling););else{ys.innerHTML="svg"===o?`<svg>${e}</svg>`:"mathml"===o?`<math>${e}</math>`:e;const r=ys.content;if("svg"===o||"mathml"===o){const e=r.firstChild;for(;e.firstChild;)r.appendChild(e.firstChild);r.removeChild(e)}t.insertBefore(r,n)}return[s?s.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},_s="transition",ws="animation",xs=Symbol("_vtc"),Ts=(e,{slots:t})=>ms(Zo,function(e){const t={};for(const d in e)d in Ss||(t[d]=e[d]);if(!1===e.css)return t;const{name:n="v",type:o,duration:r,enterFromClass:i=`${n}-enter-from`,enterActiveClass:s=`${n}-enter-active`,enterToClass:a=`${n}-enter-to`,appearFromClass:l=i,appearActiveClass:c=s,appearToClass:u=a,leaveFromClass:f=`${n}-leave-from`,leaveActiveClass:p=`${n}-leave-active`,leaveToClass:h=`${n}-leave-to`}=e,m=function(e){if(null==e)return null;if(w(e))return[Cs(e.enter),Cs(e.leave)];{const t=Cs(e);return[t,t]}}(r),g=m&&m[0],v=m&&m[1],{onBeforeEnter:y,onEnter:b,onEnterCancelled:_,onLeave:x,onLeaveCancelled:T,onBeforeAppear:S=y,onAppear:k=b,onAppearCancelled:E=_}=t,C=(e,t,n)=>{Ls(e,t?u:a),Ls(e,t?c:s),n&&n()},P=(e,t)=>{e._isLeaving=!1,Ls(e,f),Ls(e,h),Ls(e,p),t&&t()},L=e=>(t,n)=>{const r=e?k:b,s=()=>C(t,e,n);ks(r,[t,s]),Os(()=>{Ls(t,e?l:i),Ps(t,e?u:a),Es(r)||As(t,o,g,s)})};return d(t,{onBeforeEnter(e){ks(y,[e]),Ps(e,i),Ps(e,s)},onBeforeAppear(e){ks(S,[e]),Ps(e,l),Ps(e,c)},onEnter:L(!1),onAppear:L(!0),onLeave(e,t){e._isLeaving=!0;const n=()=>P(e,t);Ps(e,f),document.body.offsetHeight,Ps(e,p),Os(()=>{e._isLeaving&&(Ls(e,f),Ps(e,h),Es(x)||As(e,o,v,n))}),ks(x,[e,n])},onEnterCancelled(e){C(e,!1),ks(_,[e])},onAppearCancelled(e){C(e,!0),ks(E,[e])},onLeaveCancelled(e){P(e),ks(T,[e])}})}(e),t);Ts.displayName="Transition";const Ss={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String};Ts.props=d({},Jo,Ss);const ks=(e,t=[])=>{m(e)?e.forEach(e=>e(...t)):e&&e(...t)},Es=e=>!!e&&(m(e)?e.some(e=>e.length>1):e.length>1);function Cs(e){const t=(e=>{const t=b(e)?Number(e):NaN;return isNaN(t)?e:t})(e);return t}function Ps(e,t){t.split(/\s+/).forEach(t=>t&&e.classList.add(t)),(e[xs]||(e[xs]=new Set)).add(t)}function Ls(e,t){t.split(/\s+/).forEach(t=>t&&e.classList.remove(t));const n=e[xs];n&&(n.delete(t),n.size||(e[xs]=void 0))}function Os(e){requestAnimationFrame(()=>{requestAnimationFrame(e)})}let Is=0;function As(e,t,n,o){const r=e._endId=++Is,i=()=>{r===e._endId&&o()};if(n)return setTimeout(i,n);const{type:s,timeout:a,propCount:l}=function(e,t){const n=window.getComputedStyle(e),o=e=>(n[e]||"").split(", "),r=o(`${_s}Delay`),i=o(`${_s}Duration`),s=Ms(r,i),a=o(`${ws}Delay`),l=o(`${ws}Duration`),c=Ms(a,l);let u=null,d=0,f=0;t===_s?s>0&&(u=_s,d=s,f=i.length):t===ws?c>0&&(u=ws,d=c,f=l.length):(d=Math.max(s,c),u=d>0?s>c?_s:ws:null,f=u?u===_s?i.length:l.length:0);const p=u===_s&&/\b(transform|all)(,|$)/.test(o(`${_s}Property`).toString());return{type:u,timeout:d,propCount:f,hasTransform:p}}(e,t);if(!s)return o();const c=s+"end";let u=0;const d=()=>{e.removeEventListener(c,f),i()},f=t=>{t.target===e&&++u>=l&&d()};setTimeout(()=>{u<l&&d()},a+1),e.addEventListener(c,f)}function Ms(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map((t,n)=>Rs(t)+Rs(e[n])))}function Rs(e){return"auto"===e?0:1e3*Number(e.slice(0,-1).replace(",","."))}const $s=Symbol("_vod"),Ns=Symbol("_vsh"),Ds={beforeMount(e,{value:t},{transition:n}){e[$s]="none"===e.style.display?"":e.style.display,n&&t?n.beforeEnter(e):Fs(e,t)},mounted(e,{value:t},{transition:n}){n&&t&&n.enter(e)},updated(e,{value:t,oldValue:n},{transition:o}){!t!=!n&&(o?t?(o.beforeEnter(e),Fs(e,!0),o.enter(e)):o.leave(e,()=>{Fs(e,!1)}):Fs(e,t))},beforeUnmount(e,{value:t}){Fs(e,t)}};function Fs(e,t){e.style.display=t?e[$s]:"none",e[Ns]=!t}const Bs=Symbol(""),js=/(^|;)\s*display\s*:/;const Vs=/\s*!important$/;function Ws(e,t,n){if(m(n))n.forEach(n=>Ws(e,t,n));else if(null==n&&(n=""),n=Zs(n),t.startsWith("--"))e.setProperty(t,n);else{const o=function(e,t){const n=zs[t];if(n)return n;let o=O(t);if("filter"!==o&&o in e)return zs[t]=o;o=M(o);for(let r=0;r<Hs.length;r++){const n=Hs[r]+o;if(n in e)return zs[t]=n}return t}(e,t);Vs.test(n)?e.setProperty(A(o),n.replace(Vs,""),"important"):e[o]=n}}const Hs=["Webkit","Moz","ms"],zs={};const{unit:Us,unitRatio:qs,unitPrecision:Ys}={unit:"rem",unitRatio:10/320,unitPrecision:5},Xs=(Gs=Us,Ks=qs,Js=Ys,e=>e.replace(Ge,(e,t)=>{if(!t)return e;const n=function(e,t){const n=Math.pow(10,t+1),o=Math.floor(e*n);return 10*Math.round(o/10)/n}(parseFloat(t)*Ks,Js);return 0===n?"0":`${n}${Gs}`}));var Gs,Ks,Js;const Zs=e=>b(e)?Xs(e):e,Qs="http://www.w3.org/1999/xlink";const ea=Symbol("_vei");function ta(e,t,n,o,r=null){const i=e[ea]||(e[ea]={}),s=i[t];if(o&&s)s.value=o;else{const[n,a]=function(e){let t;if(na.test(e)){let n;for(t={};n=e.match(na);)e=e.slice(0,e.length-n[0].length),t[n[0].toLowerCase()]=!0}const n=":"===e[2]?e.slice(3):A(e.slice(2));return[n,t]}(t);if(o){const s=i[t]=function(e,t){const n=e=>{if(e._vts){if(e._vts<=n.attached)return}else e._vts=Date.now();const o=t&&t.proxy,r=o&&o.$nne,{value:i}=n;if(r&&m(i)){const n=sa(e,i);for(let o=0;o<n.length;o++){const i=n[o];Zn(i,t,5,i.__wwe?[e]:r(e))}return}Zn(sa(e,n.value),t,5,r&&!i.__wwe?r(e,i,t):[e])};return n.value=e,n.attached=ia(),n}(o,r);!function(e,t,n,o){e.addEventListener(t,n,o)}(e,n,s,a)}else s&&(!function(e,t,n,o){e.removeEventListener(t,n,o)}(e,n,s,a),i[t]=void 0)}}const na=/(?:Once|Passive|Capture)$/;let oa=0;const ra=Promise.resolve(),ia=()=>oa||(ra.then(()=>oa=0),oa=Date.now());function sa(e,t){if(m(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(e=>{const t=t=>!t._stopped&&e&&e(t);return t.__wwe=e.__wwe,t})}return t}const aa=e=>111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123;const la=["ctrl","shift","alt","meta"],ca={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&0!==e.button,middle:e=>"button"in e&&1!==e.button,right:e=>"button"in e&&2!==e.button,exact:(e,t)=>la.some(n=>e[`${n}Key`]&&!t.includes(n))},ua=(e,t)=>{const n=e._withMods||(e._withMods={}),o=t.join(".");return n[o]||(n[o]=(n,...o)=>{for(let e=0;e<t.length;e++){const o=ca[t[e]];if(o&&o(n,t))return}return e(n,...o)})},da=d({patchProp:(e,t,n,o,r,i,s,a,l)=>{if(0===t.indexOf("change:"))return function(e,t,n,o=null){if(!n||!o)return;const r=t.replace("change:",""),{attrs:i}=o,s=i[r],a=(e.__wxsProps||(e.__wxsProps={}))[r];if(a===s)return;e.__wxsProps[r]=s;const l=o.proxy;uo(()=>{n(s,a,l.$gcd(l,!0),l.$gcd(l,!1))})}(e,t,o,s);const d="svg"===r;"class"===t?function(e,t,n){const{__wxsAddClass:o,__wxsRemoveClass:r}=e;r&&r.length&&(t=(t||"").split(/\s+/).filter(e=>-1===r.indexOf(e)).join(" "),r.length=0),o&&o.length&&(t=(t||"")+" "+o.join(" "));const i=e[xs];i&&(t=(t?[t,...i]:[...i]).join(" ")),null==t?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}(e,o,d):"style"===t?function(e,t,n){const o=e.style,r=b(n);let i=!1;if(n&&!r){if(t)if(b(t))for(const e of t.split(";")){const t=e.slice(0,e.indexOf(":")).trim();null==n[t]&&Ws(o,t,"")}else for(const e in t)null==n[e]&&Ws(o,e,"");for(const e in n)"display"===e&&(i=!0),Ws(o,e,n[e])}else if(r){if(t!==n){const e=o[Bs];e&&(n+=";"+e),o.cssText=n,i=js.test(n)}}else t&&e.removeAttribute("style");$s in e&&(e[$s]=i?o.display:"",e[Ns]&&(o.display="none"));const{__wxsStyle:s}=e;if(s)for(const a in s)Ws(o,a,s[a])}(e,n,o):c(t)?u(t)||ta(e,t,0,o,s):("."===t[0]?(t=t.slice(1),1):"^"===t[0]?(t=t.slice(1),0):function(e,t,n,o){if(o)return"innerHTML"===t||"textContent"===t||!!(t in e&&aa(t)&&y(n));if("spellcheck"===t||"draggable"===t||"translate"===t)return!1;if("form"===t)return!1;if("list"===t&&"INPUT"===e.tagName)return!1;if("type"===t&&"TEXTAREA"===e.tagName)return!1;if("width"===t||"height"===t){const t=e.tagName;if("IMG"===t||"VIDEO"===t||"CANVAS"===t||"SOURCE"===t)return!1}if(aa(t)&&b(n))return!1;return t in e}(e,t,o,d))?function(e,t,n,o,r,i,s){if("innerHTML"===t||"textContent"===t)return o&&s(o,r,i),void(e[t]=null==n?"":n);const a=e.tagName;if("value"===t&&"PROGRESS"!==a&&!a.includes("-")){const o=null==n?"":n;return("OPTION"===a?e.getAttribute("value")||"":e.value)===o&&"_value"in e||(e.value=o),null==n&&e.removeAttribute(t),void(e._value=n)}let l=!1;if(""===n||null==n){const o=typeof e[t];"boolean"===o?n=X(n):null==n&&"string"===o?(n="",l=!0):"number"===o&&(n=0,l=!0)}try{e[t]=n}catch(c){}l&&e.removeAttribute(t)}(e,t,o,i,s,a,l):("true-value"===t?e._trueValue=o:"false-value"===t&&(e._falseValue=o),function(e,t,n,o){if(o&&t.startsWith("xlink:"))null==n?e.removeAttributeNS(Qs,t.slice(6,t.length)):e.setAttributeNS(Qs,t,n);else{const o=Y(t);null==n||o&&!X(n)?e.removeAttribute(t):e.setAttribute(t,o?"":n)}}(e,t,o,d))},forcePatchProp:(e,t)=>0===t.indexOf("change:")||("class"===t&&e.__wxsClassChanged?(e.__wxsClassChanged=!1,!0):!("style"!==t||!e.__wxsStyleChanged)&&(e.__wxsStyleChanged=!1,!0))},bs);let fa;const pa=(...e)=>{const t=(fa||(fa=wi(da))).createApp(...e),{mount:n}=t;return t.mount=e=>{const o=function(e){if(b(e)){return document.querySelector(e)}return e}
/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */(e);if(!o)return;const r=t._component;y(r)||r.render||r.template||(r.template=o.innerHTML),o.innerHTML="";const i=n(o,!1,function(e){if(e instanceof SVGElement)return"svg";if("function"==typeof MathMLElement&&e instanceof MathMLElement)return"mathml"}(o));return o instanceof Element&&(o.removeAttribute("v-cloak"),o.setAttribute("data-v-app","")),i},t};const ha="undefined"!=typeof document;function ma(e){return"object"==typeof e||"displayName"in e||"props"in e||"__vccOpts"in e}const ga=Object.assign;function va(e,t){const n={};for(const o in t){const r=t[o];n[o]=ba(r)?r.map(e):e(r)}return n}const ya=()=>{},ba=Array.isArray,_a=/#/g,wa=/&/g,xa=/\//g,Ta=/=/g,Sa=/\?/g,ka=/\+/g,Ea=/%5B/g,Ca=/%5D/g,Pa=/%5E/g,La=/%60/g,Oa=/%7B/g,Ia=/%7C/g,Aa=/%7D/g,Ma=/%20/g;function Ra(e){return encodeURI(""+e).replace(Ia,"|").replace(Ea,"[").replace(Ca,"]")}function $a(e){return Ra(e).replace(ka,"%2B").replace(Ma,"+").replace(_a,"%23").replace(wa,"%26").replace(La,"`").replace(Oa,"{").replace(Aa,"}").replace(Pa,"^")}function Na(e){return $a(e).replace(Ta,"%3D")}function Da(e){return null==e?"":function(e){return Ra(e).replace(_a,"%23").replace(Sa,"%3F")}(e).replace(xa,"%2F")}function Fa(e){try{return decodeURIComponent(""+e)}catch(t){}return""+e}const Ba=/\/$/;function ja(e,t,n="/"){let o,r={},i="",s="";const a=t.indexOf("#");let l=t.indexOf("?");return a<l&&a>=0&&(l=-1),l>-1&&(o=t.slice(0,l),i=t.slice(l+1,a>-1?a:t.length),r=e(i)),a>-1&&(o=o||t.slice(0,a),s=t.slice(a,t.length)),o=function(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),o=e.split("/"),r=o[o.length-1];".."!==r&&"."!==r||o.push("");let i,s,a=n.length-1;for(i=0;i<o.length;i++)if(s=o[i],"."!==s){if(".."!==s)break;a>1&&a--}return n.slice(0,a).join("/")+"/"+o.slice(i).join("/")}(null!=o?o:t,n),{fullPath:o+(i&&"?")+i+s,path:o,query:r,hash:Fa(s)}}function Va(e,t){return t&&e.toLowerCase().startsWith(t.toLowerCase())?e.slice(t.length)||"/":e}function Wa(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Ha(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!za(e[n],t[n]))return!1;return!0}function za(e,t){return ba(e)?Ua(e,t):ba(t)?Ua(t,e):e===t}function Ua(e,t){return ba(t)?e.length===t.length&&e.every((e,n)=>e===t[n]):1===e.length&&e[0]===t}const qa={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var Ya,Xa,Ga,Ka;function Ja(e){if(!e)if(ha){const t=document.querySelector("base");e=(e=t&&t.getAttribute("href")||"/").replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return"/"!==e[0]&&"#"!==e[0]&&(e="/"+e),e.replace(Ba,"")}(Xa=Ya||(Ya={})).pop="pop",Xa.push="push",(Ka=Ga||(Ga={})).back="back",Ka.forward="forward",Ka.unknown="";const Za=/^[^#]+#/;function Qa(e,t){return e.replace(Za,"#")+t}const el=()=>({left:window.scrollX,top:window.scrollY});function tl(e){let t;if("el"in e){const n=e.el,o="string"==typeof n&&n.startsWith("#"),r="string"==typeof n?o?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!r)return;t=function(e,t){const n=document.documentElement.getBoundingClientRect(),o=e.getBoundingClientRect();return{behavior:t.behavior,left:o.left-n.left-(t.left||0),top:o.top-n.top-(t.top||0)}}(r,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(null!=t.left?t.left:window.scrollX,null!=t.top?t.top:window.scrollY)}function nl(e,t){return(history.state?history.state.position-t:-1)+e}const ol=new Map;function rl(e,t){const{pathname:n,search:o,hash:r}=t,i=e.indexOf("#");if(i>-1){let t=r.includes(e.slice(i))?e.slice(i).length:1,n=r.slice(t);return"/"!==n[0]&&(n="/"+n),Va(n,"")}return Va(n,e)+o+r}function il(e,t,n,o=!1,r=!1){return{back:e,current:t,forward:n,replaced:o,position:window.history.length,scroll:r?el():null}}function sl(e){const{history:t,location:n}=window,o={value:rl(e,n)},r={value:t.state};function i(o,i,s){const a=e.indexOf("#"),l=a>-1?(n.host&&document.querySelector("base")?e:e.slice(a))+o:location.protocol+"//"+location.host+e+o;try{t[s?"replaceState":"pushState"](i,"",l),r.value=i}catch(c){n[s?"replace":"assign"](l)}}return r.value||i(o.value,{back:null,current:o.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0),{location:o,state:r,push:function(e,n){const s=ga({},r.value,t.state,{forward:e,scroll:el()});i(s.current,s,!0),i(e,ga({},il(o.value,e,null),{position:s.position+1},n),!1),o.value=e},replace:function(e,n){i(e,ga({},t.state,il(r.value.back,e,r.value.forward,!0),n,{position:r.value.position}),!0),o.value=e}}}function al(e){const t=sl(e=Ja(e)),n=function(e,t,n,o){let r=[],i=[],s=null;const a=({state:i})=>{const a=rl(e,location),l=n.value,c=t.value;let u=0;if(i){if(n.value=a,t.value=i,s&&s===l)return void(s=null);u=c?i.position-c.position:0}else o(a);r.forEach(e=>{e(n.value,l,{delta:u,type:Ya.pop,direction:u?u>0?Ga.forward:Ga.back:Ga.unknown})})};function l(){const{history:e}=window;e.state&&e.replaceState(ga({},e.state,{scroll:el()}),"")}return window.addEventListener("popstate",a),window.addEventListener("beforeunload",l,{passive:!0}),{pauseListeners:function(){s=n.value},listen:function(e){r.push(e);const t=()=>{const t=r.indexOf(e);t>-1&&r.splice(t,1)};return i.push(t),t},destroy:function(){for(const e of i)e();i=[],window.removeEventListener("popstate",a),window.removeEventListener("beforeunload",l)}}}(e,t.state,t.location,t.replace);const o=ga({location:"",base:e,go:function(e,t=!0){t||n.pauseListeners(),history.go(e)},createHref:Qa.bind(null,e)},t,n);return Object.defineProperty(o,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(o,"state",{enumerable:!0,get:()=>t.state.value}),o}function ll(e){return"string"==typeof e||"symbol"==typeof e}const cl=Symbol("");var ul,dl;function fl(e,t){return ga(new Error,{type:e,[cl]:!0},t)}function pl(e,t){return e instanceof Error&&cl in e&&(null==t||!!(e.type&t))}(dl=ul||(ul={}))[dl.aborted=4]="aborted",dl[dl.cancelled=8]="cancelled",dl[dl.duplicated=16]="duplicated";const hl="[^/]+?",ml={sensitive:!1,strict:!1,start:!0,end:!0},gl=/[.+*?^${}()[\]/\\]/g;function vl(e,t){let n=0;for(;n<e.length&&n<t.length;){const o=t[n]-e[n];if(o)return o;n++}return e.length<t.length?1===e.length&&80===e[0]?-1:1:e.length>t.length?1===t.length&&80===t[0]?1:-1:0}function yl(e,t){let n=0;const o=e.score,r=t.score;for(;n<o.length&&n<r.length;){const e=vl(o[n],r[n]);if(e)return e;n++}if(1===Math.abs(r.length-o.length)){if(bl(o))return 1;if(bl(r))return-1}return r.length-o.length}function bl(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const _l={type:0,value:""},wl=/[a-zA-Z0-9_]/;function xl(e,t,n){const o=function(e,t){const n=ga({},ml,t),o=[];let r=n.start?"^":"";const i=[];for(const l of e){const e=l.length?[]:[90];n.strict&&!l.length&&(r+="/");for(let t=0;t<l.length;t++){const o=l[t];let s=40+(n.sensitive?.25:0);if(0===o.type)t||(r+="/"),r+=o.value.replace(gl,"\\$&"),s+=40;else if(1===o.type){const{value:e,repeatable:n,optional:c,regexp:u}=o;i.push({name:e,repeatable:n,optional:c});const d=u||hl;if(d!==hl){s+=10;try{new RegExp(`(${d})`)}catch(a){throw new Error(`Invalid custom RegExp for param "${e}" (${d}): `+a.message)}}let f=n?`((?:${d})(?:/(?:${d}))*)`:`(${d})`;t||(f=c&&l.length<2?`(?:/${f})`:"/"+f),c&&(f+="?"),r+=f,s+=20,c&&(s+=-8),n&&(s+=-20),".*"===d&&(s+=-50)}e.push(s)}o.push(e)}if(n.strict&&n.end){const e=o.length-1;o[e][o[e].length-1]+=.7000000000000001}n.strict||(r+="/?"),n.end?r+="$":n.strict&&!r.endsWith("/")&&(r+="(?:/|$)");const s=new RegExp(r,n.sensitive?"":"i");return{re:s,score:o,keys:i,parse:function(e){const t=e.match(s),n={};if(!t)return null;for(let o=1;o<t.length;o++){const e=t[o]||"",r=i[o-1];n[r.name]=e&&r.repeatable?e.split("/"):e}return n},stringify:function(t){let n="",o=!1;for(const r of e){o&&n.endsWith("/")||(n+="/"),o=!1;for(const e of r)if(0===e.type)n+=e.value;else if(1===e.type){const{value:i,repeatable:s,optional:a}=e,l=i in t?t[i]:"";if(ba(l)&&!s)throw new Error(`Provided param "${i}" is an array but it is not repeatable (* or + modifiers)`);const c=ba(l)?l.join("/"):l;if(!c){if(!a)throw new Error(`Missing required param "${i}"`);r.length<2&&(n.endsWith("/")?n=n.slice(0,-1):o=!0)}n+=c}}return n||"/"}}}(function(e){if(!e)return[[]];if("/"===e)return[[_l]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(e){throw new Error(`ERR (${n})/"${c}": ${e}`)}let n=0,o=n;const r=[];let i;function s(){i&&r.push(i),i=[]}let a,l=0,c="",u="";function d(){c&&(0===n?i.push({type:0,value:c}):1===n||2===n||3===n?(i.length>1&&("*"===a||"+"===a)&&t(`A repeatable param (${c}) must be alone in its segment. eg: '/:ids+.`),i.push({type:1,value:c,regexp:u,repeatable:"*"===a||"+"===a,optional:"*"===a||"?"===a})):t("Invalid state to consume buffer"),c="")}function f(){c+=a}for(;l<e.length;)if(a=e[l++],"\\"!==a||2===n)switch(n){case 0:"/"===a?(c&&d(),s()):":"===a?(d(),n=1):f();break;case 4:f(),n=o;break;case 1:"("===a?n=2:wl.test(a)?f():(d(),n=0,"*"!==a&&"?"!==a&&"+"!==a&&l--);break;case 2:")"===a?"\\"==u[u.length-1]?u=u.slice(0,-1)+a:n=3:u+=a;break;case 3:d(),n=0,"*"!==a&&"?"!==a&&"+"!==a&&l--,u="";break;default:t("Unknown state")}else o=n,n=4;return 2===n&&t(`Unfinished custom RegExp for param "${c}"`),d(),s(),r}(e.path),n),r=ga(o,{record:e,parent:t,children:[],alias:[]});return t&&!r.record.aliasOf==!t.record.aliasOf&&t.children.push(r),r}function Tl(e,t){const n=[],o=new Map;function r(e,n,o){const a=!o,l=kl(e);l.aliasOf=o&&o.record;const c=Ll(t,e),u=[l];if("alias"in e){const t="string"==typeof e.alias?[e.alias]:e.alias;for(const e of t)u.push(kl(ga({},l,{components:o?o.record.components:l.components,path:e,aliasOf:o?o.record:l})))}let d,f;for(const t of u){const{path:u}=t;if(n&&"/"!==u[0]){const e=n.record.path,o="/"===e[e.length-1]?"":"/";t.path=n.record.path+(u&&o+u)}if(d=xl(t,n,c),o?o.alias.push(d):(f=f||d,f!==d&&f.alias.push(d),a&&e.name&&!Cl(d)&&i(e.name)),Ol(d)&&s(d),l.children){const e=l.children;for(let t=0;t<e.length;t++)r(e[t],d,o&&o.children[t])}o=o||d}return f?()=>{i(f)}:ya}function i(e){if(ll(e)){const t=o.get(e);t&&(o.delete(e),n.splice(n.indexOf(t),1),t.children.forEach(i),t.alias.forEach(i))}else{const t=n.indexOf(e);t>-1&&(n.splice(t,1),e.record.name&&o.delete(e.record.name),e.children.forEach(i),e.alias.forEach(i))}}function s(e){const t=function(e,t){let n=0,o=t.length;for(;n!==o;){const r=n+o>>1;yl(e,t[r])<0?o=r:n=r+1}const r=function(e){let t=e;for(;t=t.parent;)if(Ol(t)&&0===yl(e,t))return t;return}(e);r&&(o=t.lastIndexOf(r,o-1));return o}(e,n);n.splice(t,0,e),e.record.name&&!Cl(e)&&o.set(e.record.name,e)}return t=Ll({strict:!1,end:!0,sensitive:!1},t),e.forEach(e=>r(e)),{addRoute:r,resolve:function(e,t){let r,i,s,a={};if("name"in e&&e.name){if(r=o.get(e.name),!r)throw fl(1,{location:e});s=r.record.name,a=ga(Sl(t.params,r.keys.filter(e=>!e.optional).concat(r.parent?r.parent.keys.filter(e=>e.optional):[]).map(e=>e.name)),e.params&&Sl(e.params,r.keys.map(e=>e.name))),i=r.stringify(a)}else if(null!=e.path)i=e.path,r=n.find(e=>e.re.test(i)),r&&(a=r.parse(i),s=r.record.name);else{if(r=t.name?o.get(t.name):n.find(e=>e.re.test(t.path)),!r)throw fl(1,{location:e,currentLocation:t});s=r.record.name,a=ga({},t.params,e.params),i=r.stringify(a)}const l=[];let c=r;for(;c;)l.unshift(c.record),c=c.parent;return{name:s,path:i,params:a,matched:l,meta:Pl(l)}},removeRoute:i,clearRoutes:function(){n.length=0,o.clear()},getRoutes:function(){return n},getRecordMatcher:function(e){return o.get(e)}}}function Sl(e,t){const n={};for(const o of t)o in e&&(n[o]=e[o]);return n}function kl(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:El(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function El(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const o in e.components)t[o]="object"==typeof n?n[o]:n;return t}function Cl(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function Pl(e){return e.reduce((e,t)=>ga(e,t.meta),{})}function Ll(e,t){const n={};for(const o in e)n[o]=o in t?t[o]:e[o];return n}function Ol({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function Il(e){const t={};if(""===e||"?"===e)return t;const n=("?"===e[0]?e.slice(1):e).split("&");for(let o=0;o<n.length;++o){const e=n[o].replace(ka," "),r=e.indexOf("="),i=Fa(r<0?e:e.slice(0,r)),s=r<0?null:Fa(e.slice(r+1));if(i in t){let e=t[i];ba(e)||(e=t[i]=[e]),e.push(s)}else t[i]=s}return t}function Al(e){let t="";for(let n in e){const o=e[n];if(n=Na(n),null==o){void 0!==o&&(t+=(t.length?"&":"")+n);continue}(ba(o)?o.map(e=>e&&$a(e)):[o&&$a(o)]).forEach(e=>{void 0!==e&&(t+=(t.length?"&":"")+n,null!=e&&(t+="="+e))})}return t}function Ml(e){const t={};for(const n in e){const o=e[n];void 0!==o&&(t[n]=ba(o)?o.map(e=>null==e?null:""+e):null==o?o:""+o)}return t}const Rl=Symbol(""),$l=Symbol(""),Nl=Symbol(""),Dl=Symbol(""),Fl=Symbol("");function Bl(){let e=[];return{add:function(t){return e.push(t),()=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)}},list:()=>e.slice(),reset:function(){e=[]}}}function jl(e,t,n,o,r,i=e=>e()){const s=o&&(o.enterCallbacks[r]=o.enterCallbacks[r]||[]);return()=>new Promise((a,l)=>{const c=e=>{var i;!1===e?l(fl(4,{from:n,to:t})):e instanceof Error?l(e):"string"==typeof(i=e)||i&&"object"==typeof i?l(fl(2,{from:t,to:e})):(s&&o.enterCallbacks[r]===s&&"function"==typeof e&&s.push(e),a())},u=i(()=>e.call(o&&o.instances[r],t,n,c));let d=Promise.resolve(u);e.length<3&&(d=d.then(c)),d.catch(e=>l(e))})}function Vl(e,t,n,o,r=e=>e()){const i=[];for(const s of e)for(const e in s.components){let a=s.components[e];if("beforeRouteEnter"===t||s.instances[e])if(ma(a)){const l=(a.__vccOpts||a)[t];l&&i.push(jl(l,n,o,s,e,r))}else{let l=a();i.push(()=>l.then(i=>{if(!i)throw new Error(`Couldn't resolve component "${e}" at "${s.path}"`);const a=(l=i).__esModule||"Module"===l[Symbol.toStringTag]||l.default&&ma(l.default)?i.default:i;var l;s.mods[e]=i,s.components[e]=a;const c=(a.__vccOpts||a)[t];return c&&jl(c,n,o,s,e,r)()}))}}return i}function Wl(e){const t=si(Nl),n=si(Dl),o=ps(()=>{const n=Hn(e.to);return t.resolve(n)}),r=ps(()=>{const{matched:e}=o.value,{length:t}=e,r=e[t-1],i=n.matched;if(!r||!i.length)return-1;const s=i.findIndex(Wa.bind(null,r));if(s>-1)return s;const a=zl(e[t-2]);return t>1&&zl(r)===a&&i[i.length-1].path!==a?i.findIndex(Wa.bind(null,e[t-2])):s}),i=ps(()=>r.value>-1&&function(e,t){for(const n in t){const o=t[n],r=e[n];if("string"==typeof o){if(o!==r)return!1}else if(!ba(r)||r.length!==o.length||o.some((e,t)=>e!==r[t]))return!1}return!0}(n.params,o.value.params)),s=ps(()=>r.value>-1&&r.value===n.matched.length-1&&Ha(n.params,o.value.params));return{route:o,href:ps(()=>o.value.href),isActive:i,isExactActive:s,navigate:function(n={}){if(function(e){if(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)return;if(e.defaultPrevented)return;if(void 0!==e.button&&0!==e.button)return;if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}e.preventDefault&&e.preventDefault();return!0}(n)){const n=t[Hn(e.replace)?"replace":"push"](Hn(e.to)).catch(ya);return e.viewTransition&&"undefined"!=typeof document&&"startViewTransition"in document&&document.startViewTransition(()=>n),n}return Promise.resolve()}}}const Hl=ir({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:Wl,setup(e,{slots:t}){const n=Tn(Wl(e)),{options:o}=si(Nl),r=ps(()=>({[Ul(e.activeClass,o.linkActiveClass,"router-link-active")]:n.isActive,[Ul(e.exactActiveClass,o.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const o=t.default&&(1===(i=t.default(n)).length?i[0]:i);var i;return e.custom?o:ms("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:r.value},o)}}});function zl(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const Ul=(e,t,n)=>null!=e?e:null!=t?t:n;function ql(e,t){if(!e)return null;const n=e(t);return 1===n.length?n[0]:n}const Yl=ir({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const o=si(Fl),r=ps(()=>e.route||o.value),i=si($l,0),s=ps(()=>{let e=Hn(i);const{matched:t}=r.value;let n;for(;(n=t[e])&&!n.components;)e++;return e}),a=ps(()=>r.value.matched[s.value]);ii($l,ps(()=>s.value+1)),ii(Rl,a),ii(Fl,r);const l=Bn();return Vo(()=>[l.value,a.value,e.name],([e,t,n],[o,r,i])=>{t&&(t.instances[n]=e,r&&r!==t&&e&&e===o&&(t.leaveGuards.size||(t.leaveGuards=r.leaveGuards),t.updateGuards.size||(t.updateGuards=r.updateGuards))),!e||!t||r&&Wa(t,r)&&o||(t.enterCallbacks[n]||[]).forEach(t=>t(e))},{flush:"post"}),()=>{const o=r.value,i=e.name,s=a.value,c=s&&s.components[i];if(!c)return ql(n.default,{Component:c,route:o});const u=s.props[i],d=u?!0===u?o.params:"function"==typeof u?u(o):u:null,f=ms(c,ga({},d,t,{onVnodeUnmounted:e=>{e.component.isUnmounted&&(s.instances[i]=null)},ref:l}));return ql(n.default,{Component:f,route:o})||f}}});function Xl(e){const t=Tl(e.routes,e),n=e.parseQuery||Il,o=e.stringifyQuery||Al,r=e.history,i=Bl(),s=Bl(),a=Bl(),l=jn(qa);let c=qa;ha&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const u=va.bind(null,e=>""+e),d=va.bind(null,Da),f=va.bind(null,Fa);function p(e,i){if(i=ga({},i||l.value),"string"==typeof e){const o=ja(n,e,i.path),s=t.resolve({path:o.path},i),a=r.createHref(o.fullPath);return ga(o,s,{params:f(s.params),hash:Fa(o.hash),redirectedFrom:void 0,href:a})}let s;if(null!=e.path)s=ga({},e,{path:ja(n,e.path,i.path).path});else{const t=ga({},e.params);for(const e in t)null==t[e]&&delete t[e];s=ga({},e,{params:d(t)}),i.params=d(i.params)}const a=t.resolve(s,i),c=e.hash||"";a.params=u(f(a.params));const p=function(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}(o,ga({},e,{hash:(h=c,Ra(h).replace(Oa,"{").replace(Aa,"}").replace(Pa,"^")),path:a.path}));var h;const m=r.createHref(p);return ga({fullPath:p,hash:c,query:o===Al?Ml(e.query):e.query||{}},a,{redirectedFrom:void 0,href:m})}function h(e){return"string"==typeof e?ja(n,e,l.value.path):ga({},e)}function m(e,t){if(c!==e)return fl(8,{from:t,to:e})}function g(e){return y(e)}function v(e){const t=e.matched[e.matched.length-1];if(t&&t.redirect){const{redirect:n}=t;let o="function"==typeof n?n(e):n;return"string"==typeof o&&(o=o.includes("?")||o.includes("#")?o=h(o):{path:o},o.params={}),ga({query:e.query,hash:e.hash,params:null!=o.path?{}:e.params},o)}}function y(e,t){const n=c=p(e),r=l.value,i=e.state,s=e.force,a=!0===e.replace,u=v(n);if(u)return y(ga(h(u),{state:"object"==typeof u?ga({},i,u.state):i,force:s,replace:a}),t||n);const d=n;let f;return d.redirectedFrom=t,!s&&function(e,t,n){const o=t.matched.length-1,r=n.matched.length-1;return o>-1&&o===r&&Wa(t.matched[o],n.matched[r])&&Ha(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}(o,r,n)&&(f=fl(16,{to:d,from:r}),I(r,r,!0,!1)),(f?Promise.resolve(f):w(d,r)).catch(e=>pl(e)?pl(e,2)?e:O(e):L(e,d,r)).then(e=>{if(e){if(pl(e,2))return y(ga({replace:a},h(e.to),{state:"object"==typeof e.to?ga({},i,e.to.state):i,force:s}),t||d)}else e=T(d,r,!0,a,i);return x(d,r,e),e})}function b(e,t){const n=m(e,t);return n?Promise.reject(n):Promise.resolve()}function _(e){const t=R.values().next().value;return t&&"function"==typeof t.runWithContext?t.runWithContext(e):e()}function w(e,t){let n;const[o,r,a]=function(e,t){const n=[],o=[],r=[],i=Math.max(t.matched.length,e.matched.length);for(let s=0;s<i;s++){const i=t.matched[s];i&&(e.matched.find(e=>Wa(e,i))?o.push(i):n.push(i));const a=e.matched[s];a&&(t.matched.find(e=>Wa(e,a))||r.push(a))}return[n,o,r]}(e,t);n=Vl(o.reverse(),"beforeRouteLeave",e,t);for(const i of o)i.leaveGuards.forEach(o=>{n.push(jl(o,e,t))});const l=b.bind(null,e,t);return n.push(l),N(n).then(()=>{n=[];for(const o of i.list())n.push(jl(o,e,t));return n.push(l),N(n)}).then(()=>{n=Vl(r,"beforeRouteUpdate",e,t);for(const o of r)o.updateGuards.forEach(o=>{n.push(jl(o,e,t))});return n.push(l),N(n)}).then(()=>{n=[];for(const o of a)if(o.beforeEnter)if(ba(o.beforeEnter))for(const r of o.beforeEnter)n.push(jl(r,e,t));else n.push(jl(o.beforeEnter,e,t));return n.push(l),N(n)}).then(()=>(e.matched.forEach(e=>e.enterCallbacks={}),n=Vl(a,"beforeRouteEnter",e,t,_),n.push(l),N(n))).then(()=>{n=[];for(const o of s.list())n.push(jl(o,e,t));return n.push(l),N(n)}).catch(e=>pl(e,8)?e:Promise.reject(e))}function x(e,t,n){a.list().forEach(o=>_(()=>o(e,t,n)))}function T(e,t,n,o,i){const s=m(e,t);if(s)return s;const a=t===qa,c=ha?history.state:{};n&&(o||a?r.replace(e.fullPath,ga({scroll:a&&c&&c.scroll},i)):r.push(e.fullPath,i)),l.value=e,I(e,t,n,a),O()}let S;function k(){S||(S=r.listen((e,t,n)=>{if(!$.listening)return;const o=p(e),i=v(o);if(i)return void y(ga(i,{replace:!0,force:!0}),o).catch(ya);c=o;const s=l.value;var a,u;ha&&(a=nl(s.fullPath,n.delta),u=el(),ol.set(a,u)),w(o,s).catch(e=>pl(e,12)?e:pl(e,2)?(y(ga(h(e.to),{force:!0}),o).then(e=>{pl(e,20)&&!n.delta&&n.type===Ya.pop&&r.go(-1,!1)}).catch(ya),Promise.reject()):(n.delta&&r.go(-n.delta,!1),L(e,o,s))).then(e=>{(e=e||T(o,s,!1))&&(n.delta&&!pl(e,8)?r.go(-n.delta,!1):n.type===Ya.pop&&pl(e,20)&&r.go(-1,!1)),x(o,s,e)}).catch(ya)}))}let E,C=Bl(),P=Bl();function L(e,t,n){O(e);const o=P.list();return o.length&&o.forEach(o=>o(e,t,n)),Promise.reject(e)}function O(e){return E||(E=!e,k(),C.list().forEach(([t,n])=>e?n(e):t()),C.reset()),e}function I(t,n,o,r){const{scrollBehavior:i}=e;if(!ha||!i)return Promise.resolve();const s=!o&&function(e){const t=ol.get(e);return ol.delete(e),t}(nl(t.fullPath,0))||(r||!o)&&history.state&&history.state.scroll||null;return uo().then(()=>i(t,n,s)).then(e=>e&&tl(e)).catch(e=>L(e,t,n))}const A=e=>r.go(e);let M;const R=new Set,$={currentRoute:l,listening:!0,addRoute:function(e,n){let o,r;return ll(e)?(o=t.getRecordMatcher(e),r=n):r=e,t.addRoute(r,o)},removeRoute:function(e){const n=t.getRecordMatcher(e);n&&t.removeRoute(n)},clearRoutes:t.clearRoutes,hasRoute:function(e){return!!t.getRecordMatcher(e)},getRoutes:function(){return t.getRoutes().map(e=>e.record)},resolve:p,options:e,push:g,replace:function(e){return g(ga(h(e),{replace:!0}))},go:A,back:()=>A(-1),forward:()=>A(1),beforeEach:i.add,beforeResolve:s.add,afterEach:a.add,onError:P.add,isReady:function(){return E&&l.value!==qa?Promise.resolve():new Promise((e,t)=>{C.add([e,t])})},install(e){e.component("RouterLink",Hl),e.component("RouterView",Yl),e.config.globalProperties.$router=this,Object.defineProperty(e.config.globalProperties,"$route",{enumerable:!0,get:()=>Hn(l)}),ha&&!M&&l.value===qa&&(M=!0,g(r.location).catch(e=>{}));const t={};for(const o in qa)Object.defineProperty(t,o,{get:()=>l.value[o],enumerable:!0});e.provide(Nl,this),e.provide(Dl,Sn(t)),e.provide(Fl,l);const n=e.unmount;R.add(e),e.unmount=function(){R.delete(e),R.size<1&&(c=qa,S&&S(),S=null,l.value=qa,M=!1,E=!1),n()}}};function N(e){return e.reduce((e,t)=>e.then(()=>_(t)),Promise.resolve())}return $}function Gl(e){return si(Dl)}const Kl=["{","}"];const Jl=/^(?:\d)+/,Zl=/^(?:\w)+/;const Ql="zh-Hans",ec="zh-Hant",tc="en",nc="fr",oc="es",rc=Object.prototype.hasOwnProperty,ic=(e,t)=>rc.call(e,t),sc=new class{constructor(){this._caches=Object.create(null)}interpolate(e,t,n=Kl){if(!t)return[e];let o=this._caches[e];return o||(o=function(e,[t,n]){const o=[];let r=0,i="";for(;r<e.length;){let s=e[r++];if(s===t){i&&o.push({type:"text",value:i}),i="";let t="";for(s=e[r++];void 0!==s&&s!==n;)t+=s,s=e[r++];const a=s===n,l=Jl.test(t)?"list":a&&Zl.test(t)?"named":"unknown";o.push({value:t,type:l})}else i+=s}return i&&o.push({type:"text",value:i}),o}(e,n),this._caches[e]=o),function(e,t){const n=[];let o=0;const r=Array.isArray(t)?"list":(i=t,null!==i&&"object"==typeof i?"named":"unknown");var i;if("unknown"===r)return n;for(;o<e.length;){const i=e[o];switch(i.type){case"text":n.push(i.value);break;case"list":n.push(t[parseInt(i.value,10)]);break;case"named":"named"===r&&n.push(t[i.value])}o++}return n}(o,t)}};function ac(e,t){if(!e)return;if(e=e.trim().replace(/_/g,"-"),t&&t[e])return e;if("chinese"===(e=e.toLowerCase()))return Ql;if(0===e.indexOf("zh"))return e.indexOf("-hans")>-1?Ql:e.indexOf("-hant")>-1?ec:(n=e,["-tw","-hk","-mo","-cht"].find(e=>-1!==n.indexOf(e))?ec:Ql);var n;let o=[tc,nc,oc];t&&Object.keys(t).length>0&&(o=Object.keys(t));const r=function(e,t){return t.find(t=>0===e.indexOf(t))}(e,o);return r||void 0}class lc{constructor({locale:e,fallbackLocale:t,messages:n,watcher:o,formater:r}){this.locale=tc,this.fallbackLocale=tc,this.message={},this.messages={},this.watchers=[],t&&(this.fallbackLocale=t),this.formater=r||sc,this.messages=n||{},this.setLocale(e||tc),o&&this.watchLocale(o)}setLocale(e){const t=this.locale;this.locale=ac(e,this.messages)||this.fallbackLocale,this.messages[this.locale]||(this.messages[this.locale]={}),this.message=this.messages[this.locale],t!==this.locale&&this.watchers.forEach(e=>{e(this.locale,t)})}getLocale(){return this.locale}watchLocale(e){const t=this.watchers.push(e)-1;return()=>{this.watchers.splice(t,1)}}add(e,t,n=!0){const o=this.messages[e];o?n?Object.assign(o,t):Object.keys(t).forEach(e=>{ic(o,e)||(o[e]=t[e])}):this.messages[e]=t}f(e,t,n){return this.formater.interpolate(e,t,n).join("")}t(e,t,n){let o=this.message;return"string"==typeof t?(t=ac(t,this.messages))&&(o=this.messages[t]):n=t,ic(o,e)?this.formater.interpolate(o[e],n).join(""):e}}function cc(e,t={},n,o){if("string"!=typeof e){const n=[t,e];e=n[0],t=n[1]}"string"!=typeof e&&(e="undefined"!=typeof uni&&Bf?Bf():"undefined"!=typeof global&&global.getLocale?global.getLocale():tc),"string"!=typeof n&&(n="undefined"!=typeof __uniConfig&&__uniConfig.fallbackLocale||tc);const r=new lc({locale:e,fallbackLocale:n,messages:t,watcher:o});let i=(e,t)=>{{let e=!1;i=function(t,n){const o=Gg().$vm;return o&&(o.$locale,e||(e=!0,function(e,t){e.$watchLocale?e.$watchLocale(e=>{t.setLocale(e)}):e.$watch(()=>e.$locale,e=>{t.setLocale(e)})}(o,r))),r.t(t,n)}}return i(e,t)};return{i18n:r,f:(e,t,n)=>r.f(e,t,n),t:(e,t)=>i(e,t),add:(e,t,n=!0)=>r.add(e,t,n),watch:e=>r.watchLocale(e),getLocale:()=>r.getLocale(),setLocale:e=>r.setLocale(e)}}function uc(e,t){return e.indexOf(t[0])>-1}const dc=Fe(()=>"undefined"!=typeof __uniConfig&&__uniConfig.locales&&!!Object.keys(__uniConfig.locales).length);let fc;function pc(e){return uc(e,re)?gc().f(e,function(){const e=Bf(),t=__uniConfig.locales;return t[e]||t[__uniConfig.fallbackLocale]||t.en||{}}(),re):e}function hc(e,t){if(1===t.length){if(e){const n=e=>b(e)&&uc(e,re),o=t[0];let r=[];if(m(e)&&(r=e.filter(e=>n(e[o]))).length)return r;const i=e[t[0]];if(n(i))return e}return}const n=t.shift();return hc(e&&e[n],t)}function mc(e,t){const n=hc(e,t);if(!n)return!1;const o=t[t.length-1];if(m(n))n.forEach(e=>mc(e,[o]));else{let e=n[o];Object.defineProperty(n,o,{get:()=>pc(e),set(t){e=t}})}return!0}function gc(){if(!fc){let e;if(e=navigator.cookieEnabled&&window.localStorage&&localStorage[oe]||__uniConfig.locale||navigator.language,fc=cc(e),dc()){const t=Object.keys(__uniConfig.locales||{});t.length&&t.forEach(e=>fc.add(e,__uniConfig.locales[e])),fc.setLocale(e)}}return fc}function vc(e,t,n){return t.reduce((t,o,r)=>(t[e+o]=n[r],t),{})}const yc=Fe(()=>{const e="uni.async.",t=["error"];gc().add(tc,vc(e,t,["The connection timed out, click the screen to try again."]),!1),gc().add(oc,vc(e,t,["Se agotó el tiempo de conexión, haga clic en la pantalla para volver a intentarlo."]),!1),gc().add(nc,vc(e,t,["La connexion a expiré, cliquez sur l'écran pour réessayer."]),!1),gc().add(Ql,vc(e,t,["连接服务器超时，点击屏幕重试"]),!1),gc().add(ec,vc(e,t,["連接服務器超時，點擊屏幕重試"]),!1)}),bc=Fe(()=>{const e="uni.showToast.",t=["unpaired"];gc().add(tc,vc(e,t,["Please note showToast must be paired with hideToast"]),!1),gc().add(oc,vc(e,t,["Tenga en cuenta que showToast debe estar emparejado con hideToast"]),!1),gc().add(nc,vc(e,t,["Veuillez noter que showToast doit être associé à hideToast"]),!1),gc().add(Ql,vc(e,t,["请注意 showToast 与 hideToast 必须配对使用"]),!1),gc().add(ec,vc(e,t,["請注意 showToast 與 hideToast 必須配對使用"]),!1)}),_c=Fe(()=>{const e="uni.showLoading.",t=["unpaired"];gc().add(tc,vc(e,t,["Please note showLoading must be paired with hideLoading"]),!1),gc().add(oc,vc(e,t,["Tenga en cuenta que showLoading debe estar emparejado con hideLoading"]),!1),gc().add(nc,vc(e,t,["Veuillez noter que showLoading doit être associé à hideLoading"]),!1),gc().add(Ql,vc(e,t,["请注意 showLoading 与 hideLoading 必须配对使用"]),!1),gc().add(ec,vc(e,t,["請注意 showLoading 與 hideLoading 必須配對使用"]),!1)}),wc=Fe(()=>{const e="uni.showModal.",t=["cancel","confirm"];gc().add(tc,vc(e,t,["Cancel","OK"]),!1),gc().add(oc,vc(e,t,["Cancelar","OK"]),!1),gc().add(nc,vc(e,t,["Annuler","OK"]),!1),gc().add(Ql,vc(e,t,["取消","确定"]),!1),gc().add(ec,vc(e,t,["取消","確定"]),!1)}),xc=Fe(()=>{const e="uni.chooseFile.",t=["notUserActivation"];gc().add(tc,vc(e,t,["File chooser dialog can only be shown with a user activation"]),!1),gc().add(oc,vc(e,t,["El cuadro de diálogo del selector de archivos solo se puede mostrar con la activación del usuario"]),!1),gc().add(nc,vc(e,t,["La boîte de dialogue du sélecteur de fichier ne peut être affichée qu'avec une activation par l'utilisateur"]),!1),gc().add(Ql,vc(e,t,["文件选择器对话框只能在由用户激活时显示"]),!1),gc().add(ec,vc(e,t,["文件選擇器對話框只能在由用戶激活時顯示"]),!1)}),Tc=Fe(()=>{const e="uni.setClipboardData.",t=["success","fail"];gc().add(tc,vc(e,t,["Content copied","Copy failed, please copy manually"]),!1),gc().add(oc,vc(e,t,["Contenido copiado","Error al copiar, copie manualmente"]),!1),gc().add(nc,vc(e,t,["Contenu copié","Échec de la copie, copiez manuellement"]),!1),gc().add(Ql,vc(e,t,["内容已复制","复制失败，请手动复制"]),!1),gc().add(ec,vc(e,t,["內容已復制","復制失敗，請手動復製"]),!1)}),Sc=Fe(()=>{const e="uni.video.",t=["danmu","volume"];gc().add(tc,vc(e,t,["Danmu","Volume"]),!1),gc().add(oc,vc(e,t,["Danmu","Volumen"]),!1),gc().add(nc,vc(e,t,["Danmu","Le Volume"]),!1),gc().add(Ql,vc(e,t,["弹幕","音量"]),!1),gc().add(ec,vc(e,t,["彈幕","音量"]),!1)});function kc(e){const t=new ct;return{on:(e,n)=>t.on(e,n),once:(e,n)=>t.once(e,n),off:(e,n)=>t.off(e,n),emit:(e,...n)=>t.emit(e,...n),subscribe(n,o,r=!1){t[r?"once":"on"](`${e}.${n}`,o)},unsubscribe(n,o){t.off(`${e}.${n}`,o)},subscribeHandler(n,o,r){t.emit(`${e}.${n}`,o,r)}}}const Ec="invokeViewApi",Cc="invokeServiceApi";let Pc=1;const Lc=Object.create(null);function Oc(e,t){return e+"."+t}function Ic(e,t,n){t=Oc(e,t),Lc[t]||(Lc[t]=n)}function Ac({id:e,name:t,args:n},o){t=Oc(o,t);const r=t=>{e&&rb.publishHandler(Ec+"."+e,t)},i=Lc[t];i?i(n,r):r({})}const Mc=d(kc("service"),{invokeServiceMethod:(e,t,n)=>{const{subscribe:o,publishHandler:r}=rb,i=n?Pc++:0;n&&o(Cc+"."+i,n,!0),r(Cc,{id:i,name:e,args:t})}}),Rc=Ke(!0);let $c;function Nc(){$c&&(clearTimeout($c),$c=null)}let Dc=0,Fc=0;function Bc(e){if(Nc(),1!==e.touches.length)return;const{pageX:t,pageY:n}=e.touches[0];Dc=t,Fc=n,$c=setTimeout(function(){const t=new CustomEvent("longpress",{bubbles:!0,cancelable:!0,target:e.target,currentTarget:e.currentTarget});t.touches=e.touches,t.changedTouches=e.changedTouches,e.target.dispatchEvent(t)},350)}function jc(e){if(!$c)return;if(1!==e.touches.length)return Nc();const{pageX:t,pageY:n}=e.touches[0];return Math.abs(t-Dc)>10||Math.abs(n-Fc)>10?Nc():void 0}function Vc(e,t){const n=Number(e);return isNaN(n)?t:n}function Wc(){const e=__uniConfig.globalStyle||{},t=Vc(e.rpxCalcMaxDeviceWidth,960),n=Vc(e.rpxCalcBaseDeviceWidth,375);function o(){let e=function(){const e=/^Apple/.test(navigator.vendor)&&"number"==typeof window.orientation,t=e&&90===Math.abs(window.orientation);var n=e?Math[t?"max":"min"](screen.width,screen.height):screen.width;return Math.min(window.innerWidth,document.documentElement.clientWidth,n)||n}();e=e<=t?e:n,document.documentElement.style.fontSize=e/23.4375+"px"}o(),document.addEventListener("DOMContentLoaded",o),window.addEventListener("load",o),window.addEventListener("resize",o)}function Hc(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var zc,Uc,qc=["top","left","right","bottom"],Yc={};function Xc(){return Uc="CSS"in window&&"function"==typeof CSS.supports?CSS.supports("top: env(safe-area-inset-top)")?"env":CSS.supports("top: constant(safe-area-inset-top)")?"constant":"":""}function Gc(){if(Uc="string"==typeof Uc?Uc:Xc()){var e=[],t=!1;try{var n=Object.defineProperty({},"passive",{get:function(){t={passive:!0}}});window.addEventListener("test",null,n)}catch(a){}var o=document.createElement("div");r(o,{position:"absolute",left:"0",top:"0",width:"0",height:"0",zIndex:"-1",overflow:"hidden",visibility:"hidden"}),qc.forEach(function(e){s(o,e)}),document.body.appendChild(o),i(),zc=!0}else qc.forEach(function(e){Yc[e]=0});function r(e,t){var n=e.style;Object.keys(t).forEach(function(e){var o=t[e];n[e]=o})}function i(t){t?e.push(t):e.forEach(function(e){e()})}function s(e,n){var o=document.createElement("div"),s=document.createElement("div"),a=document.createElement("div"),l=document.createElement("div"),c={position:"absolute",width:"100px",height:"200px",boxSizing:"border-box",overflow:"hidden",paddingBottom:Uc+"(safe-area-inset-"+n+")"};r(o,c),r(s,c),r(a,{transition:"0s",animation:"none",width:"400px",height:"400px"}),r(l,{transition:"0s",animation:"none",width:"250%",height:"250%"}),o.appendChild(a),s.appendChild(l),e.appendChild(o),e.appendChild(s),i(function(){o.scrollTop=s.scrollTop=1e4;var e=o.scrollTop,r=s.scrollTop;function i(){this.scrollTop!==(this===o?e:r)&&(o.scrollTop=s.scrollTop=1e4,e=o.scrollTop,r=s.scrollTop,function(e){Jc.length||setTimeout(function(){var e={};Jc.forEach(function(t){e[t]=Yc[t]}),Jc.length=0,Zc.forEach(function(t){t(e)})},0);Jc.push(e)}(n))}o.addEventListener("scroll",i,t),s.addEventListener("scroll",i,t)});var u=getComputedStyle(o);Object.defineProperty(Yc,n,{configurable:!0,get:function(){return parseFloat(u.paddingBottom)}})}}function Kc(e){return zc||Gc(),Yc[e]}var Jc=[];var Zc=[];const Qc=Hc({get support(){return 0!=("string"==typeof Uc?Uc:Xc()).length},get top(){return Kc("top")},get left(){return Kc("left")},get right(){return Kc("right")},get bottom(){return Kc("bottom")},onChange:function(e){Xc()&&(zc||Gc(),"function"==typeof e&&Zc.push(e))},offChange:function(e){var t=Zc.indexOf(e);t>=0&&Zc.splice(t,1)}}),eu=ua(()=>{},["prevent"]),tu=ua(e=>{},["stop"]);function nu(e,t){return parseInt((e.getPropertyValue(t).match(/\d+/)||["0"])[0])}function ou(){const e=nu(document.documentElement.style,"--window-top");return e?e+Qc.top:0}function ru(){const e=document.documentElement.style,t=ou(),n=nu(e,"--window-bottom"),o=nu(e,"--window-left"),r=nu(e,"--window-right"),i=nu(e,"--top-window-height");return{top:t,bottom:n?n+Qc.bottom:0,left:o?o+Qc.left:0,right:r?r+Qc.right:0,topWindowHeight:i||0}}function iu(e){const t=document.documentElement.style;Object.keys(e).forEach(n=>{t.setProperty(n,e[n])})}function su(e){return iu(e)}function au(e){return Symbol(e)}function lu(e){return-1!==(e+="").indexOf("rpx")||-1!==e.indexOf("upx")}function cu(e,t=!1){if(t)return function(e){if(!lu(e))return e;return e.replace(/(\d+(\.\d+)?)[ru]px/g,(e,t)=>sf(parseFloat(t))+"px")}(e);if(b(e)){const t=parseInt(e)||0;return lu(e)?sf(t):t}return e}function uu(e){return e.$page}function du(e){return 0===e.tagName.indexOf("UNI-")}const fu="M1.952 18.080q-0.32-0.352-0.416-0.88t0.128-0.976l0.16-0.352q0.224-0.416 0.64-0.528t0.8 0.176l6.496 4.704q0.384 0.288 0.912 0.272t0.88-0.336l17.312-14.272q0.352-0.288 0.848-0.256t0.848 0.352l-0.416-0.416q0.32 0.352 0.32 0.816t-0.32 0.816l-18.656 18.912q-0.32 0.352-0.8 0.352t-0.8-0.32l-7.936-8.064z",pu="M15.808 0.16q-4.224 0-7.872 2.176-3.552 2.112-5.632 5.728-2.144 3.744-2.144 8.128 0 4.192 2.144 7.872 2.112 3.52 5.632 5.632 3.68 2.144 7.872 2.144 4.384 0 8.128-2.144 3.616-2.080 5.728-5.632 2.176-3.648 2.176-7.872 0-4.384-2.176-8.128-2.112-3.616-5.728-5.728-3.744-2.176-8.128-2.176zM15.136 8.672h1.728q0.128 0 0.224 0.096t0.096 0.256l-0.384 10.24q0 0.064-0.048 0.112t-0.112 0.048h-1.248q-0.096 0-0.144-0.048t-0.048-0.112l-0.384-10.24q0-0.16 0.096-0.256t0.224-0.096zM16 23.328q-0.48 0-0.832-0.352t-0.352-0.848 0.352-0.848 0.832-0.352 0.832 0.352 0.352 0.848-0.352 0.848-0.832 0.352z";function hu(e,t="#000",n=27){return zi("svg",{width:n,height:n,viewBox:"0 0 32 32"},[zi("path",{d:e,fill:t},null,8,["d","fill"])],8,["width","height"])}function mu(){{const{$pageInstance:e}=ns();return e&&Tu(e.proxy)}}function gu(e){const t=Ve(e);if(t.$page)return Tu(t);if(!t.$)return;{const{$pageInstance:e}=t.$;if(e)return Tu(e.proxy)}const n=t.$.root.proxy;return n&&n.$page?Tu(n):void 0}function vu(){const e=nh(),t=e.length;if(t)return e[t-1]}function yu(){var e;const t=null==(e=vu())?void 0:e.$page;if(t)return t.meta}function bu(){const e=yu();return e?e.id:-1}function _u(){const e=vu();if(e)return e.$vm}const wu=["navigationBar","pullToRefresh"];function xu(e,t){const n=JSON.parse(JSON.stringify(__uniConfig.globalStyle||{})),o=d({id:t},n,e);wu.forEach(t=>{o[t]=d({},n[t],e[t])});const{navigationBar:r}=o;return r.titleText&&r.titleImage&&(r.titleText=""),o}function Tu(e){var t,n;return(null==(t=e.$page)?void 0:t.id)||(null==(n=e.$basePage)?void 0:n.id)}function Su(e,t,n){if(b(e))n=t,t=e,e=_u();else if("number"==typeof e){const t=nh().find(t=>uu(t).id===e);e=t?t.$vm:_u()}if(!e)return;const o=e.$[t];return o&&((e,t)=>{let n;for(let o=0;o<e.length;o++)n=e[o](t);return n})(o,n)}function ku(e){e.preventDefault()}let Eu,Cu=0;function Pu({onPageScroll:e,onReachBottom:t,onReachBottomDistance:n}){let o=!1,r=!1,i=!0;const s=()=>{function s(){if((()=>{const{scrollHeight:e}=document.documentElement,t=window.innerHeight,o=window.scrollY,i=o>0&&e>t&&o+t+n>=e,s=Math.abs(e-Cu)>n;return!i||r&&!s?(!i&&r&&(r=!1),!1):(Cu=e,r=!0,!0)})())return t&&t(),i=!1,setTimeout(function(){i=!0},350),!0}e&&e(window.pageYOffset),t&&i&&(s()||(Eu=setTimeout(s,300))),o=!1};return function(){clearTimeout(Eu),o||requestAnimationFrame(s),o=!0}}function Lu(e,t){if(0===t.indexOf("/"))return t;if(0===t.indexOf("./"))return Lu(e,t.slice(2));const n=t.split("/"),o=n.length;let r=0;for(;r<o&&".."===n[r];r++);n.splice(0,r),t=n.join("/");const i=e.length>0?e.split("/"):[];return i.splice(i.length-r-1,r+1),Ne(i.concat(n).join("/"))}function Ou(e,t=!1){return t?__uniRoutes.find(t=>t.path===e||t.alias===e):__uniRoutes.find(t=>t.path===e)}function Iu(){Wc(),Ye(du),window.addEventListener("touchstart",Bc,Rc),window.addEventListener("touchmove",jc,Rc),window.addEventListener("touchend",Nc,Rc),window.addEventListener("touchcancel",Nc,Rc)}class Au{constructor(e){this.$bindClass=!1,this.$bindStyle=!1,this.$vm=e,this.$el=function(e,t=!1){const{vnode:n}=e;if(He(n.el))return t?n.el?[n.el]:[]:n.el;const{subTree:o}=e;if(16&o.shapeFlag){const e=o.children.filter(e=>e.el&&He(e.el));if(e.length>0)return t?e.map(e=>e.el):e[0].el}return t?n.el?[n.el]:[]:n.el}(e.$),this.$el.getAttribute&&(this.$bindClass=!!this.$el.getAttribute("class"),this.$bindStyle=!!this.$el.getAttribute("style"))}selectComponent(e){if(!this.$el||!e)return;const t=Nu(this.$el.querySelector(e));return t?Mu(t,!1):void 0}selectAllComponents(e){if(!this.$el||!e)return[];const t=[],n=this.$el.querySelectorAll(e);for(let o=0;o<n.length;o++){const e=Nu(n[o]);e&&t.push(Mu(e,!1))}return t}forceUpdate(e){"class"===e?this.$bindClass?(this.$el.__wxsClassChanged=!0,this.$vm.$forceUpdate()):this.updateWxsClass():"style"===e&&(this.$bindStyle?(this.$el.__wxsStyleChanged=!0,this.$vm.$forceUpdate()):this.updateWxsStyle())}updateWxsClass(){const{__wxsAddClass:e}=this.$el;e.length&&(this.$el.className=e.join(" "))}updateWxsStyle(){const{__wxsStyle:e}=this.$el;e&&this.$el.setAttribute("style",function(e){let t="";if(!e||b(e))return t;for(const n in e){const o=e[n],r=n.startsWith("--")?n:A(n);(b(o)||"number"==typeof o)&&(t+=`${r}:${o};`)}return t}(e))}setStyle(e){return this.$el&&e?(b(e)&&(e=U(e)),k(e)&&(this.$el.__wxsStyle=e,this.forceUpdate("style")),this):this}addClass(e){if(!this.$el||!e)return this;const t=this.$el.__wxsAddClass||(this.$el.__wxsAddClass=[]);return-1===t.indexOf(e)&&(t.push(e),this.forceUpdate("class")),this}removeClass(e){if(!this.$el||!e)return this;const{__wxsAddClass:t}=this.$el;if(t){const n=t.indexOf(e);n>-1&&t.splice(n,1)}const n=this.$el.__wxsRemoveClass||(this.$el.__wxsRemoveClass=[]);return-1===n.indexOf(e)&&(n.push(e),this.forceUpdate("class")),this}hasClass(e){return this.$el&&this.$el.classList.contains(e)}getDataset(){return this.$el&&this.$el.dataset}callMethod(e,t={}){const n=this.$vm[e];y(n)?n(JSON.parse(JSON.stringify(t))):this.$vm.ownerId&&rb.publishHandler("onWxsInvokeCallMethod",{nodeId:this.$el.__id,ownerId:this.$vm.ownerId,method:e,args:t})}requestAnimationFrame(e){return window.requestAnimationFrame(e)}getState(){return this.$el&&(this.$el.__wxsState||(this.$el.__wxsState={}))}triggerEvent(e,t={}){return this.$vm.$emit(e,t),this}getComputedStyle(e){if(this.$el){const t=window.getComputedStyle(this.$el);return e&&e.length?e.reduce((e,n)=>(e[n]=t[n],e),{}):t}return{}}setTimeout(e,t){return window.setTimeout(e,t)}clearTimeout(e){return window.clearTimeout(e)}getBoundingClientRect(){return this.$el.getBoundingClientRect()}}function Mu(e,t=!0){if(t&&e&&(e=We(e.$)),e&&e.$el)return e.$el.__wxsComponentDescriptor||(e.$el.__wxsComponentDescriptor=new Au(e)),e.$el.__wxsComponentDescriptor}function Ru(e,t){return Mu(e,t)}function $u(e,t,n,o=!0){if(t){e.__instance||(e.__instance=!0,Object.defineProperty(e,"instance",{get:()=>Ru(n.proxy,!1)}));const r=function(e,t,n=!0){if(!t)return!1;if(n&&e.length<2)return!1;const o=We(t);if(!o)return!1;const r=o.$.type;return!(!r.$wxs&&!r.$renderjs)&&o}(t,n,o);if(r)return[e,Ru(r,!1)]}}function Nu(e){if(e)return e.__vueParentComponent&&e.__vueParentComponent.proxy}function Du(e,t=!1){const{type:n,timeStamp:o,target:r,currentTarget:i}=e;let s,a;s=Je(t?r:function(e){for(;!du(e);)e=e.parentElement;return e}(r)),a=Je(i);const l={type:n,timeStamp:o,target:s,detail:{},currentTarget:a};return e instanceof CustomEvent&&k(e.detail)&&(l.detail=e.detail),e._stopped&&(l._stopped=!0),e.type.startsWith("touch")&&(l.touches=e.touches,l.changedTouches=e.changedTouches),function(e,t){d(e,{preventDefault:()=>t.preventDefault(),stopPropagation:()=>t.stopPropagation()})}(l,e),l}function Fu(e,t){return{force:1,identifier:0,clientX:e.clientX,clientY:e.clientY-t,pageX:e.pageX,pageY:e.pageY-t}}function Bu(e,t){const n=[];for(let o=0;o<e.length;o++){const{identifier:r,pageX:i,pageY:s,clientX:a,clientY:l,force:c}=e[o];n.push({identifier:r,pageX:i,pageY:s-t,clientX:a,clientY:l-t,force:c||0})}return n}const ju=Object.defineProperty({__proto__:null,$nne:function(e,t,n){const{currentTarget:o}=e;if(!(e instanceof Event&&o instanceof HTMLElement))return[e];const r=!du(o);if(r)return $u(e,t,n,!1)||[e];const i=Du(e,r);if("click"===e.type)!function(e,t){const{x:n,y:o}=t,r=ou();e.detail={x:n,y:o-r},e.touches=e.changedTouches=[Fu(t,r)]}(i,e);else if((e=>0===e.type.indexOf("mouse")||["contextmenu"].includes(e.type))(e))!function(e,t){const n=ou();e.pageX=t.pageX,e.pageY=t.pageY-n,e.clientX=t.clientX,e.clientY=t.clientY-n,e.touches=e.changedTouches=[Fu(t,n)]}(i,e);else if((e=>"undefined"!=typeof TouchEvent&&e instanceof TouchEvent||0===e.type.indexOf("touch")||["longpress"].indexOf(e.type)>=0)(e)){const t=ou();i.touches=Bu(e.touches,t),i.changedTouches=Bu(e.changedTouches,t)}else if((e=>!e.type.indexOf("key")&&e instanceof KeyboardEvent)(e)){["key","code"].forEach(t=>{Object.defineProperty(i,t,{get:()=>e[t]})})}return $u(i,t,n)||[i]},createNativeEvent:Du},Symbol.toStringTag,{value:"Module"});function Vu(e){!function(e){const t=e.globalProperties;d(t,ju),t.$gcd=Ru}(e._context.config)}let Wu=1;function Hu(e){return(e||bu())+"."+Ec}const zu=d(kc("view"),{invokeOnCallback:(e,t)=>ib.emit("api."+e,t),invokeViewMethod:(e,t,n,o)=>{const{subscribe:r,publishHandler:i}=ib,s=o?Wu++:0;o&&r(Ec+"."+s,o,!0),i(Hu(n),{id:s,name:e,args:t},n)},invokeViewMethodKeepAlive:(e,t,n,o)=>{const{subscribe:r,unsubscribe:i,publishHandler:s}=ib,a=Wu++,l=Ec+"."+a;return r(l,n),s(Hu(o),{id:a,name:e,args:t},o),()=>{i(l)}}});function Uu(e){Su(vu(),ye,e),ib.invokeOnCallback("onWindowResize",e)}function qu(e){const t=vu();Su(Gg(),ae,e),Su(t,ae)}function Yu(){Su(Gg(),le),Su(vu(),le)}const Xu=[_e,xe];function Gu(){Xu.forEach(e=>ib.subscribe(e,function(e){return(t,n)=>{Su(parseInt(n),e,t)}}(e)))}function Ku(){!function(){const{on:e}=ib;e(ye,Uu),e(Me,qu),e(Re,Yu)}(),Gu()}function Ju(){if(this.$route){const e=this.$route.meta;return e.eventChannel||(e.eventChannel=new ot(this.$page.id)),e.eventChannel}}function Zu(e){e._context.config.globalProperties.getOpenerEventChannel=Ju}function Qu(){return{path:"",query:{},scene:1001,referrerInfo:{appId:"",extraData:{}}}}function ed(e){return/^-?\d+[ur]px$/i.test(e)?e.replace(/(^-?\d+)[ur]px$/i,(e,t)=>`${sf(parseFloat(t))}px`):/^-?[\d\.]+$/.test(e)?`${e}px`:e||""}function td(e){const t=e.animation;if(!t||!t.actions||!t.actions.length)return;let n=0;const o=t.actions,r=t.actions.length;function i(){const t=o[n],s=t.option.transition,a=function(e){const t=["matrix","matrix3d","scale","scale3d","rotate3d","skew","translate","translate3d"],n=["scaleX","scaleY","scaleZ","rotate","rotateX","rotateY","rotateZ","skewX","skewY","translateX","translateY","translateZ"],o=["opacity","background-color"],r=["width","height","left","right","top","bottom"],i=e.animates,s=e.option,a=s.transition,l={},c=[];return i.forEach(e=>{let i=e.type,s=[...e.args];if(t.concat(n).includes(i))i.startsWith("rotate")||i.startsWith("skew")?s=s.map(e=>parseFloat(e)+"deg"):i.startsWith("translate")&&(s=s.map(ed)),n.indexOf(i)>=0&&(s.length=1),c.push(`${i}(${s.join(",")})`);else if(o.concat(r).includes(s[0])){i=s[0];const e=s[1];l[i]=r.includes(i)?ed(e):e}}),l.transform=l.webkitTransform=c.join(" "),l.transition=l.webkitTransition=Object.keys(l).map(e=>`${function(e){return e.replace(/[A-Z]/g,e=>`-${e.toLowerCase()}`).replace("webkit","-webkit")}(e)} ${a.duration}ms ${a.timingFunction} ${a.delay}ms`).join(","),l.transformOrigin=l.webkitTransformOrigin=s.transformOrigin,l}(t);Object.keys(a).forEach(t=>{e.$el.style[t]=a[t]}),n+=1,n<r&&setTimeout(i,s.duration+s.delay)}setTimeout(()=>{i()},0)}const nd={props:["animation"],watch:{animation:{deep:!0,handler(){td(this)}}},mounted(){td(this)}},od=e=>{e.__reserved=!0;const{props:t,mixins:n}=e;return t&&t.animation||(n||(e.mixins=[])).push(nd),rd(e)},rd=e=>(e.__reserved=!0,e.compatConfig={MODE:3},ir(e));function id(e){return e.__wwe=!0,e}function sd(e,t){return(n,o,r)=>{e.value&&t(n,function(e,t,n,o){let r;return r=Je(n),{type:t.__evName||o.type||e,timeStamp:t.timeStamp||0,target:r,currentTarget:r,detail:o}}(n,o,e.value,r||{}))}}const ad={hoverClass:{type:String,default:"none"},hoverStopPropagation:{type:Boolean,default:!1},hoverStartTime:{type:[Number,String],default:50},hoverStayTime:{type:[Number,String],default:400}};function ld(e){const t=Bn(!1);let n,o,r=!1;function i(){requestAnimationFrame(()=>{clearTimeout(o),o=setTimeout(()=>{t.value=!1},parseInt(e.hoverStayTime))})}function s(o){o._hoverPropagationStopped||e.hoverClass&&"none"!==e.hoverClass&&!e.disabled&&(e.hoverStopPropagation&&(o._hoverPropagationStopped=!0),r=!0,n=setTimeout(()=>{t.value=!0,r||i()},parseInt(e.hoverStartTime)))}function a(){r=!1,t.value&&i()}function l(){a(),window.removeEventListener("mouseup",l)}return{hovering:t,binding:{onTouchstartPassive:id(function(e){e.touches.length>1||s(e)}),onMousedown:id(function(e){r||(s(e),window.addEventListener("mouseup",l))}),onTouchend:id(function(){a()}),onMouseup:id(function(){r&&l()}),onTouchcancel:id(function(){r=!1,t.value=!1,clearTimeout(n)})}}}function cd(e,t){return b(t)&&(t=[t]),t.reduce((t,n)=>(e[n]&&(t[n]=!0),t),Object.create(null))}const ud=au("uf"),dd=au("ul");function fd(e,t,n){const o=mu();n&&!e||k(t)&&Object.keys(t).forEach(r=>{n?0!==r.indexOf("@")&&0!==r.indexOf("uni-")&&rb.on(`uni-${r}-${o}-${e}`,t[r]):0===r.indexOf("uni-")?rb.on(r,t[r]):e&&rb.on(`uni-${r}-${o}-${e}`,t[r])})}function pd(e,t,n){const o=mu();n&&!e||k(t)&&Object.keys(t).forEach(r=>{n?0!==r.indexOf("@")&&0!==r.indexOf("uni-")&&rb.off(`uni-${r}-${o}-${e}`,t[r]):0===r.indexOf("uni-")?rb.off(r,t[r]):e&&rb.off(`uni-${r}-${o}-${e}`,t[r])})}const hd=od({name:"Button",props:{id:{type:String,default:""},hoverClass:{type:String,default:"button-hover"},hoverStartTime:{type:[Number,String],default:20},hoverStayTime:{type:[Number,String],default:70},hoverStopPropagation:{type:Boolean,default:!1},disabled:{type:[Boolean,String],default:!1},formType:{type:String,default:""},openType:{type:String,default:""},loading:{type:[Boolean,String],default:!1},plain:{type:[Boolean,String],default:!1}},setup(e,{slots:t}){const n=Bn(null),o=si(ud,!1),{hovering:r,binding:i}=ld(e),s=id((t,r)=>{if(e.disabled)return t.stopImmediatePropagation();r&&n.value.click();const i=e.formType;if(i){if(!o)return;"submit"===i?o.submit(t):"reset"===i&&o.reset(t)}else;}),a=si(dd,!1);return a&&(a.addHandler(s),Pr(()=>{a.removeHandler(s)})),function(e,t){fd(e.id,t),Vo(()=>e.id,(e,n)=>{pd(n,t,!0),fd(e,t,!0)}),Lr(()=>{pd(e.id,t)})}(e,{"label-click":s}),()=>{const o=e.hoverClass,a=cd(e,"disabled"),l=cd(e,"loading"),c=cd(e,"plain"),u=o&&"none"!==o;return zi("uni-button",Ji({ref:n,onClick:s,id:e.id,class:u&&r.value?o:""},u&&i,a,l,c),[t.default&&t.default()],16,["onClick","id"])}}}),md=au("upm");function gd(){return si(md)}function vd(e){const t=function(e){return Tn(function(e){if(history.state){const t=history.state.__type__;"redirectTo"!==t&&"reLaunch"!==t||0!==nh().length||(e.isEntry=!0,e.isQuit=!0)}return e}(JSON.parse(JSON.stringify(xu(Gl().meta,e)))))}(e);return ii(md,t),t}function yd(){return Gl()}function bd(){return history.state&&history.state.__id__||1}const _d=["original","compressed"],wd=["album","camera"],xd=["GET","OPTIONS","HEAD","POST","PUT","DELETE","TRACE","CONNECT","PATCH"];function Td(e,t){return e&&-1!==t.indexOf(e)?e:t[0]}function Sd(e,t){return!m(e)||0===e.length||e.find(e=>-1===t.indexOf(e))?t:e}function kd(e){return function(){try{return e.apply(e,arguments)}catch(t){}}}let Ed=1;const Cd={};function Pd(e,t,n){if("number"==typeof e){const o=Cd[e];if(o)return o.keepAlive||delete Cd[e],o.callback(t,n)}return t}const Ld="success",Od="fail",Id="complete";function Ad(e,t={},{beforeAll:n,beforeSuccess:o}={}){k(t)||(t={});const{success:r,fail:i,complete:s}=function(e){const t={};for(const n in e){const o=e[n];y(o)&&(t[n]=kd(o),delete e[n])}return t}(t),a=y(r),l=y(i),c=y(s),u=Ed++;return function(e,t,n,o=!1){Cd[e]={name:t,keepAlive:o,callback:n}}(u,e,u=>{(u=u||{}).errMsg=function(e,t){return e&&-1!==e.indexOf(":fail")?t+e.substring(e.indexOf(":fail")):t+":ok"}(u.errMsg,e),y(n)&&n(u),u.errMsg===e+":ok"?(y(o)&&o(u,t),a&&r(u)):l&&i(u),c&&s(u)}),u}const Md="success",Rd="fail",$d="complete",Nd={},Dd={};function Fd(e,t){return function(n){return e(n,t)||n}}function Bd(e,t,n){let o=!1;for(let r=0;r<e.length;r++){const i=e[r];if(o)o=Promise.resolve(Fd(i,n));else{const e=i(t,n);if(x(e)&&(o=Promise.resolve(e)),!1===e)return{then(){},catch(){}}}}return o||{then:e=>e(t),catch(){}}}function jd(e,t={}){return[Md,Rd,$d].forEach(n=>{const o=e[n];if(!m(o))return;const r=t[n];t[n]=function(e){Bd(o,e,t).then(e=>y(r)&&r(e)||e)}}),t}function Vd(e,t){const n=[];m(Nd.returnValue)&&n.push(...Nd.returnValue);const o=Dd[e];return o&&m(o.returnValue)&&n.push(...o.returnValue),n.forEach(e=>{t=e(t)||t}),t}function Wd(e){const t=Object.create(null);Object.keys(Nd).forEach(e=>{"returnValue"!==e&&(t[e]=Nd[e].slice())});const n=Dd[e];return n&&Object.keys(n).forEach(e=>{"returnValue"!==e&&(t[e]=(t[e]||[]).concat(n[e]))}),t}function Hd(e,t,n,o){const r=Wd(e);if(r&&Object.keys(r).length){if(m(r.invoke)){return Bd(r.invoke,n).then(n=>t(jd(Wd(e),n),...o))}return t(jd(r,n),...o)}return t(n,...o)}function zd(e,t){return(n={},...o)=>function(e){return!(!k(e)||![Ld,Od,Id].find(t=>y(e[t])))}(n)?Vd(e,Hd(e,t,n,o)):Vd(e,new Promise((r,i)=>{Hd(e,t,d(n,{success:r,fail:i}),o)}))}function Ud(e,t,n,o={}){const r=t+":fail";let i="";return i=n?0===n.indexOf(r)?n:r+" "+n:r,delete o.errCode,Pd(e,d({errMsg:i},o))}function qd(e,t,n,o){if(o&&o.beforeInvoke){const e=o.beforeInvoke(t);if(b(e))return e}const r=function(e,t){const n=e[0];if(!t||!t.formatArgs||!k(t.formatArgs)&&k(n))return;const o=t.formatArgs,r=Object.keys(o);for(let i=0;i<r.length;i++){const t=r[i],s=o[t];if(y(s)){const o=s(e[0][t],n);if(b(o))return o}else h(n,t)||(n[t]=s)}}(t,o);if(r)return r}function Yd(e,t,n,o){return n=>{const r=Ad(e,n,o),i=qd(0,[n],0,o);return i?Ud(r,e,i):t(n,{resolve:t=>function(e,t,n){return Pd(e,d(n||{},{errMsg:t+":ok"}))}(r,e,t),reject:(t,n)=>Ud(r,e,function(e){return!e||b(e)?e:e.stack?("undefined"==typeof globalThis||globalThis.harmonyChannel,e.message):e}(t),n)})}}function Xd(e,t,n,o){return zd(e,Yd(e,t,0,o))}function Gd(e,t,n,o){return function(e,t,n,o){return(...e)=>{const n=qd(0,e,0,o);if(n)throw new Error(n);return t.apply(null,e)}}(0,t,0,o)}function Kd(e,t,n,o){return zd(e,function(e,t,n,o){return Yd(e,t,0,o)}(e,t,0,o))}let Jd=!1,Zd=0,Qd=0,ef=960,tf=375,nf=750;function of(){const{windowWidth:e,pixelRatio:t,platform:n}=function(){const e=Ch(),t=Oh(Lh(e,Ph(e)));return{platform:xh?"ios":"other",pixelRatio:window.devicePixelRatio,windowWidth:t}}();Zd=e,Qd=t,Jd="ios"===n}function rf(e,t){const n=Number(e);return isNaN(n)?t:n}const sf=Gd(0,(e,t)=>{if(0===Zd&&(of(),function(){const e=__uniConfig.globalStyle||{};ef=rf(e.rpxCalcMaxDeviceWidth,960),tf=rf(e.rpxCalcBaseDeviceWidth,375),nf=rf(e.rpxCalcBaseDeviceWidth,750)}()),0===(e=Number(e)))return 0;let n=t||Zd;n=e===nf||n<=ef?n:tf;let o=e/750*n;return o<0&&(o=-o),o=Math.floor(o+1e-4),0===o&&(o=1!==Qd&&Jd?.5:1),e<0?-o:o});const af=new class{constructor(){this.$emitter=new ct}on(e,t){return this.$emitter.on(e,t)}once(e,t){return this.$emitter.once(e,t)}off(e,t){e?this.$emitter.off(e,t):this.$emitter.e={}}emit(e,...t){this.$emitter.emit(e,...t)}},lf=Gd(0,(e,t)=>(af.on(e,t),()=>af.off(e,t))),cf=Gd(0,(e,t)=>{m(e)||(e=e?[e]:[]),e.forEach(e=>{af.off(e,t)})}),uf=Gd(0,(e,...t)=>{af.emit(e,...t)}),df=[.5,.8,1,1.25,1.5,2];class ff{constructor(e,t){this.id=e,this.pageId=t}play(){Ih(this.id,this.pageId,"play")}pause(){Ih(this.id,this.pageId,"pause")}stop(){Ih(this.id,this.pageId,"stop")}seek(e){Ih(this.id,this.pageId,"seek",{position:e})}sendDanmu(e){Ih(this.id,this.pageId,"sendDanmu",e)}playbackRate(e){~df.indexOf(e)||(e=1),Ih(this.id,this.pageId,"playbackRate",{rate:e})}requestFullScreen(e={}){Ih(this.id,this.pageId,"requestFullScreen",e)}exitFullScreen(){Ih(this.id,this.pageId,"exitFullScreen")}showStatusBar(){Ih(this.id,this.pageId,"showStatusBar")}hideStatusBar(){Ih(this.id,this.pageId,"hideStatusBar")}}const pf=Gd(0,(e,t)=>new ff(e,gu(t||_u()))),hf=(e,t,n,o)=>{!function(e,t,n,o,r){ib.invokeViewMethod("map."+e,{type:n,data:o},t,r)}(e,t,n,o,e=>{o&&((e,t)=>{const n=t.errMsg||"";new RegExp("\\:\\s*fail").test(n)?e.fail&&e.fail(t):e.success&&e.success(t),e.complete&&e.complete(t)})(o,e)})};const mf={aliceblue:"#f0f8ff",antiquewhite:"#faebd7",aqua:"#00ffff",aquamarine:"#7fffd4",azure:"#f0ffff",beige:"#f5f5dc",bisque:"#ffe4c4",black:"#000000",blanchedalmond:"#ffebcd",blue:"#0000ff",blueviolet:"#8a2be2",brown:"#a52a2a",burlywood:"#deb887",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cyan:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkgray:"#a9a9a9",darkgrey:"#a9a9a9",darkgreen:"#006400",darkkhaki:"#bdb76b",darkmagenta:"#8b008b",darkolivegreen:"#556b2f",darkorange:"#ff8c00",darkorchid:"#9932cc",darkred:"#8b0000",darksalmon:"#e9967a",darkseagreen:"#8fbc8f",darkslateblue:"#483d8b",darkslategray:"#2f4f4f",darkslategrey:"#2f4f4f",darkturquoise:"#00ced1",darkviolet:"#9400d3",deeppink:"#ff1493",deepskyblue:"#00bfff",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1e90ff",firebrick:"#b22222",floralwhite:"#fffaf0",forestgreen:"#228b22",fuchsia:"#ff00ff",gainsboro:"#dcdcdc",ghostwhite:"#f8f8ff",gold:"#ffd700",goldenrod:"#daa520",gray:"#808080",grey:"#808080",green:"#008000",greenyellow:"#adff2f",honeydew:"#f0fff0",hotpink:"#ff69b4",indianred:"#cd5c5c",indigo:"#4b0082",ivory:"#fffff0",khaki:"#f0e68c",lavender:"#e6e6fa",lavenderblush:"#fff0f5",lawngreen:"#7cfc00",lemonchiffon:"#fffacd",lightblue:"#add8e6",lightcoral:"#f08080",lightcyan:"#e0ffff",lightgoldenrodyellow:"#fafad2",lightgray:"#d3d3d3",lightgrey:"#d3d3d3",lightgreen:"#90ee90",lightpink:"#ffb6c1",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",lightskyblue:"#87cefa",lightslategray:"#778899",lightslategrey:"#778899",lightsteelblue:"#b0c4de",lightyellow:"#ffffe0",lime:"#00ff00",limegreen:"#32cd32",linen:"#faf0e6",magenta:"#ff00ff",maroon:"#800000",mediumaquamarine:"#66cdaa",mediumblue:"#0000cd",mediumorchid:"#ba55d3",mediumpurple:"#9370db",mediumseagreen:"#3cb371",mediumslateblue:"#7b68ee",mediumspringgreen:"#00fa9a",mediumturquoise:"#48d1cc",mediumvioletred:"#c71585",midnightblue:"#191970",mintcream:"#f5fffa",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",navajowhite:"#ffdead",navy:"#000080",oldlace:"#fdf5e6",olive:"#808000",olivedrab:"#6b8e23",orange:"#ffa500",orangered:"#ff4500",orchid:"#da70d6",palegoldenrod:"#eee8aa",palegreen:"#98fb98",paleturquoise:"#afeeee",palevioletred:"#db7093",papayawhip:"#ffefd5",peachpuff:"#ffdab9",peru:"#cd853f",pink:"#ffc0cb",plum:"#dda0dd",powderblue:"#b0e0e6",purple:"#800080",rebeccapurple:"#663399",red:"#ff0000",rosybrown:"#bc8f8f",royalblue:"#4169e1",saddlebrown:"#8b4513",salmon:"#fa8072",sandybrown:"#f4a460",seagreen:"#2e8b57",seashell:"#fff5ee",sienna:"#a0522d",silver:"#c0c0c0",skyblue:"#87ceeb",slateblue:"#6a5acd",slategray:"#708090",slategrey:"#708090",snow:"#fffafa",springgreen:"#00ff7f",steelblue:"#4682b4",tan:"#d2b48c",teal:"#008080",thistle:"#d8bfd8",tomato:"#ff6347",turquoise:"#40e0d0",violet:"#ee82ee",wheat:"#f5deb3",white:"#ffffff",whitesmoke:"#f5f5f5",yellow:"#ffff00",yellowgreen:"#9acd32",transparent:"#00000000"};function gf(e){let t=null;if(null!=(t=/^#([0-9|A-F|a-f]{6})$/.exec(e=e||"#000000"))){return[parseInt(t[1].slice(0,2),16),parseInt(t[1].slice(2,4),16),parseInt(t[1].slice(4),16),255]}if(null!=(t=/^#([0-9|A-F|a-f]{3})$/.exec(e))){let e=t[1].slice(0,1),n=t[1].slice(1,2),o=t[1].slice(2,3);return e=parseInt(e+e,16),n=parseInt(n+n,16),o=parseInt(o+o,16),[e,n,o,255]}if(null!=(t=/^rgb\((.+)\)$/.exec(e)))return t[1].split(",").map(function(e){return Math.min(255,parseInt(e.trim()))}).concat(255);if(null!=(t=/^rgba\((.+)\)$/.exec(e)))return t[1].split(",").map(function(e,t){return 3===t?Math.floor(255*parseFloat(e.trim())):Math.min(255,parseInt(e.trim()))});var n=e.toLowerCase();if(h(mf,n)){t=/^#([0-9|A-F|a-f]{6,8})$/.exec(mf[n]);const e=parseInt(t[1].slice(0,2),16),o=parseInt(t[1].slice(2,4),16),r=parseInt(t[1].slice(4,6),16);let i=parseInt(t[1].slice(6,8),16);return i=i>=0?i:255,[e,o,r,i]}return[0,0,0,255]}class vf{constructor(e,t){this.type=e,this.data=t,this.colorStop=[]}addColorStop(e,t){this.colorStop.push([e,gf(t)])}}class yf{constructor(e,t){this.type="pattern",this.data=e,this.colorStop=t}}class bf{constructor(e){this.width=e}}const _f={thresholds:[0],initialRatio:0,observeAll:!1},wf=["top","right","bottom","left"];let xf=1;function Tf(e={}){return wf.map(t=>`${Number(e[t])||0}px`).join(" ")}class Sf{constructor(e,t){this._pageId=gu(e),this._component=e,this._options=d({},_f,t)}relativeTo(e,t){return this._options.relativeToSelector=e,this._options.rootMargin=Tf(t),this}relativeToViewport(e){return this._options.relativeToSelector=void 0,this._options.rootMargin=Tf(e),this}observe(e,t){y(t)&&(this._options.selector=e,this._reqId=xf++,function({reqId:e,component:t,options:n,callback:o}){const r=vh(t);(r.__io||(r.__io={}))[e]=function(e,t,n){Ep();const o=t.relativeToSelector?e.querySelector(t.relativeToSelector):null,r=new IntersectionObserver(e=>{e.forEach(e=>{n({intersectionRatio:Pp(e),intersectionRect:Cp(e.intersectionRect),boundingClientRect:Cp(e.boundingClientRect),relativeRect:Cp(e.rootBounds),time:Date.now(),dataset:Xe(e.target),id:e.target.id})})},{root:o,rootMargin:t.rootMargin,threshold:t.thresholds});if(t.observeAll){r.USE_MUTATION_OBSERVER=!0;const n=e.querySelectorAll(t.selector);for(let e=0;e<n.length;e++)r.observe(n[e])}else{r.USE_MUTATION_OBSERVER=!1;const n=e.querySelector(t.selector);n&&r.observe(n)}return r}(r,n,o)}({reqId:this._reqId,component:this._component,options:this._options,callback:t},this._pageId))}disconnect(){this._reqId&&function({reqId:e,component:t}){const n=vh(t),o=n.__io&&n.__io[e];o&&(o.disconnect(),delete n.__io[e])}({reqId:this._reqId,component:this._component},this._pageId)}}const kf=Gd(0,(e,t)=>((e=Ve(e))&&!gu(e)&&(t=e,e=null),new Sf(e||_u(),t)));let Ef=0,Cf={};function Pf(e,t,n,o){const r={options:o},i=o&&("success"in o||"fail"in o||"complete"in o);if(i){const e=String(Ef++);r.callbackId=e,Cf[e]=o}ib.invokeViewMethod(`editor.${e}`,{type:n,data:r},t,({callbackId:e,data:t})=>{i&&(!function(e,t){e=e||{},b(t)&&(t={errMsg:t}),/:ok$/.test(t.errMsg)?y(e.success)&&e.success(t):y(e.fail)&&e.fail(t),y(e.complete)&&e.complete(t)}(Cf[e],t),delete Cf[e])})}const Lf={canvas:class{constructor(e,t){this.id=e,this.pageId=t,this.actions=[],this.path=[],this.subpath=[],this.drawingState=[],this.state={lineDash:[0,0],shadowOffsetX:0,shadowOffsetY:0,shadowBlur:0,shadowColor:[0,0,0,0],font:"10px sans-serif",fontSize:10,fontWeight:"normal",fontStyle:"normal",fontFamily:"sans-serif"}}setFillStyle(e){}setStrokeStyle(e){}setShadow(e,t,n,o){}addColorStop(e,t){}setLineWidth(e){}setLineCap(e){}setLineJoin(e){}setLineDash(e,t){}setMiterLimit(e){}fillRect(e,t,n,o){}strokeRect(e,t,n,o){}clearRect(e,t,n,o){}fill(){}stroke(){}scale(e,t){}rotate(e){}translate(e,t){}setFontSize(e){}fillText(e,t,n,o){}setTextAlign(e){}setTextBaseline(e){}drawImage(e,t,n,o,r,i,s,a,l){}setGlobalAlpha(e){}strokeText(e,t,n,o){}setTransform(e,t,n,o,r,i){}draw(e=!1,t){var n=[...this.actions];this.actions=[],this.path=[],function(e,t,n,o,r){ib.invokeViewMethod(`canvas.${e}`,{type:n,data:o},t,e=>{r&&r(e)})}(this.id,this.pageId,"actionsChanged",{actions:n,reserve:e},t)}createLinearGradient(e,t,n,o){return new vf("linear",[e,t,n,o])}createCircularGradient(e,t,n){return new vf("radial",[e,t,n])}createPattern(e,t){if(void 0===t);else if(!(["repeat","repeat-x","repeat-y","no-repeat"].indexOf(t)<0))return new yf(e,t)}measureText(e,t){let n=0;return n=function(e,t){const n=document.createElement("canvas").getContext("2d");return n.font=t,n.measureText(e).width||0}(e,this.state.font),new bf(n)}save(){this.actions.push({method:"save",data:[]}),this.drawingState.push(this.state)}restore(){this.actions.push({method:"restore",data:[]}),this.state=this.drawingState.pop()||{lineDash:[0,0],shadowOffsetX:0,shadowOffsetY:0,shadowBlur:0,shadowColor:[0,0,0,0],font:"10px sans-serif",fontSize:10,fontWeight:"normal",fontStyle:"normal",fontFamily:"sans-serif"}}beginPath(){this.path=[],this.subpath=[],this.path.push({method:"beginPath",data:[]})}moveTo(e,t){this.path.push({method:"moveTo",data:[e,t]}),this.subpath=[[e,t]]}lineTo(e,t){0===this.path.length&&0===this.subpath.length?this.path.push({method:"moveTo",data:[e,t]}):this.path.push({method:"lineTo",data:[e,t]}),this.subpath.push([e,t])}quadraticCurveTo(e,t,n,o){this.path.push({method:"quadraticCurveTo",data:[e,t,n,o]}),this.subpath.push([n,o])}bezierCurveTo(e,t,n,o,r,i){this.path.push({method:"bezierCurveTo",data:[e,t,n,o,r,i]}),this.subpath.push([r,i])}arc(e,t,n,o,r,i=!1){this.path.push({method:"arc",data:[e,t,n,o,r,i]}),this.subpath.push([e,t])}rect(e,t,n,o){this.path.push({method:"rect",data:[e,t,n,o]}),this.subpath=[[e,t]]}arcTo(e,t,n,o,r){this.path.push({method:"arcTo",data:[e,t,n,o,r]}),this.subpath.push([n,o])}clip(){this.actions.push({method:"clip",data:[...this.path]})}closePath(){this.path.push({method:"closePath",data:[]}),this.subpath.length&&(this.subpath=[this.subpath.shift()])}clearActions(){this.actions=[],this.path=[],this.subpath=[]}getActions(){var e=[...this.actions];return this.clearActions(),e}set lineDashOffset(e){this.actions.push({method:"setLineDashOffset",data:[e]})}set globalCompositeOperation(e){this.actions.push({method:"setGlobalCompositeOperation",data:[e]})}set shadowBlur(e){this.actions.push({method:"setShadowBlur",data:[e]})}set shadowColor(e){this.actions.push({method:"setShadowColor",data:[e]})}set shadowOffsetX(e){this.actions.push({method:"setShadowOffsetX",data:[e]})}set shadowOffsetY(e){this.actions.push({method:"setShadowOffsetY",data:[e]})}set font(e){var t=this;this.state.font=e;var n=e.match(/^(([\w\-]+\s)*)(\d+\.?\d*r?px)(\/(\d+\.?\d*(r?px)?))?\s+(.*)/);if(n){var o=n[1].trim().split(/\s/),r=parseFloat(n[3]),i=n[7],s=[];o.forEach(function(e,n){["italic","oblique","normal"].indexOf(e)>-1?(s.push({method:"setFontStyle",data:[e]}),t.state.fontStyle=e):["bold","normal","lighter","bolder"].indexOf(e)>-1||/^\d+$/.test(e)?(s.push({method:"setFontWeight",data:[e]}),t.state.fontWeight=e):0===n?(s.push({method:"setFontStyle",data:["normal"]}),t.state.fontStyle="normal"):1===n&&a()}),1===o.length&&a(),o=s.map(function(e){return e.data[0]}).join(" "),this.state.fontSize=r,this.state.fontFamily=i,this.actions.push({method:"setFont",data:[`${o} ${r}px ${i}`]})}function a(){s.push({method:"setFontWeight",data:["normal"]}),t.state.fontWeight="normal"}}get font(){return this.state.font}set fillStyle(e){this.setFillStyle(e)}set strokeStyle(e){this.setStrokeStyle(e)}set globalAlpha(e){e=Math.floor(255*parseFloat(e)),this.actions.push({method:"setGlobalAlpha",data:[e]})}set textAlign(e){this.actions.push({method:"setTextAlign",data:[e]})}set lineCap(e){this.actions.push({method:"setLineCap",data:[e]})}set lineJoin(e){this.actions.push({method:"setLineJoin",data:[e]})}set lineWidth(e){this.actions.push({method:"setLineWidth",data:[e]})}set miterLimit(e){this.actions.push({method:"setMiterLimit",data:[e]})}set textBaseline(e){this.actions.push({method:"setTextBaseline",data:[e]})}},map:class{constructor(e,t){this.id=e,this.pageId=t}getCenterLocation(e){hf(this.id,this.pageId,"getCenterLocation",e)}moveToLocation(e){hf(this.id,this.pageId,"moveToLocation",e)}getScale(e){hf(this.id,this.pageId,"getScale",e)}getRegion(e){hf(this.id,this.pageId,"getRegion",e)}includePoints(e){hf(this.id,this.pageId,"includePoints",e)}translateMarker(e){hf(this.id,this.pageId,"translateMarker",e)}$getAppMap(){}addCustomLayer(e){hf(this.id,this.pageId,"addCustomLayer",e)}removeCustomLayer(e){hf(this.id,this.pageId,"removeCustomLayer",e)}addGroundOverlay(e){hf(this.id,this.pageId,"addGroundOverlay",e)}removeGroundOverlay(e){hf(this.id,this.pageId,"removeGroundOverlay",e)}updateGroundOverlay(e){hf(this.id,this.pageId,"updateGroundOverlay",e)}initMarkerCluster(e){hf(this.id,this.pageId,"initMarkerCluster",e)}addMarkers(e){hf(this.id,this.pageId,"addMarkers",e)}removeMarkers(e){hf(this.id,this.pageId,"removeMarkers",e)}moveAlong(e){hf(this.id,this.pageId,"moveAlong",e)}setLocMarkerIcon(e){hf(this.id,this.pageId,"setLocMarkerIcon",e)}openMapApp(e){hf(this.id,this.pageId,"openMapApp",e)}on(e,t){hf(this.id,this.pageId,"on",{name:e,callback:t})}},video:ff,editor:class{constructor(e,t){this.id=e,this.pageId=t}format(e,t){this._exec("format",{name:e,value:t})}insertDivider(){this._exec("insertDivider")}insertImage(e){this._exec("insertImage",e)}insertText(e){this._exec("insertText",e)}setContents(e){this._exec("setContents",e)}getContents(e){this._exec("getContents",e)}clear(e){this._exec("clear",e)}removeFormat(e){this._exec("removeFormat",e)}undo(e){this._exec("undo",e)}redo(e){this._exec("redo",e)}blur(e){this._exec("blur",e)}getSelectionText(e){this._exec("getSelectionText",e)}scrollIntoView(e){this._exec("scrollIntoView",e)}_exec(e,t){Pf(this.id,this.pageId,e,t)}}};function Of(e){if(e&&e.contextInfo){const{id:t,type:n,page:o}=e.contextInfo,r=Lf[n];e.context=new r(t,o),delete e.contextInfo}}class If{constructor(e,t,n,o){this._selectorQuery=e,this._component=t,this._selector=n,this._single=o}boundingClientRect(e){return this._selectorQuery._push(this._selector,this._component,this._single,{id:!0,dataset:!0,rect:!0,size:!0},e),this._selectorQuery}fields(e,t){return this._selectorQuery._push(this._selector,this._component,this._single,e,t),this._selectorQuery}scrollOffset(e){return this._selectorQuery._push(this._selector,this._component,this._single,{id:!0,dataset:!0,scrollOffset:!0},e),this._selectorQuery}context(e){return this._selectorQuery._push(this._selector,this._component,this._single,{context:!0},e),this._selectorQuery}node(e){return this._selectorQuery._push(this._selector,this._component,this._single,{node:!0},e),this._selectorQuery}}class Af{constructor(e){this._component=void 0,this._page=e,this._queue=[],this._queueCb=[]}exec(e){return function(e,t,n){const o=[];t.forEach(({component:t,selector:n,single:r,fields:i})=>{null===t?o.push(function(e){const t={};e.id&&(t.id="");e.dataset&&(t.dataset={});e.rect&&(t.left=0,t.right=0,t.top=0,t.bottom=0);e.size&&(t.width=document.documentElement.clientWidth,t.height=document.documentElement.clientHeight);if(e.scrollOffset){const e=document.documentElement,n=document.body;t.scrollLeft=e.scrollLeft||n.scrollLeft||0,t.scrollTop=e.scrollTop||n.scrollTop||0,t.scrollHeight=e.scrollHeight||n.scrollHeight||0,t.scrollWidth=e.scrollWidth||n.scrollWidth||0}return t}(i)):o.push(function(e,t,n,o,r){const i=function(e,t){if(!e)return t.$el;return e.$el}(t,e),s=i.parentElement;if(!s)return o?null:[];const{nodeType:a}=i,l=3===a||8===a;if(o){const e=l?s.querySelector(n):Mh(i,n)?i:i.querySelector(n);return e?Ah(e,r):null}{let e=[];const t=(l?s:i).querySelectorAll(n);return t&&t.length&&[].forEach.call(t,t=>{e.push(Ah(t,r))}),!l&&Mh(i,n)&&e.unshift(Ah(i,r)),e}}(e,t,n,r,i))}),n(o)}(this._page,this._queue,t=>{const n=this._queueCb;t.forEach((e,t)=>{m(e)?e.forEach(Of):Of(e);const o=n[t];y(o)&&o.call(this,e)}),y(e)&&e.call(this,t)}),this._nodesRef}in(e){return this._component=Ve(e),this}select(e){return this._nodesRef=new If(this,this._component,e,!0)}selectAll(e){return this._nodesRef=new If(this,this._component,e,!1)}selectViewport(){return this._nodesRef=new If(this,null,"",!0)}_push(e,t,n,o,r){this._queue.push({component:t,selector:e,single:n,fields:o}),this._queueCb.push(r)}}const Mf=Gd(0,e=>((e=Ve(e))&&!gu(e)&&(e=null),new Af(e||_u()))),Rf={formatArgs:{}},$f={duration:400,timingFunction:"linear",delay:0,transformOrigin:"50% 50% 0"};class Nf{constructor(e){this.actions=[],this.currentTransform={},this.currentStepAnimates=[],this.option=d({},$f,e)}_getOption(e){const t={transition:d({},this.option,e),transformOrigin:""};return t.transformOrigin=t.transition.transformOrigin,delete t.transition.transformOrigin,t}_pushAnimates(e,t){this.currentStepAnimates.push({type:e,args:t})}_converType(e){return e.replace(/[A-Z]/g,e=>`-${e.toLowerCase()}`)}_getValue(e){return"number"==typeof e?`${e}px`:e}export(){const e=this.actions;return this.actions=[],{actions:e}}step(e){return this.currentStepAnimates.forEach(e=>{"style"!==e.type?this.currentTransform[e.type]=e:this.currentTransform[`${e.type}.${e.args[0]}`]=e}),this.actions.push({animates:Object.values(this.currentTransform),option:this._getOption(e)}),this.currentStepAnimates=[],this}}const Df=Fe(()=>{const e=["opacity","backgroundColor"],t=["width","height","left","right","top","bottom"];["matrix","matrix3d","rotate","rotate3d","rotateX","rotateY","rotateZ","scale","scale3d","scaleX","scaleY","scaleZ","skew","skewX","skewY","translate","translate3d","translateX","translateY","translateZ"].concat(e,t).forEach(n=>{Nf.prototype[n]=function(...o){return e.concat(t).includes(n)?this._pushAnimates("style",[this._converType(n),t.includes(n)?this._getValue(o[0]):o[0]]):this._pushAnimates(n,o),this}})}),Ff=Gd(0,e=>(Df(),new Nf(e)),0,Rf),Bf=Gd(0,()=>{const e=Gg();return e&&e.$vm?e.$vm.$locale:gc().getLocale()}),jf=Gd(0,e=>{const t=Gg();if(!t)return!1;return t.$vm.$locale!==e&&(t.$vm.$locale=e,navigator.cookieEnabled&&window.localStorage&&(localStorage[oe]=e),ib.invokeOnCallback("onLocaleChange",{locale:e}),!0)}),Vf={[pe]:[],[fe]:[],[ue]:[],[ae]:[],[le]:[]};const Wf={formatArgs:{showToast:!0},beforeInvoke(){Tc()},beforeSuccess(e,t){if(!t.showToast)return;const{t:n}=gc(),o=n("uni.setClipboardData.success");o&&Ly({title:o,icon:"success",mask:!1})}},Hf={formatArgs:{count(e,t){(!e||e<=0)&&(t.count=9)},sizeType(e,t){t.sizeType=Sd(e,_d)},sourceType(e,t){t.sourceType=Sd(e,wd)},extension(e,t){if(e instanceof Array&&0===e.length)return"param extension should not be empty.";e||(t.extension=["*"])}}},zf={formatArgs:{sourceType(e,t){t.sourceType=Sd(e,wd)},compressed:!0,maxDuration:60,camera:"back",extension(e,t){if(e instanceof Array&&0===e.length)return"param extension should not be empty.";e||(t.extension=["*"])}}},Uf=["all","image","video"],qf={formatArgs:{count(e,t){(!e||e<=0)&&(t.count=100)},sourceType(e,t){t.sourceType=Sd(e,wd)},type(e,t){t.type=Td(e,Uf)},extension(e,t){if(e instanceof Array&&0===e.length)return"param extension should not be empty.";e||("all"!==t.type&&t.type?t.extension=["*"]:t.extension=[""])}}},Yf={formatArgs:{urls(e,t){t.urls=e.map(e=>b(e)&&e?bh(e):"")},current(e,t){"number"==typeof e?t.current=e>0&&e<t.urls.length?e:0:b(e)&&e&&(t.current=bh(e))}}},Xf="json",Gf=["text","arraybuffer"],Kf=encodeURIComponent;const Jf={formatArgs:{method(e,t){t.method=Td((e||"").toUpperCase(),xd)},data(e,t){t.data=e||""},url(e,t){t.method===xd[0]&&k(t.data)&&Object.keys(t.data).length&&(t.url=function(e,t){let n=e.split("#");const o=n[1]||"";n=n[0].split("?");let r=n[1]||"";e=n[0];const i=r.split("&").filter(e=>e),s={};i.forEach(e=>{const t=e.split("=");s[t[0]]=t[1]});for(const a in t)if(h(t,a)){let e=t[a];null==e?e="":k(e)&&(e=JSON.stringify(e)),s[Kf(a)]=Kf(e)}return r=Object.keys(s).map(e=>`${e}=${s[e]}`).join("&"),e+(r?"?"+r:"")+(o?"#"+o:"")}(e,t.data))},header(e,t){const n=t.header=e||{};t.method!==xd[0]&&(Object.keys(n).find(e=>"content-type"===e.toLowerCase())||(n["Content-Type"]="application/json"))},dataType(e,t){t.dataType=(e||Xf).toLowerCase()},responseType(e,t){t.responseType=(e||"").toLowerCase(),-1===Gf.indexOf(t.responseType)&&(t.responseType="text")}}},Zf={formatArgs:{header(e,t){t.header=e||{}}}},Qf={formatArgs:{filePath(e,t){e&&(t.filePath=bh(e))},header(e,t){t.header=e||{}},formData(e,t){t.formData=e||{}}}};const ep={url:{type:String,required:!0}},tp="navigateTo",np="redirectTo",op="reLaunch",rp="switchTab",ip="preloadPage",sp=(dp(["slide-in-right","slide-in-left","slide-in-top","slide-in-bottom","fade-in","zoom-out","zoom-fade-out","pop-in","none"]),dp(["slide-out-right","slide-out-left","slide-out-top","slide-out-bottom","fade-out","zoom-in","zoom-fade-in","pop-out","none"]),hp(tp)),ap=hp(np),lp=hp(op),cp=hp(rp),up={formatArgs:{delta(e,t){e=parseInt(e+"")||1,t.delta=Math.min(nh().length-1,e)}}};function dp(e){return{animationType:{type:String,validator(t){if(t&&-1===e.indexOf(t))return"`"+t+"` is not supported for `animationType` (supported values are: `"+e.join("`|`")+"`)"}},animationDuration:{type:Number}}}let fp;function pp(){fp=""}function hp(e){return{formatArgs:{url:mp(e)},beforeAll:pp}}function mp(e){return function(t,n){if(!t)return'Missing required args: "url"';const o=(t=function(e){if(0===e.indexOf("/")||0===e.indexOf("uni:"))return e;let t="";const n=nh();return n.length&&(t=uu(n[n.length-1]).route),Lu(t,e)}(t)).split("?")[0],r=Ou(o,!0);if(!r)return"page `"+t+"` is not found";if(e===tp||e===np){if(r.meta.isTabBar)return`can not ${e} a tabbar page`}else if(e===rp&&!r.meta.isTabBar)return"can not switch to no-tabBar page";if(e!==rp&&e!==ip||!r.meta.isTabBar||"appLaunch"===n.openType||(t=o),r.meta.isEntry&&(t=t.replace(r.alias,"/")),n.url=function(e){if(!b(e))return e;const t=e.indexOf("?");if(-1===t)return e;const n=e.slice(t+1).trim().replace(/^(\?|#|&)/,"");if(!n)return e;e=e.slice(0,t);const o=[];return n.split("&").forEach(e=>{const t=e.replace(/\+/g," ").split("="),n=t.shift(),r=t.length>0?t.join("="):"";o.push(n+"="+encodeURIComponent(r))}),o.length?e+"?"+o.join("&"):e}(t),"unPreloadPage"!==e)if(e!==ip){if(fp===t&&"appLaunch"!==n.openType)return`${fp} locked`;__uniConfig.ready&&(fp=t)}else if(r.meta.isTabBar){const e=nh(),t=r.path.slice(1);if(e.find(e=>e.route===t))return"tabBar page `"+t+"` already exists"}}}const gp="setNavigationBarTitle",vp={formatArgs:{duration:300}},yp={beforeInvoke(){wc()},formatArgs:{title:"",content:"",placeholderText:"",showCancel:!0,editable:!1,cancelText(e,t){if(!h(t,"cancelText")){const{t:e}=gc();t.cancelText=e("uni.showModal.cancel")}},cancelColor:"#000",confirmText(e,t){if(!h(t,"confirmText")){const{t:e}=gc();t.confirmText=e("uni.showModal.confirm")}},confirmColor:"#007aff"}},bp=["success","loading","none","error"],_p={formatArgs:{title:"",icon(e,t){t.icon=Td(e,bp)},image(e,t){t.image=e?bh(e):""},duration:1500,mask:!1}},wp={beforeInvoke(){const e=yu();if(e&&!e.isTabBar)return"not TabBar page"},formatArgs:{index(e){if(!__uniConfig.tabBar.list[e])return"tabbar item not found"}}},xp="setTabBarItem",Tp={beforeInvoke:wp.beforeInvoke,formatArgs:d({pagePath(e,t){e&&(t.pagePath=De(e))}},wp.formatArgs)},Sp="hideTabBar",kp="showTabBar",Ep=function(){if("object"==typeof window)if("IntersectionObserver"in window&&"IntersectionObserverEntry"in window&&"intersectionRatio"in window.IntersectionObserverEntry.prototype)"isIntersecting"in window.IntersectionObserverEntry.prototype||Object.defineProperty(window.IntersectionObserverEntry.prototype,"isIntersecting",{get:function(){return this.intersectionRatio>0}});else{var e=function(){for(var e=window.document,t=r(e);t;)t=r(e=t.ownerDocument);return e}(),t=[],n=null,o=null;s.prototype.THROTTLE_TIMEOUT=100,s.prototype.POLL_INTERVAL=null,s.prototype.USE_MUTATION_OBSERVER=!0,s._setupCrossOriginUpdater=function(){return n||(n=function(e,n){o=e&&n?f(e,n):{top:0,bottom:0,left:0,right:0,width:0,height:0},t.forEach(function(e){e._checkForIntersections()})}),n},s._resetCrossOriginUpdater=function(){n=null,o=null},s.prototype.observe=function(e){if(!this._observationTargets.some(function(t){return t.element==e})){if(!e||1!=e.nodeType)throw new Error("target must be an Element");this._registerInstance(),this._observationTargets.push({element:e,entry:null}),this._monitorIntersections(e.ownerDocument),this._checkForIntersections()}},s.prototype.unobserve=function(e){this._observationTargets=this._observationTargets.filter(function(t){return t.element!=e}),this._unmonitorIntersections(e.ownerDocument),0==this._observationTargets.length&&this._unregisterInstance()},s.prototype.disconnect=function(){this._observationTargets=[],this._unmonitorAllIntersections(),this._unregisterInstance()},s.prototype.takeRecords=function(){var e=this._queuedEntries.slice();return this._queuedEntries=[],e},s.prototype._initThresholds=function(e){var t=e||[0];return Array.isArray(t)||(t=[t]),t.sort().filter(function(e,t,n){if("number"!=typeof e||isNaN(e)||e<0||e>1)throw new Error("threshold must be a number between 0 and 1 inclusively");return e!==n[t-1]})},s.prototype._parseRootMargin=function(e){var t=(e||"0px").split(/\s+/).map(function(e){var t=/^(-?\d*\.?\d+)(px|%)$/.exec(e);if(!t)throw new Error("rootMargin must be specified in pixels or percent");return{value:parseFloat(t[1]),unit:t[2]}});return t[1]=t[1]||t[0],t[2]=t[2]||t[0],t[3]=t[3]||t[1],t},s.prototype._monitorIntersections=function(t){var n=t.defaultView;if(n&&-1==this._monitoringDocuments.indexOf(t)){var o=this._checkForIntersections,i=null,s=null;this.POLL_INTERVAL?i=n.setInterval(o,this.POLL_INTERVAL):(a(n,"resize",o,!0),a(t,"scroll",o,!0),this.USE_MUTATION_OBSERVER&&"MutationObserver"in n&&(s=new n.MutationObserver(o)).observe(t,{attributes:!0,childList:!0,characterData:!0,subtree:!0})),this._monitoringDocuments.push(t),this._monitoringUnsubscribes.push(function(){var e=t.defaultView;e&&(i&&e.clearInterval(i),l(e,"resize",o,!0)),l(t,"scroll",o,!0),s&&s.disconnect()});var c=this.root&&(this.root.ownerDocument||this.root)||e;if(t!=c){var u=r(t);u&&this._monitorIntersections(u.ownerDocument)}}},s.prototype._unmonitorIntersections=function(t){var n=this._monitoringDocuments.indexOf(t);if(-1!=n){var o=this.root&&(this.root.ownerDocument||this.root)||e;if(!this._observationTargets.some(function(e){var n=e.element.ownerDocument;if(n==t)return!0;for(;n&&n!=o;){var i=r(n);if((n=i&&i.ownerDocument)==t)return!0}return!1})){var i=this._monitoringUnsubscribes[n];if(this._monitoringDocuments.splice(n,1),this._monitoringUnsubscribes.splice(n,1),i(),t!=o){var s=r(t);s&&this._unmonitorIntersections(s.ownerDocument)}}}},s.prototype._unmonitorAllIntersections=function(){var e=this._monitoringUnsubscribes.slice(0);this._monitoringDocuments.length=0,this._monitoringUnsubscribes.length=0;for(var t=0;t<e.length;t++)e[t]()},s.prototype._checkForIntersections=function(){if(this.root||!n||o){var e=this._rootIsInDom(),t=e?this._getRootRect():{top:0,bottom:0,left:0,right:0,width:0,height:0};this._observationTargets.forEach(function(o){var r=o.element,s=u(r),a=this._rootContainsTarget(r),l=o.entry,c=e&&a&&this._computeTargetAndRootIntersection(r,s,t),d=null;this._rootContainsTarget(r)?n&&!this.root||(d=t):d={top:0,bottom:0,left:0,right:0,width:0,height:0};var f=o.entry=new i({time:window.performance&&performance.now&&performance.now(),target:r,boundingClientRect:s,rootBounds:d,intersectionRect:c});l?e&&a?this._hasCrossedThreshold(l,f)&&this._queuedEntries.push(f):l&&l.isIntersecting&&this._queuedEntries.push(f):this._queuedEntries.push(f)},this),this._queuedEntries.length&&this._callback(this.takeRecords(),this)}},s.prototype._computeTargetAndRootIntersection=function(t,r,i){if("none"!=window.getComputedStyle(t).display){for(var s=r,a=h(t),l=!1;!l&&a;){var d=null,p=1==a.nodeType?window.getComputedStyle(a):{};if("none"==p.display)return null;if(a==this.root||9==a.nodeType)if(l=!0,a==this.root||a==e)n&&!this.root?!o||0==o.width&&0==o.height?(a=null,d=null,s=null):d=o:d=i;else{var m=h(a),g=m&&u(m),v=m&&this._computeTargetAndRootIntersection(m,g,i);g&&v?(a=m,d=f(g,v)):(a=null,s=null)}else{var y=a.ownerDocument;a!=y.body&&a!=y.documentElement&&"visible"!=p.overflow&&(d=u(a))}if(d&&(s=c(d,s)),!s)break;a=a&&h(a)}return s}},s.prototype._getRootRect=function(){var t;if(this.root&&!m(this.root))t=u(this.root);else{var n=m(this.root)?this.root:e,o=n.documentElement,r=n.body;t={top:0,left:0,right:o.clientWidth||r.clientWidth,width:o.clientWidth||r.clientWidth,bottom:o.clientHeight||r.clientHeight,height:o.clientHeight||r.clientHeight}}return this._expandRectByRootMargin(t)},s.prototype._expandRectByRootMargin=function(e){var t=this._rootMarginValues.map(function(t,n){return"px"==t.unit?t.value:t.value*(n%2?e.width:e.height)/100}),n={top:e.top-t[0],right:e.right+t[1],bottom:e.bottom+t[2],left:e.left-t[3]};return n.width=n.right-n.left,n.height=n.bottom-n.top,n},s.prototype._hasCrossedThreshold=function(e,t){var n=e&&e.isIntersecting?e.intersectionRatio||0:-1,o=t.isIntersecting?t.intersectionRatio||0:-1;if(n!==o)for(var r=0;r<this.thresholds.length;r++){var i=this.thresholds[r];if(i==n||i==o||i<n!=i<o)return!0}},s.prototype._rootIsInDom=function(){return!this.root||p(e,this.root)},s.prototype._rootContainsTarget=function(t){var n=this.root&&(this.root.ownerDocument||this.root)||e;return p(n,t)&&(!this.root||n==t.ownerDocument)},s.prototype._registerInstance=function(){t.indexOf(this)<0&&t.push(this)},s.prototype._unregisterInstance=function(){var e=t.indexOf(this);-1!=e&&t.splice(e,1)},window.IntersectionObserver=s,window.IntersectionObserverEntry=i}function r(e){try{return e.defaultView&&e.defaultView.frameElement||null}catch(t){return null}}function i(e){this.time=e.time,this.target=e.target,this.rootBounds=d(e.rootBounds),this.boundingClientRect=d(e.boundingClientRect),this.intersectionRect=d(e.intersectionRect||{top:0,bottom:0,left:0,right:0,width:0,height:0}),this.isIntersecting=!!e.intersectionRect;var t=this.boundingClientRect,n=t.width*t.height,o=this.intersectionRect,r=o.width*o.height;this.intersectionRatio=n?Number((r/n).toFixed(4)):this.isIntersecting?1:0}function s(e,t){var n,o,r,i=t||{};if("function"!=typeof e)throw new Error("callback must be a function");if(i.root&&1!=i.root.nodeType&&9!=i.root.nodeType)throw new Error("root must be a Document or Element");this._checkForIntersections=(n=this._checkForIntersections.bind(this),o=this.THROTTLE_TIMEOUT,r=null,function(){r||(r=setTimeout(function(){n(),r=null},o))}),this._callback=e,this._observationTargets=[],this._queuedEntries=[],this._rootMarginValues=this._parseRootMargin(i.rootMargin),this.thresholds=this._initThresholds(i.threshold),this.root=i.root||null,this.rootMargin=this._rootMarginValues.map(function(e){return e.value+e.unit}).join(" "),this._monitoringDocuments=[],this._monitoringUnsubscribes=[]}function a(e,t,n,o){"function"==typeof e.addEventListener?e.addEventListener(t,n,o):"function"==typeof e.attachEvent&&e.attachEvent("on"+t,n)}function l(e,t,n,o){"function"==typeof e.removeEventListener?e.removeEventListener(t,n,o):"function"==typeof e.detatchEvent&&e.detatchEvent("on"+t,n)}function c(e,t){var n=Math.max(e.top,t.top),o=Math.min(e.bottom,t.bottom),r=Math.max(e.left,t.left),i=Math.min(e.right,t.right),s=i-r,a=o-n;return s>=0&&a>=0&&{top:n,bottom:o,left:r,right:i,width:s,height:a}||null}function u(e){var t;try{t=e.getBoundingClientRect()}catch(n){}return t?(t.width&&t.height||(t={top:t.top,right:t.right,bottom:t.bottom,left:t.left,width:t.right-t.left,height:t.bottom-t.top}),t):{top:0,bottom:0,left:0,right:0,width:0,height:0}}function d(e){return!e||"x"in e?e:{top:e.top,y:e.top,bottom:e.bottom,left:e.left,x:e.left,right:e.right,width:e.width,height:e.height}}function f(e,t){var n=t.top-e.top,o=t.left-e.left;return{top:n,left:o,height:t.height,width:t.width,bottom:n+t.height,right:o+t.width}}function p(e,t){for(var n=t;n;){if(n==e)return!0;n=h(n)}return!1}function h(t){var n=t.parentNode;return 9==t.nodeType&&t!=e?r(t):(n&&n.assignedSlot&&(n=n.assignedSlot.parentNode),n&&11==n.nodeType&&n.host?n.host:n)}function m(e){return e&&9===e.nodeType}};function Cp(e){const{bottom:t,height:n,left:o,right:r,top:i,width:s}=e||{};return{bottom:t,height:n,left:o,right:r,top:i,width:s}}function Pp(e){const{intersectionRatio:t,boundingClientRect:{height:n,width:o},intersectionRect:{height:r,width:i}}=e;return 0!==t?t:r===n?i/o:r/n}function Lp(){const e=_u();if(!e)return;const t=th(),n=t.keys();for(const o of n){const e=t.get(o);e.$.__isTabBar?e.$.__isActive=!1:rh(o)}e.$.__isTabBar&&(e.$.__isVisible=!1,Su(e,le))}function Op(e,t){return e===t.fullPath||"/"===e&&t.meta.isEntry}function Ip(e){const t=th().values();for(const n of t){const t=Gp(n);if(Op(e,t))return n.$.__isActive=!0,t.id}}const Ap=Kd(rp,({url:e,tabBarText:t,isAutomatedTesting:n},{resolve:o,reject:r})=>{if(Kp.handledBeforeEntryPageRoutes)return Lp(),Dp({type:rp,url:e,tabBarText:t,isAutomatedTesting:n},Ip(e)).then(o).catch(r);Zp.push({args:{type:rp,url:e,tabBarText:t,isAutomatedTesting:n},resolve:o,reject:r})},0,cp);function Mp(){const e=vu();if(!e)return;const t=Gp(e);rh(lh(t.path,t.id))}const Rp=Kd(np,({url:e,isAutomatedTesting:t},{resolve:n,reject:o})=>{if(Kp.handledBeforeEntryPageRoutes)return Mp(),Dp({type:np,url:e,isAutomatedTesting:t}).then(n).catch(o);Qp.push({args:{type:np,url:e,isAutomatedTesting:t},resolve:n,reject:o})},0,ap);function $p(){const e=th().keys();for(const t of e)rh(t)}const Np=Kd(op,({url:e,isAutomatedTesting:t},{resolve:n,reject:o})=>{if(Kp.handledBeforeEntryPageRoutes)return $p(),Dp({type:op,url:e,isAutomatedTesting:t}).then(n).catch(o);eh.push({args:{type:op,url:e,isAutomatedTesting:t},resolve:n,reject:o})},0,lp);function Dp({type:e,url:t,tabBarText:n,events:o,isAutomatedTesting:r},i){const s=Gg().$router,{path:a,query:l}=function(e){const[t,n]=e.split("?",2);return{path:t,query:tt(n||"")}}(t);return new Promise((t,c)=>{const u=function(e,t){return{__id__:t||++ih,__type__:e}}(e,i);s["navigateTo"===e?"push":"replace"]({path:a,query:l,state:u,force:!0}).then(i=>{if(pl(i))return c(i.message);if("switchTab"===e&&(s.currentRoute.value.meta.tabBarText=n),"navigateTo"===e){const e=s.currentRoute.value.meta;return e.eventChannel?o&&(Object.keys(o).forEach(t=>{e.eventChannel._addListener(t,"on",o[t])}),e.eventChannel._clearCache()):e.eventChannel=new ot(u.__id__,o),t(r?{__id__:u.__id__}:{eventChannel:e.eventChannel})}return r?t({__id__:u.__id__}):t()})})}function Fp(){if(Kp.handledBeforeEntryPageRoutes)return;Kp.handledBeforeEntryPageRoutes=!0;const e=[...Jp];Jp.length=0,e.forEach(({args:e,resolve:t,reject:n})=>Dp(e).then(t).catch(n));const t=[...Zp];Zp.length=0,t.forEach(({args:e,resolve:t,reject:n})=>(Lp(),Dp(e,Ip(e.url)).then(t).catch(n)));const n=[...Qp];Qp.length=0,n.forEach(({args:e,resolve:t,reject:n})=>(Mp(),Dp(e).then(t).catch(n)));const o=[...eh];eh.length=0,o.forEach(({args:e,resolve:t,reject:n})=>($p(),Dp(e).then(t).catch(n)))}let Bp;function jp(){var e;return Bp||(Bp=__uniConfig.tabBar&&Tn((e=__uniConfig.tabBar,dc()&&e.list&&e.list.forEach(e=>{mc(e,["text"])}),e))),Bp}function Vp(e){const t=window.CSS&&window.CSS.supports;return t&&(t(e)||t.apply(window.CSS,e.split(":")))}const Wp=Vp("top:env(a)"),Hp=Vp("top:constant(a)"),zp=Vp("backdrop-filter:blur(10px)"),Up=(()=>Wp?"env":Hp?"constant":"")();function qp(e){return Up?`calc(${e}px + ${Up}(safe-area-inset-bottom))`:`${e}px`}const Yp="$$",Xp=new Map;function Gp(e){return e.$page}const Kp={handledBeforeEntryPageRoutes:!1},Jp=[],Zp=[],Qp=[],eh=[];function th(){return Xp}function nh(){return oh()}function oh(){const e=[],t=Xp.values();for(const n of t)n.$.__isTabBar?n.$.__isActive&&e.push(n):e.push(n);return e}function rh(e,t=!0){const n=Xp.get(e);n.$.__isUnload=!0,Su(n,me),Xp.delete(e),t&&function(e){const t=ch.get(e);t&&(ch.delete(e),uh.pruneCacheEntry(t))}(e)}let ih=bd();function sh(e){const t=gd();let n=e.fullPath;return e.meta.isEntry&&-1===n.indexOf(e.meta.route)&&(n="/"+e.meta.route+n.replace("/","")),function(e,t,n,o,r,i){const{id:s,route:a}=o,l=ft(o.navigationBar,__uniConfig.themeConfig,i).titleColor;return{id:s,path:Ne(a),route:a,fullPath:t,options:n,meta:o,openType:e,eventChannel:r,statusBarStyle:"#ffffff"===l?"light":"dark"}}("navigateTo",n,{},t)}function ah(e){const t=sh(e.$route);!function(e,t){e.route=t.route,e.$vm=e,e.$page=t,e.$mpType="page",e.$fontFamilySet=new Set,t.meta.isTabBar&&(e.$.__isTabBar=!0,e.$.__isActive=!0)}(e,t),Xp.set(lh(t.path,t.id),e),1===Xp.size&&setTimeout(()=>{Fp()},0)}function lh(e,t){return e+Yp+t}const ch=new Map,uh={get:e=>ch.get(e),set(e,t){!function(e){const t=parseInt(e.split(Yp)[1]);if(!t)return;uh.forEach((e,n)=>{const o=parseInt(n.split(Yp)[1]);if(o&&o>t){if(function(e){return"tabBar"===e.props.type}(e))return;uh.delete(n),uh.pruneCacheEntry(e),uo(()=>{Xp.forEach((e,t)=>{e.$.isUnmounted&&Xp.delete(t)})})}})}(e),ch.set(e,t)},delete(e){ch.get(e)&&ch.delete(e)},forEach(e){ch.forEach(e)}};function dh(e,t){!function(e){const t=ph(e),{body:n}=document;hh&&n.removeAttribute(hh),t&&n.setAttribute(t,""),hh=t}(e),function(e){let t=0;if(e.isTabBar){const e=jp();e.shown&&(t=parseInt(e.height))}var n;su({"--window-top":(n=0,Up?`calc(${n}px + ${Up}(safe-area-inset-top))`:`${n}px`),"--window-bottom":qp(t)})}(t),function(e){{const t="nvue-dir-"+__uniConfig.nvue["flex-direction"];e.isNVue?(document.body.setAttribute("nvue",""),document.body.setAttribute(t,"")):(document.body.removeAttribute("nvue"),document.body.removeAttribute(t))}}(t),gh(e,t)}function fh(e){const t=ph(e);t&&function(e){const t=document.querySelector("uni-page-body");t&&t.setAttribute(e,"")}(t)}function ph(e){return e.type.__scopeId}let hh,mh;function gh(e,t){if(document.removeEventListener("touchmove",ku),mh&&document.removeEventListener("scroll",mh),t.disableScroll)return document.addEventListener("touchmove",ku);const{onPageScroll:n,onReachBottom:o}=e,r="transparent"===t.navigationBar.type;if(!(null==n?void 0:n.length)&&!(null==o?void 0:o.length)&&!r)return;const i={},s=Gp(e.proxy).id;(n||r)&&(i.onPageScroll=function(e,t,n){return o=>{t&&rb.publishHandler(_e,{scrollTop:o},e),n&&rb.emit(e+"."+_e,{scrollTop:o})}}(s,n,r)),(null==o?void 0:o.length)&&(i.onReachBottomDistance=t.onReachBottomDistance||50,i.onReachBottom=()=>rb.publishHandler(xe,{},s)),mh=Pu(i),requestAnimationFrame(()=>document.addEventListener("scroll",mh))}function vh(e){return e.$el}function yh(e){const{base:t}=__uniConfig.router;return 0===Ne(e).indexOf(t)?Ne(e):t+e}function bh(e){const{base:t,assets:n}=__uniConfig.router;if("./"===t&&(0!==e.indexOf("./")||!e.includes("/static/")&&0!==e.indexOf("./"+(n||"assets")+"/")||(e=e.slice(1))),0===e.indexOf("/")){if(0!==e.indexOf("//"))return yh(e.slice(1));e="https:"+e}if(ie.test(e)||se.test(e)||0===e.indexOf("blob:"))return e;const o=oh();return o.length?yh(Lu(Gp(o[o.length-1]).route,e).slice(1)):e}const _h=navigator.userAgent,wh=/android/i.test(_h),xh=/iphone|ipad|ipod/i.test(_h),Th=_h.match(/Windows NT ([\d|\d.\d]*)/i),Sh=/Macintosh|Mac/i.test(_h),kh=/Linux|X11/i.test(_h),Eh=Sh&&navigator.maxTouchPoints>0;function Ch(){return/^Apple/.test(navigator.vendor)&&"number"==typeof window.orientation}function Ph(e){return e&&90===Math.abs(window.orientation)}function Lh(e,t){return e?Math[t?"max":"min"](screen.width,screen.height):screen.width}function Oh(e){return Math.min(window.innerWidth,document.documentElement.clientWidth,e)||e}function Ih(e,t,n,o){ib.invokeViewMethod("video."+e,{videoId:e,type:n,data:o},t)}function Ah(e,t){const n={},{top:o,topWindowHeight:r}=ru();if(t.node){const t=e.tagName.split("-")[1]||e.tagName;t&&(n.node=e.querySelector(t))}if(t.id&&(n.id=e.id),t.dataset&&(n.dataset=Xe(e)),t.rect||t.size){const i=e.getBoundingClientRect();t.rect&&(n.left=i.left,n.right=i.right,n.top=i.top-o-r,n.bottom=i.bottom-o-r),t.size&&(n.width=i.width,n.height=i.height)}if(m(t.properties)&&t.properties.forEach(e=>{e=e.replace(/-([a-z])/g,function(e,t){return t.toUpperCase()})}),t.scrollOffset)if("UNI-SCROLL-VIEW"===e.tagName){const t=e.children[0].children[0];n.scrollLeft=t.scrollLeft,n.scrollTop=t.scrollTop,n.scrollHeight=t.scrollHeight,n.scrollWidth=t.scrollWidth}else n.scrollLeft=0,n.scrollTop=0,n.scrollHeight=0,n.scrollWidth=0;if(m(t.computedStyle)){const o=getComputedStyle(e);t.computedStyle.forEach(e=>{n[e]=o[e]})}return t.context&&(n.contextInfo=function(e){return e.__uniContextInfo}(e)),n}function Mh(e,t){return(e.matches||e.matchesSelector||e.mozMatchesSelector||e.msMatchesSelector||e.oMatchesSelector||e.webkitMatchesSelector||function(e){const t=this.parentElement.querySelectorAll(e);let n=t.length;for(;--n>=0&&t.item(n)!==this;);return n>-1}).call(e,t)}const Rh={};function $h(e,t){const n=Rh[e];return n?Promise.resolve(n):/^data:[a-z-]+\/[a-z-]+;base64,/.test(e)?Promise.resolve(function(e){const t=e.split(","),n=t[0].match(/:(.*?);/),o=n?n[1]:"",r=atob(t[1]);let i=r.length;const s=new Uint8Array(i);for(;i--;)s[i]=r.charCodeAt(i);return Nh(s,o)}(e)):new Promise((t,n)=>{const o=new XMLHttpRequest;o.open("GET",e,!0),o.responseType="blob",o.onload=function(){t(this.response)},o.onerror=n,o.send()})}function Nh(e,t){let n;if(e instanceof File)n=e;else{t=t||e.type||"";const r=`${Date.now()}${function(e){const t=e.split("/")[1];return t?`.${t}`:""}(t)}`;try{n=new File([e],r,{type:t})}catch(o){n=e=e instanceof Blob?e:new Blob([e],{type:t}),n.name=n.name||r}}return n}function Dh(e){for(const n in Rh)if(h(Rh,n)){if(Rh[n]===e)return n}var t=(window.URL||window.webkitURL).createObjectURL(e);return Rh[t]=e,t}function Fh(e){(window.URL||window.webkitURL).revokeObjectURL(e),delete Rh[e]}const Bh=Qu(),jh=Qu();const Vh=od({name:"ResizeSensor",props:{initial:{type:Boolean,default:!1}},emits:["resize"],setup(e,{emit:t}){const n=Bn(null),o=function(e){return()=>{const{firstElementChild:t,lastElementChild:n}=e.value;t.scrollLeft=1e5,t.scrollTop=1e5,n.scrollLeft=1e5,n.scrollTop=1e5}}(n),r=function(e,t,n){const o=Tn({width:-1,height:-1});return Vo(()=>d({},o),e=>t("resize",e)),()=>{const t=e.value;t&&(o.width=t.offsetWidth,o.height=t.offsetHeight,n())}}(n,t,o);return function(e,t,n,o){hr(o),kr(()=>{t.initial&&uo(n);const r=e.value;r.offsetParent!==r.parentElement&&(r.parentElement.style.position="relative"),"AnimationEvent"in window||o()})}(n,e,r,o),()=>zi("uni-resize-sensor",{ref:n,onAnimationstartOnce:r},[zi("div",{onScroll:r},[zi("div",null,null)],40,["onScroll"]),zi("div",{onScroll:r},[zi("div",null,null)],40,["onScroll"])],40,["onAnimationstartOnce"])}});function Wh(){}const Hh={cursorSpacing:{type:[Number,String],default:0},showConfirmBar:{type:[Boolean,String],default:"auto"},adjustPosition:{type:[Boolean,String],default:!0},autoBlur:{type:[Boolean,String],default:!1}};function zh(e,t,n){function o(e){const t=ps(()=>0===String(navigator.vendor).indexOf("Apple"));e.addEventListener("focus",()=>{clearTimeout(undefined),document.addEventListener("click",Wh,!1)});e.addEventListener("blur",()=>{t.value&&e.blur(),document.removeEventListener("click",Wh,!1),t.value&&document.documentElement.scrollTo(document.documentElement.scrollLeft,document.documentElement.scrollTop)})}Vo(()=>t.value,e=>e&&o(e))}var Uh=/^<([-A-Za-z0-9_]+)((?:\s+[a-zA-Z_:][-a-zA-Z0-9_:.]*(?:\s*=\s*(?:(?:"[^"]*")|(?:'[^']*')|[^>\s]+))?)*)\s*(\/?)>/,qh=/^<\/([-A-Za-z0-9_]+)[^>]*>/,Yh=/([a-zA-Z_:][-a-zA-Z0-9_:.]*)(?:\s*=\s*(?:(?:"((?:\\.|[^"])*)")|(?:'((?:\\.|[^'])*)')|([^>\s]+)))?/g,Xh=em("area,base,basefont,br,col,frame,hr,img,input,link,meta,param,embed,command,keygen,source,track,wbr"),Gh=em("a,address,article,applet,aside,audio,blockquote,button,canvas,center,dd,del,dir,div,dl,dt,fieldset,figcaption,figure,footer,form,frameset,h1,h2,h3,h4,h5,h6,header,hgroup,hr,iframe,isindex,li,map,menu,noframes,noscript,object,ol,output,p,pre,section,script,table,tbody,td,tfoot,th,thead,tr,ul,video"),Kh=em("abbr,acronym,applet,b,basefont,bdo,big,br,button,cite,code,del,dfn,em,font,i,iframe,img,input,ins,kbd,label,map,object,q,s,samp,script,select,small,span,strike,strong,sub,sup,textarea,tt,u,var"),Jh=em("colgroup,dd,dt,li,options,p,td,tfoot,th,thead,tr"),Zh=em("checked,compact,declare,defer,disabled,ismap,multiple,nohref,noresize,noshade,nowrap,readonly,selected"),Qh=em("script,style");function em(e){for(var t={},n=e.split(","),o=0;o<n.length;o++)t[n[o]]=!0;return t}const tm={src:{type:String,default:""},mode:{type:String,default:"scaleToFill"},lazyLoad:{type:[Boolean,String],default:!1},draggable:{type:Boolean,default:!1}},nm={widthFix:["offsetWidth","height",(e,t)=>e/t],heightFix:["offsetHeight","width",(e,t)=>e*t]},om={aspectFit:["center center","contain"],aspectFill:["center center","cover"],widthFix:[,"100% 100%"],heightFix:[,"100% 100%"],top:["center top"],bottom:["center bottom"],center:["center center"],left:["left center"],right:["right center"],"top left":["left top"],"top right":["right top"],"bottom left":["left bottom"],"bottom right":["right bottom"]},rm=od({name:"Image",props:tm,setup(e,{emit:t}){const n=Bn(null),o=function(e,t){const n=Bn(""),o=ps(()=>{let e="auto",o="";const r=om[t.mode];return r?(r[0]&&(o=r[0]),r[1]&&(e=r[1])):(o="0% 0%",e="100% 100%"),`background-image:${n.value?'url("'+n.value+'")':"none"};background-position:${o};background-size:${e};`}),r=Tn({rootEl:e,src:ps(()=>t.src?bh(t.src):""),origWidth:0,origHeight:0,origStyle:{width:"",height:""},modeStyle:o,imgSrc:n});return kr(()=>{const t=e.value;r.origWidth=t.clientWidth||0,r.origHeight=t.clientHeight||0}),r}(n,e),r=sd(n,t),{fixSize:i}=function(e,t,n){const o=()=>{const{mode:o}=t,r=nm[o];if(!r)return;const{origWidth:i,origHeight:s}=n,a=i&&s?i/s:0;if(!a)return;const l=e.value,c=l[r[0]];c&&(l.style[r[1]]=function(e){im&&e>10&&(e=2*Math.round(e/2));return e}(r[2](c,a))+"px")},r=()=>{const{style:t}=e.value,{origStyle:{width:o,height:r}}=n;t.width=o,t.height=r};return Vo(()=>t.mode,(e,t)=>{nm[t]&&r(),nm[e]&&o()}),{fixSize:o,resetSize:r}}(n,e,o);return function(e,t,n,o,r){let i,s;const a=(t=0,n=0,o="")=>{e.origWidth=t,e.origHeight=n,e.imgSrc=o},l=l=>{if(!l)return c(),void a();i=i||new Image,i.onload=e=>{const{width:u,height:d}=i;a(u,d,l),uo(()=>{o()}),i.draggable=t.draggable,s&&s.remove(),s=i,n.value.appendChild(i),c(),r("load",e,{width:u,height:d})},i.onerror=t=>{a(),c(),r("error",t,{errMsg:`GET ${e.src} 404 (Not Found)`})},i.src=l},c=()=>{i&&(i.onload=null,i.onerror=null,i=null)};Vo(()=>e.src,e=>l(e)),Vo(()=>e.imgSrc,e=>{!e&&s&&(s.remove(),s=null)}),kr(()=>l(e.src)),Pr(()=>c())}(o,e,n,i,r),()=>zi("uni-image",{ref:n},[zi("div",{style:o.modeStyle},null,4),nm[e.mode]?zi(Vh,{onResize:i},null,8,["onResize"]):zi("span",null,null)],512)}});const im="Google Inc."===navigator.vendor;const sm=Ke(!0),am=[];let lm=0,cm=!1;const um=e=>am.forEach(t=>t.userAction=e);function dm(e={userAction:!1}){if(!cm){["touchstart","touchmove","touchend","mousedown","mouseup"].forEach(e=>{document.addEventListener(e,function(){!lm&&um(!0),lm++,setTimeout(()=>{! --lm&&um(!1)},0)},sm)}),cm=!0}am.push(e)}const fm=()=>!!lm;function pm(){const e=Tn({userAction:!1});return kr(()=>{dm(e)}),Pr(()=>{!function(e){const t=am.indexOf(e);t>=0&&am.splice(t,1)}(e)}),{state:e}}function hm(){const e=Tn({attrs:{}});return kr(()=>{let t=ns();for(;t;){const n=t.type.__scopeId;n&&(e.attrs[n]=""),t=t.proxy&&"page"===t.proxy.$mpType?null:t.parent}}),{state:e}}function mm(e,t){const n=document.activeElement;if(!n)return t({});const o={};["input","textarea"].includes(n.tagName.toLowerCase())&&(o.start=n.selectionStart,o.end=n.selectionEnd),t(o)}function gm(e,t,n){"number"===t&&isNaN(Number(e))&&(e="");return null==e?"":String(e)}const vm=["none","text","decimal","numeric","tel","search","email","url"],ym=d({},{name:{type:String,default:""},modelValue:{type:[String,Number]},value:{type:[String,Number]},disabled:{type:[Boolean,String],default:!1},autoFocus:{type:[Boolean,String],default:!1},focus:{type:[Boolean,String],default:!1},cursor:{type:[Number,String],default:-1},selectionStart:{type:[Number,String],default:-1},selectionEnd:{type:[Number,String],default:-1},type:{type:String,default:"text"},password:{type:[Boolean,String],default:!1},placeholder:{type:String,default:""},placeholderStyle:{type:String,default:""},placeholderClass:{type:String,default:""},maxlength:{type:[Number,String],default:140},confirmType:{type:String,default:"done"},confirmHold:{type:Boolean,default:!1},ignoreCompositionEvent:{type:Boolean,default:!0},step:{type:String,default:"0.000000000000000001"},inputmode:{type:String,default:void 0,validator:e=>!!~vm.indexOf(e)},cursorColor:{type:String,default:""}},Hh),bm=["input","focus","blur","update:value","update:modelValue","update:focus","compositionstart","compositionupdate","compositionend","keyboardheightchange"];function _m(e,t,n,o){let r=null;r=nt(n=>{t.value=gm(n,e.type)},100,{setTimeout:setTimeout,clearTimeout:clearTimeout}),Vo(()=>e.modelValue,r),Vo(()=>e.value,r);const i=function(e,t){let n,o,r=0;const i=function(...i){const s=Date.now();clearTimeout(n),o=()=>{o=null,r=s,e.apply(this,i)},s-r<t?n=setTimeout(o,t-(s-r)):o()};return i.cancel=function(){clearTimeout(n),o=null},i.flush=function(){clearTimeout(n),o&&o()},i}((e,t)=>{r.cancel(),n("update:modelValue",t.value),n("update:value",t.value),o("input",e,t)},100);return Sr(()=>{r.cancel(),i.cancel()}),{trigger:o,triggerInput:(e,t,n)=>{r.cancel(),i(e,t),n&&i.flush()}}}function wm(e,t){pm();const n=ps(()=>e.autoFocus||e.focus);function o(){if(!n.value)return;const e=t.value;e?e.focus():setTimeout(o,100)}Vo(()=>e.focus,e=>{e?o():function(){const e=t.value;e&&e.blur()}()}),kr(()=>{n.value&&uo(o)})}function xm(e,t,n,o){Ic(bu(),"getSelectedTextRange",mm);const{fieldRef:r,state:i,trigger:s}=function(e,t,n){const o=Bn(null),r=sd(t,n),i=ps(()=>{const t=Number(e.selectionStart);return isNaN(t)?-1:t}),s=ps(()=>{const t=Number(e.selectionEnd);return isNaN(t)?-1:t}),a=ps(()=>{const t=Number(e.cursor);return isNaN(t)?-1:t}),l=ps(()=>{var t=Number(e.maxlength);return isNaN(t)?140:t});let c="";c=gm(e.modelValue,e.type)||gm(e.value,e.type);const u=Tn({value:c,valueOrigin:c,maxlength:l,focus:e.focus,composing:!1,selectionStart:i,selectionEnd:s,cursor:a});return Vo(()=>u.focus,e=>n("update:focus",e)),Vo(()=>u.maxlength,e=>u.value=u.value.slice(0,e),{immediate:!1}),{fieldRef:o,state:u,trigger:r}}(e,t,n),{triggerInput:a}=_m(e,i,n,s);wm(e,r),zh(0,r);const{state:l}=hm();!function(e,t){const n=si(ud,!1);if(!n)return;const o=ns(),r={submit(){const n=o.proxy;return[n[e],b(t)?n[t]:t.value]},reset(){b(t)?o.proxy[t]="":t.value=""}};n.addField(r),Pr(()=>{n.removeField(r)})}("name",i),function(e,t,n,o,r,i){function s(){const n=e.value;n&&t.focus&&t.selectionStart>-1&&t.selectionEnd>-1&&"number"!==n.type&&(n.selectionStart=t.selectionStart,n.selectionEnd=t.selectionEnd)}function a(){const n=e.value;n&&t.focus&&t.selectionStart<0&&t.selectionEnd<0&&t.cursor>-1&&"number"!==n.type&&(n.selectionEnd=n.selectionStart=t.cursor)}function l(e){return"number"===e.type?null:e.selectionEnd}Vo([()=>t.selectionStart,()=>t.selectionEnd],s),Vo(()=>t.cursor,a),Vo(()=>e.value,function(){const c=e.value;if(!c)return;const u=function(e,o){e.stopPropagation(),y(i)&&!1===i(e,t)||(t.value=c.value,t.composing&&n.ignoreCompositionEvent||r(e,{value:c.value,cursor:l(c)},o))};function d(e){n.ignoreCompositionEvent||o(e.type,e,{value:e.data})}c.addEventListener("change",e=>e.stopPropagation()),c.addEventListener("focus",function(e){t.focus=!0,o("focus",e,{value:t.value}),s(),a()}),c.addEventListener("blur",function(e){t.composing&&(t.composing=!1,u(e,!0)),t.focus=!1,o("blur",e,{value:t.value,cursor:l(e.target)})}),c.addEventListener("input",u),c.addEventListener("compositionstart",e=>{e.stopPropagation(),t.composing=!0,d(e)}),c.addEventListener("compositionend",e=>{e.stopPropagation(),t.composing&&(t.composing=!1,u(e)),d(e)}),c.addEventListener("compositionupdate",d)})}(r,i,e,s,a,o);return{fieldRef:r,state:i,scopedAttrsState:l,fixDisabledColor:0===String(navigator.vendor).indexOf("Apple")&&CSS.supports("image-orientation:from-image"),trigger:s}}const Tm=d({},ym,{placeholderClass:{type:String,default:"input-placeholder"},textContentType:{type:String,default:""}}),Sm=Fe(()=>{{const e=navigator.userAgent;let t="";const n=e.match(/OS\s([\w_]+)\slike/);if(n)t=n[1].replace(/_/g,".");else if(/Macintosh|Mac/i.test(e)&&navigator.maxTouchPoints>0){const n=e.match(/Version\/(\S*)\b/);n&&(t=n[1])}return!!t&&parseInt(t)>=16&&parseFloat(t)<17.2}});function km(e,t,n,o,r){if(t.value)if("."===e.data){if("."===t.value.slice(-1))return n.value=o.value=t.value=t.value.slice(0,-1),!1;if(t.value&&!t.value.includes("."))return t.value+=".",r&&(r.fn=()=>{n.value=o.value=t.value=t.value.slice(0,-1),o.removeEventListener("blur",r.fn)},o.addEventListener("blur",r.fn)),!1}else if("deleteContentBackward"===e.inputType&&Sm()&&"."===t.value.slice(-2,-1))return t.value=n.value=o.value=t.value.slice(0,-2),!0}const Em=od({name:"Input",props:Tm,emits:["confirm",...bm],setup(e,{emit:t,expose:n}){const o=["text","number","idcard","digit","password","tel"],r=["off","one-time-code"],i=ps(()=>{let t="";switch(e.type){case"text":t="text","search"===e.confirmType&&(t="search");break;case"idcard":t="text";break;case"digit":t="number";break;default:t=o.includes(e.type)?e.type:"text"}return e.password?"password":t}),s=ps(()=>{const t=r.indexOf(e.textContentType),n=r.indexOf(A(e.textContentType));return r[-1!==t?t:-1!==n?n:0]});let a=function(e,t){if("number"===t.value){const t=void 0===e.modelValue?e.value:e.modelValue,n=Bn(null!=t?t.toLocaleString():"");return Vo(()=>e.modelValue,e=>{n.value=null!=e?e.toLocaleString():""}),Vo(()=>e.value,e=>{n.value=null!=e?e.toLocaleString():""}),n}return Bn("")}(e,i),l={fn:null};const c=Bn(null),{fieldRef:u,state:d,scopedAttrsState:f,fixDisabledColor:p,trigger:h}=xm(e,c,t,(t,n)=>{const o=t.target;if("number"===i.value){if(l.fn&&(o.removeEventListener("blur",l.fn),l.fn=null),o.validity&&!o.validity.valid){if((!a.value||!o.value)&&"-"===t.data||"-"===a.value[0]&&"deleteContentBackward"===t.inputType)return a.value="-",n.value="",l.fn=()=>{a.value=o.value=""},o.addEventListener("blur",l.fn),!1;const e=km(t,a,n,o,l);return"boolean"==typeof e?e:(a.value=n.value=o.value="-"===a.value?"":a.value,!1)}{const e=km(t,a,n,o,l);if("boolean"==typeof e)return e;a.value=o.value}const r=n.maxlength;if(r>0&&o.value.length>r){o.value=o.value.slice(0,r),n.value=o.value;return(void 0!==e.modelValue&&null!==e.modelValue?e.modelValue.toString():"")!==o.value}}});Vo(()=>d.value,t=>{"number"!==e.type||"-"===a.value&&""===t||(a.value=t.toString())});const m=["number","digit"],g=ps(()=>m.includes(e.type)?e.step:"");function v(t){if("Enter"!==t.key)return;const n=t.target;t.stopPropagation(),h("confirm",t,{value:n.value}),!e.confirmHold&&n.blur()}return n({$triggerInput:e=>{t("update:modelValue",e.value),t("update:value",e.value),d.value=e.value}}),()=>{let t=e.disabled&&p?zi("input",{key:"disabled-input",ref:u,value:d.value,tabindex:"-1",readonly:!!e.disabled,type:i.value,maxlength:d.maxlength,step:g.value,class:"uni-input-input",style:e.cursorColor?{caretColor:e.cursorColor}:{},onFocus:e=>e.target.blur()},null,44,["value","readonly","type","maxlength","step","onFocus"]):zi("input",{key:"input",ref:u,value:d.value,onInput:e=>{d.value=e.target.value.toString()},disabled:!!e.disabled,type:i.value,maxlength:d.maxlength,step:g.value,enterkeyhint:e.confirmType,pattern:"number"===e.type?"[0-9]*":void 0,class:"uni-input-input",style:e.cursorColor?{caretColor:e.cursorColor}:{},autocomplete:s.value,onKeyup:v,inputmode:e.inputmode},null,44,["value","onInput","disabled","type","maxlength","step","enterkeyhint","pattern","autocomplete","onKeyup","inputmode"]);return zi("uni-input",{ref:c},[zi("div",{class:"uni-input-wrapper"},[qo(zi("div",Ji(f.attrs,{style:e.placeholderStyle,class:["uni-input-placeholder",e.placeholderClass]}),[e.placeholder],16),[[Ds,!(d.value.length||"-"===a.value||a.value.includes("."))]]),"search"===e.confirmType?zi("form",{action:"",onSubmit:e=>e.preventDefault(),class:"uni-input-form"},[t],40,["onSubmit"]):t])],512)}}});const Cm=["class","style"],Pm=/^on[A-Z]+/,Lm=(e={})=>{const{excludeListeners:t=!1,excludeKeys:n=[]}=e,o=ns(),r=jn({}),i=jn({}),s=jn({}),a=n.concat(Cm);return o.attrs=Tn(o.attrs),Bo(()=>{const e=(n=o.attrs,Object.keys(n).map(e=>[e,n[e]])).reduce((e,[n,o])=>(a.includes(n)?e.exclude[n]=o:Pm.test(n)?(t||(e.attrs[n]=o),e.listeners[n]=o):e.attrs[n]=o,e),{exclude:{},attrs:{},listeners:{}});var n;r.value=e.attrs,i.value=e.listeners,s.value=e.exclude}),{$attrs:r,$listeners:i,$excludeAttrs:s}};function Om(e){const t=[];return m(e)&&e.forEach(e=>{Fi(e)?e.type===Ei?t.push(...Om(e.children)):t.push(e):m(e)&&t.push(...Om(e))}),t}const Im=od({inheritAttrs:!1,name:"MovableArea",props:{scaleArea:{type:Boolean,default:!1}},setup(e,{slots:t}){const n=Bn(null),o=Bn(!1);let{setContexts:r,events:i}=function(e,t){const n=Bn(0),o=Bn(0),r=Tn({x:null,y:null}),i=Bn(null);let s=null,a=[];function l(t){t&&1!==t&&(e.scaleArea?a.forEach(function(e){e._setScale(t)}):s&&s._setScale(t))}function c(e,n=a){let o=t.value;function r(e){for(let t=0;t<n.length;t++){const o=n[t];if(e===o.rootRef.value)return o}return e===o||e===document.body||e===document?null:r(e.parentNode)}return r(e)}const u=id(t=>{let n=t.touches;if(n&&n.length>1){let t={x:n[1].pageX-n[0].pageX,y:n[1].pageY-n[0].pageY};if(i.value=Am(t),r.x=t.x,r.y=t.y,!e.scaleArea){let e=c(n[0].target),t=c(n[1].target);s=e&&e===t?e:null}}}),d=id(e=>{let t=e.touches;if(t&&t.length>1){e.preventDefault();let n={x:t[1].pageX-t[0].pageX,y:t[1].pageY-t[0].pageY};if(null!==r.x&&i.value&&i.value>0){l(Am(n)/i.value)}r.x=n.x,r.y=n.y}}),f=id(t=>{let n=t.touches;n&&n.length||t.changedTouches&&(r.x=0,r.y=0,i.value=null,e.scaleArea?a.forEach(function(e){e._endScale()}):s&&s._endScale())});function p(){h(),a.forEach(function(e,t){e.setParent()})}function h(){let e=window.getComputedStyle(t.value),r=t.value.getBoundingClientRect();n.value=r.width-["Left","Right"].reduce(function(t,n){const o="padding"+n;return t+parseFloat(e["border"+n+"Width"])+parseFloat(e[o])},0),o.value=r.height-["Top","Bottom"].reduce(function(t,n){const o="padding"+n;return t+parseFloat(e["border"+n+"Width"])+parseFloat(e[o])},0)}return ii("movableAreaWidth",n),ii("movableAreaHeight",o),{setContexts(e){a=e},events:{_onTouchstart:u,_onTouchmove:d,_onTouchend:f,_resize:p}}}(e,n);const{$listeners:s,$attrs:a,$excludeAttrs:l}=Lm(),c=s.value;["onTouchstart","onTouchmove","onTouchend"].forEach(e=>{let t=c[e],n=i[`_${e}`];c[e]=t?[].concat(t,n):n}),kr(()=>{i._resize(),o.value=!0});let u=[];const d=[];function f(){const e=[];for(let t=0;t<u.length;t++){let n=u[t];n=n.el;const o=d.find(e=>n===e.rootRef.value);o&&e.push(An(o))}r(e)}return ii("_isMounted",o),ii("movableAreaRootRef",n),ii("addMovableViewContext",e=>{d.push(e),f()}),ii("removeMovableViewContext",e=>{const t=d.indexOf(e);t>=0&&(d.splice(t,1),f())}),()=>{const e=t.default&&t.default();return u=Om(e),zi("uni-movable-area",Ji({ref:n},a.value,l.value,c),[zi(Vh,{onResize:i._resize},null,8,["onResize"]),u],16)}}});function Am(e){return Math.sqrt(e.x*e.x+e.y*e.y)}const Mm=function(e,t,n,o){e.addEventListener(t,e=>{y(n)&&!1===n(e)&&((void 0===e.cancelable||e.cancelable)&&e.preventDefault(),e.stopPropagation())},{passive:!1})};let Rm,$m;function Nm(e,t,n){Pr(()=>{document.removeEventListener("mousemove",Rm),document.removeEventListener("mouseup",$m)});let o=0,r=0,i=0,s=0;const a=function(e,n,a,l){if(!1===t({cancelable:e.cancelable,target:e.target,currentTarget:e.currentTarget,preventDefault:e.preventDefault.bind(e),stopPropagation:e.stopPropagation.bind(e),touches:e.touches,changedTouches:e.changedTouches,detail:{state:n,x:a,y:l,dx:a-o,dy:l-r,ddx:a-i,ddy:l-s,timeStamp:e.timeStamp}}))return!1};let l,c,u=null;Mm(e,"touchstart",function(e){if(l=!0,1===e.touches.length&&!u)return u=e,o=i=e.touches[0].pageX,r=s=e.touches[0].pageY,a(e,"start",o,r)}),Mm(e,"mousedown",function(e){if(c=!0,!l&&!u)return u=e,o=i=e.pageX,r=s=e.pageY,a(e,"start",o,r)}),Mm(e,"touchmove",function(e){if(1===e.touches.length&&u){const t=a(e,"move",e.touches[0].pageX,e.touches[0].pageY);return i=e.touches[0].pageX,s=e.touches[0].pageY,t}});const d=Rm=function(e){if(!l&&c&&u){const t=a(e,"move",e.pageX,e.pageY);return i=e.pageX,s=e.pageY,t}};document.addEventListener("mousemove",d),Mm(e,"touchend",function(e){if(0===e.touches.length&&u)return l=!1,u=null,a(e,"end",e.changedTouches[0].pageX,e.changedTouches[0].pageY)});const f=$m=function(e){if(c=!1,!l&&u)return u=null,a(e,"end",e.pageX,e.pageY)};document.addEventListener("mouseup",f),Mm(e,"touchcancel",function(e){if(u){l=!1;const t=u;return u=null,a(e,n?"cancel":"end",t.touches[0].pageX,t.touches[0].pageY)}})}function Dm(e,t,n){return e>t-n&&e<t+n}function Fm(e,t){return Dm(e,0,t)}function Bm(){}function jm(e,t){this._m=e,this._f=1e3*t,this._startTime=0,this._v=0}function Vm(e,t,n){this._m=e,this._k=t,this._c=n,this._solution=null,this._endPosition=0,this._startTime=0}function Wm(e,t,n){this._springX=new Vm(e,t,n),this._springY=new Vm(e,t,n),this._springScale=new Vm(e,t,n),this._startTime=0}Bm.prototype.x=function(e){return Math.sqrt(e)},jm.prototype.setV=function(e,t){const n=Math.pow(Math.pow(e,2)+Math.pow(t,2),.5);this._x_v=e,this._y_v=t,this._x_a=-this._f*this._x_v/n,this._y_a=-this._f*this._y_v/n,this._t=Math.abs(e/this._x_a)||Math.abs(t/this._y_a),this._lastDt=null,this._startTime=(new Date).getTime()},jm.prototype.setS=function(e,t){this._x_s=e,this._y_s=t},jm.prototype.s=function(e){void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),e>this._t&&(e=this._t,this._lastDt=e);let t=this._x_v*e+.5*this._x_a*Math.pow(e,2)+this._x_s,n=this._y_v*e+.5*this._y_a*Math.pow(e,2)+this._y_s;return(this._x_a>0&&t<this._endPositionX||this._x_a<0&&t>this._endPositionX)&&(t=this._endPositionX),(this._y_a>0&&n<this._endPositionY||this._y_a<0&&n>this._endPositionY)&&(n=this._endPositionY),{x:t,y:n}},jm.prototype.ds=function(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),e>this._t&&(e=this._t),{dx:this._x_v+this._x_a*e,dy:this._y_v+this._y_a*e}},jm.prototype.delta=function(){return{x:-1.5*Math.pow(this._x_v,2)/this._x_a||0,y:-1.5*Math.pow(this._y_v,2)/this._y_a||0}},jm.prototype.dt=function(){return-this._x_v/this._x_a},jm.prototype.done=function(){const e=Dm(this.s().x,this._endPositionX)||Dm(this.s().y,this._endPositionY)||this._lastDt===this._t;return this._lastDt=null,e},jm.prototype.setEnd=function(e,t){this._endPositionX=e,this._endPositionY=t},jm.prototype.reconfigure=function(e,t){this._m=e,this._f=1e3*t},Vm.prototype._solve=function(e,t){const n=this._c,o=this._m,r=this._k,i=n*n-4*o*r;if(0===i){const r=-n/(2*o),i=e,s=t/(r*e);return{x:function(e){return(i+s*e)*Math.pow(Math.E,r*e)},dx:function(e){const t=Math.pow(Math.E,r*e);return r*(i+s*e)*t+s*t}}}if(i>0){const r=(-n-Math.sqrt(i))/(2*o),s=(-n+Math.sqrt(i))/(2*o),a=(t-r*e)/(s-r),l=e-a;return{x:function(e){let t,n;return e===this._t&&(t=this._powER1T,n=this._powER2T),this._t=e,t||(t=this._powER1T=Math.pow(Math.E,r*e)),n||(n=this._powER2T=Math.pow(Math.E,s*e)),l*t+a*n},dx:function(e){let t,n;return e===this._t&&(t=this._powER1T,n=this._powER2T),this._t=e,t||(t=this._powER1T=Math.pow(Math.E,r*e)),n||(n=this._powER2T=Math.pow(Math.E,s*e)),l*r*t+a*s*n}}}const s=Math.sqrt(4*o*r-n*n)/(2*o),a=-n/2*o,l=e,c=(t-a*e)/s;return{x:function(e){return Math.pow(Math.E,a*e)*(l*Math.cos(s*e)+c*Math.sin(s*e))},dx:function(e){const t=Math.pow(Math.E,a*e),n=Math.cos(s*e),o=Math.sin(s*e);return t*(c*s*n-l*s*o)+a*t*(c*o+l*n)}}},Vm.prototype.x=function(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),this._solution?this._endPosition+this._solution.x(e):0},Vm.prototype.dx=function(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),this._solution?this._solution.dx(e):0},Vm.prototype.setEnd=function(e,t,n){if(n||(n=(new Date).getTime()),e!==this._endPosition||!Fm(t,.1)){t=t||0;let o=this._endPosition;this._solution&&(Fm(t,.1)&&(t=this._solution.dx((n-this._startTime)/1e3)),o=this._solution.x((n-this._startTime)/1e3),Fm(t,.1)&&(t=0),Fm(o,.1)&&(o=0),o+=this._endPosition),this._solution&&Fm(o-e,.1)&&Fm(t,.1)||(this._endPosition=e,this._solution=this._solve(o-this._endPosition,t),this._startTime=n)}},Vm.prototype.snap=function(e){this._startTime=(new Date).getTime(),this._endPosition=e,this._solution={x:function(){return 0},dx:function(){return 0}}},Vm.prototype.done=function(e){return e||(e=(new Date).getTime()),Dm(this.x(),this._endPosition,.1)&&Fm(this.dx(),.1)},Vm.prototype.reconfigure=function(e,t,n){this._m=e,this._k=t,this._c=n,this.done()||(this._solution=this._solve(this.x()-this._endPosition,this.dx()),this._startTime=(new Date).getTime())},Vm.prototype.springConstant=function(){return this._k},Vm.prototype.damping=function(){return this._c},Vm.prototype.configuration=function(){return[{label:"Spring Constant",read:this.springConstant.bind(this),write:function(e,t){e.reconfigure(1,t,e.damping())}.bind(this,this),min:100,max:1e3},{label:"Damping",read:this.damping.bind(this),write:function(e,t){e.reconfigure(1,e.springConstant(),t)}.bind(this,this),min:1,max:500}]},Wm.prototype.setEnd=function(e,t,n,o){const r=(new Date).getTime();this._springX.setEnd(e,o,r),this._springY.setEnd(t,o,r),this._springScale.setEnd(n,o,r),this._startTime=r},Wm.prototype.x=function(){const e=((new Date).getTime()-this._startTime)/1e3;return{x:this._springX.x(e),y:this._springY.x(e),scale:this._springScale.x(e)}},Wm.prototype.done=function(){const e=(new Date).getTime();return this._springX.done(e)&&this._springY.done(e)&&this._springScale.done(e)},Wm.prototype.reconfigure=function(e,t,n){this._springX.reconfigure(e,t,n),this._springY.reconfigure(e,t,n),this._springScale.reconfigure(e,t,n)};function Hm(e,t){return+((1e3*e-1e3*t)/1e3).toFixed(1)}const zm=od({name:"MovableView",props:{direction:{type:String,default:"none"},inertia:{type:[Boolean,String],default:!1},outOfBounds:{type:[Boolean,String],default:!1},x:{type:[Number,String],default:0},y:{type:[Number,String],default:0},damping:{type:[Number,String],default:20},friction:{type:[Number,String],default:2},disabled:{type:[Boolean,String],default:!1},scale:{type:[Boolean,String],default:!1},scaleMin:{type:[Number,String],default:.1},scaleMax:{type:[Number,String],default:10},scaleValue:{type:[Number,String],default:1},animation:{type:[Boolean,String],default:!0}},emits:["change","scale"],setup(e,{slots:t,emit:n}){const o=Bn(null),r=sd(o,n),{setParent:i}=function(e,t,n){const o=si("_isMounted",Bn(!1)),r=si("addMovableViewContext",()=>{}),i=si("removeMovableViewContext",()=>{});let s,a,l=Bn(1),c=Bn(1),u=Bn(!1),d=Bn(0),f=Bn(0),p=null,h=null,m=!1,g=null,v=null;const y=new Bm,b=new Bm,_={historyX:[0,0],historyY:[0,0],historyT:[0,0]},w=ps(()=>{let t=Number(e.friction);return isNaN(t)||t<=0?2:t}),x=new jm(1,w.value);Vo(()=>e.disabled,()=>{U()});const{_updateOldScale:T,_endScale:S,_setScale:k,scaleValueSync:E,_updateBoundary:C,_updateOffset:P,_updateWH:L,_scaleOffset:O,minX:I,minY:A,maxX:M,maxY:R,FAandSFACancel:$,_getLimitXY:N,_setTransform:D,_revise:F,dampingNumber:B,xMove:j,yMove:V,xSync:W,ySync:H,_STD:z}=function(e,t,n,o,r,i,s,a,l,c){const u=ps(()=>{let t=Number(e.scaleMin);return isNaN(t)?.1:t}),d=ps(()=>{let t=Number(e.scaleMax);return isNaN(t)?10:t}),f=Bn(Number(e.scaleValue)||1);Vo(f,e=>{D(e)}),Vo(u,()=>{N()}),Vo(d,()=>{N()}),Vo(()=>e.scaleValue,e=>{f.value=Number(e)||0});const{_updateBoundary:p,_updateOffset:h,_updateWH:m,_scaleOffset:g,minX:v,minY:y,maxX:b,maxY:_}=function(e,t,n){const o=si("movableAreaWidth",Bn(0)),r=si("movableAreaHeight",Bn(0)),i=si("movableAreaRootRef"),s={x:0,y:0},a={x:0,y:0},l=Bn(0),c=Bn(0),u=Bn(0),d=Bn(0),f=Bn(0),p=Bn(0);function h(){let e=0-s.x+a.x,t=o.value-l.value-s.x-a.x;u.value=Math.min(e,t),f.value=Math.max(e,t);let n=0-s.y+a.y,i=r.value-c.value-s.y-a.y;d.value=Math.min(n,i),p.value=Math.max(n,i)}function m(){s.x=Ym(e.value,i.value),s.y=Xm(e.value,i.value)}function g(o){o=o||t.value,o=n(o);let r=e.value.getBoundingClientRect();c.value=r.height/t.value,l.value=r.width/t.value;let i=c.value*o,s=l.value*o;a.x=(s-l.value)/2,a.y=(i-c.value)/2}return{_updateBoundary:h,_updateOffset:m,_updateWH:g,_scaleOffset:a,minX:u,minY:d,maxX:f,maxY:p}}(t,o,$),{FAandSFACancel:w,_getLimitXY:x,_animationTo:T,_setTransform:S,_revise:k,dampingNumber:E,xMove:C,yMove:P,xSync:L,ySync:O,_STD:I}=function(e,t,n,o,r,i,s,a,l,c,u,d,f,p){const h=ps(()=>{let e=Number(t.damping);return isNaN(e)?20:e}),m=ps(()=>"all"===t.direction||"horizontal"===t.direction),g=ps(()=>"all"===t.direction||"vertical"===t.direction),v=Bn(Km(t.x)),y=Bn(Km(t.y));Vo(()=>t.x,e=>{v.value=Km(e)}),Vo(()=>t.y,e=>{y.value=Km(e)}),Vo(v,e=>{k(e)}),Vo(y,e=>{E(e)});const b=new Wm(1,9*Math.pow(h.value,2)/40,h.value);function _(e,t){let n=!1;return e>r.value?(e=r.value,n=!0):e<s.value&&(e=s.value,n=!0),t>i.value?(t=i.value,n=!0):t<a.value&&(t=a.value,n=!0),{x:e,y:t,outOfBounds:n}}function w(){d&&d.cancel(),u&&u.cancel()}function x(e,n,r,i,s,a){w(),m.value||(e=l.value),g.value||(n=c.value),t.scale||(r=o.value);let d=_(e,n);e=d.x,n=d.y,t.animation?(b._springX._solution=null,b._springY._solution=null,b._springScale._solution=null,b._springX._endPosition=l.value,b._springY._endPosition=c.value,b._springScale._endPosition=o.value,b.setEnd(e,n,r,1),u=Gm(b,function(){let e=b.x();T(e.x,e.y,e.scale,i,s,a)},function(){u.cancel()})):T(e,n,r,i,s,a)}function T(r,i,s,a="",u,d){null!==r&&"NaN"!==r.toString()&&"number"==typeof r||(r=l.value||0),null!==i&&"NaN"!==i.toString()&&"number"==typeof i||(i=c.value||0),r=Number(r.toFixed(1)),i=Number(i.toFixed(1)),s=Number(s.toFixed(1)),l.value===r&&c.value===i||u||p("change",{},{x:Hm(r,n.x),y:Hm(i,n.y),source:a}),t.scale||(s=o.value),s=+(s=f(s)).toFixed(3),d&&s!==o.value&&p("scale",{},{x:r,y:i,scale:s});let h="translateX("+r+"px) translateY("+i+"px) translateZ(0px) scale("+s+")";e.value&&(e.value.style.transform=h,e.value.style.webkitTransform=h,l.value=r,c.value=i,o.value=s)}function S(e){let t=_(l.value,c.value),n=t.x,r=t.y,i=t.outOfBounds;return i&&x(n,r,o.value,e),i}function k(e){if(m.value){if(e+n.x===l.value)return l;u&&u.cancel(),x(e+n.x,y.value+n.y,o.value)}return e}function E(e){if(g.value){if(e+n.y===c.value)return c;u&&u.cancel(),x(v.value+n.x,e+n.y,o.value)}return e}return{FAandSFACancel:w,_getLimitXY:_,_animationTo:x,_setTransform:T,_revise:S,dampingNumber:h,xMove:m,yMove:g,xSync:v,ySync:y,_STD:b}}(t,e,g,o,b,_,v,y,s,a,l,c,$,n);function A(t,n){if(e.scale){t=$(t),m(t),p();const e=x(s.value,a.value),o=e.x,r=e.y;n?T(o,r,t,"",!0,!0):qm(function(){S(o,r,t,"",!0,!0)})}}function M(){i.value=!0}function R(e){r.value=e}function $(e){return e=Math.max(.1,u.value,e),e=Math.min(10,d.value,e)}function N(){if(!e.scale)return!1;A(o.value,!0),R(o.value)}function D(t){return!!e.scale&&(A(t=$(t),!0),R(t),t)}function F(){i.value=!1,R(o.value)}function B(e){e&&(e=r.value*e,M(),A(e))}return{_updateOldScale:R,_endScale:F,_setScale:B,scaleValueSync:f,_updateBoundary:p,_updateOffset:h,_updateWH:m,_scaleOffset:g,minX:v,minY:y,maxX:b,maxY:_,FAandSFACancel:w,_getLimitXY:x,_animationTo:T,_setTransform:S,_revise:k,dampingNumber:E,xMove:C,yMove:P,xSync:L,ySync:O,_STD:I}}(e,n,t,l,c,u,d,f,p,h);function U(){u.value||e.disabled||($(),_.historyX=[0,0],_.historyY=[0,0],_.historyT=[0,0],j.value&&(s=d.value),V.value&&(a=f.value),n.value.style.willChange="transform",g=null,v=null,m=!0)}function q(t){if(!u.value&&!e.disabled&&m){let n=d.value,o=f.value;if(null===v&&(v=Math.abs(t.detail.dx/t.detail.dy)>1?"htouchmove":"vtouchmove"),j.value&&(n=t.detail.dx+s,_.historyX.shift(),_.historyX.push(n),V.value||null!==g||(g=Math.abs(t.detail.dx/t.detail.dy)<1)),V.value&&(o=t.detail.dy+a,_.historyY.shift(),_.historyY.push(o),j.value||null!==g||(g=Math.abs(t.detail.dy/t.detail.dx)<1)),_.historyT.shift(),_.historyT.push(t.detail.timeStamp),!g){t.preventDefault();let r="touch";n<I.value?e.outOfBounds?(r="touch-out-of-bounds",n=I.value-y.x(I.value-n)):n=I.value:n>M.value&&(e.outOfBounds?(r="touch-out-of-bounds",n=M.value+y.x(n-M.value)):n=M.value),o<A.value?e.outOfBounds?(r="touch-out-of-bounds",o=A.value-b.x(A.value-o)):o=A.value:o>R.value&&(e.outOfBounds?(r="touch-out-of-bounds",o=R.value+b.x(o-R.value)):o=R.value),qm(function(){D(n,o,l.value,r)})}}}function Y(){if(!u.value&&!e.disabled&&m&&(n.value.style.willChange="auto",m=!1,!g&&!F("out-of-bounds")&&e.inertia)){const e=1e3*(_.historyX[1]-_.historyX[0])/(_.historyT[1]-_.historyT[0]),t=1e3*(_.historyY[1]-_.historyY[0])/(_.historyT[1]-_.historyT[0]),n=d.value,o=f.value;x.setV(e,t),x.setS(n,o);const r=x.delta().x,i=x.delta().y;let s=r+n,a=i+o;s<I.value?(s=I.value,a=o+(I.value-n)*i/r):s>M.value&&(s=M.value,a=o+(M.value-n)*i/r),a<A.value?(a=A.value,s=n+(A.value-o)*r/i):a>R.value&&(a=R.value,s=n+(R.value-o)*r/i),x.setEnd(s,a),h=Gm(x,function(){let e=x.s(),t=e.x,n=e.y;D(t,n,l.value,"friction")},function(){h.cancel()})}e.outOfBounds||e.inertia||$()}function X(){if(!o.value)return;$();let t=e.scale?E.value:1;P(),L(t),C();let n=N(W.value+O.x,H.value+O.y),r=n.x,i=n.y;D(r,i,t,"",!0),T(t)}return kr(()=>{Nm(n.value,e=>{switch(e.detail.state){case"start":U();break;case"move":q(e);break;case"end":Y()}}),X(),x.reconfigure(1,w.value),z.reconfigure(1,9*Math.pow(B.value,2)/40,B.value),n.value.style.transformOrigin="center";const e={rootRef:n,setParent:X,_endScale:S,_setScale:k};r(e),Lr(()=>{i(e)})}),Lr(()=>{$()}),{setParent:X}}(e,r,o);return()=>zi("uni-movable-view",{ref:o},[zi(Vh,{onResize:i},null,8,["onResize"]),t.default&&t.default()],512)}});let Um=!1;function qm(e){Um||(Um=!0,requestAnimationFrame(function(){e(),Um=!1}))}function Ym(e,t){if(e===t)return 0;let n=e.offsetLeft;return e.offsetParent?n+=Ym(e.offsetParent,t):0}function Xm(e,t){if(e===t)return 0;let n=e.offsetTop;return e.offsetParent?n+=Xm(e.offsetParent,t):0}function Gm(e,t,n){let o={id:0,cancelled:!1};return function e(t,n,o,r){if(!t||!t.cancelled){o(n);let i=n.done();i||t.cancelled||(t.id=requestAnimationFrame(e.bind(null,t,n,o,r))),i&&r&&r(n)}}(o,e,t,n),{cancel:function(e){e&&e.id&&cancelAnimationFrame(e.id),e&&(e.cancelled=!0)}.bind(null,o),model:e}}function Km(e){return/\d+[ur]px$/i.test(e)?sf(parseFloat(e)):Number(e)||0}const Jm=od({name:"PickerView",props:{value:{type:Array,default:()=>[],validator:function(e){return m(e)&&e.filter(e=>"number"==typeof e).length===e.length}},indicatorStyle:{type:String,default:""},indicatorClass:{type:String,default:""},maskStyle:{type:String,default:""},maskClass:{type:String,default:""}},emits:["change","pickstart","pickend","update:value"],setup(e,{slots:t,emit:n}){const o=Bn(null),r=Bn(null),i=sd(o,n),s=function(e){const t=Tn([...e.value]),n=Tn({value:t,height:34});return Vo(()=>e.value,(e,t)=>{n.value.length=e.length,e.forEach((e,t)=>{e!==n.value[t]&&n.value.splice(t,1,e)})}),n}(e),a=Bn(null);kr(()=>{const e=a.value;e&&(s.height=e.$el.offsetHeight)});let l=Bn([]),c=Bn([]);function u(e){let t=c.value;t=t.filter(e=>e.type!==Pi);let n=t.indexOf(e);return-1!==n?n:l.value.indexOf(e)}return ii("getPickerViewColumn",function(e){return ps({get(){const t=u(e.vnode);return s.value[t]||0},set(t){const o=u(e.vnode);if(o<0)return;if(s.value[o]!==t){s.value[o]=t;const e=s.value.map(e=>e);n("update:value",e),i("change",{},{value:e})}}})}),ii("pickerViewProps",e),ii("pickerViewState",s),()=>{const e=t.default&&t.default();{const t=Om(e);l.value=t,uo(()=>{c.value=t})}return zi("uni-picker-view",{ref:o},[zi(Vh,{ref:a,onResize:({height:e})=>s.height=e},null,8,["onResize"]),zi("div",{ref:r,class:"uni-picker-view-wrapper"},[e],512)],512)}}});class Zm{constructor(e){this._drag=e,this._dragLog=Math.log(e),this._x=0,this._v=0,this._startTime=0}set(e,t){this._x=e,this._v=t,this._startTime=(new Date).getTime()}setVelocityByEnd(e){this._v=(e-this._x)*this._dragLog/(Math.pow(this._drag,100)-1)}x(e){void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3);const t=e===this._dt&&this._powDragDt?this._powDragDt:this._powDragDt=Math.pow(this._drag,e);return this._dt=e,this._x+this._v*t/this._dragLog-this._v/this._dragLog}dx(e){void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3);const t=e===this._dt&&this._powDragDt?this._powDragDt:this._powDragDt=Math.pow(this._drag,e);return this._dt=e,this._v*t}done(){return Math.abs(this.dx())<3}reconfigure(e){const t=this.x(),n=this.dx();this._drag=e,this._dragLog=Math.log(e),this.set(t,n)}configuration(){const e=this;return[{label:"Friction",read:function(){return e._drag},write:function(t){e.reconfigure(t)},min:.001,max:.1,step:.001}]}}function Qm(e,t,n){return e>t-n&&e<t+n}function eg(e,t){return Qm(e,0,t)}class tg{constructor(e,t,n){this._m=e,this._k=t,this._c=n,this._solution=null,this._endPosition=0,this._startTime=0}_solve(e,t){const n=this._c,o=this._m,r=this._k,i=n*n-4*o*r;if(0===i){const r=-n/(2*o),i=e,s=t/(r*e);return{x:function(e){return(i+s*e)*Math.pow(Math.E,r*e)},dx:function(e){const t=Math.pow(Math.E,r*e);return r*(i+s*e)*t+s*t}}}if(i>0){const r=(-n-Math.sqrt(i))/(2*o),s=(-n+Math.sqrt(i))/(2*o),a=(t-r*e)/(s-r),l=e-a;return{x:function(e){let t,n;return e===this._t&&(t=this._powER1T,n=this._powER2T),this._t=e,t||(t=this._powER1T=Math.pow(Math.E,r*e)),n||(n=this._powER2T=Math.pow(Math.E,s*e)),l*t+a*n},dx:function(e){let t,n;return e===this._t&&(t=this._powER1T,n=this._powER2T),this._t=e,t||(t=this._powER1T=Math.pow(Math.E,r*e)),n||(n=this._powER2T=Math.pow(Math.E,s*e)),l*r*t+a*s*n}}}const s=Math.sqrt(4*o*r-n*n)/(2*o),a=-n/2*o,l=e,c=(t-a*e)/s;return{x:function(e){return Math.pow(Math.E,a*e)*(l*Math.cos(s*e)+c*Math.sin(s*e))},dx:function(e){const t=Math.pow(Math.E,a*e),n=Math.cos(s*e),o=Math.sin(s*e);return t*(c*s*n-l*s*o)+a*t*(c*o+l*n)}}}x(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),this._solution?this._endPosition+this._solution.x(e):0}dx(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),this._solution?this._solution.dx(e):0}setEnd(e,t,n){if(n||(n=(new Date).getTime()),e!==this._endPosition||!eg(t,.4)){t=t||0;let o=this._endPosition;this._solution&&(eg(t,.4)&&(t=this._solution.dx((n-this._startTime)/1e3)),o=this._solution.x((n-this._startTime)/1e3),eg(t,.4)&&(t=0),eg(o,.4)&&(o=0),o+=this._endPosition),this._solution&&eg(o-e,.4)&&eg(t,.4)||(this._endPosition=e,this._solution=this._solve(o-this._endPosition,t),this._startTime=n)}}snap(e){this._startTime=(new Date).getTime(),this._endPosition=e,this._solution={x:function(){return 0},dx:function(){return 0}}}done(e){return e||(e=(new Date).getTime()),Qm(this.x(),this._endPosition,.4)&&eg(this.dx(),.4)}reconfigure(e,t,n){this._m=e,this._k=t,this._c=n,this.done()||(this._solution=this._solve(this.x()-this._endPosition,this.dx()),this._startTime=(new Date).getTime())}springConstant(){return this._k}damping(){return this._c}configuration(){return[{label:"Spring Constant",read:this.springConstant.bind(this),write:function(e,t){e.reconfigure(1,t,e.damping())}.bind(this,this),min:100,max:1e3},{label:"Damping",read:this.damping.bind(this),write:function(e,t){e.reconfigure(1,e.springConstant(),t)}.bind(this,this),min:1,max:500}]}}class ng{constructor(e,t,n){this._extent=e,this._friction=t||new Zm(.01),this._spring=n||new tg(1,90,20),this._startTime=0,this._springing=!1,this._springOffset=0}snap(e,t){this._springOffset=0,this._springing=!0,this._spring.snap(e),this._spring.setEnd(t)}set(e,t){this._friction.set(e,t),e>0&&t>=0?(this._springOffset=0,this._springing=!0,this._spring.snap(e),this._spring.setEnd(0)):e<-this._extent&&t<=0?(this._springOffset=0,this._springing=!0,this._spring.snap(e),this._spring.setEnd(-this._extent)):this._springing=!1,this._startTime=(new Date).getTime()}x(e){if(!this._startTime)return 0;if(e||(e=((new Date).getTime()-this._startTime)/1e3),this._springing)return this._spring.x()+this._springOffset;let t=this._friction.x(e),n=this.dx(e);return(t>0&&n>=0||t<-this._extent&&n<=0)&&(this._springing=!0,this._spring.setEnd(0,n),t<-this._extent?this._springOffset=-this._extent:this._springOffset=0,t=this._spring.x()+this._springOffset),t}dx(e){let t;return t=this._lastTime===e?this._lastDx:this._springing?this._spring.dx(e):this._friction.dx(e),this._lastTime=e,this._lastDx=t,t}done(){return this._springing?this._spring.done():this._friction.done()}setVelocityByEnd(e){this._friction.setVelocityByEnd(e)}configuration(){const e=this._friction.configuration();return e.push.apply(e,this._spring.configuration()),e}}class og{constructor(e,t){t=t||{},this._element=e,this._options=t,this._enableSnap=t.enableSnap||!1,this._itemSize=t.itemSize||0,this._enableX=t.enableX||!1,this._enableY=t.enableY||!1,this._shouldDispatchScrollEvent=!!t.onScroll,this._enableX?(this._extent=(t.scrollWidth||this._element.offsetWidth)-this._element.parentElement.offsetWidth,this._scrollWidth=t.scrollWidth):(this._extent=(t.scrollHeight||this._element.offsetHeight)-this._element.parentElement.offsetHeight,this._scrollHeight=t.scrollHeight),this._position=0,this._scroll=new ng(this._extent,t.friction,t.spring),this._onTransitionEnd=this.onTransitionEnd.bind(this),this.updatePosition()}onTouchStart(){this._startPosition=this._position,this._lastChangePos=this._startPosition,this._startPosition>0?this._startPosition/=.5:this._startPosition<-this._extent&&(this._startPosition=(this._startPosition+this._extent)/.5-this._extent),this._animation&&(this._animation.cancel(),this._scrolling=!1),this.updatePosition()}onTouchMove(e,t){let n=this._startPosition;this._enableX?n+=e:this._enableY&&(n+=t),n>0?n*=.5:n<-this._extent&&(n=.5*(n+this._extent)-this._extent),this._position=n,this.updatePosition(),this.dispatchScroll()}onTouchEnd(e,t,n){if(this._enableSnap&&this._position>-this._extent&&this._position<0){if(this._enableY&&(Math.abs(t)<this._itemSize&&Math.abs(n.y)<300||Math.abs(n.y)<150))return void this.snap();if(this._enableX&&(Math.abs(e)<this._itemSize&&Math.abs(n.x)<300||Math.abs(n.x)<150))return void this.snap()}let o;if(this._enableX?this._scroll.set(this._position,n.x):this._enableY&&this._scroll.set(this._position,n.y),this._enableSnap){const e=this._scroll._friction.x(100),t=e%this._itemSize;o=Math.abs(t)>this._itemSize/2?e-(this._itemSize-Math.abs(t)):e-t,o<=0&&o>=-this._extent&&this._scroll.setVelocityByEnd(o)}this._lastTime=Date.now(),this._lastDelay=0,this._scrolling=!0,this._lastChangePos=this._position,this._lastIdx=Math.floor(Math.abs(this._position/this._itemSize)),this._animation=function(e,t,n){const o={id:0,cancelled:!1};return function e(t,n,o,r){if(!t||!t.cancelled){o(n);const i=n.done();i||t.cancelled||(t.id=requestAnimationFrame(e.bind(null,t,n,o,r))),i&&r&&r(n)}}(o,e,t,n),{cancel:function(e){e&&e.id&&cancelAnimationFrame(e.id),e&&(e.cancelled=!0)}.bind(null,o),model:e}}(this._scroll,()=>{const e=Date.now(),t=(e-this._scroll._startTime)/1e3,n=this._scroll.x(t);this._position=n,this.updatePosition();const o=this._scroll.dx(t);this._shouldDispatchScrollEvent&&e-this._lastTime>this._lastDelay&&(this.dispatchScroll(),this._lastDelay=Math.abs(2e3/o),this._lastTime=e)},()=>{this._enableSnap&&(o<=0&&o>=-this._extent&&(this._position=o,this.updatePosition()),y(this._options.onSnap)&&this._options.onSnap(Math.floor(Math.abs(this._position)/this._itemSize))),this._shouldDispatchScrollEvent&&this.dispatchScroll(),this._scrolling=!1})}onTransitionEnd(){this._element.style.webkitTransition="",this._element.style.transition="",this._element.removeEventListener("transitionend",this._onTransitionEnd),this._snapping&&(this._snapping=!1),this.dispatchScroll()}snap(){const e=this._itemSize,t=this._position%e,n=Math.abs(t)>this._itemSize/2?this._position-(e-Math.abs(t)):this._position-t;this._position!==n&&(this._snapping=!0,this.scrollTo(-n),y(this._options.onSnap)&&this._options.onSnap(Math.floor(Math.abs(this._position)/this._itemSize)))}scrollTo(e,t){this._animation&&(this._animation.cancel(),this._scrolling=!1),"number"==typeof e&&(this._position=-e),this._position<-this._extent?this._position=-this._extent:this._position>0&&(this._position=0);const n="transform "+(t||.2)+"s ease-out";this._element.style.webkitTransition="-webkit-"+n,this._element.style.transition=n,this.updatePosition(),this._element.addEventListener("transitionend",this._onTransitionEnd)}dispatchScroll(){if(y(this._options.onScroll)&&Math.round(Number(this._lastPos))!==Math.round(this._position)){this._lastPos=this._position;const e={target:{scrollLeft:this._enableX?-this._position:0,scrollTop:this._enableY?-this._position:0,scrollHeight:this._scrollHeight||this._element.offsetHeight,scrollWidth:this._scrollWidth||this._element.offsetWidth,offsetHeight:this._element.parentElement.offsetHeight,offsetWidth:this._element.parentElement.offsetWidth}};this._options.onScroll(e)}}update(e,t,n){let o=0;const r=this._position;this._enableX?(o=this._element.childNodes.length?(t||this._element.offsetWidth)-this._element.parentElement.offsetWidth:0,this._scrollWidth=t):(o=this._element.childNodes.length?(t||this._element.offsetHeight)-this._element.parentElement.offsetHeight:0,this._scrollHeight=t),"number"==typeof e&&(this._position=-e),this._position<-o?this._position=-o:this._position>0&&(this._position=0),this._itemSize=n||this._itemSize,this.updatePosition(),r!==this._position&&(this.dispatchScroll(),y(this._options.onSnap)&&this._options.onSnap(Math.floor(Math.abs(this._position)/this._itemSize))),this._extent=o,this._scroll._extent=o}updatePosition(){let e="";this._enableX?e="translateX("+this._position+"px) translateZ(0)":this._enableY&&(e="translateY("+this._position+"px) translateZ(0)"),this._element.style.webkitTransform=e,this._element.style.transform=e}isScrolling(){return this._scrolling||this._snapping}}const rg=od({name:"PickerViewColumn",setup(e,{slots:t,emit:n}){const o=Bn(null),r=Bn(null),i=si("getPickerViewColumn"),s=ns(),a=i?i(s):Bn(0),l=si("pickerViewProps"),c=si("pickerViewState"),u=Bn(34),d=Bn(null);kr(()=>{const e=d.value;u.value=e.$el.offsetHeight});const f=ps(()=>(c.height-u.value)/2),{state:p}=hm();let h;const m=Tn({current:a.value,length:0});let g;function v(){h&&!g&&(g=!0,uo(()=>{g=!1;let e=Math.min(m.current,m.length-1);e=Math.max(e,0),h.update(e*u.value,void 0,u.value)}))}Vo(()=>a.value,e=>{e!==m.current&&(m.current=e,v())}),Vo(()=>m.current,e=>a.value=e),Vo([()=>u.value,()=>m.length,()=>c.height],v);let y=0;function b(e){const t=y+e.deltaY;if(Math.abs(t)>10){y=0;let e=Math.min(m.current+(t<0?-1:1),m.length-1);m.current=e=Math.max(e,0),h.scrollTo(e*u.value)}else y=t;e.preventDefault()}function _({clientY:e}){const t=o.value;if(!h.isScrolling()){const n=e-t.getBoundingClientRect().top-c.height/2,o=u.value/2;if(!(Math.abs(n)<=o)){const e=Math.ceil((Math.abs(n)-o)/u.value),t=n<0?-e:e;let r=Math.min(m.current+t,m.length-1);m.current=r=Math.max(r,0),h.scrollTo(r*u.value)}}}const w=()=>{const e=o.value,t=r.value,{scroller:n,handleTouchStart:i,handleTouchMove:s,handleTouchEnd:a}=function(e,t){const n={trackingID:-1,maxDy:0,maxDx:0},o=new og(e,t);function r(e){const t=e,o=e;return"move"===t.detail.state||"end"===t.detail.state?{x:t.detail.dx,y:t.detail.dy}:{x:o.screenX-n.x,y:o.screenY-n.y}}return{scroller:o,handleTouchStart:function(e){const t=e,r=e;"start"===t.detail.state?(n.trackingID="touch",n.x=t.detail.x,n.y=t.detail.y):(n.trackingID="mouse",n.x=r.screenX,n.y=r.screenY),n.maxDx=0,n.maxDy=0,n.historyX=[0],n.historyY=[0],n.historyTime=[t.detail.timeStamp||r.timeStamp],n.listener=o,o.onTouchStart&&o.onTouchStart(),("boolean"!=typeof e.cancelable||e.cancelable)&&e.preventDefault()},handleTouchMove:function(e){const t=e,o=e;if(-1!==n.trackingID){("boolean"!=typeof e.cancelable||e.cancelable)&&e.preventDefault();const i=r(e);if(i){for(n.maxDy=Math.max(n.maxDy,Math.abs(i.y)),n.maxDx=Math.max(n.maxDx,Math.abs(i.x)),n.historyX.push(i.x),n.historyY.push(i.y),n.historyTime.push(t.detail.timeStamp||o.timeStamp);n.historyTime.length>10;)n.historyTime.shift(),n.historyX.shift(),n.historyY.shift();n.listener&&n.listener.onTouchMove&&n.listener.onTouchMove(i.x,i.y)}}},handleTouchEnd:function(e){if(-1!==n.trackingID){e.preventDefault();const t=r(e);if(t){const e=n.listener;n.trackingID=-1,n.listener=null;const o={x:0,y:0};if(n.historyTime.length>2)for(let t=n.historyTime.length-1,r=n.historyTime[t],i=n.historyX[t],s=n.historyY[t];t>0;){t--;const e=r-n.historyTime[t];if(e>30&&e<50){o.x=(i-n.historyX[t])/(e/1e3),o.y=(s-n.historyY[t])/(e/1e3);break}}n.historyTime=[],n.historyX=[],n.historyY=[],e&&e.onTouchEnd&&e.onTouchEnd(t.x,t.y,o)}}}}}(t,{enableY:!0,enableX:!1,enableSnap:!0,itemSize:u.value,friction:new Zm(1e-4),spring:new tg(2,90,20),onSnap:e=>{isNaN(e)||e===m.current||(m.current=e)}});h=n,Nm(e,e=>{switch(e.detail.state){case"start":i(e);break;case"move":s(e),e.stopPropagation();break;case"end":case"cancel":a(e)}},!0),function(e){let t=0,n=0;e.addEventListener("touchstart",e=>{const o=e.changedTouches[0];t=o.clientX,n=o.clientY}),e.addEventListener("touchend",e=>{const o=e.changedTouches[0];if(Math.abs(o.clientX-t)<20&&Math.abs(o.clientY-n)<20){const t={bubbles:!0,cancelable:!0,target:e.target,currentTarget:e.currentTarget},n=new CustomEvent("click",t);["screenX","screenY","clientX","clientY","pageX","pageY"].forEach(e=>{n[e]=o[e]}),e.target.dispatchEvent(n)}})}(e),v()};return kr(w),()=>{const e=t.default&&t.default();m.length=Om(e).length;const n=`${f.value}px 0`;return zi("uni-picker-view-column",{ref:o},[zi("div",{onWheel:b,onClick:_,class:"uni-picker-view-group"},[zi("div",Ji(p.attrs,{class:["uni-picker-view-mask",l.maskClass],style:`background-size: 100% ${f.value}px;${l.maskStyle}`}),null,16),zi("div",Ji(p.attrs,{class:["uni-picker-view-indicator",l.indicatorClass],style:l.indicatorStyle}),[zi(Vh,{ref:d,onResize:({height:e})=>u.value=e},null,8,["onResize"])],16),zi("div",{ref:r,class:["uni-picker-view-content"],style:{padding:n,"--picker-view-column-indicator-height":`${u.value}px`}},[e],4)],40,["onWheel","onClick"])],512)}}}),ig={a:"",abbr:"",address:"",article:"",aside:"",b:"",bdi:"",bdo:["dir"],big:"",blockquote:"",br:"",caption:"",center:"",cite:"",code:"",col:["span","width"],colgroup:["span","width"],dd:"",del:"",div:"",dl:"",dt:"",em:"",fieldset:"",font:"",footer:"",h1:"",h2:"",h3:"",h4:"",h5:"",h6:"",header:"",hr:"",i:"",img:["alt","src","height","width"],ins:"",label:"",legend:"",li:"",mark:"",nav:"",ol:["start","type"],p:"",pre:"",q:"",rt:"",ruby:"",s:"",section:"",small:"",span:"",strong:"",sub:"",sup:"",table:["width"],tbody:"",td:["colspan","height","rowspan","width"],tfoot:"",th:["colspan","height","rowspan","width"],thead:"",tr:["colspan","height","rowspan","width"],tt:"",u:"",ul:""},sg={amp:"&",gt:">",lt:"<",nbsp:" ",quot:'"',apos:"'",ldquo:"“",rdquo:"”",yen:"￥",radic:"√",lceil:"⌈",rceil:"⌉",lfloor:"⌊",rfloor:"⌋",hellip:"…"};const ag=(e,t,n)=>!n||m(n)&&!n.length?[]:n.map(n=>{var o;if(k(n)){if(!h(n,"type")||"node"===n.type){let r={[e]:""};const i=null==(o=n.name)?void 0:o.toLowerCase();if(!h(ig,i))return;return function(e,t){if(k(t))for(const n in t)if(h(t,n)){const o=t[n];"img"===e&&"src"===n&&(t[n]=bh(o))}}(i,n.attrs),r=d(r,function(e,t){if(["a","img"].includes(e.name)&&t)return{onClick:n=>{t(n,{node:e}),n.stopPropagation(),n.preventDefault(),n.returnValue=!1}}}(n,t),n.attrs),ms(n.name,r,ag(e,t,n.children))}return"text"===n.type&&b(n.text)&&""!==n.text?qi((n.text||"").replace(/&(([a-zA-Z]+)|(#x{0,1}[\da-zA-Z]+));/gi,function(e,t){return h(sg,t)&&sg[t]?sg[t]:/^#[0-9]{1,4}$/.test(t)?String.fromCharCode(t.slice(1)):/^#x[0-9a-f]{1,4}$/i.test(t)?String.fromCharCode(0+t.slice(1)):e})):void 0}});function lg(e){e=function(e){return e.replace(/<\?xml.*\?>\n/,"").replace(/<!doctype.*>\n/,"").replace(/<!DOCTYPE.*>\n/,"")}(e);const t=[],n={children:[]};return function(e,t){var n,o,r,i=[],s=e;for(i.last=function(){return this[this.length-1]};e;){if(o=!0,i.last()&&Qh[i.last()])e=e.replace(new RegExp("([\\s\\S]*?)</"+i.last()+"[^>]*>"),function(e,n){return n=n.replace(/<!--([\s\S]*?)-->|<!\[CDATA\[([\s\S]*?)]]>/g,"$1$2"),t.chars&&t.chars(n),""}),c("",i.last());else if(0==e.indexOf("\x3c!--")?(n=e.indexOf("--\x3e"))>=0&&(t.comment&&t.comment(e.substring(4,n)),e=e.substring(n+3),o=!1):0==e.indexOf("</")?(r=e.match(qh))&&(e=e.substring(r[0].length),r[0].replace(qh,c),o=!1):0==e.indexOf("<")&&(r=e.match(Uh))&&(e=e.substring(r[0].length),r[0].replace(Uh,l),o=!1),o){var a=(n=e.indexOf("<"))<0?e:e.substring(0,n);e=n<0?"":e.substring(n),t.chars&&t.chars(a)}if(e==s)throw"Parse Error: "+e;s=e}function l(e,n,o,r){if(n=n.toLowerCase(),Gh[n])for(;i.last()&&Kh[i.last()];)c("",i.last());if(Jh[n]&&i.last()==n&&c("",n),(r=Xh[n]||!!r)||i.push(n),t.start){var s=[];o.replace(Yh,function(e,t){var n=arguments[2]?arguments[2]:arguments[3]?arguments[3]:arguments[4]?arguments[4]:Zh[t]?t:"";s.push({name:t,value:n,escaped:n.replace(/(^|[^\\])"/g,'$1\\"')})}),t.start&&t.start(n,s,r)}}function c(e,n){if(n)for(o=i.length-1;o>=0&&i[o]!=n;o--);else var o=0;if(o>=0){for(var r=i.length-1;r>=o;r--)t.end&&t.end(i[r]);i.length=o}}c()}(e,{start:function(e,o,r){const i={name:e};if(0!==o.length&&(i.attrs=function(e){return e.reduce(function(e,t){let n=t.value;const o=t.name;return n.match(/ /)&&-1===["style","src"].indexOf(o)&&(n=n.split(" ")),e[o]?Array.isArray(e[o])?e[o].push(n):e[o]=[e[o],n]:e[o]=n,e},{})}(o)),r){const e=t[0]||n;e.children||(e.children=[]),e.children.push(i)}else t.unshift(i)},end:function(e){const o=t.shift();if(o.name,0===t.length)n.children.push(o);else{const e=t[0];e.children||(e.children=[]),e.children.push(o)}},chars:function(e){const o={type:"text",text:e};if(0===t.length)n.children.push(o);else{const e=t[0];e.children||(e.children=[]),e.children.push(o)}},comment:function(e){const n={node:"comment",text:e},o=t[0];o&&(o.children||(o.children=[]),o.children.push(n))}}),n.children}const cg=od({name:"RichText",compatConfig:{MODE:3},props:{nodes:{type:[Array,String],default:function(){return[]}}},emits:["itemclick"],setup(e,{emit:t}){const n=ns(),o=n&&n.vnode.scopeId||"",r=Bn(null),i=Bn([]),s=sd(r,t);function a(e,t={}){s("itemclick",e,t)}return Vo(()=>e.nodes,function(){let t=e.nodes;b(t)&&(t=lg(e.nodes)),i.value=ag(o,a,t)},{immediate:!0}),()=>ms("uni-rich-text",{ref:r},ms("div",{},i.value))}}),ug=od({name:"Refresher",props:{refreshState:{type:String,default:""},refresherHeight:{type:Number,default:0},refresherThreshold:{type:Number,default:45},refresherDefaultStyle:{type:String,default:"black"},refresherBackground:{type:String,default:"#fff"}},setup(e,{slots:t}){const n=Bn(null),o=ps(()=>{const t={backgroundColor:e.refresherBackground};switch(e.refreshState){case"pulling":t.height=e.refresherHeight+"px";break;case"refreshing":t.height=e.refresherThreshold+"px",t.transition="height 0.3s";break;case"":case"refresherabort":case"restore":t.height="0px",t.transition="height 0.3s"}return t}),r=ps(()=>{const t=e.refresherHeight/e.refresherThreshold;return 360*(t>1?1:t)});return()=>{const{refreshState:i,refresherDefaultStyle:s,refresherThreshold:a}=e;return zi("div",{ref:n,style:o.value,class:"uni-scroll-view-refresher"},["none"!==s?zi("div",{class:"uni-scroll-view-refresh"},[zi("div",{class:"uni-scroll-view-refresh-inner"},["pulling"==i?zi("svg",{key:"refresh__icon",style:{transform:"rotate("+r.value+"deg)"},fill:"#2BD009",class:"uni-scroll-view-refresh__icon",width:"24",height:"24",viewBox:"0 0 24 24"},[zi("path",{d:"M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"},null),zi("path",{d:"M0 0h24v24H0z",fill:"none"},null)],4):null,"refreshing"==i?zi("svg",{key:"refresh__spinner",class:"uni-scroll-view-refresh__spinner",width:"24",height:"24",viewBox:"25 25 50 50"},[zi("circle",{cx:"50",cy:"50",r:"20",fill:"none",style:"color: #2bd009","stroke-width":"3"},null)]):null])]):null,"none"===s?zi("div",{class:"uni-scroll-view-refresher-container",style:{height:`${a}px`}},[t.default&&t.default()]):null],4)}}}),dg=Ke(!0),fg=od({name:"ScrollView",compatConfig:{MODE:3},props:{direction:{type:[String],default:"vertical"},scrollX:{type:[Boolean,String],default:!1},scrollY:{type:[Boolean,String],default:!1},showScrollbar:{type:[Boolean,String],default:!0},upperThreshold:{type:[Number,String],default:50},lowerThreshold:{type:[Number,String],default:50},scrollTop:{type:[Number,String],default:0},scrollLeft:{type:[Number,String],default:0},scrollIntoView:{type:String,default:""},scrollWithAnimation:{type:[Boolean,String],default:!1},enableBackToTop:{type:[Boolean,String],default:!1},refresherEnabled:{type:[Boolean,String],default:!1},refresherThreshold:{type:Number,default:45},refresherDefaultStyle:{type:String,default:"black"},refresherBackground:{type:String,default:"#fff"},refresherTriggered:{type:[Boolean,String],default:!1}},emits:["scroll","scrolltoupper","scrolltolower","refresherrefresh","refresherrestore","refresherpulling","refresherabort","update:refresherTriggered"],setup(e,{emit:t,slots:n,expose:o}){const r=Bn(null),i=Bn(null),s=Bn(null),a=Bn(null),l=sd(r,t),{state:c,scrollTopNumber:u,scrollLeftNumber:d}=function(e){const t=ps(()=>Number(e.scrollTop)||0),n=ps(()=>Number(e.scrollLeft)||0),o=Tn({lastScrollTop:t.value,lastScrollLeft:n.value,lastScrollToUpperTime:0,lastScrollToLowerTime:0,refresherHeight:0,refreshState:""});return{state:o,scrollTopNumber:t,scrollLeftNumber:n}}(e),{realScrollX:f,realScrollY:p}=function(e,t,n,o,r,i,s,a,l){let c=!1,u=0,d=!1,f=()=>{};const p=ps(()=>e.scrollX),h=ps(()=>e.scrollY),m=ps(()=>{let t=Number(e.upperThreshold);return isNaN(t)?50:t}),g=ps(()=>{let t=Number(e.lowerThreshold);return isNaN(t)?50:t});function v(e,t){const n=s.value;let o=0,r="";if(e<0?e=0:"x"===t&&e>n.scrollWidth-n.offsetWidth?e=n.scrollWidth-n.offsetWidth:"y"===t&&e>n.scrollHeight-n.offsetHeight&&(e=n.scrollHeight-n.offsetHeight),"x"===t?o=n.scrollLeft-e:"y"===t&&(o=n.scrollTop-e),0===o)return;let i=a.value;i.style.transition="transform .3s ease-out",i.style.webkitTransition="-webkit-transform .3s ease-out","x"===t?r="translateX("+o+"px) translateZ(0)":"y"===t&&(r="translateY("+o+"px) translateZ(0)"),i.removeEventListener("transitionend",f),i.removeEventListener("webkitTransitionEnd",f),f=()=>x(e,t),i.addEventListener("transitionend",f),i.addEventListener("webkitTransitionEnd",f),"x"===t?n.style.overflowX="hidden":"y"===t&&(n.style.overflowY="hidden"),i.style.transform=r,i.style.webkitTransform=r}function y(e){const n=e.target;r("scroll",e,{scrollLeft:n.scrollLeft,scrollTop:n.scrollTop,scrollHeight:n.scrollHeight,scrollWidth:n.scrollWidth,deltaX:t.lastScrollLeft-n.scrollLeft,deltaY:t.lastScrollTop-n.scrollTop}),h.value&&(n.scrollTop<=m.value&&t.lastScrollTop-n.scrollTop>0&&e.timeStamp-t.lastScrollToUpperTime>200&&(r("scrolltoupper",e,{direction:"top"}),t.lastScrollToUpperTime=e.timeStamp),n.scrollTop+n.offsetHeight+g.value>=n.scrollHeight&&t.lastScrollTop-n.scrollTop<0&&e.timeStamp-t.lastScrollToLowerTime>200&&(r("scrolltolower",e,{direction:"bottom"}),t.lastScrollToLowerTime=e.timeStamp)),p.value&&(n.scrollLeft<=m.value&&t.lastScrollLeft-n.scrollLeft>0&&e.timeStamp-t.lastScrollToUpperTime>200&&(r("scrolltoupper",e,{direction:"left"}),t.lastScrollToUpperTime=e.timeStamp),n.scrollLeft+n.offsetWidth+g.value>=n.scrollWidth&&t.lastScrollLeft-n.scrollLeft<0&&e.timeStamp-t.lastScrollToLowerTime>200&&(r("scrolltolower",e,{direction:"right"}),t.lastScrollToLowerTime=e.timeStamp)),t.lastScrollTop=n.scrollTop,t.lastScrollLeft=n.scrollLeft}function b(t){h.value&&(e.scrollWithAnimation?v(t,"y"):s.value.scrollTop=t)}function _(t){p.value&&(e.scrollWithAnimation?v(t,"x"):s.value.scrollLeft=t)}function w(t){if(t){if(!/^[_a-zA-Z][-_a-zA-Z0-9:]*$/.test(t))return;let n=i.value.querySelector("#"+t);if(n){let t=s.value.getBoundingClientRect(),o=n.getBoundingClientRect();if(p.value){let n=o.left-t.left,r=s.value.scrollLeft+n;e.scrollWithAnimation?v(r,"x"):s.value.scrollLeft=r}if(h.value){let n=o.top-t.top,r=s.value.scrollTop+n;e.scrollWithAnimation?v(r,"y"):s.value.scrollTop=r}}}}function x(e,t){a.value.style.transition="",a.value.style.webkitTransition="",a.value.style.transform="",a.value.style.webkitTransform="";let n=s.value;"x"===t?(n.style.overflowX=p.value?"auto":"hidden",n.scrollLeft=e):"y"===t&&(n.style.overflowY=h.value?"auto":"hidden",n.scrollTop=e),a.value.removeEventListener("transitionend",f),a.value.removeEventListener("webkitTransitionEnd",f)}function T(n){if(e.refresherEnabled){switch(n){case"refreshing":t.refresherHeight=e.refresherThreshold,c||(c=!0,r("refresherpulling",{},{deltaY:t.refresherHeight,dy:t.refresherHeight}),r("refresherrefresh",{},{dy:k.y-S.y}),l("update:refresherTriggered",!0));break;case"restore":case"refresherabort":c=!1,t.refresherHeight=u=0,"restore"===n&&(d=!1,r("refresherrestore",{},{dy:k.y-S.y})),"refresherabort"===n&&d&&(d=!1,r("refresherabort",{},{dy:k.y-S.y}))}t.refreshState=n}}let S={x:0,y:0},k={y:e.refresherThreshold};return kr(()=>{uo(()=>{b(n.value),_(o.value)}),w(e.scrollIntoView);let i=function(e){e.preventDefault(),e.stopPropagation(),y(e)},a=null,l=function(n){if(null===S)return;let o=n.touches[0].pageX,i=n.touches[0].pageY,l=s.value;if(Math.abs(o-S.x)>Math.abs(i-S.y))if(p.value){if(0===l.scrollLeft&&o>S.x)return void(a=!1);if(l.scrollWidth===l.offsetWidth+l.scrollLeft&&o<S.x)return void(a=!1);a=!0}else a=!1;else if(h.value)if(0===l.scrollTop&&i>S.y)a=!1,e.refresherEnabled&&!1!==n.cancelable&&n.preventDefault();else{if(l.scrollHeight===l.offsetHeight+l.scrollTop&&i<S.y)return void(a=!1);a=!0}else a=!1;if(a&&n.stopPropagation(),0===l.scrollTop&&1===n.touches.length&&T("pulling"),e.refresherEnabled&&"pulling"===t.refreshState){const o=i-S.y;0===u&&(u=i),c?(t.refresherHeight=o+e.refresherThreshold,d=!1):(t.refresherHeight=i-u,t.refresherHeight>0&&(d=!0,r("refresherpulling",n,{deltaY:o,dy:o})))}},f=function(e){1===e.touches.length&&(S={x:e.touches[0].pageX,y:e.touches[0].pageY})},m=function(n){k={x:n.changedTouches[0].pageX,y:n.changedTouches[0].pageY},t.refresherHeight>=e.refresherThreshold?T("refreshing"):T("refresherabort"),S={x:0,y:0},k={x:0,y:e.refresherThreshold}};s.value.addEventListener("touchstart",f,dg),s.value.addEventListener("touchmove",l,Ke(!1)),s.value.addEventListener("scroll",i,Ke(!1)),s.value.addEventListener("touchend",m,dg),Pr(()=>{s.value.removeEventListener("touchstart",f),s.value.removeEventListener("touchmove",l),s.value.removeEventListener("scroll",i),s.value.removeEventListener("touchend",m)})}),hr(()=>{h.value&&(s.value.scrollTop=t.lastScrollTop),p.value&&(s.value.scrollLeft=t.lastScrollLeft)}),Vo(n,e=>{b(e)}),Vo(o,e=>{_(e)}),Vo(()=>e.scrollIntoView,e=>{w(e)}),Vo(()=>e.refresherTriggered,e=>{!0===e?T("refreshing"):!1===e&&T("restore")}),{realScrollX:p,realScrollY:h,_scrollTopChanged:b,_scrollLeftChanged:_}}(e,c,u,d,l,r,i,a,t),h=ps(()=>{let e="";return f.value?e+="overflow-x:auto;":e+="overflow-x:hidden;",p.value?e+="overflow-y:auto;":e+="overflow-y:hidden;",e}),m=ps(()=>{let t="uni-scroll-view";return!1===e.showScrollbar&&(t+=" uni-scroll-view-scrollbar-hidden"),t});return o({$getMain:()=>i.value}),()=>{const{refresherEnabled:t,refresherBackground:o,refresherDefaultStyle:l,refresherThreshold:u}=e,{refresherHeight:d,refreshState:f}=c;return zi("uni-scroll-view",{ref:r},[zi("div",{ref:s,class:"uni-scroll-view"},[zi("div",{ref:i,style:h.value,class:m.value},[t?zi(ug,{refreshState:f,refresherHeight:d,refresherThreshold:u,refresherDefaultStyle:l,refresherBackground:o},{default:()=>["none"==l?n.refresher&&n.refresher():null]},8,["refreshState","refresherHeight","refresherThreshold","refresherDefaultStyle","refresherBackground"]):null,zi("div",{ref:a,class:"uni-scroll-view-content"},[n.default&&n.default()],512)],6)],512)],512)}}});function pg(e,t,n,o,r,i){function s(){c&&(clearTimeout(c),c=null)}let a,l,c=null,u=!0,d=0,f=1,p=null,h=!1,m=0,g="";const v=ps(()=>n.value.length>t.displayMultipleItems),y=ps(()=>e.circular&&v.value);function b(r){Math.floor(2*d)===Math.floor(2*r)&&Math.ceil(2*d)===Math.ceil(2*r)||y.value&&function(o){if(!u)for(let r=n.value,i=r.length,s=o+t.displayMultipleItems,a=0;a<i;a++){const t=r[a],n=Math.floor(o/i)*i+a,l=n+i,c=n-i,u=Math.max(o-(n+1),n-s,0),d=Math.max(o-(l+1),l-s,0),f=Math.max(o-(c+1),c-s,0),p=Math.min(u,d,f),h=[n,l,c][[u,d,f].indexOf(p)];t.updatePosition(h,e.vertical)}}(r);const s="translate("+(e.vertical?"0":100*-r*f+"%")+", "+(e.vertical?100*-r*f+"%":"0")+") translateZ(0)",l=o.value;if(l&&(l.style.webkitTransform=s,l.style.transform=s),d=r,!a){if(r%1==0)return;a=r}r-=Math.floor(a);const c=n.value;r<=-(c.length-1)?r+=c.length:r>=c.length&&(r-=c.length),r=a%1>.5||a<0?r-1:r,i("transition",{},{dx:e.vertical?0:r*l.offsetWidth,dy:e.vertical?r*l.offsetHeight:0})}function _(e){const o=n.value.length;if(!o)return-1;const r=(Math.round(e)%o+o)%o;if(y.value){if(o<=t.displayMultipleItems)return 0}else if(r>o-t.displayMultipleItems)return o-t.displayMultipleItems;return r}function w(){p=null}function x(){if(!p)return void(h=!1);const e=p,o=e.toPos,r=e.acc,s=e.endTime,c=e.source,u=s-Date.now();if(u<=0){b(o),p=null,h=!1,a=null;const e=n.value[t.current];if(e){const n=e.getItemId();i("animationfinish",{},{current:t.current,currentItemId:n,source:c})}return}b(o+r*u*u/2),l=requestAnimationFrame(x)}function T(e,o,r){w();const i=t.duration,s=n.value.length;let a=d;if(y.value)if(r<0){for(;a<e;)a+=s;for(;a-s>e;)a-=s}else if(r>0){for(;a>e;)a-=s;for(;a+s<e;)a+=s;a+s-e<e-a&&(a+=s)}else{for(;a+s<e;)a+=s;for(;a-s>e;)a-=s;a+s-e<e-a&&(a+=s)}else"click"===o&&(e=e+t.displayMultipleItems-1<s?e:0);p={toPos:e,acc:2*(a-e)/(i*i),endTime:Date.now()+i,source:o},h||(h=!0,l=requestAnimationFrame(x))}function S(){s();const e=n.value,o=function(){c=null,g="autoplay",y.value?t.current=_(t.current+1):t.current=t.current+t.displayMultipleItems<e.length?t.current+1:0,T(t.current,"autoplay",y.value?1:0),c=setTimeout(o,t.interval)};u||e.length<=t.displayMultipleItems||(c=setTimeout(o,t.interval))}function k(e){e?S():s()}return Vo([()=>e.current,()=>e.currentItemId,()=>[...n.value]],()=>{let o=-1;if(e.currentItemId)for(let t=0,r=n.value;t<r.length;t++){if(r[t].getItemId()===e.currentItemId){o=t;break}}o<0&&(o=Math.round(e.current)||0),o=o<0?0:o,t.current!==o&&(g="",t.current=o)}),Vo([()=>e.vertical,()=>y.value,()=>t.displayMultipleItems,()=>[...n.value]],function(){s(),p&&(b(p.toPos),p=null);const r=n.value;for(let t=0;t<r.length;t++)r[t].updatePosition(t,e.vertical);f=1;const i=o.value;if(1===t.displayMultipleItems&&r.length){const e=r[0].getBoundingClientRect(),t=i.getBoundingClientRect();f=e.width/t.width,f>0&&f<1||(f=1)}const a=d;d=-2;const l=t.current;l>=0?(u=!1,t.userTracking?(b(a+l-m),m=l):(b(l),e.autoplay&&S())):(u=!0,b(-t.displayMultipleItems-1))}),Vo(()=>t.interval,()=>{c&&(s(),S())}),Vo(()=>t.current,(e,o)=>{!function(e,o){const r=g;g="";const s=n.value;if(!r){const t=s.length;T(e,"",y.value&&o+(t-e)%t>t/2?1:0)}const a=s[e];if(a){const e=t.currentItemId=a.getItemId();i("change",{},{current:t.current,currentItemId:e,source:r})}}(e,o),r("update:current",e)}),Vo(()=>t.currentItemId,e=>{r("update:currentItemId",e)}),Vo(()=>e.autoplay&&!t.userTracking,k),k(e.autoplay&&!t.userTracking),kr(()=>{let r=!1,i=0,a=0;function l(e){t.userTracking=!1;const n=i/Math.abs(i);let o=0;!e&&Math.abs(i)>.2&&(o=.5*n);const r=_(d+o);e?b(m):(g="touch",t.current=r,T(r,"touch",0!==o?o:0===r&&y.value&&d>=1?1:0))}Nm(o.value,c=>{if(!e.disableTouch&&!u){if("start"===c.detail.state)return t.userTracking=!0,r=!1,s(),m=d,i=0,a=Date.now(),void w();if("end"===c.detail.state)return l(!1);if("cancel"===c.detail.state)return l(!0);if(t.userTracking){if(!r){r=!0;const n=Math.abs(c.detail.dx),o=Math.abs(c.detail.dy);if((n>=o&&e.vertical||n<=o&&!e.vertical)&&(t.userTracking=!1),!t.userTracking)return void(e.autoplay&&S())}return function(r){const s=a;a=Date.now();const l=n.value.length-t.displayMultipleItems;function c(e){return.5-.25/(e+.5)}function u(e,t){let n=m+e;i=.6*i+.4*t,y.value||(n<0||n>l)&&(n<0?n=-c(-n):n>l&&(n=l+c(n-l)),i=0),b(n)}const d=a-s||1,f=o.value;e.vertical?u(-r.dy/f.offsetHeight,-r.ddy/d):u(-r.dx/f.offsetWidth,-r.ddx/d)}(c.detail),!1}}})}),Lr(()=>{s(),cancelAnimationFrame(l)}),{onSwiperDotClick:function(e){T(t.current=e,g="click",y.value?1:0)},circularEnabled:y,swiperEnabled:v}}const hg=od({name:"Swiper",props:{indicatorDots:{type:[Boolean,String],default:!1},vertical:{type:[Boolean,String],default:!1},autoplay:{type:[Boolean,String],default:!1},circular:{type:[Boolean,String],default:!1},interval:{type:[Number,String],default:5e3},duration:{type:[Number,String],default:500},current:{type:[Number,String],default:0},indicatorColor:{type:String,default:""},indicatorActiveColor:{type:String,default:""},previousMargin:{type:String,default:""},nextMargin:{type:String,default:""},currentItemId:{type:String,default:""},skipHiddenItemLayout:{type:[Boolean,String],default:!1},displayMultipleItems:{type:[Number,String],default:1},disableTouch:{type:[Boolean,String],default:!1},navigation:{type:[Boolean,String],default:!1},navigationColor:{type:String,default:"#fff"},navigationActiveColor:{type:String,default:"rgba(53, 53, 53, 0.6)"}},emits:["change","transition","animationfinish","update:current","update:currentItemId"],setup(e,{slots:t,emit:n}){const o=Bn(null),r=sd(o,n),i=Bn(null),s=Bn(null),a=function(e){return Tn({interval:ps(()=>{const t=Number(e.interval);return isNaN(t)?5e3:t}),duration:ps(()=>{const t=Number(e.duration);return isNaN(t)?500:t}),displayMultipleItems:ps(()=>{const t=Math.round(e.displayMultipleItems);return isNaN(t)?1:t}),current:Math.round(e.current)||0,currentItemId:e.currentItemId,userTracking:!1})}(e),l=ps(()=>{let t={};return(e.nextMargin||e.previousMargin)&&(t=e.vertical?{left:0,right:0,top:cu(e.previousMargin,!0),bottom:cu(e.nextMargin,!0)}:{top:0,bottom:0,left:cu(e.previousMargin,!0),right:cu(e.nextMargin,!0)}),t}),c=ps(()=>{const t=Math.abs(100/a.displayMultipleItems)+"%";return{width:e.vertical?"100%":t,height:e.vertical?t:"100%"}});let u=[];const d=[],f=Bn([]);function p(){const e=[];for(let t=0;t<u.length;t++){let n=u[t];n instanceof Element||(n=n.el);const o=d.find(e=>n===e.rootRef.value);o&&e.push(An(o))}f.value=e}ii("addSwiperContext",function(e){d.push(e),p()});ii("removeSwiperContext",function(e){const t=d.indexOf(e);t>=0&&(d.splice(t,1),p())});const{onSwiperDotClick:h,circularEnabled:m,swiperEnabled:g}=pg(e,a,f,s,n,r);let v=()=>null;return v=mg(o,e,a,h,f,m,g),()=>{const n=t.default&&t.default();return u=Om(n),zi("uni-swiper",{ref:o},[zi("div",{ref:i,class:"uni-swiper-wrapper"},[zi("div",{class:"uni-swiper-slides",style:l.value},[zi("div",{ref:s,class:"uni-swiper-slide-frame",style:c.value},[n],4)],4),e.indicatorDots&&zi("div",{class:["uni-swiper-dots",e.vertical?"uni-swiper-dots-vertical":"uni-swiper-dots-horizontal"]},[f.value.map((t,n,o)=>zi("div",{onClick:()=>h(n),class:{"uni-swiper-dot":!0,"uni-swiper-dot-active":n<a.current+a.displayMultipleItems&&n>=a.current||n<a.current+a.displayMultipleItems-o.length},style:{background:n===a.current?e.indicatorActiveColor:e.indicatorColor}},null,14,["onClick"]))],2),v()],512)],512)}}}),mg=(e,t,n,o,r,i,s)=>{let a=!1,l=!1,c=!1,u=Bn(!1);function f(e,n){const o=e.currentTarget;o&&(o.style.backgroundColor="over"===n?t.navigationActiveColor:"")}Bo(()=>{a="auto"===t.navigation,u.value=!0!==t.navigation||a,b()}),Bo(()=>{const e=r.value.length,t=!i.value;l=0===n.current&&t,c=n.current===e-1&&t||t&&n.current+n.displayMultipleItems>=e,s.value||(l=!0,c=!0,a&&(u.value=!0))});const p={onMouseover:e=>f(e,"over"),onMouseout:e=>f(e,"out")};function h(e,t,s){if(e.stopPropagation(),s)return;const a=r.value.length;let l=n.current;switch(t){case"prev":l--,l<0&&i.value&&(l=a-1);break;case"next":l++,l>=a&&i.value&&(l=0)}o(l)}const m=()=>hu("M21.781 7.844l-9.063 8.594 9.063 8.594q0.25 0.25 0.25 0.609t-0.25 0.578q-0.25 0.25-0.578 0.25t-0.578-0.25l-9.625-9.125q-0.156-0.125-0.203-0.297t-0.047-0.359q0-0.156 0.047-0.328t0.203-0.297l9.625-9.125q0.25-0.25 0.578-0.25t0.578 0.25q0.25 0.219 0.25 0.578t-0.25 0.578z",t.navigationColor,26);let g;const v=n=>{clearTimeout(g);const{clientX:o,clientY:r}=n,{left:i,right:s,top:a,bottom:l,width:c,height:d}=e.value.getBoundingClientRect();let f=!1;if(f=t.vertical?!(r-a<d/3||l-r<d/3):!(o-i<c/3||s-o<c/3),f)return g=setTimeout(()=>{u.value=f},300);u.value=f},y=()=>{u.value=!0};function b(){e.value&&(e.value.removeEventListener("mousemove",v),e.value.removeEventListener("mouseleave",y),a&&(e.value.addEventListener("mousemove",v),e.value.addEventListener("mouseleave",y)))}return kr(b),function(){const e={"uni-swiper-navigation-hide":u.value,"uni-swiper-navigation-vertical":t.vertical};return t.navigation?zi(Ei,null,[zi("div",Ji({class:["uni-swiper-navigation uni-swiper-navigation-prev",d({"uni-swiper-navigation-disabled":l},e)],onClick:e=>h(e,"prev",l)},p),[m()],16,["onClick"]),zi("div",Ji({class:["uni-swiper-navigation uni-swiper-navigation-next",d({"uni-swiper-navigation-disabled":c},e)],onClick:e=>h(e,"next",c)},p),[m()],16,["onClick"])]):null}},gg=od({name:"SwiperItem",props:{itemId:{type:String,default:""}},setup(e,{slots:t}){const n=Bn(null),o={rootRef:n,getItemId:()=>e.itemId,getBoundingClientRect:()=>n.value.getBoundingClientRect(),updatePosition(e,t){const o=t?"0":100*e+"%",r=t?100*e+"%":"0",i=n.value,s=`translate(${o},${r}) translateZ(0)`;i&&(i.style.webkitTransform=s,i.style.transform=s)}};return kr(()=>{const e=si("addSwiperContext");e&&e(o)}),Lr(()=>{const e=si("removeSwiperContext");e&&e(o)}),()=>zi("uni-swiper-item",{ref:n,style:{position:"absolute",width:"100%",height:"100%"}},[t.default&&t.default()],512)}}),vg={ensp:" ",emsp:" ",nbsp:" "};function yg(e,t){return function(e,{space:t,decode:n}){let o="",r=!1;for(let i of e)t&&vg[t]&&" "===i&&(i=vg[t]),r?(o+="n"===i?ne:"\\"===i?"\\":"\\"+i,r=!1):"\\"===i?r=!0:o+=i;return n?o.replace(/&nbsp;/g,vg.nbsp).replace(/&ensp;/g,vg.ensp).replace(/&emsp;/g,vg.emsp).replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&").replace(/&quot;/g,'"').replace(/&apos;/g,"'"):o}(e,t).split(ne)}const bg=od({name:"Text",props:{selectable:{type:[Boolean,String],default:!1},space:{type:String,default:""},decode:{type:[Boolean,String],default:!1}},setup(e,{slots:t}){const n=Bn(null);return()=>{const o=[];return t.default&&t.default().forEach(t=>{if(8&t.shapeFlag&&t.type!==Pi){const n=yg(t.children,{space:e.space,decode:e.decode}),r=n.length-1;n.forEach((e,t)=>{(0!==t||e)&&o.push(qi(e)),t!==r&&o.push(zi("br"))})}else o.push(t)}),zi("uni-text",{ref:n,selectable:!!e.selectable||null},[zi("span",null,o)],8,["selectable"])}}}),_g=d({},ym,{placeholderClass:{type:String,default:"input-placeholder"},autoHeight:{type:[Boolean,String],default:!1},confirmType:{type:String,default:"return",validator:e=>xg.concat("return").includes(e)}});let wg=!1;const xg=["done","go","next","search","send"];const Tg=od({name:"Textarea",props:_g,emits:["confirm","linechange",...bm],setup(e,{emit:t,expose:n}){const o=Bn(null),r=Bn(null),{fieldRef:i,state:s,scopedAttrsState:a,fixDisabledColor:l,trigger:c}=xm(e,o,t),u=ps(()=>s.value.split(ne)),d=ps(()=>xg.includes(e.confirmType)),f=Bn(0),p=Bn(null);function h({height:e}){f.value=e}function m(e){"Enter"===e.key&&d.value&&e.preventDefault()}function g(t){if("Enter"===t.key&&d.value){!function(e){c("confirm",e,{value:s.value})}(t);const n=t.target;!e.confirmHold&&n.blur()}}return Vo(()=>f.value,t=>{const n=o.value,i=p.value,s=r.value;let a=parseFloat(getComputedStyle(n).lineHeight);isNaN(a)&&(a=i.offsetHeight);var l=Math.round(t/a);c("linechange",{},{height:t,heightRpx:750/window.innerWidth*t,lineCount:l}),e.autoHeight&&(n.style.height="auto",s.style.height=t+"px")}),function(){const e="(prefers-color-scheme: dark)";wg=0===String(navigator.platform).indexOf("iP")&&0===String(navigator.vendor).indexOf("Apple")&&window.matchMedia(e).media!==e}(),n({$triggerInput:e=>{t("update:modelValue",e.value),t("update:value",e.value),s.value=e.value}}),()=>{let t=e.disabled&&l?zi("textarea",{key:"disabled-textarea",ref:i,value:s.value,tabindex:"-1",readonly:!!e.disabled,maxlength:s.maxlength,class:{"uni-textarea-textarea":!0,"uni-textarea-textarea-fix-margin":wg},style:{overflowY:e.autoHeight?"hidden":"auto",...e.cursorColor&&{caretColor:e.cursorColor}},onFocus:e=>e.target.blur()},null,46,["value","readonly","maxlength","onFocus"]):zi("textarea",{key:"textarea",ref:i,value:s.value,disabled:!!e.disabled,maxlength:s.maxlength,enterkeyhint:e.confirmType,inputmode:e.inputmode,class:{"uni-textarea-textarea":!0,"uni-textarea-textarea-fix-margin":wg},style:{overflowY:e.autoHeight?"hidden":"auto",...e.cursorColor&&{caretColor:e.cursorColor}},onKeydown:m,onKeyup:g},null,46,["value","disabled","maxlength","enterkeyhint","inputmode","onKeydown","onKeyup"]);return zi("uni-textarea",{ref:o},[zi("div",{ref:r,class:"uni-textarea-wrapper"},[qo(zi("div",Ji(a.attrs,{style:e.placeholderStyle,class:["uni-textarea-placeholder",e.placeholderClass]}),[e.placeholder],16),[[Ds,!s.value.length]]),zi("div",{ref:p,class:"uni-textarea-line"},[" "],512),zi("div",{class:"uni-textarea-compute"},[u.value.map(e=>zi("div",null,[e.trim()?e:"."])),zi(Vh,{initial:!0,onResize:h},null,8,["initial","onResize"])]),"search"===e.confirmType?zi("form",{action:"",onSubmit:()=>!1,class:"uni-input-form"},[t],40,["onSubmit"]):t],512)],512)}}}),Sg=od({name:"View",props:d({},ad),setup(e,{slots:t}){const n=Bn(null),{hovering:o,binding:r}=ld(e);return()=>{const i=e.hoverClass;return i&&"none"!==i?zi("uni-view",Ji({class:o.value?i:"",ref:n},r),[$r(t,"default")],16):zi("uni-view",{ref:n},[$r(t,"default")],512)}}});function kg(e,t){if(t||(t=e.id),t)return e.$options.name.toLowerCase()+"."+t}function Eg(e,t,n){e&&Ic(bu(),e,({type:e,data:n},o)=>{t(e,n,o)})}function Cg(e,t){e&&function(e,t){t=Oc(e,t),delete Lc[t]}(bu(),e)}let Pg=0;function Lg(e,t,n,o){y(t)&&xr(e,t.bind(n),o)}function Og(e,t,n){const o=e.mpType||n.$mpType;if(o&&"component"!==o&&(Object.keys(e).forEach(o=>{if(function(e,t,n=!0){return!(n&&!y(t))&&(it.indexOf(e)>-1||0===e.indexOf("on"))}(o,e[o],!1)){const r=e[o];m(r)?r.forEach(e=>Lg(o,e,n,t)):Lg(o,r,n,t)}}),"page"===o)){t.__isVisible=!0;try{let e=t.attrs.__pageQuery;0,Su(n,he,e),delete t.attrs.__pageQuery;const o=n.$page;"preloadPage"!==(null==o?void 0:o.openType)&&Su(n,ae)}catch(r){}}}function Ig(e,t,n){Og(e,t,n)}function Ag(e,t,n){return e[t]=n}function Mg(e,...t){const n=this[e];return n?n(...t):null}function Rg(e){const t=e.config.errorHandler;return function(n,o,r){t&&t(n,o,r);const i=e._instance;if(!i||!i.proxy)throw n;i[ue]?Su(i.proxy,ue,n):o&&o.$.vnode}}function $g(e,t){return e?[...new Set([].concat(e,t))]:t}function Ng(e){const t=e.config;var n;t.errorHandler=at(e,Rg),n=t.optionMergeStrategies,it.forEach(e=>{n[e]=$g});const o=t.globalProperties;o.$set=Ag,o.$applyOptions=Ig,o.$callMethod=Mg,function(e){st.forEach(t=>t(e))}(e)}function Dg(e){const t=Xl({history:jg(),strict:!!__uniConfig.router.strict,routes:__uniRoutes,scrollBehavior:Bg});t.beforeEach((e,t)=>{var n;e&&t&&e.meta.isTabBar&&t.meta.isTabBar&&(n=t.meta.tabBarIndex,"undefined"!=typeof window&&(Fg[n]={left:window.pageXOffset,top:window.pageYOffset}))}),e.router=t,e.use(t)}let Fg=Object.create(null);const Bg=(e,t,n)=>{if(n)return n;if(e&&t&&e.meta.isTabBar&&t.meta.isTabBar){const t=(o=e.meta.tabBarIndex,Fg[o]);if(t)return t}return{left:0,top:0};var o};function jg(){let{routerBase:e}=__uniConfig.router;"/"===e&&(e="");const t=(n=e,(n=location.host?n||location.pathname+location.search:"").includes("#")||(n+="#"),al(n));var n;return t.listen((e,t,n)=>{"back"===n.direction&&function(e=1){const t=oh(),n=t.length-1,o=n-e;for(let r=n;r>o;r--){const e=Gp(t[r]);rh(lh(e.path,e.id),!1)}}(Math.abs(n.delta))}),t}const Vg={install(e){Ng(e),Vu(e),Zu(e),e.config.warnHandler||(e.config.warnHandler=Wg),Dg(e)}};function Wg(e,t,n){if(t){if("PageMetaHead"===t.$.type.name)return;const e=t.$.parent;if(e&&"PageMeta"===e.type.name)return}const o=[`[Vue warn]: ${e}`];n.length&&o.push("\n",n)}const Hg={class:"uni-async-loading"},zg=zi("i",{class:"uni-loading"},null,-1),Ug=rd({name:"AsyncLoading",render:()=>(Ai(),Di("div",Hg,[zg]))});function qg(){window.location.reload()}const Yg=rd({name:"AsyncError",setup(){yc();const{t:e}=gc();return()=>zi("div",{class:"uni-async-error",onClick:qg},[e("uni.async.error")],8,["onClick"])}});let Xg;function Gg(){return Xg}function Kg(e){Xg=e,Object.defineProperty(Xg.$.ctx,"$children",{get:()=>oh().map(e=>e.$vm)});const t=Xg.$.appContext.app;t.component(Ug.name)||t.component(Ug.name,Ug),t.component(Yg.name)||t.component(Yg.name,Yg),function(e){e.$vm=e,e.$mpType="app";const t=Bn(gc().getLocale());Object.defineProperty(e,"$locale",{get:()=>t.value,set(e){t.value=e}})}(Xg),function(e,t){const n=e.$options||{};n.globalData=d(n.globalData||{},t),Object.defineProperty(e,"globalData",{get:()=>n.globalData,set(e){n.globalData=e}})}(Xg),Ku(),Iu()}function Jg(e,{clone:t,init:n,setup:o,before:r}){t&&(e=d({},e)),r&&r(e);const i=e.setup;return e.setup=(e,t)=>{const r=ns();if(n(r.proxy),o(r),i)return i(e,t)},e}function Zg(e,t){return e&&(e.__esModule||"Module"===e[Symbol.toStringTag])?Jg(e.default,t):Jg(e,t)}function Qg(e){return Zg(e,{clone:!0,init:ah,setup(e){e.$pageInstance=e;const t=yd(),n=Qe(t.query);e.attrs.__pageQuery=n,Gp(e.proxy).options=n,e.proxy.options=n;const o=gd();var r,i;return e.onReachBottom=Tn([]),e.onPageScroll=Tn([]),Vo([e.onReachBottom,e.onPageScroll],()=>{const t=vu();e.proxy===t&&gh(e,o)},{once:!0}),Sr(()=>{dh(e,o)}),kr(()=>{fh(e);const{onReady:n}=e;n&&N(n),ov(t)}),gr(()=>{if(!e.__isVisible){dh(e,o),e.__isVisible=!0;const{onShow:n}=e;n&&N(n),uo(()=>{ov(t)})}},"ba",r),function(e,t){gr(e,"bda",t)}(()=>{if(e.__isVisible&&!e.__isUnload){e.__isVisible=!1;{const{onHide:t}=e;t&&N(t)}}}),i=o.id,rb.subscribe(Oc(i,Ec),Ac),Pr(()=>{!function(e){rb.unsubscribe(Oc(e,Ec)),Object.keys(Lc).forEach(t=>{0===t.indexOf(e+".")&&delete Lc[t]})}(o.id)}),n}})}function ev(){const{windowWidth:e,windowHeight:t,screenWidth:n,screenHeight:o}=xv(),r=90===Math.abs(Number(window.orientation))?"landscape":"portrait";ib.emit(ye,{deviceOrientation:r,size:{windowWidth:e,windowHeight:t,screenWidth:n,screenHeight:o}})}function tv(e){k(e.data)&&"WEB_INVOKE_APPSERVICE"===e.data.type&&ib.emit("onWebInvokeAppService",e.data.data,e.data.pageId)}function nv(){const{emit:e}=ib;"visible"===document.visibilityState?e(Me,d({},jh)):e(Re)}function ov(e){const{tabBarText:t,tabBarIndex:n,route:o}=e.meta;t&&Su("onTabItemTap",{index:n,text:t,pagePath:o})}function rv(e){e=e>0&&e<1/0?e:0;const t=Math.floor(e/3600),n=Math.floor(e%3600/60),o=Math.floor(e%3600%60),r=(t<10?"0":"")+t;let i=(n<10?"0":"")+n+":"+((o<10?"0":"")+o);return"00"!==r&&(i=r+":"+i),i}function iv(e,t,n){const o=Tn({seeking:!1,gestureType:"none",volumeOld:0,volumeNew:0,currentTimeOld:0,currentTimeNew:0,toastThin:!1}),r={x:0,y:0};let i=null;let s;return{state:o,onTouchstart:function(e){const t=e.targetTouches[0];r.x=t.pageX,r.y=t.pageY,o.gestureType="none",o.volumeOld=0},onTouchmove:function(a){function l(){a.stopPropagation(),a.preventDefault()}n.fullscreen&&l();const c=o.gestureType;if("stop"===c)return;const u=a.targetTouches[0],d=u.pageX,f=u.pageY,p=r,h=t.value;if("progress"===c?(!function(e){const n=t.value,r=n.duration;let i=e/600*r+o.currentTimeOld;i<0?i=0:i>r&&(i=r);o.currentTimeNew=i}(d-p.x),o.seeking=!0):"volume"===c&&function(e){const n=t.value,r=o.volumeOld;let i;"number"==typeof r&&(i=r-e/200,i<0?i=0:i>1&&(i=1),clearTimeout(s),s=void 0,null==s&&(s=setTimeout(()=>{o.toastThin=!1,s=void 0},1e3)),n.volume=i,o.volumeNew=i)}(f-p.y),"none"===c)if(Math.abs(d-p.x)>Math.abs(f-p.y)){if(!e.enableProgressGesture)return void(o.gestureType="stop");o.gestureType="progress",o.currentTimeOld=o.currentTimeNew=h.currentTime,n.fullscreen||l()}else{if(!e.pageGesture&&!e.vslideGesture)return void(o.gestureType="stop");"none"!==o.gestureType&&null!=i||(i=setTimeout(()=>{o.toastThin=!0},500)),o.gestureType="volume",o.volumeOld=h.volume,n.fullscreen||l()}},onTouchend:function(e){const n=t.value;"none"!==o.gestureType&&"stop"!==o.gestureType&&(e.stopPropagation(),e.preventDefault()),"progress"===o.gestureType&&o.currentTimeOld!==o.currentTimeNew&&(n.currentTime=o.currentTimeNew),o.gestureType="none"}}}function sv(e,t,n,o,r,i,s,a){const l={play:e,stop:n,pause:t,seek:o,sendDanmu:r,playbackRate:i,requestFullScreen:s,exitFullScreen:a};!function(e,t){const n=ns().proxy;kr(()=>{Eg(t||kg(n),e),Vo(()=>n.id,(t,o)=>{Eg(kg(n,t),e),Cg(o&&kg(n,o))})}),Pr(()=>{Cg(t||kg(n))})}((e,t)=>{let n;switch(e){case"seek":n=t.position;break;case"sendDanmu":n=t;break;case"playbackRate":n=t.rate}e in l&&l[e](n)},function(){const e=mu(),t=ns().proxy,n=t.$options.name.toLowerCase(),o=t.id||"context"+Pg++;return kr(()=>{t.$el.__uniContextInfo={id:o,type:n,page:e}}),`${n}.${o}`}())}const av={id:{type:String,default:""},src:{type:String,default:""},duration:{type:[Number,String],default:""},controls:{type:[Boolean,String],default:!0},danmuList:{type:Array,default:()=>[]},danmuBtn:{type:[Boolean,String],default:!1},enableDanmu:{type:[Boolean,String],default:!1},autoplay:{type:[Boolean,String],default:!1},loop:{type:[Boolean,String],default:!1},muted:{type:[Boolean,String],default:!1},objectFit:{type:String,default:"contain"},poster:{type:String,default:""},direction:{type:[String,Number],default:""},showProgress:{type:Boolean,default:!0},initialTime:{type:[String,Number],default:0},showFullscreenBtn:{type:[Boolean,String],default:!0},pageGesture:{type:[Boolean,String],default:!1},vslideGesture:{type:[Boolean,String],default:!1},enableProgressGesture:{type:[Boolean,String],default:!0},showPlayBtn:{type:[Boolean,String],default:!0},showCenterPlayBtn:{type:[Boolean,String],default:!0}},lv=od({name:"Video",props:av,emits:["fullscreenchange","progress","loadedmetadata","waiting","error","play","pause","ended","timeupdate"],setup(e,{emit:t,attrs:n,slots:o}){const r=Bn(null),i=Bn(null),s=sd(r,t),{state:a}=pm(),{$attrs:l}=Lm({excludeListeners:!0});gc(),Sc();const{videoRef:c,state:u,play:d,pause:f,stop:p,seek:h,playbackRate:g,toggle:v,onDurationChange:y,onLoadedMetadata:b,onProgress:_,onWaiting:w,onVideoError:x,onPlay:T,onPause:S,onEnded:k,onTimeUpdate:E}=function(e,t,n){const o=Bn(null),r=ps(()=>bh(e.src)),i=ps(()=>"true"===e.muted||!0===e.muted),s=Tn({start:!1,src:r,playing:!1,currentTime:0,duration:0,progress:0,buffered:0,muted:i,pauseUpdatingCurrentTime:!1});function a(e){const t=e.target,n=t.buffered;n.length&&(s.buffered=n.end(n.length-1)/t.duration*100)}function l(){o.value.pause()}function c(e){const t=o.value;"number"!=typeof(e=Number(e))||isNaN(e)||(t.currentTime=e)}return Vo(()=>r.value,()=>{s.playing=!1,s.currentTime=0}),Vo(()=>s.buffered,e=>{n("progress",{},{buffered:e})}),Vo(()=>i.value,e=>{o.value.muted=e}),{videoRef:o,state:s,play:function(){const e=o.value;s.start=!0,e.play()},pause:l,stop:function(){c(0),l()},seek:c,playbackRate:function(e){o.value.playbackRate=e},toggle:function(){const e=o.value;s.playing?e.pause():e.play()},onDurationChange:function({target:e}){s.duration=e.duration},onLoadedMetadata:function(t){const o=Number(e.initialTime)||0,r=t.target;o>0&&(r.currentTime=o),n("loadedmetadata",t,{width:r.videoWidth,height:r.videoHeight,duration:r.duration}),a(t)},onProgress:a,onWaiting:function(e){n("waiting",e,{})},onVideoError:function(e){s.playing=!1,n("error",e,{})},onPlay:function(e){s.start=!0,s.playing=!0,n("play",e,{})},onPause:function(e){s.playing=!1,n("pause",e,{})},onEnded:function(e){s.playing=!1,n("ended",e,{})},onTimeUpdate:function(e){const t=e.target;s.pauseUpdatingCurrentTime||(s.currentTime=t.currentTime);const o=t.currentTime;n("timeupdate",e,{currentTime:o,duration:t.duration})}}}(e,0,s),{state:C,danmuRef:P,updateDanmu:L,toggleDanmu:O,sendDanmu:I}=function(e,t){const n=Bn(null),o=Tn({enable:Boolean(e.enableDanmu)});let r={time:0,index:-1};const i=m(e.danmuList)?JSON.parse(JSON.stringify(e.danmuList)):[];function s(e){const t=document.createElement("p");t.className="uni-video-danmu-item",t.innerText=e.text;let o=`bottom: ${100*Math.random()}%;color: ${e.color};`;t.setAttribute("style",o),n.value.appendChild(t),setTimeout(function(){o+="left: 0;-webkit-transform: translateX(-100%);transform: translateX(-100%);",t.setAttribute("style",o),setTimeout(function(){t.remove()},4e3)},17)}return i.sort(function(e,t){return(e.time||0)-(t.time||0)}),{state:o,danmuRef:n,updateDanmu:function(e){const n=e.target.currentTime,a=r,l={time:n,index:a.index};if(n>a.time)for(let r=a.index+1;r<i.length;r++){const e=i[r];if(!(n>=(e.time||0)))break;l.index=r,t.playing&&o.enable&&s(e)}else if(n<a.time)for(let t=a.index-1;t>-1&&n<=(i[t].time||0);t--)l.index=t-1;r=l},toggleDanmu:function(){o.enable=!o.enable},sendDanmu:function(e){i.splice(r.index+1,0,{text:String(e.text),color:e.color,time:t.currentTime||0})}}}(e,u),{state:A,onFullscreenChange:M,emitFullscreenChange:R,toggleFullscreen:$,requestFullScreen:N,exitFullScreen:D}=function(e,t,n,o,r){const i=Tn({fullscreen:!1}),s=/^Apple/.test(navigator.vendor);function a(t){i.fullscreen=t,e("fullscreenchange",{},{fullScreen:t,direction:"vertical"})}function l(e){const i=r.value,l=t.value,c=n.value;let u;e?!document.fullscreenEnabled&&!document.webkitFullscreenEnabled||s&&!o.userAction?c.webkitEnterFullScreen?c.webkitEnterFullScreen():(u=!0,l.remove(),l.classList.add("uni-video-type-fullscreen"),document.body.appendChild(l)):l[document.fullscreenEnabled?"requestFullscreen":"webkitRequestFullscreen"]():document.fullscreenEnabled||document.webkitFullscreenEnabled?document.fullscreenElement?document.exitFullscreen():document.webkitFullscreenElement&&document.webkitExitFullscreen():c.webkitExitFullScreen?c.webkitExitFullScreen():(u=!0,l.remove(),l.classList.remove("uni-video-type-fullscreen"),i.appendChild(l)),u&&a(e)}function c(){l(!1)}return Pr(c),{state:i,onFullscreenChange:function(e,t){t&&document.fullscreenEnabled||a(!(!document.fullscreenElement&&!document.webkitFullscreenElement))},emitFullscreenChange:a,toggleFullscreen:l,requestFullScreen:function(){l(!0)},exitFullScreen:c}}(s,i,c,a,r),{state:F,onTouchstart:B,onTouchend:j,onTouchmove:V}=iv(e,c,A),{state:W,progressRef:H,ballRef:z,clickProgress:U,toggleControls:q}=function(e,t,n,o){const r=Bn(null),i=Bn(null),s=ps(()=>e.showCenterPlayBtn&&!t.start),a=Bn(!0),l=ps(()=>!s.value&&e.controls&&a.value),c=Tn({seeking:!1,touching:!1,controlsTouching:!1,centerPlayBtnShow:s,controlsShow:l,controlsVisible:a});let u;function d(){u=setTimeout(()=>{c.controlsVisible=!1},3e3)}function f(){u&&(clearTimeout(u),u=null)}return Pr(()=>{u&&clearTimeout(u)}),Vo(()=>c.controlsShow&&t.playing&&!c.controlsTouching,e=>{e?d():f()}),kr(()=>{const e=Ke(!1);let s,a,l,u=!0;const d=i.value;function f(e){const n=e.targetTouches[0],i=n.pageX,d=n.pageY;if(u&&Math.abs(i-s)<Math.abs(d-a))return void p(e);u=!1;const f=r.value.offsetWidth;let h=l+(i-s)/f*100;h<0?h=0:h>100&&(h=100),t.progress=h,null==o||o(t.duration*h/100),c.seeking=!0,e.preventDefault(),e.stopPropagation()}function p(o){c.controlsTouching=!1,c.touching&&(d.removeEventListener("touchmove",f,e),u||(o.preventDefault(),o.stopPropagation(),n(t.duration*t.progress/100)),c.touching=!1)}d.addEventListener("touchstart",n=>{c.controlsTouching=!0;const o=n.targetTouches[0];s=o.pageX,a=o.pageY,l=t.progress,u=!0,c.touching=!0,d.addEventListener("touchmove",f,e)}),d.addEventListener("touchend",p),d.addEventListener("touchcancel",p)}),{state:c,progressRef:r,ballRef:i,clickProgress:function(e){const o=r.value;let i=e.target,s=e.offsetX;for(;i&&i!==o;)s+=i.offsetLeft,i=i.parentNode;const a=o.offsetWidth;let l=0;s>=0&&s<=a&&(l=s/a,n(t.duration*l))},toggleControls:function(){c.controlsVisible=!c.controlsVisible},autoHideStart:d,autoHideEnd:f}}(e,u,h,e=>{F.currentTimeNew=e});sv(d,f,p,h,I,g,N,D);const Y=function(e,t,n){const o=ps(()=>"progress"===t.gestureType||n.touching);return Vo(o,o=>{e.pauseUpdatingCurrentTime=o,n.controlsTouching=o,"progress"===t.gestureType&&o&&(n.controlsVisible=o)}),Vo([()=>e.currentTime,()=>{av.duration}],()=>{e.progress=e.currentTime/e.duration*100}),Vo(()=>t.currentTimeNew,t=>{e.currentTime=t}),o}(u,F,W);return()=>zi("uni-video",{ref:r,id:e.id,onClick:q},[zi("div",{ref:i,class:"uni-video-container",onTouchstart:B,onTouchend:j,onTouchmove:V,onFullscreenchange:ua(M,["stop"]),onWebkitfullscreenchange:ua(e=>M(e,!0),["stop"])},[zi("video",Ji({ref:c,style:{"object-fit":e.objectFit},muted:!!e.muted,loop:!!e.loop,src:u.src,poster:e.poster,autoplay:!!e.autoplay},l.value,{class:"uni-video-video","webkit-playsinline":!0,playsinline:!0,onDurationchange:y,onLoadedmetadata:b,onProgress:_,onWaiting:w,onError:x,onPlay:T,onPause:S,onEnded:k,onTimeupdate:e=>{E(e),L(e)},onWebkitbeginfullscreen:()=>R(!0),onX5videoenterfullscreen:()=>R(!0),onWebkitendfullscreen:()=>R(!1),onX5videoexitfullscreen:()=>R(!1)}),null,16,["muted","loop","src","poster","autoplay","webkit-playsinline","playsinline","onDurationchange","onLoadedmetadata","onProgress","onWaiting","onError","onPlay","onPause","onEnded","onTimeupdate","onWebkitbeginfullscreen","onX5videoenterfullscreen","onWebkitendfullscreen","onX5videoexitfullscreen"]),qo(zi("div",{class:"uni-video-bar uni-video-bar-full",onClick:ua(()=>{},["stop"])},[zi("div",{class:"uni-video-controls"},[qo(zi("div",{class:{"uni-video-icon":!0,"uni-video-control-button":!0,"uni-video-control-button-play":!u.playing,"uni-video-control-button-pause":u.playing},onClick:ua(v,["stop"])},null,10,["onClick"]),[[Ds,e.showPlayBtn]]),qo(zi("div",{class:"uni-video-current-time"},[rv(u.currentTime)],512),[[Ds,e.showProgress]]),qo(zi("div",{ref:H,class:"uni-video-progress-container",onClick:ua(U,["stop"])},[zi("div",{class:{"uni-video-progress":!0,"uni-video-progress-progressing":Y.value}},[zi("div",{style:{width:u.buffered-u.progress+"%",left:u.progress+"%"},class:"uni-video-progress-buffered"},null,4),zi("div",{style:{width:u.progress+"%"},class:"uni-video-progress-played"},null,4),zi("div",{ref:z,style:{left:u.progress+"%"},class:{"uni-video-ball":!0,"uni-video-ball-progressing":Y.value}},[zi("div",{class:"uni-video-inner"},null)],6)],2)],8,["onClick"]),[[Ds,e.showProgress]]),qo(zi("div",{class:"uni-video-duration"},[rv(Number(e.duration)||u.duration)],512),[[Ds,e.showProgress]])]),qo(zi("div",{class:{"uni-video-icon":!0,"uni-video-danmu-button":!0,"uni-video-danmu-button-active":C.enable},onClick:ua(O,["stop"])},null,10,["onClick"]),[[Ds,e.danmuBtn]]),qo(zi("div",{class:{"uni-video-icon":!0,"uni-video-fullscreen":!0,"uni-video-type-fullscreen":A.fullscreen},onClick:ua(()=>$(!A.fullscreen),["stop"])},null,10,["onClick"]),[[Ds,e.showFullscreenBtn]])],8,["onClick"]),[[Ds,W.controlsShow]]),qo(zi("div",{ref:P,style:"z-index: 0;",class:"uni-video-danmu"},null,512),[[Ds,u.start&&C.enable]]),W.centerPlayBtnShow&&zi("div",{class:"uni-video-cover",onClick:ua(()=>{},["stop"])},[zi("div",{class:"uni-video-cover-play-button uni-video-icon",onClick:ua(d,["stop"])},null,8,["onClick"])],8,["onClick"]),zi("div",{class:"uni-video-loading"},["volume"===F.gestureType?zi("div",{class:{"uni-video-toast-container":!0,"uni-video-toast-container-thin":F.toastThin},style:{marginTop:"5px"}},[!F.toastThin&&F.volumeNew>0&&"volume"===F.gestureType?zi("text",{class:"uni-video-icon uni-video-toast-icon"},[""]):!F.toastThin&&zi("text",{class:"uni-video-icon uni-video-toast-icon"},[""]),zi("div",{class:"uni-video-toast-draw",style:{width:100*F.volumeNew+"%"}},null)],2):null]),zi("div",{class:{"uni-video-toast":!0,"uni-video-toast-progress":Y.value}},[zi("div",{class:"uni-video-toast-title"},[zi("span",{class:"uni-video-toast-title-current-time"},[rv(F.currentTimeNew)])," / ",Number(e.duration)||rv(u.duration)])],2),zi("div",{class:"uni-video-slots"},[o.default&&o.default()])],40,["onTouchstart","onTouchend","onTouchmove","onFullscreenchange","onWebkitfullscreenchange"])],8,["id","onClick"])}}),cv=Kd("makePhoneCall",({phoneNumber:e},{resolve:t})=>(window.location.href=`tel:${e}`,t())),uv="__DC_STAT_UUID",dv=navigator.cookieEnabled&&(window.localStorage||window.sessionStorage)||{};let fv;function pv(){if(fv=fv||dv[uv],!fv){fv=Date.now()+""+Math.floor(1e7*Math.random());try{dv[uv]=fv}catch(e){}}return fv}function hv(){if(!0!==__uniConfig.darkmode)return b(__uniConfig.darkmode)?__uniConfig.darkmode:"light";try{return window.matchMedia("(prefers-color-scheme: light)").matches?"light":"dark"}catch(e){return"light"}}function mv(){let e,t="0",n="",o="phone";const r=navigator.language;if(xh){e="iOS";const o=_h.match(/OS\s([\w_]+)\slike/);o&&(t=o[1].replace(/_/g,"."));const r=_h.match(/\(([a-zA-Z]+);/);r&&(n=r[1])}else if(wh){e="Android";const o=_h.match(/Android[\s/]([\w\.]+)[;\s]/);o&&(t=o[1]);const r=_h.match(/\((.+?)\)/),i=r?r[1].split(";"):_h.split(" "),s=[/\bAndroid\b/i,/\bLinux\b/i,/\bU\b/i,/^\s?[a-z][a-z]$/i,/^\s?[a-z][a-z]-[a-z][a-z]$/i,/\bwv\b/i,/\/[\d\.,]+$/,/^\s?[\d\.,]+$/,/\bBrowser\b/i,/\bMobile\b/i];for(let e=0;e<i.length;e++){const t=i[e];if(t.indexOf("Build")>0){n=t.split("Build")[0].trim();break}let o;for(let e=0;e<s.length;e++)if(s[e].test(t)){o=!0;break}if(!o){n=t.trim();break}}}else if(Eh){if(n="iPad",e="iOS",o="pad",t=y(window.BigInt)?"14.0":"13.0",14===parseInt(t)){const e=_h.match(/Version\/(\S*)\b/);e&&(t=e[1])}}else if(Th||Sh||kh){n="PC",e="PC",o="pc",t="0";let r=_h.match(/\((.+?)\)/)[1];if(Th){switch(e="Windows",Th[1]){case"5.1":t="XP";break;case"6.0":t="Vista";break;case"6.1":t="7";break;case"6.2":t="8";break;case"6.3":t="8.1";break;case"10.0":t="10"}const n=r&&r.match(/[Win|WOW]([\d]+)/);n&&(t+=` x${n[1]}`)}else if(Sh){e="macOS";const n=r&&r.match(/Mac OS X (.+)/)||"";t&&(t=n[1].replace(/_/g,"."),-1!==t.indexOf(";")&&(t=t.split(";")[0]))}else if(kh){e="Linux";const n=r&&r.match(/Linux (.*)/)||"";n&&(t=n[1],-1!==t.indexOf(";")&&(t=t.split(";")[0]))}}else e="Other",t="0",o="unknown";const i=`${e} ${t}`,s=e.toLocaleLowerCase();let a="",l=String(function(){const e=navigator.userAgent,t=e.indexOf("compatible")>-1&&e.indexOf("MSIE")>-1,n=e.indexOf("Edge")>-1&&!t,o=e.indexOf("Trident")>-1&&e.indexOf("rv:11.0")>-1;if(t){new RegExp("MSIE (\\d+\\.\\d+);").test(e);const t=parseFloat(RegExp.$1);return t>6?t:6}return n?-1:o?11:-1}());if("-1"!==l)a="IE";else{const e=["Version","Firefox","Chrome","Edge{0,1}"],t=["Safari","Firefox","Chrome","Edge"];for(let n=0;n<e.length;n++){const o=e[n],r=new RegExp(`(${o})/(\\S*)\\b`);r.test(_h)&&(a=t[n],l=_h.match(r)[2])}}let c="portrait";const u=void 0===window.screen.orientation?window.orientation:window.screen.orientation.angle;return c=90===Math.abs(u)?"landscape":"portrait",{deviceBrand:void 0,brand:void 0,deviceModel:n,deviceOrientation:c,model:n,system:i,platform:s,browserName:a.toLocaleLowerCase(),browserVersion:l,language:r,deviceType:o,ua:_h,osname:e,osversion:t,theme:hv()}}const gv=Gd(0,()=>{const e=window.devicePixelRatio,t=Ch(),n=Ph(t),o=Lh(t,n),r=function(e,t){return e?Math[t?"min":"max"](screen.height,screen.width):screen.height}(t,n),i=Oh(o);let s=window.innerHeight;const a=Qc.top,l={left:Qc.left,right:i-Qc.right,top:Qc.top,bottom:s-Qc.bottom,width:i-Qc.left-Qc.right,height:s-Qc.top-Qc.bottom},{top:c,bottom:u}=ru();return s-=c,s-=u,{windowTop:c,windowBottom:u,windowWidth:i,windowHeight:s,pixelRatio:e,screenWidth:o,screenHeight:r,statusBarHeight:a,safeArea:l,safeAreaInsets:{top:Qc.top,right:Qc.right,bottom:Qc.bottom,left:Qc.left},screenTop:r-s}});let vv,yv=!0;function bv(){yv&&(vv=mv())}const _v=Gd(0,()=>{bv();const{deviceBrand:e,deviceModel:t,brand:n,model:o,platform:r,system:i,deviceOrientation:s,deviceType:a,osname:l,osversion:c}=vv;return d({brand:n,deviceBrand:e,deviceModel:t,devicePixelRatio:window.devicePixelRatio,deviceId:pv(),deviceOrientation:s,deviceType:a,model:o,platform:r,system:i,osName:l?l.toLocaleLowerCase():void 0,osVersion:c})}),wv=Gd(0,()=>{bv();const{theme:e,language:t,browserName:n,browserVersion:o}=vv;return d({appId:__uniConfig.appId,appName:__uniConfig.appName,appVersion:__uniConfig.appVersion,appVersionCode:__uniConfig.appVersionCode,appLanguage:Bf?Bf():t,enableDebug:!1,hostSDKVersion:void 0,hostPackageName:void 0,hostFontSizeSetting:void 0,hostName:n,hostVersion:o,hostTheme:e,hostLanguage:t,language:t,SDKVersion:"",theme:e,version:"",uniPlatform:"web",isUniAppX:!1,uniCompileVersion:__uniConfig.compilerVersion,uniCompilerVersion:__uniConfig.compilerVersion,uniRuntimeVersion:__uniConfig.compilerVersion},{})}),xv=Gd(0,()=>{yv=!0,bv(),yv=!1;const e=gv(),t=_v(),n=wv();yv=!0;const{ua:o,browserName:r,browserVersion:i,osname:s,osversion:a}=vv,l=d(e,t,n,{ua:o,browserName:r,browserVersion:i,uniPlatform:"web",uniCompileVersion:__uniConfig.compilerVersion,uniRuntimeVersion:__uniConfig.compilerVersion,fontSizeSetting:void 0,osName:s.toLocaleLowerCase(),osVersion:a,osLanguage:void 0,osTheme:void 0});return delete l.screenTop,delete l.enableDebug,__uniConfig.darkmode||delete l.theme,function(e){let t={};return k(e)&&Object.keys(e).sort().forEach(n=>{const o=n;t[o]=e[o]}),Object.keys(t)?t:e}(l)});const Tv=Kd("getNetworkType",(e,{resolve:t})=>{const n=navigator.connection||navigator.webkitConnection||navigator.mozConnection;let o="unknown";return n?(o=n.type,"cellular"===o&&n.effectiveType?o=n.effectiveType.replace("slow-",""):!o&&n.effectiveType?o=n.effectiveType:["none","wifi"].includes(o)||(o="unknown")):!1===navigator.onLine&&(o="none"),t({networkType:o})});const Sv=Kd("setClipboardData",(e,t)=>{return n=void 0,o=[e,t],r=function*({data:e},{resolve:t,reject:n}){try{yield navigator.clipboard.writeText(e),t()}catch(o){!function(e,t,n){const o=document.getElementById("#clipboard");o&&o.remove();const r=document.createElement("textarea");r.setAttribute("inputmode","none"),r.id="#clipboard",r.style.position="fixed",r.style.top="-9999px",r.style.zIndex="-9999",document.body.appendChild(r),r.value=e,r.select(),r.setSelectionRange(0,r.value.length);const i=document.execCommand("Copy",!1);r.blur(),i?t():n()}(e,t,n)}},new Promise((e,t)=>{var i=e=>{try{a(r.next(e))}catch(n){t(n)}},s=e=>{try{a(r.throw(e))}catch(n){t(n)}},a=t=>t.done?e(t.value):Promise.resolve(t.value).then(i,s);a((r=r.apply(n,o)).next())});var n,o,r},0,Wf);const kv=Gd(0,(e,t)=>{const n=typeof t,o="string"===n?t:JSON.stringify({type:n,data:t});localStorage.setItem(e,o)});function Ev(e){const t=localStorage&&localStorage.getItem(e);if(!b(t))throw new Error("data not found");let n=t;try{const e=function(e){const t=["object","string","number","boolean","undefined"];try{const n=b(e)?JSON.parse(e):e,o=n.type;if(t.indexOf(o)>=0){const e=Object.keys(n);if(2===e.length&&"data"in n){if(typeof n.data===o)return n.data;if("object"===o&&/^\d{4}-\d{2}-\d{2}T\d{2}\:\d{2}\:\d{2}\.\d{3}Z$/.test(n.data))return new Date(n.data)}else if(1===e.length)return""}}catch(n){}}(JSON.parse(t));void 0!==e&&(n=e)}catch(o){}return n}const Cv=Gd(0,e=>{try{return Ev(e)}catch(t){return""}}),Pv=Gd(0,e=>{localStorage&&localStorage.removeItem(e)}),Lv=Kd("hideKeyboard",(e,{resolve:t,reject:n})=>{const o=document.activeElement;!o||"TEXTAREA"!==o.tagName&&"INPUT"!==o.tagName||(o.blur(),t())}),Ov={image:{jpg:"jpeg",jpe:"jpeg",pbm:"x-portable-bitmap",pgm:"x-portable-graymap",pnm:"x-portable-anymap",ppm:"x-portable-pixmap",psd:"vnd.adobe.photoshop",pic:"x-pict",rgb:"x-rgb",svg:"svg+xml",svgz:"svg+xml",tif:"tiff",xif:"vnd.xiff",wbmp:"vnd.wap.wbmp",wdp:"vnd.ms-photo",xbm:"x-xbitmap",ico:"x-icon"},video:{"3g2":"3gpp2","3gp":"3gpp",avi:"x-msvideo",f4v:"x-f4v",flv:"x-flv",jpgm:"jpm",jpgv:"jpeg",m1v:"mpeg",m2v:"mpeg",mpe:"mpeg",mpg:"mpeg",mpg4:"mpeg",m4v:"x-m4v",mkv:"x-matroska",mov:"quicktime",qt:"quicktime",movie:"x-sgi-movie",mp4v:"mp4",ogv:"ogg",smv:"x-smv",wm:"x-ms-wm",wmv:"x-ms-wmv",wmx:"x-ms-wmx",wvx:"x-ms-wvx"}};function Iv({count:e,sourceType:t,type:n,extension:o}){dm();const r=document.createElement("input");return r.type="file",function(e,t){for(const n in t)e.style[n]=t[n]}(r,{position:"absolute",visibility:"hidden",zIndex:"-999",width:"0",height:"0",top:"0",left:"0"}),r.accept=o.map(e=>{if("all"!==n){const t=e.replace(".","");return`${n}/${Ov[n][t]||t}`}return function(){const e=window.navigator.userAgent.toLowerCase().match(/MicroMessenger/i);return!(!e||"micromessenger"!==e[0])}()?".":0===e.indexOf(".")?e:`.${e}`}).join(","),e&&e>1&&(r.multiple=!0),"all"!==n&&t instanceof Array&&1===t.length&&"camera"===t[0]&&r.setAttribute("capture","camera"),r}let Av=null;const Mv=Kd("chooseFile",({count:e,sourceType:t,type:n,extension:o},{resolve:r,reject:i})=>{xc();const{t:s}=gc();Av&&(document.body.removeChild(Av),Av=null),Av=Iv({count:e,sourceType:t,type:n,extension:o}),document.body.appendChild(Av),Av.addEventListener("change",function(t){const n=t.target,o=[];if(n&&n.files){const t=n.files.length;for(let r=0;r<t;r++){const t=n.files[r];let i;Object.defineProperty(t,"path",{get:()=>(i=i||Dh(t),i)}),r<e&&o.push(t)}}r({get tempFilePaths(){return o.map(({path:e})=>e)},tempFiles:o})}),Av.click(),fm()},0,qf);let Rv=null;const $v=Kd("chooseImage",({count:e,sourceType:t,extension:n},{resolve:o,reject:r})=>{xc();const{t:i}=gc();Rv&&(document.body.removeChild(Rv),Rv=null),Rv=Iv({count:e,sourceType:t,extension:n,type:"image"}),document.body.appendChild(Rv),Rv.addEventListener("change",function(t){const n=t.target,r=[];if(n&&n.files){const t=n.files.length;for(let o=0;o<t;o++){const t=n.files[o];let i;Object.defineProperty(t,"path",{get:()=>(i=i||Dh(t),i)}),o<e&&r.push(t)}}o({get tempFilePaths(){return r.map(({path:e})=>e)},tempFiles:r})}),Rv.click(),fm()},0,Hf),Nv={esc:["Esc","Escape"],enter:["Enter"]},Dv=Object.keys(Nv);const Fv=zi("div",{class:"uni-mask"},null,-1);function Bv(e,t,n){return t.onClose=(...e)=>(t.visible=!1,n.apply(null,e)),pa(ir({setup:()=>()=>(Ai(),Di(e,t,null,16))}))}function jv(e){let t=document.getElementById(e);return t||(t=document.createElement("div"),t.id=e,document.body.append(t)),t}function Vv(e,{onEsc:t,onEnter:n}){const o=Bn(e.visible),{key:r,disable:i}=function(){const e=Bn(""),t=Bn(!1),n=n=>{if(t.value)return;const o=Dv.find(e=>-1!==Nv[e].indexOf(n.key));o&&(e.value=o),uo(()=>e.value="")};return kr(()=>{document.addEventListener("keyup",n)}),Pr(()=>{document.removeEventListener("keyup",n)}),{key:e,disable:t}}();return Vo(()=>e.visible,e=>o.value=e),Vo(()=>o.value,e=>i.value=!e),Bo(()=>{const{value:e}=r;"esc"===e?t&&t():"enter"===e&&n&&n()}),o}let Wv=0,Hv="";function zv(e){let t=Wv;Wv+=e?1:-1,Wv=Math.max(0,Wv),Wv>0?0===t&&(Hv=document.body.style.overflow,document.body.style.overflow="hidden"):(document.body.style.overflow=Hv,Hv="")}const Uv=rd({name:"ImageView",props:{src:{type:String,default:""}},setup(e){const t=Tn({direction:"none"});let n=1,o=0,r=0,i=0,s=0;function a({detail:e}){n=e.scale}function l(e){const t=e.target.getBoundingClientRect();o=t.width,r=t.height}function c(e){const t=e.target.getBoundingClientRect();i=t.width,s=t.height,d(e)}function u(e){const a=n*o>i,l=n*r>s;t.direction=a&&l?"all":a?"horizontal":l?"vertical":"none",d(e)}function d(e){"all"!==t.direction&&"horizontal"!==t.direction||e.stopPropagation()}return()=>{const n={position:"absolute",left:"0",top:"0",width:"100%",height:"100%"};return zi(Im,{style:n,onTouchstart:id(c),onTouchmove:id(d),onTouchend:id(u)},{default:()=>[zi(zm,{style:n,direction:t.direction,inertia:!0,scale:!0,"scale-min":"1","scale-max":"4",onScale:a},{default:()=>[zi("img",{src:e.src,style:{position:"absolute",left:"50%",top:"50%",transform:"translate(-50%, -50%)",maxHeight:"100%",maxWidth:"100%"},onLoad:l},null,40,["src","onLoad"])]},8,["style","direction","inertia","scale","onScale"])]},8,["style","onTouchstart","onTouchmove","onTouchend"])}}});function qv(e){let t="number"==typeof e.current?e.current:e.urls.indexOf(e.current);return t=t<0?0:t,t}const Yv=rd({name:"ImagePreview",props:{urls:{type:Array,default:()=>[]},current:{type:[Number,String],default:0}},emits:["close"],setup(e,{emit:t}){kr(()=>zv(!0)),Lr(()=>zv(!1));const n=Bn(null),o=Bn(qv(e));let r;function i(){r||uo(()=>{t("close")})}function s(e){o.value=e.detail.current}Vo(()=>e.current,()=>o.value=qv(e)),kr(()=>{const e=n.value;let t=0,o=0;e.addEventListener("mousedown",e=>{r=!1,t=e.clientX,o=e.clientY}),e.addEventListener("mouseup",e=>{(Math.abs(e.clientX-t)>20||Math.abs(e.clientY-o)>20)&&(r=!0)})});const a={position:"absolute","box-sizing":"border-box",top:"0",right:"0",width:"60px",height:"44px",padding:"6px","line-height":"32px","font-size":"26px",color:"white","text-align":"center",cursor:"pointer"};return()=>{let t;return zi("div",{ref:n,style:{display:"block",position:"fixed",left:"0",top:"0",width:"100%",height:"100%",zIndex:999,background:"rgba(0,0,0,0.8)"},onClick:i},[zi(hg,{navigation:"auto",current:o.value,onChange:s,"indicator-dots":!1,autoplay:!1,style:{position:"absolute",left:"0",top:"0",width:"100%",height:"100%"}},(r=t=e.urls.map(e=>zi(gg,null,{default:()=>[zi(Uv,{src:e},null,8,["src"])]})),"function"==typeof r||"[object Object]"===Object.prototype.toString.call(r)&&!Fi(r)?t:{default:()=>[t],_:1}),8,["current","onChange"]),zi("div",{style:a},[hu("M17.25 16.156l7.375-7.313q0.281-0.281 0.281-0.641t-0.281-0.641q-0.25-0.25-0.625-0.25t-0.625 0.25l-7.375 7.344-7.313-7.344q-0.25-0.25-0.625-0.25t-0.625 0.25q-0.281 0.25-0.281 0.625t0.281 0.625l7.313 7.344-7.375 7.344q-0.281 0.25-0.281 0.625t0.281 0.625q0.125 0.125 0.281 0.188t0.344 0.063q0.156 0 0.328-0.063t0.297-0.188l7.375-7.344 7.375 7.406q0.125 0.156 0.297 0.219t0.328 0.063q0.188 0 0.344-0.078t0.281-0.203q0.281-0.25 0.281-0.609t-0.281-0.641l-7.375-7.406z","#ffffff",26)],4)],8,["onClick"]);var r}}});let Xv,Gv=null;const Kv=()=>{Gv=null,uo(()=>{null==Xv||Xv.unmount(),Xv=null})},Jv=Kd("previewImage",(e,{resolve:t})=>{Gv?d(Gv,e):(Gv=Tn(e),uo(()=>{Xv=Bv(Yv,Gv,Kv),Xv.mount(jv("u-a-p"))})),t()},0,Yf);let Zv=null;const Qv=Kd("chooseVideo",({sourceType:e,extension:t},{resolve:n,reject:o})=>{xc();const{t:r}=gc();Zv&&(document.body.removeChild(Zv),Zv=null),Zv=Iv({sourceType:e,extension:t,type:"video"}),document.body.appendChild(Zv),Zv.addEventListener("change",function(e){const t=e.target.files[0];let o="";const r={tempFilePath:o,tempFile:t,size:t.size,duration:0,width:0,height:0,name:t.name};Object.defineProperty(r,"tempFilePath",{get(){return o=o||Dh(this.tempFile),o}});const i=document.createElement("video");if(void 0!==i.onloadedmetadata){const e=Dh(t);i.onloadedmetadata=function(){Fh(e),n(d(r,{duration:i.duration||0,width:i.videoWidth||0,height:i.videoHeight||0}))},setTimeout(()=>{i.onloadedmetadata=null,Fh(e),n(r)},300),i.src=e}else n(r)}),Zv.click(),fm()},0,zf),ey=Xd("request",({url:e,data:t,header:n={},method:o,dataType:r,responseType:i,withCredentials:s,timeout:a=__uniConfig.networkTimeout.request},{resolve:l,reject:c})=>{let u=null;const d=function(e){const t=Object.keys(e).find(e=>"content-type"===e.toLowerCase());if(!t)return;const n=e[t];if(0===n.indexOf("application/json"))return"json";if(0===n.indexOf("application/x-www-form-urlencoded"))return"urlencoded";return"string"}(n);if("GET"!==o)if(b(t)||t instanceof ArrayBuffer)u=t;else if("json"===d)try{u=JSON.stringify(t)}catch(g){u=t.toString()}else if("urlencoded"===d){const e=[];for(const n in t)h(t,n)&&e.push(encodeURIComponent(n)+"="+encodeURIComponent(t[n]));u=e.join("&")}else u=t.toString();const f=new XMLHttpRequest,p=new ty(f);f.open(o,e);for(const v in n)h(n,v)&&f.setRequestHeader(v,n[v]);const m=setTimeout(function(){f.onload=f.onabort=f.onerror=null,p.abort(),c("timeout",{errCode:5})},a);return f.responseType=i,f.onload=function(){clearTimeout(m);const e=f.status;let t="text"===i?f.responseText:f.response;if("text"===i&&"json"===r)try{t=JSON.parse(t)}catch(g){}l({data:t,statusCode:e,header:ny(f.getAllResponseHeaders()),cookies:[]})},f.onabort=function(){clearTimeout(m),c("abort",{errCode:600003})},f.onerror=function(){clearTimeout(m),c(void 0,{errCode:5})},f.withCredentials=s,f.send(u),p},0,Jf);class ty{constructor(e){this._xhr=e}abort(){this._xhr&&(this._xhr.abort(),delete this._xhr)}onHeadersReceived(e){throw new Error("Method not implemented.")}offHeadersReceived(e){throw new Error("Method not implemented.")}}function ny(e){const t={};return e.split(ne).forEach(e=>{const n=e.match(/(\S+\s*):\s*(.*)/);n&&3===n.length&&(t[n[1]]=n[2])}),t}class oy{constructor(e){this._callbacks=[],this._xhr=e}onProgressUpdate(e){y(e)&&this._callbacks.push(e)}offProgressUpdate(e){const t=this._callbacks.indexOf(e);t>=0&&this._callbacks.splice(t,1)}abort(){this._xhr&&(this._xhr.abort(),delete this._xhr)}onHeadersReceived(e){throw new Error("Method not implemented.")}offHeadersReceived(e){throw new Error("Method not implemented.")}}const ry=Xd("downloadFile",({url:e,header:t={},timeout:n=__uniConfig.networkTimeout.downloadFile},{resolve:o,reject:r})=>{var i,s=new XMLHttpRequest,a=new oy(s);return s.open("GET",e,!0),Object.keys(t).forEach(e=>{s.setRequestHeader(e,t[e])}),s.responseType="blob",s.onload=function(){clearTimeout(i);const t=s.status,n=this.response;let r;const a=s.getResponseHeader("content-disposition");if(a){const e=a.match(/filename="?(\S+)"?\b/);e&&(r=e[1])}n.name=r||function(e){const t=(e=e.split("#")[0].split("?")[0]).split("/");return t[t.length-1]}(e),o({statusCode:t,tempFilePath:Dh(n)})},s.onabort=function(){clearTimeout(i),r("abort",{errCode:600003})},s.onerror=function(){clearTimeout(i),r("",{errCode:602001})},s.onprogress=function(e){a._callbacks.forEach(t=>{var n=e.loaded,o=e.total;t({progress:Math.round(n/o*100),totalBytesWritten:n,totalBytesExpectedToWrite:o})})},s.send(),i=setTimeout(function(){s.onprogress=s.onload=s.onabort=s.onerror=null,a.abort(),r("timeout",{errCode:5})},n),a},0,Zf);class iy{constructor(e){this._callbacks=[],this._xhr=e}onProgressUpdate(e){y(e)&&this._callbacks.push(e)}offProgressUpdate(e){const t=this._callbacks.indexOf(e);t>=0&&this._callbacks.splice(t,1)}abort(){this._isAbort=!0,this._xhr&&(this._xhr.abort(),delete this._xhr)}onHeadersReceived(e){throw new Error("Method not implemented.")}offHeadersReceived(e){throw new Error("Method not implemented.")}}const sy=Xd("uploadFile",({url:e,file:t,filePath:n,name:o,files:r,header:i={},formData:s={},timeout:a=__uniConfig.networkTimeout.uploadFile},{resolve:l,reject:c})=>{var u=new iy;return m(r)&&r.length||(r=[{name:o,file:t,uri:n}]),Promise.all(r.map(({file:e,uri:t})=>e instanceof Blob?Promise.resolve(Nh(e)):$h(t))).then(function(t){var n,o=new XMLHttpRequest,d=new FormData;Object.keys(s).forEach(e=>{d.append(e,s[e])}),Object.values(r).forEach(({name:e},n)=>{const o=t[n];d.append(e||"file",o,o.name||`file-${Date.now()}`)}),o.open("POST",e),Object.keys(i).forEach(e=>{o.setRequestHeader(e,i[e])}),o.upload.onprogress=function(e){u._callbacks.forEach(t=>{var n=e.loaded,o=e.total;t({progress:Math.round(n/o*100),totalBytesSent:n,totalBytesExpectedToSend:o})})},o.onerror=function(){clearTimeout(n),c("",{errCode:602001})},o.onabort=function(){clearTimeout(n),c("abort",{errCode:600003})},o.onload=function(){clearTimeout(n);const e=o.status;l({statusCode:e,data:o.responseText||o.response})},u._isAbort?c("abort",{errCode:600003}):(n=setTimeout(function(){o.upload.onprogress=o.onload=o.onabort=o.onerror=null,u.abort(),c("timeout",{errCode:5})},a),o.send(d),u._xhr=o)}).catch(()=>{setTimeout(()=>{c("file error")},0)}),u},0,Qf),ay=Kd("navigateBack",(e,{resolve:t,reject:n})=>{let o=!0;return!0===Su(be,{from:e.from||"navigateBack"})&&(o=!1),o?(Gg().$router.go(-e.delta),t()):n(be)},0,up),ly=Kd(tp,({url:e,events:t,isAutomatedTesting:n},{resolve:o,reject:r})=>{if(Kp.handledBeforeEntryPageRoutes)return Dp({type:tp,url:e,events:t,isAutomatedTesting:n}).then(o).catch(r);Jp.push({args:{type:tp,url:e,events:t,isAutomatedTesting:n},resolve:o,reject:r})},0,sp);function cy(e){__uniConfig.darkmode&&ib.on(de,e)}function uy(e){ib.off(de,e)}function dy(e){let t={};return __uniConfig.darkmode&&(t=ft(e,__uniConfig.themeConfig,hv())),__uniConfig.darkmode?t:e}const fy={light:{cancelColor:"#000000"},dark:{cancelColor:"rgb(170, 170, 170)"}},py=ir({props:{title:{type:String,default:""},content:{type:String,default:""},showCancel:{type:Boolean,default:!0},cancelText:{type:String,default:"Cancel"},cancelColor:{type:String,default:"#000000"},confirmText:{type:String,default:"OK"},confirmColor:{type:String,default:"#007aff"},visible:{type:Boolean},editable:{type:Boolean,default:!1},placeholderText:{type:String,default:""}},setup(e,{emit:t}){const n=Bn(""),o=()=>s.value=!1,r=()=>(o(),t("close","cancel")),i=()=>(o(),t("close","confirm",n.value)),s=Vv(e,{onEsc:r,onEnter:()=>{!e.editable&&i()}}),a=function(e){const t=Bn(e.cancelColor),n=({theme:e})=>{((e,t)=>{t.value=fy[e].cancelColor})(e,t)};return Bo(()=>{e.visible?(t.value=e.cancelColor,"#000"===e.cancelColor&&("dark"===hv()&&n({theme:"dark"}),cy(n))):uy(n)}),t}(e);return()=>{const{title:t,content:o,showCancel:l,confirmText:c,confirmColor:u,editable:d,placeholderText:f}=e;return n.value=o,zi(Ts,{name:"uni-fade"},{default:()=>[qo(zi("uni-modal",{onTouchmove:eu},[Fv,zi("div",{class:"uni-modal"},[t?zi("div",{class:"uni-modal__hd"},[zi("strong",{class:"uni-modal__title",textContent:t||""},null,8,["textContent"])]):null,d?zi("textarea",{class:"uni-modal__textarea",rows:"1",placeholder:f,value:o,onInput:e=>n.value=e.target.value},null,40,["placeholder","value","onInput"]):zi("div",{class:"uni-modal__bd",onTouchmovePassive:tu,textContent:o},null,40,["onTouchmovePassive","textContent"]),zi("div",{class:"uni-modal__ft"},[l&&zi("div",{style:{color:a.value},class:"uni-modal__btn uni-modal__btn_default",onClick:r},[e.cancelText],12,["onClick"]),zi("div",{style:{color:u},class:"uni-modal__btn uni-modal__btn_primary",onClick:i},[c],12,["onClick"])])])],40,["onTouchmove"]),[[Ds,s.value]])]})}}});let hy;const my=Fe(()=>{ib.on("onHidePopup",()=>hy.visible=!1)});let gy;function vy(e,t){const n="confirm"===e,o={confirm:n,cancel:"cancel"===e};n&&hy.editable&&(o.content=t),gy&&gy(o)}const yy=Kd("showModal",(e,{resolve:t})=>{my(),gy=t,hy?(d(hy,e),hy.visible=!0):(hy=Tn(e),uo(()=>(Bv(py,hy,vy).mount(jv("u-a-m")),uo(()=>hy.visible=!0))))},0,yp),by={title:{type:String,default:""},icon:{default:"success",validator:e=>-1!==bp.indexOf(e)},image:{type:String,default:""},duration:{type:Number,default:1500},mask:{type:Boolean,default:!1},visible:{type:Boolean}},_y="uni-toast__icon",wy={light:"#fff",dark:"rgba(255,255,255,0.9)"},xy=e=>wy[e],Ty=ir({name:"Toast",props:by,setup(e){bc(),_c();const{Icon:t}=function(e){const t=Bn(xy(hv())),n=({theme:e})=>t.value=xy(e);Bo(()=>{e.visible?cy(n):uy(n)});const o=ps(()=>{switch(e.icon){case"success":return zi(hu(fu,t.value,38),{class:_y});case"error":return zi(hu(pu,t.value,38),{class:_y});case"loading":return zi("i",{class:[_y,"uni-loading"]},null,2);default:return null}});return{Icon:o}}(e),n=Vv(e,{});return()=>{const{mask:o,duration:r,title:i,image:s}=e;return zi(Ts,{name:"uni-fade"},{default:()=>[qo(zi("uni-toast",{"data-duration":r},[o?zi("div",{class:"uni-mask",style:"background: transparent;",onTouchmove:eu},null,40,["onTouchmove"]):"",s||t.value?zi("div",{class:"uni-toast"},[s?zi("img",{src:s,class:_y},null,10,["src"]):t.value,zi("p",{class:"uni-toast__content"},[i])]):zi("div",{class:"uni-sample-toast"},[zi("p",{class:"uni-simple-toast__text"},[i])])],8,["data-duration"]),[[Ds,n.value]])]})}}});let Sy,ky,Ey="";const Cy=gt();function Py(e){Sy?d(Sy,e):(Sy=Tn(d(e,{visible:!1})),uo(()=>{Cy.run(()=>{Vo([()=>Sy.visible,()=>Sy.duration],([e,t])=>{if(e){if(ky&&clearTimeout(ky),"onShowLoading"===Ey)return;ky=setTimeout(()=>{Iy("onHideToast")},t)}else ky&&clearTimeout(ky)})}),ib.on("onHidePopup",()=>Iy("onHidePopup")),Bv(Ty,Sy,()=>{}).mount(jv("u-a-t"))})),setTimeout(()=>{Sy.visible=!0},10)}const Ly=Kd("showToast",(e,{resolve:t,reject:n})=>{Py(e),Ey="onShowToast",t()},0,_p),Oy=Kd("hideLoading",(e,{resolve:t,reject:n})=>{Iy("onHideLoading"),t()});function Iy(e){const{t:t}=gc();if(!Ey)return;let n="";"onHideToast"===e&&"onShowToast"!==Ey?n=t("uni.showToast.unpaired"):"onHideLoading"===e&&"onShowLoading"!==Ey&&(n=t("uni.showLoading.unpaired")),n||(Ey="",setTimeout(()=>{Sy.visible=!1},10))}const Ay=Kd("loadFontFace",({family:e,source:t,desc:n},{resolve:o,reject:r})=>{(function(e,t,n){const o=document.fonts;if(o){const r=new FontFace(e,t,n);return r.load().then(()=>{o.add&&o.add(r)})}return new Promise(o=>{const r=document.createElement("style"),i=[];if(n){const{style:e,weight:t,stretch:o,unicodeRange:r,variant:s,featureSettings:a}=n;e&&i.push(`font-style:${e}`),t&&i.push(`font-weight:${t}`),o&&i.push(`font-stretch:${o}`),r&&i.push(`unicode-range:${r}`),s&&i.push(`font-variant:${s}`),a&&i.push(`font-feature-settings:${a}`)}r.innerText=`@font-face{font-family:"${e}";src:${t};${i.join(";")}}`,document.head.appendChild(r),o()})})(e,t=t.startsWith('url("')||t.startsWith("url('")?`url('${bh(t.substring(5,t.length-2))}')`:t.startsWith("url(")?`url('${bh(t.substring(4,t.length-1))}')`:bh(t),n).then(()=>{o()}).catch(e=>{r(`loadFontFace:fail ${e}`)})});function My(e){function t(){var t;t=e.navigationBar.titleText,document.title=t,ib.emit("onNavigationBarChange",{titleText:t})}Bo(t),hr(t)}const Ry=Kd(gp,(e,{resolve:t,reject:n})=>{!function(e,t,n,o,r){if(!e)return r("page not found");const{navigationBar:i}=e;switch(t){case"setNavigationBarColor":const{frontColor:e,backgroundColor:t,animation:o}=n,{duration:r,timingFunc:s}=o;e&&(i.titleColor="#000000"===e?"#000000":"#ffffff"),t&&(i.backgroundColor=t),i.duration=r+"ms",i.timingFunc=s;break;case"showNavigationBarLoading":i.loading=!0;break;case"hideNavigationBarLoading":i.loading=!1;break;case gp:const{title:a}=n;i.titleText=a}o()}(yu(),gp,e,t,n)}),$y=Kd("pageScrollTo",({scrollTop:e,selector:t,duration:n},{resolve:o})=>{!function(e,t){if(b(e)){const t=document.querySelector(e);if(t){const{top:n}=t.getBoundingClientRect();e=n+window.pageYOffset;const o=document.querySelector("uni-page-head");o&&(e-=o.offsetHeight)}}e<0&&(e=0);const n=document.documentElement,{clientHeight:o,scrollHeight:r}=n;if(e=Math.min(e,r-o),0===t)return void(n.scrollTop=document.body.scrollTop=e);if(window.scrollY===e)return;const i=t=>{if(t<=0)return void window.scrollTo(0,e);const n=e-window.scrollY;requestAnimationFrame(function(){window.scrollTo(0,window.scrollY+n/t*10),i(t-10)})};i(t)}(t||e||0,n),o()},0,vp),Ny=["text","iconPath","iconfont","selectedIconPath","visible"],Dy=["color","selectedColor","backgroundColor","borderStyle","borderColor","midButton"],Fy=["badge","redDot"];function By(e,t,n){t.forEach(function(t){h(n,t)&&(e[t]=n[t])})}function jy(e,t,n,o){var r;let i=!1;const s=oh();if(s.length&&Gp(s[s.length-1]).meta.isTabBar&&(i=!0),!i)return o("not TabBar page");const{index:a}=t;if("number"==typeof a){const e=null==(r=null==__uniConfig?void 0:__uniConfig.tabBar)?void 0:r.list.length;if(!e||a>=e)return o("tabbar item not found")}const l=jp();switch(e){case kp:l.shown=!0;break;case Sp:l.shown=!1;break;case xp:const e=l.list[a],n=e.pagePath;By(e,Ny,t);const{pagePath:o}=t;if(o){const e=Ne(o);e!==n&&function(e,t,n){const o=Ou(Ne(t));if(o){const{meta:e}=o;delete e.tabBarIndex,e.isQuit=e.isTabBar=!1}const r=Ou(Ne(n));if(r){const{meta:t}=r;t.tabBarIndex=e,t.isQuit=t.isTabBar=!0;const o=__uniConfig.tabBar;o&&o.list&&o.list[e]&&(o.list[e].pagePath=De(n))}}(a,n,e)}break;case"setTabBarStyle":By(l,Dy,t);break;case"showTabBarRedDot":By(l.list[a],Fy,{badge:"",redDot:!0});break;case"setTabBarBadge":By(l.list[a],Fy,{badge:t.text,redDot:!0});break;case"hideTabBarRedDot":case"removeTabBarBadge":By(l.list[a],Fy,{badge:"",redDot:!1})}n()}const Vy=Kd(xp,(e,{resolve:t,reject:n})=>{jy(xp,e,t,n)},0,Tp),Wy=Kd(Sp,(e,{resolve:t,reject:n})=>{jy(Sp,e||{},t,n)}),Hy=Kd(kp,(e,{resolve:t,reject:n})=>{jy(kp,e||{},t,n)}),zy=rd({name:"TabBar",setup(){const e=Bn([]),t=jp(),n=function(e,t){const n=Cn(e),o=n?Tn(dy(e)):dy(e);return __uniConfig.darkmode&&n&&Vo(e,e=>{const t=dy(e);for(const n in t)o[n]=t[n]}),t&&cy(t),o}(t,()=>{const e=dy(t);n.backgroundColor=e.backgroundColor,n.borderStyle=e.borderStyle,n.color=e.color,n.selectedColor=e.selectedColor,n.blurEffect=e.blurEffect,n.midButton=e.midButton,e.list&&e.list.length&&e.list.forEach((e,t)=>{n.list[t].iconPath=e.iconPath,n.list[t].selectedIconPath=e.selectedIconPath})});!function(e,t){function n(){let n=[];n=e.list.filter(e=>!1!==e.visible),t.value=n}Bn(d({type:"midButton"},e.midButton)),Bo(n)}(n,e),function(e){Vo(()=>e.shown,t=>{su({"--window-bottom":qp(t?parseInt(e.height):0)})})}(n);const o=function(e,t,n){return Bo(()=>{const o=e.meta;if(o.isTabBar){const e=o.route,r=n.value.findIndex(t=>t.pagePath===e);t.selectedIndex=r}}),(t,n)=>{const{type:o}=t;return()=>{const{pagePath:o,text:r}=t;let i=Ne(o);i===__uniRoutes[0].alias&&(i="/"),e.path!==i?Ap({from:"tabBar",url:i,tabBarText:r}):Su("onTabItemTap",{index:n,text:r,pagePath:o})}}}(Gl(),n,e),{style:r,borderStyle:i,placeholderStyle:s}=function(e){const t=ps(()=>{let t=e.backgroundColor;const n=e.blurEffect;return t||zp&&n&&"none"!==n&&(t=Xy[n]),{backgroundColor:t||Uy,backdropFilter:"none"!==n?"blur(10px)":n}}),n=ps(()=>{const{borderStyle:t,borderColor:n}=e;return n&&b(n)?{backgroundColor:n}:{backgroundColor:Gy[t]||Gy.black}}),o=ps(()=>({height:e.height}));return{style:t,borderStyle:n,placeholderStyle:o}}(n);return kr(()=>{n.iconfontSrc&&Ay({family:"UniTabbarIconFont",source:`url("${n.iconfontSrc}")`})}),()=>{const t=function(e,t,n){const{selectedIndex:o,selectedColor:r,color:i}=e;return n.value.map((n,s)=>{const a=o===s;return function(e,t,n,o,r,i,s,a){return zi("div",{key:s,class:"uni-tabbar__item",onClick:a(r,s)},[Ky(e,t||"",n,o,r,i)],8,["onClick"])}(a?r:i,a&&n.selectedIconPath||n.iconPath||"",n.iconfont?a&&n.iconfont.selectedText||n.iconfont.text:void 0,n.iconfont?a&&n.iconfont.selectedColor||n.iconfont.color:void 0,n,e,s,t)})}(n,o,e);return zi("uni-tabbar",{class:"uni-tabbar-"+n.position},[zi("div",{class:"uni-tabbar",style:r.value},[zi("div",{class:"uni-tabbar-border",style:i.value},null,4),t],4),zi("div",{class:"uni-placeholder",style:s.value},null,4)],2)}}});const Uy="#f7f7fa",qy="rgb(0, 0, 0, 0.8)",Yy="rgb(250, 250, 250, 0.8)",Xy={dark:qy,light:Yy,extralight:Yy},Gy={white:"rgba(255, 255, 255, 0.33)",black:"rgba(0, 0, 0, 0.33)"};function Ky(e,t,n,o,r,i){const{height:s}=i;return zi("div",{class:"uni-tabbar__bd",style:{height:s}},[n?Zy(n,o||qy,r,i):t&&Jy(t,r,i),r.text&&Qy(e,r,i),r.redDot&&eb(r.badge)],4)}function Jy(e,t,n){const{type:o,text:r}=t,{iconWidth:i}=n;return zi("div",{class:"uni-tabbar__icon"+(r?" uni-tabbar__icon__diff":""),style:{width:i,height:i}},["midButton"!==o&&zi("img",{src:bh(e)},null,8,["src"])],6)}function Zy(e,t,n,o){var r;const{type:i,text:s}=n,{iconWidth:a}=o,l="uni-tabbar__icon"+(s?" uni-tabbar__icon__diff":""),c={width:a,height:a},u={fontSize:(null==(r=n.iconfont)?void 0:r.fontSize)||a,color:t};return zi("div",{class:l,style:c},["midButton"!==i&&zi("div",{class:"uni-tabbar__iconfont",style:u},[e],4)],6)}function Qy(e,t,n){const{iconPath:o,text:r}=t,{fontSize:i,spacing:s}=n;return zi("div",{class:"uni-tabbar__label",style:{color:e,fontSize:i,lineHeight:o?"normal":1.8,marginTop:o?s:"inherit"}},[r],4)}function eb(e){return zi("div",{class:"uni-tabbar__reddot"+(e?" uni-tabbar__badge":"")},[e],2)}const tb="0px",nb=rd({name:"Layout",setup(e,{emit:t}){const n=Bn(null);iu({"--status-bar-height":tb,"--top-window-height":tb,"--window-left":tb,"--window-right":tb,"--window-margin":tb,"--tab-bar-height":tb});const o=function(){const e=Gl();return{routeKey:ps(()=>lh("/"+e.meta.route,bd())),isTabBar:ps(()=>e.meta.isTabBar),routeCache:uh}}(),{layoutState:r,windowState:i}=function(){yd();{const e=Tn({marginWidth:0,leftWindowWidth:0,rightWindowWidth:0});return Vo(()=>e.marginWidth,e=>iu({"--window-margin":e+"px"})),Vo(()=>e.leftWindowWidth+e.marginWidth,e=>{iu({"--window-left":e+"px"})}),Vo(()=>e.rightWindowWidth+e.marginWidth,e=>{iu({"--window-right":e+"px"})}),{layoutState:e,windowState:ps(()=>({}))}}}();!function(e,t){const n=yd();function o(){const o=document.body.clientWidth,r=oh();let i={};if(r.length>0){i=Gp(r[r.length-1]).meta}else{const e=Ou(n.path,!0);e&&(i=e.meta)}const s=parseInt(String((h(i,"maxWidth")?i.maxWidth:__uniConfig.globalStyle.maxWidth)||Number.MAX_SAFE_INTEGER));let a=!1;a=o>s,a&&s?(e.marginWidth=(o-s)/2,uo(()=>{const e=t.value;e&&e.setAttribute("style","max-width:"+s+"px;margin:0 auto;")})):(e.marginWidth=0,uo(()=>{const e=t.value;e&&e.removeAttribute("style")}))}Vo([()=>n.path],o),kr(()=>{o(),window.addEventListener("resize",o)})}(r,n);const s=function(){const e=yd(),t=jp(),n=ps(()=>e.meta.isTabBar&&t.shown);return iu({"--tab-bar-height":t.height}),n}(),a=function(e){const t=Bn(!1);return ps(()=>({"uni-app--showtabbar":e&&e.value,"uni-app--maxwidth":t.value}))}(s);return()=>{const e=function(e){const t=function({routeKey:e,isTabBar:t,routeCache:n}){return zi(Yl,null,{default:Eo(({Component:o})=>[(Ai(),Di(fr,{matchBy:"key",cache:n},[(Ai(),Di(Ro(o),{type:t.value?"tabBar":"",key:e.value}))],1032,["cache"]))]),_:1})}(e);return t}(o),t=function(e){return qo(zi(zy,null,null,512),[[Ds,e.value]])}(s);return zi("uni-app",{ref:n,class:a.value},[e,t],2)}}});const ob=od({name:"CoverView",compatConfig:{MODE:3},props:{scrollTop:{type:[String,Number],default:0}},setup(e,{slots:t}){const n=Bn(null),o=Bn(null);function r(e){let t=o.value;"scroll"===getComputedStyle(t).overflowY&&(t.scrollTop=function(e){let t=String(e);/\d+[ur]px$/i.test(t)&&t.replace(/\d+[ur]px$/i,e=>String(sf(parseFloat(e))));return parseFloat(t)||0}(e))}return Vo(()=>e.scrollTop,e=>{r(e)}),kr(()=>{r(e.scrollTop)}),()=>zi("uni-cover-view",{"scroll-top":e.scrollTop,ref:n},[zi("div",{ref:o,class:"uni-cover-view"},[t.default&&t.default()],512)],8,["scroll-top"])}}),rb=d(Mc,{publishHandler(e,t,n){ib.subscribeHandler(e,t,n)}}),ib=d(zu,{publishHandler(e,t,n){rb.subscribeHandler(e,t,n)}}),sb=rd({name:"PageBody",setup(e,t){const n=!1,o=Bn(null),r=Bn(null);return Vo(()=>n.enablePullDownRefresh,()=>{r.value=null},{immediate:!0}),()=>zi(Ei,null,[!1,zi("uni-page-wrapper",Ji({ref:o},r.value),[zi("uni-page-body",null,[$r(t.slots,"default")]),null],16)])}}),ab=rd({name:"Page",setup(e,t){let n=vd(bd());n.navigationBar;const o={};return My(n),()=>zi("uni-page",{"data-page":n.route,style:o},[lb(t),null])}});function lb(e){return Ai(),Di(sb,{key:0},{default:Eo(()=>[$r(e.slots,"page")]),_:3})}const cb={loading:"AsyncLoading",error:"AsyncError",delay:200,timeout:6e4,suspensible:!0};window.uni={},window.wx={},window.rpx2px=sf;const ub=Object.assign({}),db=Object.assign;window.__uniConfig=db({easycom:{autoscan:!0,custom:{"^uv-(.*)":"@climblee/uv-ui/components/uv-$1/uv-$1.vue","^uni-(.*)":"@dcloudio/uni-ui/lib/uni-$1/uni-$1.vue"}},globalStyle:{backgroundColor:"#F6F7F9",navigationBar:{backgroundColor:"#F8F8F8",titleText:"uni-app",type:"default",titleColor:"#000000"},isNVue:!1},tabBar:{position:"bottom",color:"#444B56",selectedColor:"#0165FF",borderStyle:"white",blurEffect:"none",fontSize:"24rpx",iconWidth:"24px",spacing:"3px",height:"50px",list:[{pagePath:"pages/index/index",iconPath:"/static/image/tabBar/shouye01.png",selectedIconPath:"/static/image/tabBar/shouye02.png",text:"Home"},{pagePath:"pages/team/index",iconPath:"/static/image/tabBar/tuandui01.png",selectedIconPath:"/static/image/tabBar/tuandui02.png",text:"Team"},{pagePath:"pages/my/index",iconPath:"/static/image/tabBar/my01.png",selectedIconPath:"/static/image/tabBar/my02.png",text:"Account"}],backgroundColor:"#ffffff",selectedIndex:0,shown:!0},compilerVersion:"4.64"},{appId:"__UNI__035C603",appName:"RenTo",appVersion:"1.0.1",appVersionCode:101,async:cb,debug:!1,networkTimeout:{request:6e4,connectSocket:6e4,uploadFile:6e4,downloadFile:6e4},sdkConfigs:{},qqMapKey:void 0,bMapKey:void 0,googleMapKey:void 0,aMapKey:void 0,aMapSecurityJsCode:void 0,aMapServiceHost:void 0,nvue:{"flex-direction":"column"},locale:"",fallbackLocale:"",locales:Object.keys(ub).reduce((e,t)=>{const n=t.replace(/\.\/locale\/(uni-app.)?(.*).json/,"$2");return db(e[n]||(e[n]={}),ub[t].default),e},{}),router:{mode:"hash",base:"/",assets:"assets",routerBase:"/"},darkmode:!1,themeConfig:{}}),window.__uniLayout=window.__uniLayout||{};const fb={delay:cb.delay,timeout:cb.timeout,suspensible:cb.suspensible};cb.loading&&(fb.loadingComponent={name:"SystemAsyncLoading",render:()=>zi(Ao(cb.loading))}),cb.error&&(fb.errorComponent={name:"SystemAsyncError",render:()=>zi(Ao(cb.error))});const pb=()=>o(()=>import("./pages-index-index.DrimU85T.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29])).then(e=>Qg(e.default||e)),hb=ar(db({loader:pb},fb)),mb=()=>o(()=>import("./pages-search-index.B6yUP09N.js"),__vite__mapDeps([30,11,12,13,3,4,14,15,16,17,18,19,20,21,31,32,26,27,25,33])).then(e=>Qg(e.default||e)),gb=ar(db({loader:mb},fb)),vb=()=>o(()=>import("./pages-team-index.BB0SFNjK.js"),__vite__mapDeps([34,18,3,4,19,1,2,5,6,7,22,23,24,35,16,17,36,37])).then(e=>Qg(e.default||e)),yb=ar(db({loader:vb},fb)),bb=()=>o(()=>import("./pages-my-index.DaQeUbl0.js"),__vite__mapDeps([38,8,1,2,3,4,5,6,7,9,10,39,22,23,24,26,27,40])).then(e=>Qg(e.default||e)),_b=ar(db({loader:bb},fb)),wb=()=>o(()=>import("./pages-my-wallet-index.DP5H9dBN.js"),__vite__mapDeps([41,12,13,3,4,14,42,43,5,6,9,44,15,16,17,18,19,20,45,26,27,1,2,7,46,28,47,48])).then(e=>Qg(e.default||e)),xb=ar(db({loader:wb},fb)),Tb=()=>o(()=>import("./pages-my-wallet-fund.Do4Ow9L1.js"),__vite__mapDeps([49,50,3,4,51,43,5,6,9,44,52,53,46,45,12,13,14,26,27,1,2,7,28,47,54])).then(e=>Qg(e.default||e)),Sb=ar(db({loader:Tb},fb)),kb=()=>o(()=>import("./pages-my-wallet-tips.D-6_2IF1.js"),__vite__mapDeps([55,56,43,3,4,5,6,9,44,57])).then(e=>Qg(e.default||e)),Eb=ar(db({loader:kb},fb)),Cb=()=>o(()=>import("./pages-my-notice-index.Dd87FZ8y.js"),__vite__mapDeps([58,12,13,3,4,14,43,5,6,9,44,59,60,61,62,15,16,17,18,19,20,63,64,28,65])).then(e=>Qg(e.default||e)),Pb=ar(db({loader:Cb},fb)),Lb=()=>o(()=>import("./pages-my-notice-detail.CLrmZBc7.js"),__vite__mapDeps([66,67,3,4,68,43,5,6,9,44,23,24,63,64])).then(e=>Qg(e.default||e)),Ob=ar(db({loader:Lb},fb)),Ib=()=>o(()=>import("./pages-my-filings-index.CoHdojQ0.js"),__vite__mapDeps([69,12,13,3,4,14,43,5,6,9,44,15,16,17,18,19,20,26,27,39,28])).then(e=>Qg(e.default||e)),Ab=ar(db({loader:Ib},fb)),Mb=()=>o(()=>import("./pages-my-info-index.Bhuau0sL.js"),__vite__mapDeps([70,22,3,4,23,24,1,2,5,6,7,71,72,43,9,44,73])).then(e=>Qg(e.default||e)),Rb=ar(db({loader:Mb},fb)),$b=()=>o(()=>import("./pages-my-info-security.FDz5YJr_.js"),__vite__mapDeps([74,22,3,4,23,24,1,2,5,6,7,50,51,75,76,43,9,44,77,78,79,80,52,53,73,64,59,60,71,72,81])).then(e=>Qg(e.default||e)),Nb=ar(db({loader:$b},fb)),Db=()=>o(()=>import("./pages-my-settlement-index.1TnO0ru9.js"),__vite__mapDeps([82,12,13,3,4,14,42,43,5,6,9,44,15,16,17,18,19,20,26,27,83,1,2,7,84,28])).then(e=>Qg(e.default||e)),Fb=ar(db({loader:Db},fb)),Bb=()=>o(()=>import("./pages-my-income-index.BZpUl0fm.js"),__vite__mapDeps([85,3,4,43,5,6,9,44,64])).then(e=>Qg(e.default||e)),jb=ar(db({loader:Bb},fb)),Vb=()=>o(()=>import("./pages-my-income-details.D4C0U5tN.js"),__vite__mapDeps([86,3,4,43,5,6,9,44,61,62])).then(e=>Qg(e.default||e)),Wb=ar(db({loader:Vb},fb)),Hb=()=>o(()=>import("./pages-my-income-detailsSearch.B_X4DLl9.js"),__vite__mapDeps([87,31,3,4,32,25,88])).then(e=>Qg(e.default||e)),zb=ar(db({loader:Hb},fb)),Ub=()=>o(()=>import("./pages-my-settlement-details.DTvJ0iMR.js"),__vite__mapDeps([89,43,3,4,5,6,9,44])).then(e=>Qg(e.default||e)),qb=ar(db({loader:Ub},fb)),Yb=()=>o(()=>import("./pages-my-orderHistory-index.C8qHhe8G.js"),__vite__mapDeps([90,91,92,50,3,4,51,12,13,14,52,53,93,94,15,16,17,18,19,20,1,2,5,6,7,9,83,84,95,64,63,28,43,44])).then(e=>Qg(e.default||e)),Xb=ar(db({loader:Yb},fb)),Gb=()=>o(()=>import("./pages-my-improve-index.BC2uRMdL.js"),__vite__mapDeps([96,97,3,4,52,53,77,78,79,80,43,5,6,9,44,98,99])).then(e=>Qg(e.default||e)),Kb=ar(db({loader:Gb},fb)),Jb=()=>o(()=>import("./pages-my-bankCard-index.3EoR0nFR.js"),__vite__mapDeps([100,12,13,3,4,14,22,23,24,1,2,5,6,7,43,9,44,46,15,16,17,18,19,20,26,27,101,28])).then(e=>Qg(e.default||e)),Zb=ar(db({loader:Jb},fb)),Qb=()=>o(()=>import("./pages-my-bankCard-add.CjkiTyRW.js"),__vite__mapDeps([102,1,2,3,4,5,6,7,50,51,43,9,44,52,53,26,27,83,18,19,84,46,101,103])).then(e=>Qg(e.default||e)),e_=ar(db({loader:Qb},fb)),t_=()=>o(()=>import("./pages-my-media-index.BvCkRSzm.js"),__vite__mapDeps([104,12,13,3,4,14,105,43,5,6,9,44,59,60,61,62,15,16,17,18,19,20,26,27,106,107,28])).then(e=>Qg(e.default||e)),n_=ar(db({loader:t_},fb)),o_=()=>o(()=>import("./pages-my-fund-index.DGCQdzm7.js"),__vite__mapDeps([108,91,92,50,3,4,51,12,13,14,52,53,93,94,15,16,17,18,19,20,1,2,5,6,7,9,83,84,95,64,63,28,43,44,26,27])).then(e=>Qg(e.default||e)),r_=ar(db({loader:o_},fb)),i_=()=>o(()=>import("./pages-my-media-add.fnrpn6P-.js"),__vite__mapDeps([109,22,3,4,23,24,1,2,5,6,7,50,51,110,12,13,14,52,53,93,94,15,16,17,18,19,20,9,92,83,84,95,111,64,112,43,44,106,28,113,26,27,107,114])).then(e=>Qg(e.default||e)),s_=ar(db({loader:i_},fb)),a_=()=>o(()=>import("./pages-my-media-tips.BGdMetpK.js"),__vite__mapDeps([115,56,43,3,4,5,6,9,44,116])).then(e=>Qg(e.default||e)),l_=ar(db({loader:a_},fb)),c_=()=>o(()=>import("./pages-my-improve-country.DcX8mBvP.js"),__vite__mapDeps([117,3,4,2,98,31,32,61,62,43,5,6,9,44,118])).then(e=>Qg(e.default||e)),u_=ar(db({loader:c_},fb)),d_=()=>o(()=>import("./pages-login-index.nHXU7vgH.js"),__vite__mapDeps([119,120,1,2,3,4,5,6,7,75,76,59,60,52,53,64,73,9,121])).then(e=>Qg(e.default||e)),f_=ar(db({loader:d_},fb)),p_=()=>o(()=>import("./pages-login-register.C4IN5Wwj.js"),__vite__mapDeps([122,120,1,2,3,4,5,6,7,97,75,76,43,9,44,77,78,79,80,52,53,73,64,123])).then(e=>Qg(e.default||e)),h_=ar(db({loader:p_},fb)),m_=()=>o(()=>import("./pages-details-index.knM1iBbO.js"),__vite__mapDeps([124,22,3,4,23,24,1,2,5,6,7,12,13,14,9,125,126,67,68,127,43,44,61,62,128,15,16,17,18,19,20,64,63,28,35,36,26,27,25,129,130])).then(e=>Qg(e.default||e)),g_=ar(db({loader:m_},fb)),v_=()=>o(()=>import("./pages-details-promotionRecord.C7LGOuoP.js"),__vite__mapDeps([131,91,92,50,3,4,51,12,13,14,52,53,93,94,15,16,17,18,19,20,1,2,5,6,7,9,83,84,95,64,63,28,132,111,112,133,43,44,134,78,79,135,26,27,129,136])).then(e=>Qg(e.default||e)),y_=ar(db({loader:v_},fb)),b_=()=>o(()=>import("./pages-details-form.CGwn_X86.js"),__vite__mapDeps([137,50,3,4,51,43,5,6,9,44,110,22,23,24,1,2,7,12,13,14,52,53,93,94,15,16,17,18,19,20,92,83,84,95,111,64,112,106,28,113,35,36,26,27,63,138,139])).then(e=>Qg(e.default||e)),__=ar(db({loader:b_},fb)),w_=()=>o(()=>import("./pages-details-media.DWsO6Wv6.js"),__vite__mapDeps([140,125,3,4,126,12,13,14,105,43,5,6,9,44,59,60,61,62,93,94,15,16,17,18,19,20,26,27,129,106,28])).then(e=>Qg(e.default||e)),x_=ar(db({loader:w_},fb)),T_=()=>o(()=>import("./pages-details-voucherForm.CIzotMYU.js"),__vite__mapDeps([141,22,3,4,23,24,1,2,5,6,7,50,51,43,9,44,110,12,13,14,52,53,93,94,15,16,17,18,19,20,92,83,84,95,111,64,112,106,28,113,26,27,63,138,142])).then(e=>Qg(e.default||e)),S_=ar(db({loader:T_},fb)),k_=()=>o(()=>import("./pages-details-vouncherHistory.sC9wAjX7.js"),__vite__mapDeps([143,91,92,50,3,4,51,12,13,14,52,53,93,94,15,16,17,18,19,20,1,2,5,6,7,9,83,84,95,64,63,28,132,111,112,133,43,44,26,27,129,144])).then(e=>Qg(e.default||e)),E_=ar(db({loader:k_},fb)),C_=()=>o(()=>import("./pages-details-tips.BIMTiqK6.js"),__vite__mapDeps([145,56,43,3,4,5,6,9,44,146])).then(e=>Qg(e.default||e)),P_=ar(db({loader:C_},fb)),L_=()=>o(()=>import("./pages-details-promotionCode.BEFqVWKi.js"),__vite__mapDeps([147,91,92,50,3,4,51,12,13,14,52,53,93,94,15,16,17,18,19,20,1,2,5,6,7,9,83,84,95,64,63,28,132,111,112,133,43,44,26,27,61,62,134,78,79,135,129,148])).then(e=>Qg(e.default||e)),O_=ar(db({loader:L_},fb)),I_=()=>o(()=>import("./pages-details-promotionCodeDetails.DFKKPOYw.js"),__vite__mapDeps([149,43,3,4,5,6,9,44,63])).then(e=>Qg(e.default||e)),A_=ar(db({loader:I_},fb)),M_=()=>o(()=>import("./pages-details-search.pK1wvTW_.js"),__vite__mapDeps([150,125,3,4,126,127,1,2,5,6,7,43,9,44,12,13,14,61,62,128,31,32,15,16,17,18,19,20,26,27,129,151])).then(e=>Qg(e.default||e)),R_=ar(db({loader:M_},fb)),$_=()=>o(()=>import("./pages-details-shortPlayInfo.D9DI-TtR.js"),__vite__mapDeps([152,1,2,3,4,5,6,7,125,126,23,24,43,9,44,26,27,138,153])).then(e=>Qg(e.default||e)),N_=ar(db({loader:$_},fb)),D_=()=>o(()=>import("./pages-common-promotion-code.BSsEUspS.js"),__vite__mapDeps([154,26,3,4,27,43,5,6,9,44,155])).then(e=>Qg(e.default||e)),F_=ar(db({loader:D_},fb)),B_=()=>o(()=>import("./pages-system-xy.BrSceaeY.js"),__vite__mapDeps([156,43,3,4,5,6,9,44,23,24])).then(e=>Qg(e.default||e)),j_=ar(db({loader:B_},fb)),V_=()=>o(()=>import("./pages-system-video.D8XYjyMg.js"),__vite__mapDeps([157,43,3,4,5,6,9,44,158])).then(e=>Qg(e.default||e)),W_=ar(db({loader:V_},fb)),H_=()=>o(()=>import("./pages-system-404.CGU7uJrc.js"),__vite__mapDeps([159,13,3,4,14])).then(e=>Qg(e.default||e)),z_=ar(db({loader:H_},fb));function U_(e,t){return Ai(),Di(ab,null,{page:Eo(()=>[zi(e,db({},t,{ref:"page"}),null,512)]),_:1})}window.__uniRoutes=[{path:"/",alias:"/pages/index/index",component:{setup(){const e=Gg(),t=e&&e.$route&&e.$route.query||{};return()=>U_(hb,t)}},loader:pb,meta:{isQuit:!0,isEntry:!0,isTabBar:!0,tabBarIndex:0,navigationBar:{titleText:"",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/search/index",component:{setup(){const e=Gg(),t=e&&e.$route&&e.$route.query||{};return()=>U_(gb,t)}},loader:mb,meta:{navigationBar:{titleText:"",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/team/index",component:{setup(){const e=Gg(),t=e&&e.$route&&e.$route.query||{};return()=>U_(yb,t)}},loader:vb,meta:{isQuit:!0,isTabBar:!0,tabBarIndex:1,navigationBar:{titleText:"",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/my/index",component:{setup(){const e=Gg(),t=e&&e.$route&&e.$route.query||{};return()=>U_(_b,t)}},loader:bb,meta:{isQuit:!0,isTabBar:!0,tabBarIndex:2,navigationBar:{titleText:"",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/my/wallet/index",component:{setup(){const e=Gg(),t=e&&e.$route&&e.$route.query||{};return()=>U_(xb,t)}},loader:wb,meta:{navigationBar:{titleText:"",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/my/wallet/fund",component:{setup(){const e=Gg(),t=e&&e.$route&&e.$route.query||{};return()=>U_(Sb,t)}},loader:Tb,meta:{navigationBar:{titleText:"",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/my/wallet/tips",component:{setup(){const e=Gg(),t=e&&e.$route&&e.$route.query||{};return()=>U_(Eb,t)}},loader:kb,meta:{navigationBar:{titleText:"",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/my/notice/index",component:{setup(){const e=Gg(),t=e&&e.$route&&e.$route.query||{};return()=>U_(Pb,t)}},loader:Cb,meta:{navigationBar:{titleText:"",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/my/notice/detail",component:{setup(){const e=Gg(),t=e&&e.$route&&e.$route.query||{};return()=>U_(Ob,t)}},loader:Lb,meta:{navigationBar:{titleText:"",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/my/filings/index",component:{setup(){const e=Gg(),t=e&&e.$route&&e.$route.query||{};return()=>U_(Ab,t)}},loader:Ib,meta:{navigationBar:{titleText:"",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/my/info/index",component:{setup(){const e=Gg(),t=e&&e.$route&&e.$route.query||{};return()=>U_(Rb,t)}},loader:Mb,meta:{navigationBar:{titleText:"",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/my/info/security",component:{setup(){const e=Gg(),t=e&&e.$route&&e.$route.query||{};return()=>U_(Nb,t)}},loader:$b,meta:{navigationBar:{titleText:"",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/my/settlement/index",component:{setup(){const e=Gg(),t=e&&e.$route&&e.$route.query||{};return()=>U_(Fb,t)}},loader:Db,meta:{navigationBar:{titleText:"",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/my/income/index",component:{setup(){const e=Gg(),t=e&&e.$route&&e.$route.query||{};return()=>U_(jb,t)}},loader:Bb,meta:{navigationBar:{titleText:"",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/my/income/details",component:{setup(){const e=Gg(),t=e&&e.$route&&e.$route.query||{};return()=>U_(Wb,t)}},loader:Vb,meta:{navigationBar:{titleText:"",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/my/income/detailsSearch",component:{setup(){const e=Gg(),t=e&&e.$route&&e.$route.query||{};return()=>U_(zb,t)}},loader:Hb,meta:{navigationBar:{titleText:"",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/my/settlement/details",component:{setup(){const e=Gg(),t=e&&e.$route&&e.$route.query||{};return()=>U_(qb,t)}},loader:Ub,meta:{navigationBar:{titleText:"",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/my/orderHistory/index",component:{setup(){const e=Gg(),t=e&&e.$route&&e.$route.query||{};return()=>U_(Xb,t)}},loader:Yb,meta:{navigationBar:{titleText:"",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/my/improve/index",component:{setup(){const e=Gg(),t=e&&e.$route&&e.$route.query||{};return()=>U_(Kb,t)}},loader:Gb,meta:{navigationBar:{titleText:"",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/my/bankCard/index",component:{setup(){const e=Gg(),t=e&&e.$route&&e.$route.query||{};return()=>U_(Zb,t)}},loader:Jb,meta:{navigationBar:{titleText:"",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/my/bankCard/add",component:{setup(){const e=Gg(),t=e&&e.$route&&e.$route.query||{};return()=>U_(e_,t)}},loader:Qb,meta:{navigationBar:{titleText:"",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/my/media/index",component:{setup(){const e=Gg(),t=e&&e.$route&&e.$route.query||{};return()=>U_(n_,t)}},loader:t_,meta:{navigationBar:{titleText:"",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/my/fund/index",component:{setup(){const e=Gg(),t=e&&e.$route&&e.$route.query||{};return()=>U_(r_,t)}},loader:o_,meta:{navigationBar:{titleText:"",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/my/media/add",component:{setup(){const e=Gg(),t=e&&e.$route&&e.$route.query||{};return()=>U_(s_,t)}},loader:i_,meta:{navigationBar:{titleText:"",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/my/media/tips",component:{setup(){const e=Gg(),t=e&&e.$route&&e.$route.query||{};return()=>U_(l_,t)}},loader:a_,meta:{navigationBar:{titleText:"",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/my/improve/country",component:{setup(){const e=Gg(),t=e&&e.$route&&e.$route.query||{};return()=>U_(u_,t)}},loader:c_,meta:{navigationBar:{titleText:"",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/login/index",component:{setup(){const e=Gg(),t=e&&e.$route&&e.$route.query||{};return()=>U_(f_,t)}},loader:d_,meta:{navigationBar:{titleText:"",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/login/register",component:{setup(){const e=Gg(),t=e&&e.$route&&e.$route.query||{};return()=>U_(h_,t)}},loader:p_,meta:{navigationBar:{titleText:"",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/details/index",component:{setup(){const e=Gg(),t=e&&e.$route&&e.$route.query||{};return()=>U_(g_,t)}},loader:m_,meta:{navigationBar:{titleText:"",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/details/promotionRecord",component:{setup(){const e=Gg(),t=e&&e.$route&&e.$route.query||{};return()=>U_(y_,t)}},loader:v_,meta:{navigationBar:{titleText:"",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/details/form",component:{setup(){const e=Gg(),t=e&&e.$route&&e.$route.query||{};return()=>U_(__,t)}},loader:b_,meta:{navigationBar:{titleText:"",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/details/media",component:{setup(){const e=Gg(),t=e&&e.$route&&e.$route.query||{};return()=>U_(x_,t)}},loader:w_,meta:{navigationBar:{titleText:"",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/details/voucherForm",component:{setup(){const e=Gg(),t=e&&e.$route&&e.$route.query||{};return()=>U_(S_,t)}},loader:T_,meta:{navigationBar:{titleText:"",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/details/vouncherHistory",component:{setup(){const e=Gg(),t=e&&e.$route&&e.$route.query||{};return()=>U_(E_,t)}},loader:k_,meta:{navigationBar:{titleText:"",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/details/tips",component:{setup(){const e=Gg(),t=e&&e.$route&&e.$route.query||{};return()=>U_(P_,t)}},loader:C_,meta:{navigationBar:{titleText:"",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/details/promotionCode",component:{setup(){const e=Gg(),t=e&&e.$route&&e.$route.query||{};return()=>U_(O_,t)}},loader:L_,meta:{navigationBar:{titleText:"",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/details/promotionCodeDetails",component:{setup(){const e=Gg(),t=e&&e.$route&&e.$route.query||{};return()=>U_(A_,t)}},loader:I_,meta:{navigationBar:{titleText:"",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/details/search",component:{setup(){const e=Gg(),t=e&&e.$route&&e.$route.query||{};return()=>U_(R_,t)}},loader:M_,meta:{navigationBar:{titleText:"",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/details/shortPlayInfo",component:{setup(){const e=Gg(),t=e&&e.$route&&e.$route.query||{};return()=>U_(N_,t)}},loader:$_,meta:{navigationBar:{titleText:"",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/common/promotion-code",component:{setup(){const e=Gg(),t=e&&e.$route&&e.$route.query||{};return()=>U_(F_,t)}},loader:D_,meta:{navigationBar:{titleText:"",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/system/xy",component:{setup(){const e=Gg(),t=e&&e.$route&&e.$route.query||{};return()=>U_(j_,t)}},loader:B_,meta:{navigationBar:{titleText:"",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/system/video",component:{setup(){const e=Gg(),t=e&&e.$route&&e.$route.query||{};return()=>U_(W_,t)}},loader:V_,meta:{navigationBar:{titleText:"",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/system/404",component:{setup(){const e=Gg(),t=e&&e.$route&&e.$route.query||{};return()=>U_(z_,t)}},loader:H_,meta:{navigationBar:{titleText:"",style:"custom",type:"default"},isNVue:!1}}].map(e=>(e.meta.route=(e.alias||e.path).slice(1),e
/*!
  * shared v11.1.7
  * (c) 2025 kazuya kawaguchi
  * Released under the MIT License.
  */));const q_=e=>JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029").replace(/\u0027/g,"\\u0027"),Y_=e=>"number"==typeof e&&isFinite(e),X_=e=>"[object RegExp]"===cw(e),G_=e=>uw(e)&&0===Object.keys(e).length,K_=Object.assign,J_=Object.create,Z_=(e=null)=>J_(e);let Q_;function ew(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}const tw=Object.prototype.hasOwnProperty;function nw(e,t){return tw.call(e,t)}const ow=Array.isArray,rw=e=>"function"==typeof e,iw=e=>"string"==typeof e,sw=e=>"boolean"==typeof e,aw=e=>null!==e&&"object"==typeof e,lw=Object.prototype.toString,cw=e=>lw.call(e),uw=e=>"[object Object]"===cw(e);function dw(e,t=""){return e.reduce((e,n,o)=>0===o?e+n:e+t+n,"")}function fw(e,t){}
/*!
  * message-compiler v11.1.7
  * (c) 2025 kazuya kawaguchi
  * Released under the MIT License.
  */function pw(e,t,n){return{start:e,end:t}}const hw=1,mw=2,gw=3,vw=4,yw=5,bw=6,_w=7,ww=8,xw=9,Tw=10,Sw=11,kw=12,Ew=13,Cw=14;function Pw(e,t,n={}){const{domain:o,messages:r,args:i}=n,s=new SyntaxError(String(e));return s.code=e,t&&(s.location=t),s.domain=o,s}function Lw(e){throw e}const Ow=" ",Iw="\n",Aw=String.fromCharCode(8232),Mw=String.fromCharCode(8233);function Rw(e){const t=e;let n=0,o=1,r=1,i=0;const s=e=>"\r"===t[e]&&t[e+1]===Iw,a=e=>t[e]===Mw,l=e=>t[e]===Aw,c=e=>s(e)||(e=>t[e]===Iw)(e)||a(e)||l(e),u=e=>s(e)||a(e)||l(e)?Iw:t[e];function d(){return i=0,c(n)&&(o++,r=0),s(n)&&n++,n++,r++,t[n]}return{index:()=>n,line:()=>o,column:()=>r,peekOffset:()=>i,charAt:u,currentChar:()=>u(n),currentPeek:()=>u(n+i),next:d,peek:function(){return s(n+i)&&i++,i++,t[n+i]},reset:function(){n=0,o=1,r=1,i=0},resetPeek:function(e=0){i=e},skipToPeek:function(){const e=n+i;for(;e!==n;)d();i=0}}}const $w=void 0;function Nw(e,t={}){const n=!1!==t.location,o=Rw(e),r=()=>o.index(),i=()=>{return e=o.line(),t=o.column(),n=o.index(),{line:e,column:t,offset:n};var e,t,n},s=i(),a=r(),l={currentType:13,offset:a,startLoc:s,endLoc:s,lastType:13,lastOffset:a,lastStartLoc:s,lastEndLoc:s,braceNest:0,inLinked:!1,text:""},c=()=>l,{onError:u}=t;function d(e,t,o,...r){const i=c();if(t.column+=o,t.offset+=o,u){const o=Pw(e,n?pw(i.startLoc,t):null,{domain:"tokenizer",args:r});u(o)}}function f(e,t,o){e.endLoc=i(),e.currentType=t;const r={type:t};return n&&(r.loc=pw(e.startLoc,e.endLoc)),null!=o&&(r.value=o),r}const p=e=>f(e,13);function h(e,t){return e.currentChar()===t?(e.next(),t):(d(hw,i(),0,t),"")}function m(e){let t="";for(;e.currentPeek()===Ow||e.currentPeek()===Iw;)t+=e.currentPeek(),e.peek();return t}function g(e){const t=m(e);return e.skipToPeek(),t}function v(e){if(e===$w)return!1;const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||95===t}function y(e,t){const{currentType:n}=t;if(2!==n)return!1;m(e);const o=function(e){if(e===$w)return!1;const t=e.charCodeAt(0);return t>=48&&t<=57}("-"===e.currentPeek()?e.peek():e.currentPeek());return e.resetPeek(),o}function b(e){m(e);const t="|"===e.currentPeek();return e.resetPeek(),t}function _(e,t=!0){const n=(t=!1,o="")=>{const r=e.currentPeek();return"{"===r?t:"@"!==r&&r?"|"===r?!(o===Ow||o===Iw):r===Ow?(e.peek(),n(!0,Ow)):r!==Iw||(e.peek(),n(!0,Iw)):t},o=n();return t&&e.resetPeek(),o}function w(e,t){const n=e.currentChar();return n===$w?$w:t(n)?(e.next(),n):null}function x(e){const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||t>=48&&t<=57||95===t||36===t}function T(e){return w(e,x)}function S(e){const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||t>=48&&t<=57||95===t||36===t||45===t}function k(e){return w(e,S)}function E(e){const t=e.charCodeAt(0);return t>=48&&t<=57}function C(e){return w(e,E)}function P(e){const t=e.charCodeAt(0);return t>=48&&t<=57||t>=65&&t<=70||t>=97&&t<=102}function L(e){return w(e,P)}function O(e){let t="",n="";for(;t=C(e);)n+=t;return n}function I(e){return"'"!==e&&e!==Iw}function A(e){const t=e.currentChar();switch(t){case"\\":case"'":return e.next(),`\\${t}`;case"u":return M(e,t,4);case"U":return M(e,t,6);default:return d(vw,i(),0,t),""}}function M(e,t,n){h(e,t);let o="";for(let r=0;r<n;r++){const n=L(e);if(!n){d(yw,i(),0,`\\${t}${o}${e.currentChar()}`);break}o+=n}return`\\${t}${o}`}function R(e){return"{"!==e&&"}"!==e&&e!==Ow&&e!==Iw}function $(e){g(e);const t=h(e,"|");return g(e),t}function N(e,t){let n=null;switch(e.currentChar()){case"{":return t.braceNest>=1&&d(xw,i(),0),e.next(),n=f(t,2,"{"),g(e),t.braceNest++,n;case"}":return t.braceNest>0&&2===t.currentType&&d(ww,i(),0),e.next(),n=f(t,3,"}"),t.braceNest--,t.braceNest>0&&g(e),t.inLinked&&0===t.braceNest&&(t.inLinked=!1),n;case"@":return t.braceNest>0&&d(_w,i(),0),n=D(e,t)||p(t),t.braceNest=0,n;default:{let o=!0,r=!0,s=!0;if(b(e))return t.braceNest>0&&d(_w,i(),0),n=f(t,1,$(e)),t.braceNest=0,t.inLinked=!1,n;if(t.braceNest>0&&(4===t.currentType||5===t.currentType||6===t.currentType))return d(_w,i(),0),t.braceNest=0,F(e,t);if(o=function(e,t){const{currentType:n}=t;if(2!==n)return!1;m(e);const o=v(e.currentPeek());return e.resetPeek(),o}(e,t))return n=f(t,4,function(e){g(e);let t="",n="";for(;t=k(e);)n+=t;return e.currentChar()===$w&&d(_w,i(),0),n}(e)),g(e),n;if(r=y(e,t))return n=f(t,5,function(e){g(e);let t="";return"-"===e.currentChar()?(e.next(),t+=`-${O(e)}`):t+=O(e),e.currentChar()===$w&&d(_w,i(),0),t}(e)),g(e),n;if(s=function(e,t){const{currentType:n}=t;if(2!==n)return!1;m(e);const o="'"===e.currentPeek();return e.resetPeek(),o}(e,t))return n=f(t,6,function(e){g(e),h(e,"'");let t="",n="";for(;t=w(e,I);)n+="\\"===t?A(e):t;const o=e.currentChar();return o===Iw||o===$w?(d(gw,i(),0),o===Iw&&(e.next(),h(e,"'")),n):(h(e,"'"),n)}(e)),g(e),n;if(!o&&!r&&!s)return n=f(t,12,function(e){g(e);let t="",n="";for(;t=w(e,R);)n+=t;return n}(e)),d(mw,i(),0,n.value),g(e),n;break}}return n}function D(e,t){const{currentType:n}=t;let o=null;const r=e.currentChar();switch(7!==n&&8!==n&&11!==n&&9!==n||r!==Iw&&r!==Ow||d(Tw,i(),0),r){case"@":return e.next(),o=f(t,7,"@"),t.inLinked=!0,o;case".":return g(e),e.next(),f(t,8,".");case":":return g(e),e.next(),f(t,9,":");default:return b(e)?(o=f(t,1,$(e)),t.braceNest=0,t.inLinked=!1,o):function(e,t){const{currentType:n}=t;if(7!==n)return!1;m(e);const o="."===e.currentPeek();return e.resetPeek(),o}(e,t)||function(e,t){const{currentType:n}=t;if(7!==n&&11!==n)return!1;m(e);const o=":"===e.currentPeek();return e.resetPeek(),o}(e,t)?(g(e),D(e,t)):function(e,t){const{currentType:n}=t;if(8!==n)return!1;m(e);const o=v(e.currentPeek());return e.resetPeek(),o}(e,t)?(g(e),f(t,11,function(e){let t="",n="";for(;t=T(e);)n+=t;return n}(e))):function(e,t){const{currentType:n}=t;if(9!==n)return!1;const o=()=>{const t=e.currentPeek();return"{"===t?v(e.peek()):!("@"===t||"|"===t||":"===t||"."===t||t===Ow||!t)&&(t===Iw?(e.peek(),o()):_(e,!1))},r=o();return e.resetPeek(),r}(e,t)?(g(e),"{"===r?N(e,t)||o:f(t,10,function(e){const t=n=>{const o=e.currentChar();return"{"!==o&&"@"!==o&&"|"!==o&&"("!==o&&")"!==o&&o?o===Ow?n:(n+=o,e.next(),t(n)):n};return t("")}(e))):(7===n&&d(Tw,i(),0),t.braceNest=0,t.inLinked=!1,F(e,t))}}function F(e,t){let n={type:13};if(t.braceNest>0)return N(e,t)||p(t);if(t.inLinked)return D(e,t)||p(t);switch(e.currentChar()){case"{":return N(e,t)||p(t);case"}":return d(bw,i(),0),e.next(),f(t,3,"}");case"@":return D(e,t)||p(t);default:if(b(e))return n=f(t,1,$(e)),t.braceNest=0,t.inLinked=!1,n;if(_(e))return f(t,0,function(e){let t="";for(;;){const n=e.currentChar();if("{"===n||"}"===n||"@"===n||"|"===n||!n)break;if(n===Ow||n===Iw)if(_(e))t+=n,e.next();else{if(b(e))break;t+=n,e.next()}else t+=n,e.next()}return t}(e))}return n}return{nextToken:function(){const{currentType:e,offset:t,startLoc:n,endLoc:s}=l;return l.lastType=e,l.lastOffset=t,l.lastStartLoc=n,l.lastEndLoc=s,l.offset=r(),l.startLoc=i(),o.currentChar()===$w?f(l,13):F(o,l)},currentOffset:r,currentPosition:i,context:c}}const Dw=/(?:\\\\|\\'|\\u([0-9a-fA-F]{4})|\\U([0-9a-fA-F]{6}))/g;function Fw(e,t,n){switch(e){case"\\\\":return"\\";case"\\'":return"'";default:{const e=parseInt(t||n,16);return e<=55295||e>=57344?String.fromCodePoint(e):"�"}}}function Bw(e={}){const t=!1!==e.location,{onError:n}=e;function o(e,o,r,i,...s){const a=e.currentPosition();if(a.offset+=i,a.column+=i,n){const e=Pw(o,t?pw(r,a):null,{domain:"parser",args:s});n(e)}}function r(e,n,o){const r={type:e};return t&&(r.start=n,r.end=n,r.loc={start:o,end:o}),r}function i(e,n,o,r){t&&(e.end=n,e.loc&&(e.loc.end=o))}function s(e,t){const n=e.context(),o=r(3,n.offset,n.startLoc);return o.value=t,i(o,e.currentOffset(),e.currentPosition()),o}function a(e,t){const n=e.context(),{lastOffset:o,lastStartLoc:s}=n,a=r(5,o,s);return a.index=parseInt(t,10),e.nextToken(),i(a,e.currentOffset(),e.currentPosition()),a}function l(e,t){const n=e.context(),{lastOffset:o,lastStartLoc:s}=n,a=r(4,o,s);return a.key=t,e.nextToken(),i(a,e.currentOffset(),e.currentPosition()),a}function c(e,t){const n=e.context(),{lastOffset:o,lastStartLoc:s}=n,a=r(9,o,s);return a.value=t.replace(Dw,Fw),e.nextToken(),i(a,e.currentOffset(),e.currentPosition()),a}function u(e){const t=e.context(),n=r(6,t.offset,t.startLoc);let s=e.nextToken();if(8===s.type){const t=function(e){const t=e.nextToken(),n=e.context(),{lastOffset:s,lastStartLoc:a}=n,l=r(8,s,a);return 11!==t.type?(o(e,kw,n.lastStartLoc,0),l.value="",i(l,s,a),{nextConsumeToken:t,node:l}):(null==t.value&&o(e,Cw,n.lastStartLoc,0,jw(t)),l.value=t.value||"",i(l,e.currentOffset(),e.currentPosition()),{node:l})}(e);n.modifier=t.node,s=t.nextConsumeToken||e.nextToken()}switch(9!==s.type&&o(e,Cw,t.lastStartLoc,0,jw(s)),s=e.nextToken(),2===s.type&&(s=e.nextToken()),s.type){case 10:null==s.value&&o(e,Cw,t.lastStartLoc,0,jw(s)),n.key=function(e,t){const n=e.context(),o=r(7,n.offset,n.startLoc);return o.value=t,i(o,e.currentOffset(),e.currentPosition()),o}(e,s.value||"");break;case 4:null==s.value&&o(e,Cw,t.lastStartLoc,0,jw(s)),n.key=l(e,s.value||"");break;case 5:null==s.value&&o(e,Cw,t.lastStartLoc,0,jw(s)),n.key=a(e,s.value||"");break;case 6:null==s.value&&o(e,Cw,t.lastStartLoc,0,jw(s)),n.key=c(e,s.value||"");break;default:{o(e,Ew,t.lastStartLoc,0);const a=e.context(),l=r(7,a.offset,a.startLoc);return l.value="",i(l,a.offset,a.startLoc),n.key=l,i(n,a.offset,a.startLoc),{nextConsumeToken:s,node:n}}}return i(n,e.currentOffset(),e.currentPosition()),{node:n}}function d(e){const t=e.context(),n=r(2,1===t.currentType?e.currentOffset():t.offset,1===t.currentType?t.endLoc:t.startLoc);n.items=[];let d=null;do{const r=d||e.nextToken();switch(d=null,r.type){case 0:null==r.value&&o(e,Cw,t.lastStartLoc,0,jw(r)),n.items.push(s(e,r.value||""));break;case 5:null==r.value&&o(e,Cw,t.lastStartLoc,0,jw(r)),n.items.push(a(e,r.value||""));break;case 4:null==r.value&&o(e,Cw,t.lastStartLoc,0,jw(r)),n.items.push(l(e,r.value||""));break;case 6:null==r.value&&o(e,Cw,t.lastStartLoc,0,jw(r)),n.items.push(c(e,r.value||""));break;case 7:{const t=u(e);n.items.push(t.node),d=t.nextConsumeToken||null;break}}}while(13!==t.currentType&&1!==t.currentType);return i(n,1===t.currentType?t.lastOffset:e.currentOffset(),1===t.currentType?t.lastEndLoc:e.currentPosition()),n}function f(e){const t=e.context(),{offset:n,startLoc:s}=t,a=d(e);return 13===t.currentType?a:function(e,t,n,s){const a=e.context();let l=0===s.items.length;const c=r(1,t,n);c.cases=[],c.cases.push(s);do{const t=d(e);l||(l=0===t.items.length),c.cases.push(t)}while(13!==a.currentType);return l&&o(e,Sw,n,0),i(c,e.currentOffset(),e.currentPosition()),c}(e,n,s,a)}return{parse:function(n){const s=Nw(n,K_({},e)),a=s.context(),l=r(0,a.offset,a.startLoc);return t&&l.loc&&(l.loc.source=n),l.body=f(s),e.onCacheKey&&(l.cacheKey=e.onCacheKey(n)),13!==a.currentType&&o(s,Cw,a.lastStartLoc,0,n[a.offset]||""),i(l,s.currentOffset(),s.currentPosition()),l}}}function jw(e){if(13===e.type)return"EOF";const t=(e.value||"").replace(/\r?\n/gu,"\\n");return t.length>10?t.slice(0,9)+"…":t}function Vw(e,t){for(let n=0;n<e.length;n++)Ww(e[n],t)}function Ww(e,t){switch(e.type){case 1:Vw(e.cases,t),t.helper("plural");break;case 2:Vw(e.items,t);break;case 6:Ww(e.key,t),t.helper("linked"),t.helper("type");break;case 5:t.helper("interpolate"),t.helper("list");break;case 4:t.helper("interpolate"),t.helper("named")}}function Hw(e,t={}){const n=function(e){const t={ast:e,helpers:new Set};return{context:()=>t,helper:e=>(t.helpers.add(e),e)}}(e);n.helper("normalize"),e.body&&Ww(e.body,n);const o=n.context();e.helpers=Array.from(o.helpers)}function zw(e){if(1===e.items.length){const t=e.items[0];3!==t.type&&9!==t.type||(e.static=t.value,delete t.value)}else{const t=[];for(let n=0;n<e.items.length;n++){const o=e.items[n];if(3!==o.type&&9!==o.type)break;if(null==o.value)break;t.push(o.value)}if(t.length===e.items.length){e.static=dw(t);for(let t=0;t<e.items.length;t++){const n=e.items[t];3!==n.type&&9!==n.type||delete n.value}}}}function Uw(e){switch(e.t=e.type,e.type){case 0:{const t=e;Uw(t.body),t.b=t.body,delete t.body;break}case 1:{const t=e,n=t.cases;for(let e=0;e<n.length;e++)Uw(n[e]);t.c=n,delete t.cases;break}case 2:{const t=e,n=t.items;for(let e=0;e<n.length;e++)Uw(n[e]);t.i=n,delete t.items,t.static&&(t.s=t.static,delete t.static);break}case 3:case 9:case 8:case 7:{const t=e;t.value&&(t.v=t.value,delete t.value);break}case 6:{const t=e;Uw(t.key),t.k=t.key,delete t.key,t.modifier&&(Uw(t.modifier),t.m=t.modifier,delete t.modifier);break}case 5:{const t=e;t.i=t.index,delete t.index;break}case 4:{const t=e;t.k=t.key,delete t.key;break}}delete e.type}function qw(e,t){const{helper:n}=e;switch(t.type){case 0:!function(e,t){t.body?qw(e,t.body):e.push("null")}(e,t);break;case 1:!function(e,t){const{helper:n,needIndent:o}=e;if(t.cases.length>1){e.push(`${n("plural")}([`),e.indent(o());const r=t.cases.length;for(let n=0;n<r&&(qw(e,t.cases[n]),n!==r-1);n++)e.push(", ");e.deindent(o()),e.push("])")}}(e,t);break;case 2:!function(e,t){const{helper:n,needIndent:o}=e;e.push(`${n("normalize")}([`),e.indent(o());const r=t.items.length;for(let i=0;i<r&&(qw(e,t.items[i]),i!==r-1);i++)e.push(", ");e.deindent(o()),e.push("])")}(e,t);break;case 6:!function(e,t){const{helper:n}=e;e.push(`${n("linked")}(`),qw(e,t.key),t.modifier?(e.push(", "),qw(e,t.modifier),e.push(", _type")):e.push(", undefined, _type"),e.push(")")}(e,t);break;case 8:case 7:case 9:case 3:e.push(JSON.stringify(t.value),t);break;case 5:e.push(`${n("interpolate")}(${n("list")}(${t.index}))`,t);break;case 4:e.push(`${n("interpolate")}(${n("named")}(${JSON.stringify(t.key)}))`,t)}}function Yw(e,t={}){const n=K_({},t),o=!!n.jit,r=!!n.minify,i=null==n.optimize||n.optimize,s=Bw(n).parse(e);return o?(i&&function(e){const t=e.body;2===t.type?zw(t):t.cases.forEach(e=>zw(e))}(s),r&&Uw(s),{ast:s,code:""}):(Hw(s,n),((e,t={})=>{const n=iw(t.mode)?t.mode:"normal",o=iw(t.filename)?t.filename:"message.intl";t.sourceMap;const r=null!=t.breakLineCode?t.breakLineCode:"arrow"===n?";":"\n",i=t.needIndent?t.needIndent:"arrow"!==n,s=e.helpers||[],a=function(e,t){const{filename:n,breakLineCode:o,needIndent:r}=t,i=!1!==t.location,s={filename:n,code:"",column:1,line:1,offset:0,map:void 0,breakLineCode:o,needIndent:r,indentLevel:0};function a(e,t){s.code+=e}function l(e,t=!0){const n=t?o:"";a(r?n+"  ".repeat(e):n)}return i&&e.loc&&(s.source=e.loc.source),{context:()=>s,push:a,indent:function(e=!0){const t=++s.indentLevel;e&&l(t)},deindent:function(e=!0){const t=--s.indentLevel;e&&l(t)},newline:function(){l(s.indentLevel)},helper:e=>`_${e}`,needIndent:()=>s.needIndent}}(e,{filename:o,breakLineCode:r,needIndent:i});a.push("normal"===n?"function __msg__ (ctx) {":"(ctx) => {"),a.indent(i),s.length>0&&(a.push(`const { ${dw(s.map(e=>`${e}: _${e}`),", ")} } = ctx`),a.newline()),a.push("return "),qw(a,e),a.deindent(i),a.push("}"),delete e.helpers;const{code:l,map:c}=a.context();return{ast:e,code:l,map:c?c.toJSON():void 0}})(s,n))}
/*!
  * core-base v11.1.7
  * (c) 2025 kazuya kawaguchi
  * Released under the MIT License.
  */function Xw(e){return aw(e)&&0===ex(e)&&(nw(e,"b")||nw(e,"body"))}const Gw=["b","body"];const Kw=["c","cases"];const Jw=["s","static"];const Zw=["i","items"];const Qw=["t","type"];function ex(e){return ix(e,Qw)}const tx=["v","value"];function nx(e,t){const n=ix(e,tx);if(null!=n)return n;throw ax(t)}const ox=["m","modifier"];const rx=["k","key"];function ix(e,t,n){for(let o=0;o<t.length;o++){const n=t[o];if(nw(e,n)&&null!=e[n])return e[n]}return n}const sx=[...Gw,...Kw,...Jw,...Zw,...rx,...ox,...tx,...Qw];function ax(e){return new Error(`unhandled node type: ${e}`)}function lx(e){return t=>function(e,t){const n=(o=t,ix(o,Gw));var o;if(null==n)throw ax(0);if(1===ex(n)){const t=function(e){return ix(e,Kw,[])}(n);return e.plural(t.reduce((t,n)=>[...t,cx(e,n)],[]))}return cx(e,n)}(t,e)}function cx(e,t){const n=function(e){return ix(e,Jw)}(t);if(null!=n)return"text"===e.type?n:e.normalize([n]);{const n=function(e){return ix(e,Zw,[])}(t).reduce((t,n)=>[...t,ux(e,n)],[]);return e.normalize(n)}}function ux(e,t){const n=ex(t);switch(n){case 3:case 9:case 7:case 8:return nx(t,n);case 4:{const o=t;if(nw(o,"k")&&o.k)return e.interpolate(e.named(o.k));if(nw(o,"key")&&o.key)return e.interpolate(e.named(o.key));throw ax(n)}case 5:{const o=t;if(nw(o,"i")&&Y_(o.i))return e.interpolate(e.list(o.i));if(nw(o,"index")&&Y_(o.index))return e.interpolate(e.list(o.index));throw ax(n)}case 6:{const n=t,o=function(e){return ix(e,ox)}(n),r=function(e){const t=ix(e,rx);if(t)return t;throw ax(6)}(n);return e.linked(ux(e,r),o?ux(e,o):void 0,e.type)}default:throw new Error(`unhandled node on format message part: ${n}`)}}const dx=e=>e;let fx=Z_();const px=17,hx=18,mx=19,gx=21,vx=22,yx=23;function bx(e){return Pw(e,null,void 0)}function _x(e,t){return null!=t.locale?xx(t.locale):xx(e.locale)}let wx;function xx(e){if(iw(e))return e;if(rw(e)){if(e.resolvedOnce&&null!=wx)return wx;if("Function"===e.constructor.name){const n=e();if(aw(t=n)&&rw(t.then)&&rw(t.catch))throw bx(gx);return wx=n}throw bx(vx)}throw bx(yx);var t}function Tx(e,t,n){return[...new Set([n,...ow(t)?t:aw(t)?Object.keys(t):iw(t)?[t]:[n]])]}function Sx(e,t,n){const o=iw(n)?n:Rx,r=e;r.__localeChainCache||(r.__localeChainCache=new Map);let i=r.__localeChainCache.get(o);if(!i){i=[];let e=[n];for(;ow(e);)e=kx(i,e,t);const s=ow(t)||!uw(t)?t:t.default?t.default:null;e=iw(s)?[s]:s,ow(e)&&kx(i,e,!1),r.__localeChainCache.set(o,i)}return i}function kx(e,t,n){let o=!0;for(let r=0;r<t.length&&sw(o);r++){const i=t[r];iw(i)&&(o=Ex(e,t[r],n))}return o}function Ex(e,t,n){let o;const r=t.split("-");do{o=Cx(e,r.join("-"),n),r.splice(-1,1)}while(r.length&&!0===o);return o}function Cx(e,t,n){let o=!1;if(!e.includes(t)&&(o=!0,t)){o="!"!==t[t.length-1];const r=t.replace(/!/g,"");e.push(r),(ow(n)||uw(n))&&n[r]&&(o=n[r])}return o}const Px=[];Px[0]={w:[0],i:[3,0],"[":[4],o:[7]},Px[1]={w:[1],".":[2],"[":[4],o:[7]},Px[2]={w:[2],i:[3,0],0:[3,0]},Px[3]={i:[3,0],0:[3,0],w:[1,1],".":[2,1],"[":[4,1],o:[7,1]},Px[4]={"'":[5,0],'"':[6,0],"[":[4,2],"]":[1,3],o:8,l:[4,0]},Px[5]={"'":[4,0],o:8,l:[5,0]},Px[6]={'"':[4,0],o:8,l:[6,0]};const Lx=/^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;function Ox(e){if(null==e)return"o";switch(e.charCodeAt(0)){case 91:case 93:case 46:case 34:case 39:return e;case 95:case 36:case 45:return"i";case 9:case 10:case 13:case 160:case 65279:case 8232:case 8233:return"w"}return"i"}function Ix(e){const t=e.trim();return("0"!==e.charAt(0)||!isNaN(parseInt(e)))&&(n=t,Lx.test(n)?function(e){const t=e.charCodeAt(0);return t!==e.charCodeAt(e.length-1)||34!==t&&39!==t?e:e.slice(1,-1)}(t):"*"+t);var n}const Ax=new Map;function Mx(e,t){return aw(e)?e[t]:null}const Rx="en-US",$x=e=>`${e.charAt(0).toLocaleUpperCase()}${e.substr(1)}`;let Nx,Dx,Fx;let Bx=null;const jx=e=>{Bx=e};let Vx=0;function Wx(e={}){const t=rw(e.onWarn)?e.onWarn:fw,n=iw(e.version)?e.version:"11.1.7",o=iw(e.locale)||rw(e.locale)?e.locale:Rx,r=rw(o)?Rx:o,i=ow(e.fallbackLocale)||uw(e.fallbackLocale)||iw(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:r,s=uw(e.messages)?e.messages:Hx(r),a=uw(e.datetimeFormats)?e.datetimeFormats:Hx(r),l=uw(e.numberFormats)?e.numberFormats:Hx(r),c=K_(Z_(),e.modifiers,{upper:(e,t)=>"text"===t&&iw(e)?e.toUpperCase():"vnode"===t&&aw(e)&&"__v_isVNode"in e?e.children.toUpperCase():e,lower:(e,t)=>"text"===t&&iw(e)?e.toLowerCase():"vnode"===t&&aw(e)&&"__v_isVNode"in e?e.children.toLowerCase():e,capitalize:(e,t)=>"text"===t&&iw(e)?$x(e):"vnode"===t&&aw(e)&&"__v_isVNode"in e?$x(e.children):e}),u=e.pluralRules||Z_(),d=rw(e.missing)?e.missing:null,f=!sw(e.missingWarn)&&!X_(e.missingWarn)||e.missingWarn,p=!sw(e.fallbackWarn)&&!X_(e.fallbackWarn)||e.fallbackWarn,h=!!e.fallbackFormat,m=!!e.unresolving,g=rw(e.postTranslation)?e.postTranslation:null,v=uw(e.processor)?e.processor:null,y=!sw(e.warnHtmlMessage)||e.warnHtmlMessage,b=!!e.escapeParameter,_=rw(e.messageCompiler)?e.messageCompiler:Nx,w=rw(e.messageResolver)?e.messageResolver:Dx||Mx,x=rw(e.localeFallbacker)?e.localeFallbacker:Fx||Tx,T=aw(e.fallbackContext)?e.fallbackContext:void 0,S=e,k=aw(S.__datetimeFormatters)?S.__datetimeFormatters:new Map,E=aw(S.__numberFormatters)?S.__numberFormatters:new Map,C=aw(S.__meta)?S.__meta:{};Vx++;const P={version:n,cid:Vx,locale:o,fallbackLocale:i,messages:s,modifiers:c,pluralRules:u,missing:d,missingWarn:f,fallbackWarn:p,fallbackFormat:h,unresolving:m,postTranslation:g,processor:v,warnHtmlMessage:y,escapeParameter:b,messageCompiler:_,messageResolver:w,localeFallbacker:x,fallbackContext:T,onWarn:t,__meta:C};return P.datetimeFormats=a,P.numberFormats=l,P.__datetimeFormatters=k,P.__numberFormatters=E,P}const Hx=e=>({[e]:Z_()});function zx(e,t,n,o,r){const{missing:i,onWarn:s}=e;if(null!==i){const o=i(e,n,t,r);return iw(o)?o:t}return t}function Ux(e,t,n){e.__localeChainCache=new Map,e.localeFallbacker(e,n,t)}function qx(e,t){return e!==t&&e.split("-")[0]===t.split("-")[0]}function Yx(e,t){const n=t.indexOf(e);if(-1===n)return!1;for(let o=n+1;o<t.length;o++)if(qx(e,t[o]))return!0;return!1}function Xx(e,...t){const{datetimeFormats:n,unresolving:o,fallbackLocale:r,onWarn:i,localeFallbacker:s}=e,{__datetimeFormatters:a}=e,[l,c,u,d]=Kx(...t);sw(u.missingWarn)?u.missingWarn:e.missingWarn;sw(u.fallbackWarn)?u.fallbackWarn:e.fallbackWarn;const f=!!u.part,p=_x(e,u),h=s(e,r,p);if(!iw(l)||""===l)return new Intl.DateTimeFormat(p,d).format(c);let m,g={},v=null;for(let _=0;_<h.length&&(m=h[_],g=n[m]||{},v=g[l],!uw(v));_++)zx(e,l,m,0,"datetime format");if(!uw(v)||!iw(m))return o?-1:l;let y=`${m}__${l}`;G_(d)||(y=`${y}__${JSON.stringify(d)}`);let b=a.get(y);return b||(b=new Intl.DateTimeFormat(m,K_({},v,d)),a.set(y,b)),f?b.formatToParts(c):b.format(c)}const Gx=["localeMatcher","weekday","era","year","month","day","hour","minute","second","timeZoneName","formatMatcher","hour12","timeZone","dateStyle","timeStyle","calendar","dayPeriod","numberingSystem","hourCycle","fractionalSecondDigits"];function Kx(...e){const[t,n,o,r]=e,i=Z_();let s,a=Z_();if(iw(t)){const e=t.match(/(\d{4}-\d{2}-\d{2})(T|\s)?(.*)/);if(!e)throw bx(mx);const n=e[3]?e[3].trim().startsWith("T")?`${e[1].trim()}${e[3].trim()}`:`${e[1].trim()}T${e[3].trim()}`:e[1].trim();s=new Date(n);try{s.toISOString()}catch{throw bx(mx)}}else if("[object Date]"===cw(t)){if(isNaN(t.getTime()))throw bx(hx);s=t}else{if(!Y_(t))throw bx(px);s=t}return iw(n)?i.key=n:uw(n)&&Object.keys(n).forEach(e=>{Gx.includes(e)?a[e]=n[e]:i[e]=n[e]}),iw(o)?i.locale=o:uw(o)&&(a=o),uw(r)&&(a=r),[i.key||"",s,i,a]}function Jx(e,t,n){const o=e;for(const r in n){const e=`${t}__${r}`;o.__datetimeFormatters.has(e)&&o.__datetimeFormatters.delete(e)}}function Zx(e,...t){const{numberFormats:n,unresolving:o,fallbackLocale:r,onWarn:i,localeFallbacker:s}=e,{__numberFormatters:a}=e,[l,c,u,d]=eT(...t);sw(u.missingWarn)?u.missingWarn:e.missingWarn;sw(u.fallbackWarn)?u.fallbackWarn:e.fallbackWarn;const f=!!u.part,p=_x(e,u),h=s(e,r,p);if(!iw(l)||""===l)return new Intl.NumberFormat(p,d).format(c);let m,g={},v=null;for(let _=0;_<h.length&&(m=h[_],g=n[m]||{},v=g[l],!uw(v));_++)zx(e,l,m,0,"number format");if(!uw(v)||!iw(m))return o?-1:l;let y=`${m}__${l}`;G_(d)||(y=`${y}__${JSON.stringify(d)}`);let b=a.get(y);return b||(b=new Intl.NumberFormat(m,K_({},v,d)),a.set(y,b)),f?b.formatToParts(c):b.format(c)}const Qx=["localeMatcher","style","currency","currencyDisplay","currencySign","useGrouping","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits","compactDisplay","notation","signDisplay","unit","unitDisplay","roundingMode","roundingPriority","roundingIncrement","trailingZeroDisplay"];function eT(...e){const[t,n,o,r]=e,i=Z_();let s=Z_();if(!Y_(t))throw bx(px);const a=t;return iw(n)?i.key=n:uw(n)&&Object.keys(n).forEach(e=>{Qx.includes(e)?s[e]=n[e]:i[e]=n[e]}),iw(o)?i.locale=o:uw(o)&&(s=o),uw(r)&&(s=r),[i.key||"",a,i,s]}function tT(e,t,n){const o=e;for(const r in n){const e=`${t}__${r}`;o.__numberFormatters.has(e)&&o.__numberFormatters.delete(e)}}const nT=e=>e,oT=e=>"",rT=e=>0===e.length?"":dw(e),iT=e=>null==e?"":ow(e)||uw(e)&&e.toString===lw?JSON.stringify(e,null,2):String(e);function sT(e,t){return e=Math.abs(e),2===t?e?e>1?1:0:1:e?Math.min(e,2):0}function aT(e={}){const t=e.locale,n=function(e){const t=Y_(e.pluralIndex)?e.pluralIndex:-1;return e.named&&(Y_(e.named.count)||Y_(e.named.n))?Y_(e.named.count)?e.named.count:Y_(e.named.n)?e.named.n:t:t}(e),o=aw(e.pluralRules)&&iw(t)&&rw(e.pluralRules[t])?e.pluralRules[t]:sT,r=aw(e.pluralRules)&&iw(t)&&rw(e.pluralRules[t])?sT:void 0,i=e.list||[],s=e.named||Z_();Y_(e.pluralIndex)&&function(e,t){t.count||(t.count=e),t.n||(t.n=e)}(n,s);function a(t,n){const o=rw(e.messages)?e.messages(t,!!n):!!aw(e.messages)&&e.messages[t];return o||(e.parent?e.parent.message(t):oT)}const l=uw(e.processor)&&rw(e.processor.normalize)?e.processor.normalize:rT,c=uw(e.processor)&&rw(e.processor.interpolate)?e.processor.interpolate:iT,u={list:e=>i[e],named:e=>s[e],plural:e=>e[o(n,e.length,r)],linked:(t,...n)=>{const[o,r]=n;let i="text",s="";1===n.length?aw(o)?(s=o.modifier||s,i=o.type||i):iw(o)&&(s=o||s):2===n.length&&(iw(o)&&(s=o||s),iw(r)&&(i=r||i));const l=a(t,!0)(u),c="vnode"===i&&ow(l)&&s?l[0]:l;return s?(d=s,e.modifiers?e.modifiers[d]:nT)(c,i):c;var d},message:a,type:uw(e.processor)&&iw(e.processor.type)?e.processor.type:"text",interpolate:c,normalize:l,values:K_(Z_(),i,s)};return u}const lT=()=>"",cT=e=>rw(e);function uT(e,...t){const{fallbackFormat:n,postTranslation:o,unresolving:r,messageCompiler:i,fallbackLocale:s,messages:a}=e,[l,c]=pT(...t),u=sw(c.missingWarn)?c.missingWarn:e.missingWarn,d=sw(c.fallbackWarn)?c.fallbackWarn:e.fallbackWarn,f=sw(c.escapeParameter)?c.escapeParameter:e.escapeParameter,p=!!c.resolvedMessage,h=iw(c.default)||sw(c.default)?sw(c.default)?i?l:()=>l:c.default:n?i?l:()=>l:null,m=n||null!=h&&(iw(h)||rw(h)),g=_x(e,c);f&&function(e){ow(e.list)?e.list=e.list.map(e=>iw(e)?ew(e):e):aw(e.named)&&Object.keys(e.named).forEach(t=>{iw(e.named[t])&&(e.named[t]=ew(e.named[t]))})}(c);let[v,y,b]=p?[l,g,a[g]||Z_()]:dT(e,l,g,s,d,u),_=v,w=l;if(p||iw(_)||Xw(_)||cT(_)||m&&(_=h,w=_),!(p||(iw(_)||Xw(_)||cT(_))&&iw(y)))return r?-1:l;let x=!1;const T=cT(_)?_:fT(e,l,y,_,w,()=>{x=!0});if(x)return _;const S=function(e,t,n,o){const{modifiers:r,pluralRules:i,messageResolver:s,fallbackLocale:a,fallbackWarn:l,missingWarn:c,fallbackContext:u}=e,d=(o,r)=>{let i=s(n,o);if(null==i&&(u||r)){const[,,n]=dT(u||e,o,t,a,l,c);i=s(n,o)}if(iw(i)||Xw(i)){let n=!1;const r=fT(e,o,t,i,o,()=>{n=!0});return n?lT:r}return cT(i)?i:lT},f={locale:t,modifiers:r,pluralRules:i,messages:d};e.processor&&(f.processor=e.processor);o.list&&(f.list=o.list);o.named&&(f.named=o.named);Y_(o.plural)&&(f.pluralIndex=o.plural);return f}(e,y,b,c),k=function(e,t,n){const o=t(n);return o}(0,T,aT(S));return o?o(k,l):k}function dT(e,t,n,o,r,i){const{messages:s,onWarn:a,messageResolver:l,localeFallbacker:c}=e,u=c(e,o,n);let d,f=Z_(),p=null;for(let h=0;h<u.length&&(d=u[h],f=s[d]||Z_(),null===(p=l(f,t))&&(p=f[t]),!(iw(p)||Xw(p)||cT(p)));h++)if(!Yx(d,u)){const n=zx(e,t,d,0,"translate");n!==t&&(p=n)}return[p,d,f]}function fT(e,t,n,o,r,i){const{messageCompiler:s,warnHtmlMessage:a}=e;if(cT(o)){const e=o;return e.locale=e.locale||n,e.key=e.key||t,e}if(null==s){const e=()=>o;return e.locale=n,e.key=t,e}const l=s(o,function(e,t,n,o,r,i){return{locale:t,key:n,warnHtmlMessage:r,onError:e=>{throw i&&i(e),e},onCacheKey:e=>((e,t,n)=>q_({l:e,k:t,s:n}))(t,n,e)}}(0,n,r,0,a,i));return l.locale=n,l.key=t,l.source=o,l}function pT(...e){const[t,n,o]=e,r=Z_();if(!(iw(t)||Y_(t)||cT(t)||Xw(t)))throw bx(px);const i=Y_(t)?String(t):(cT(t),t);return Y_(n)?r.plural=n:iw(n)?r.default=n:uw(n)&&!G_(n)?r.named=n:ow(n)&&(r.list=n),Y_(o)?r.plural=o:iw(o)?r.default=o:uw(o)&&K_(r,o),[i,r]}"boolean"!=typeof __INTLIFY_DROP_MESSAGE_COMPILER__&&((Q_||(Q_="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:Z_())).__INTLIFY_DROP_MESSAGE_COMPILER__=!1);
/*!
  * shared v11.1.7
  * (c) 2025 kazuya kawaguchi
  * Released under the MIT License.
  */
const hT="undefined"!=typeof window,mT=(e,t=!1)=>t?Symbol.for(e):Symbol(e),gT=e=>"number"==typeof e&&isFinite(e),vT=e=>"[object RegExp]"===OT(e),yT=Object.assign,bT=Object.create,_T=(e=null)=>bT(e);let wT;const xT=Object.prototype.hasOwnProperty;function TT(e,t){return xT.call(e,t)}const ST=Array.isArray,kT=e=>"function"==typeof e,ET=e=>"string"==typeof e,CT=e=>"boolean"==typeof e,PT=e=>null!==e&&"object"==typeof e,LT=Object.prototype.toString,OT=e=>LT.call(e),IT=e=>"[object Object]"===OT(e),AT=e=>!PT(e)||ST(e);function MT(e,t){if(AT(e)||AT(t))throw new Error("Invalid value");const n=[{src:e,des:t}];for(;n.length;){const{src:e,des:t}=n.pop();Object.keys(e).forEach(o=>{"__proto__"!==o&&(PT(e[o])&&!PT(t[o])&&(t[o]=Array.isArray(e[o])?[]:_T()),AT(t[o])||AT(e[o])?t[o]=e[o]:n.push({src:e[o],des:t[o]}))})}}
/*!
  * vue-i18n v11.1.7
  * (c) 2025 kazuya kawaguchi
  * Released under the MIT License.
  */const RT=24,$T=25,NT=26,DT=27,FT=28,BT=29,jT=31,VT=32;function WT(e,...t){return Pw(e,null,void 0)}const HT=mT("__translateVNode"),zT=mT("__datetimeParts"),UT=mT("__numberParts"),qT=mT("__setPluralRules"),YT=mT("__injectWithOption"),XT=mT("__dispose");function GT(e){if(!PT(e))return e;if(Xw(e))return e;for(const t in e)if(TT(e,t))if(t.includes(".")){const n=t.split("."),o=n.length-1;let r=e,i=!1;for(let e=0;e<o;e++){if("__proto__"===n[e])throw new Error(`unsafe key: ${n[e]}`);if(n[e]in r||(r[n[e]]=_T()),!PT(r[n[e]])){i=!0;break}r=r[n[e]]}if(i||(Xw(r)?sx.includes(n[o])||delete e[t]:(r[n[o]]=e[t],delete e[t])),!Xw(r)){const e=r[n[o]];PT(e)&&GT(e)}}else PT(e[t])&&GT(e[t]);return e}function KT(e,t){const{messages:n,__i18n:o,messageResolver:r,flatJson:i}=t,s=IT(n)?n:ST(o)?_T():{[e]:_T()};if(ST(o)&&o.forEach(e=>{if("locale"in e&&"resource"in e){const{locale:t,resource:n}=e;t?(s[t]=s[t]||_T(),MT(n,s[t])):MT(n,s)}else ET(e)&&MT(JSON.parse(e),s)}),null==r&&i)for(const a in s)TT(s,a)&&GT(s[a]);return s}function JT(e){return e.type}function ZT(e,t,n){let o=PT(t.messages)?t.messages:_T();"__i18nGlobal"in n&&(o=KT(e.locale.value,{messages:o,__i18n:n.__i18nGlobal}));const r=Object.keys(o);if(r.length&&r.forEach(t=>{e.mergeLocaleMessage(t,o[t])}),PT(t.datetimeFormats)){const n=Object.keys(t.datetimeFormats);n.length&&n.forEach(n=>{e.mergeDateTimeFormat(n,t.datetimeFormats[n])})}if(PT(t.numberFormats)){const n=Object.keys(t.numberFormats);n.length&&n.forEach(n=>{e.mergeNumberFormat(n,t.numberFormats[n])})}}function QT(e){return zi(Ci,null,e,0)}const eS=()=>[],tS=()=>!1;let nS=0;function oS(e){return(t,n,o,r)=>e(n,o,ns()||void 0,r)}function rS(e={}){const{__root:t,__injectWithOption:n}=e,o=void 0===t,r=e.flatJson,i=hT?Bn:jn;let s=!CT(e.inheritLocale)||e.inheritLocale;const a=i(t&&s?t.locale.value:ET(e.locale)?e.locale:Rx),l=i(t&&s?t.fallbackLocale.value:ET(e.fallbackLocale)||ST(e.fallbackLocale)||IT(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:a.value),c=i(KT(a.value,e)),u=i(IT(e.datetimeFormats)?e.datetimeFormats:{[a.value]:{}}),d=i(IT(e.numberFormats)?e.numberFormats:{[a.value]:{}});let f=t?t.missingWarn:!CT(e.missingWarn)&&!vT(e.missingWarn)||e.missingWarn,p=t?t.fallbackWarn:!CT(e.fallbackWarn)&&!vT(e.fallbackWarn)||e.fallbackWarn,h=t?t.fallbackRoot:!CT(e.fallbackRoot)||e.fallbackRoot,m=!!e.fallbackFormat,g=kT(e.missing)?e.missing:null,v=kT(e.missing)?oS(e.missing):null,y=kT(e.postTranslation)?e.postTranslation:null,b=t?t.warnHtmlMessage:!CT(e.warnHtmlMessage)||e.warnHtmlMessage,_=!!e.escapeParameter;const w=t?t.modifiers:IT(e.modifiers)?e.modifiers:{};let x,T=e.pluralRules||t&&t.pluralRules;x=(()=>{o&&jx(null);const t={version:"11.1.7",locale:a.value,fallbackLocale:l.value,messages:c.value,modifiers:w,pluralRules:T,missing:null===v?void 0:v,missingWarn:f,fallbackWarn:p,fallbackFormat:m,unresolving:!0,postTranslation:null===y?void 0:y,warnHtmlMessage:b,escapeParameter:_,messageResolver:e.messageResolver,messageCompiler:e.messageCompiler,__meta:{framework:"vue"}};t.datetimeFormats=u.value,t.numberFormats=d.value,t.__datetimeFormatters=IT(x)?x.__datetimeFormatters:void 0,t.__numberFormatters=IT(x)?x.__numberFormatters:void 0;const n=Wx(t);return o&&jx(n),n})(),Ux(x,a.value,l.value);const S=ps({get:()=>a.value,set:e=>{x.locale=e,a.value=e}}),k=ps({get:()=>l.value,set:e=>{x.fallbackLocale=e,l.value=e,Ux(x,a.value,e)}}),E=ps(()=>c.value),C=ps(()=>u.value),P=ps(()=>d.value);const L=(e,n,r,i,s,f)=>{let p;a.value,l.value,c.value,u.value,d.value;try{0,o||(x.fallbackContext=t?Bx:void 0),p=e(x)}finally{o||(x.fallbackContext=void 0)}if("translate exists"!==r&&gT(p)&&-1===p||"translate exists"===r&&!p){const[e,o]=n();return t&&h?i(t):s(e)}if(f(p))return p;throw WT(RT)};function O(...e){return L(t=>Reflect.apply(uT,null,[t,...e]),()=>pT(...e),"translate",t=>Reflect.apply(t.t,t,[...e]),e=>e,e=>ET(e))}const I={normalize:function(e){return e.map(e=>ET(e)||gT(e)||CT(e)?QT(String(e)):e)},interpolate:e=>e,type:"vnode"};function A(e){return c.value[e]||{}}nS++,t&&hT&&(Vo(t.locale,e=>{s&&(a.value=e,x.locale=e,Ux(x,a.value,l.value))}),Vo(t.fallbackLocale,e=>{s&&(l.value=e,x.fallbackLocale=e,Ux(x,a.value,l.value))}));const M={id:nS,locale:S,fallbackLocale:k,get inheritLocale(){return s},set inheritLocale(e){s=e,e&&t&&(a.value=t.locale.value,l.value=t.fallbackLocale.value,Ux(x,a.value,l.value))},get availableLocales(){return Object.keys(c.value).sort()},messages:E,get modifiers(){return w},get pluralRules(){return T||{}},get isGlobal(){return o},get missingWarn(){return f},set missingWarn(e){f=e,x.missingWarn=f},get fallbackWarn(){return p},set fallbackWarn(e){p=e,x.fallbackWarn=p},get fallbackRoot(){return h},set fallbackRoot(e){h=e},get fallbackFormat(){return m},set fallbackFormat(e){m=e,x.fallbackFormat=m},get warnHtmlMessage(){return b},set warnHtmlMessage(e){b=e,x.warnHtmlMessage=e},get escapeParameter(){return _},set escapeParameter(e){_=e,x.escapeParameter=e},t:O,getLocaleMessage:A,setLocaleMessage:function(e,t){if(r){const n={[e]:t};for(const e in n)TT(n,e)&&GT(n[e]);t=n[e]}c.value[e]=t,x.messages=c.value},mergeLocaleMessage:function(e,t){c.value[e]=c.value[e]||{};const n={[e]:t};if(r)for(const o in n)TT(n,o)&&GT(n[o]);MT(t=n[e],c.value[e]),x.messages=c.value},getPostTranslationHandler:function(){return kT(y)?y:null},setPostTranslationHandler:function(e){y=e,x.postTranslation=e},getMissingHandler:function(){return g},setMissingHandler:function(e){null!==e&&(v=oS(e)),g=e,x.missing=v},[qT]:function(e){T=e,x.pluralRules=T}};return M.datetimeFormats=C,M.numberFormats=P,M.rt=function(...e){const[t,n,o]=e;if(o&&!PT(o))throw WT($T);return O(t,n,yT({resolvedMessage:!0},o||{}))},M.te=function(e,t){return L(()=>{if(!e)return!1;const n=A(ET(t)?t:a.value),o=x.messageResolver(n,e);return Xw(o)||cT(o)||ET(o)},()=>[e],"translate exists",n=>Reflect.apply(n.te,n,[e,t]),tS,e=>CT(e))},M.tm=function(e){const n=function(e){let t=null;const n=Sx(x,l.value,a.value);for(let o=0;o<n.length;o++){const r=c.value[n[o]]||{},i=x.messageResolver(r,e);if(null!=i){t=i;break}}return t}(e);return null!=n?n:t&&t.tm(e)||{}},M.d=function(...e){return L(t=>Reflect.apply(Xx,null,[t,...e]),()=>Kx(...e),"datetime format",t=>Reflect.apply(t.d,t,[...e]),()=>"",e=>ET(e)||ST(e))},M.n=function(...e){return L(t=>Reflect.apply(Zx,null,[t,...e]),()=>eT(...e),"number format",t=>Reflect.apply(t.n,t,[...e]),()=>"",e=>ET(e)||ST(e))},M.getDateTimeFormat=function(e){return u.value[e]||{}},M.setDateTimeFormat=function(e,t){u.value[e]=t,x.datetimeFormats=u.value,Jx(x,e,t)},M.mergeDateTimeFormat=function(e,t){u.value[e]=yT(u.value[e]||{},t),x.datetimeFormats=u.value,Jx(x,e,t)},M.getNumberFormat=function(e){return d.value[e]||{}},M.setNumberFormat=function(e,t){d.value[e]=t,x.numberFormats=d.value,tT(x,e,t)},M.mergeNumberFormat=function(e,t){d.value[e]=yT(d.value[e]||{},t),x.numberFormats=d.value,tT(x,e,t)},M[YT]=n,M[HT]=function(...e){return L(t=>{let n;const o=t;try{o.processor=I,n=Reflect.apply(uT,null,[o,...e])}finally{o.processor=null}return n},()=>pT(...e),"translate",t=>t[HT](...e),e=>[QT(e)],e=>ST(e))},M[zT]=function(...e){return L(t=>Reflect.apply(Xx,null,[t,...e]),()=>Kx(...e),"datetime format",t=>t[zT](...e),eS,e=>ET(e)||ST(e))},M[UT]=function(...e){return L(t=>Reflect.apply(Zx,null,[t,...e]),()=>eT(...e),"number format",t=>t[UT](...e),eS,e=>ET(e)||ST(e))},M}function iS(e={}){const t=rS(function(e){const t=ET(e.locale)?e.locale:Rx,n=ET(e.fallbackLocale)||ST(e.fallbackLocale)||IT(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:t,o=kT(e.missing)?e.missing:void 0,r=!CT(e.silentTranslationWarn)&&!vT(e.silentTranslationWarn)||!e.silentTranslationWarn,i=!CT(e.silentFallbackWarn)&&!vT(e.silentFallbackWarn)||!e.silentFallbackWarn,s=!CT(e.fallbackRoot)||e.fallbackRoot,a=!!e.formatFallbackMessages,l=IT(e.modifiers)?e.modifiers:{},c=e.pluralizationRules,u=kT(e.postTranslation)?e.postTranslation:void 0,d=!ET(e.warnHtmlInMessage)||"off"!==e.warnHtmlInMessage,f=!!e.escapeParameterHtml,p=!CT(e.sync)||e.sync;let h=e.messages;if(IT(e.sharedMessages)){const t=e.sharedMessages;h=Object.keys(t).reduce((e,n)=>{const o=e[n]||(e[n]={});return yT(o,t[n]),e},h||{})}const{__i18n:m,__root:g,__injectWithOption:v}=e,y=e.datetimeFormats,b=e.numberFormats;return{locale:t,fallbackLocale:n,messages:h,flatJson:e.flatJson,datetimeFormats:y,numberFormats:b,missing:o,missingWarn:r,fallbackWarn:i,fallbackRoot:s,fallbackFormat:a,modifiers:l,pluralRules:c,postTranslation:u,warnHtmlMessage:d,escapeParameter:f,messageResolver:e.messageResolver,inheritLocale:p,__i18n:m,__root:g,__injectWithOption:v}}(e)),{__extender:n}=e,o={id:t.id,get locale(){return t.locale.value},set locale(e){t.locale.value=e},get fallbackLocale(){return t.fallbackLocale.value},set fallbackLocale(e){t.fallbackLocale.value=e},get messages(){return t.messages.value},get datetimeFormats(){return t.datetimeFormats.value},get numberFormats(){return t.numberFormats.value},get availableLocales(){return t.availableLocales},get missing(){return t.getMissingHandler()},set missing(e){t.setMissingHandler(e)},get silentTranslationWarn(){return CT(t.missingWarn)?!t.missingWarn:t.missingWarn},set silentTranslationWarn(e){t.missingWarn=CT(e)?!e:e},get silentFallbackWarn(){return CT(t.fallbackWarn)?!t.fallbackWarn:t.fallbackWarn},set silentFallbackWarn(e){t.fallbackWarn=CT(e)?!e:e},get modifiers(){return t.modifiers},get formatFallbackMessages(){return t.fallbackFormat},set formatFallbackMessages(e){t.fallbackFormat=e},get postTranslation(){return t.getPostTranslationHandler()},set postTranslation(e){t.setPostTranslationHandler(e)},get sync(){return t.inheritLocale},set sync(e){t.inheritLocale=e},get warnHtmlInMessage(){return t.warnHtmlMessage?"warn":"off"},set warnHtmlInMessage(e){t.warnHtmlMessage="off"!==e},get escapeParameterHtml(){return t.escapeParameter},set escapeParameterHtml(e){t.escapeParameter=e},get pluralizationRules(){return t.pluralRules||{}},__composer:t,t:(...e)=>Reflect.apply(t.t,t,[...e]),rt:(...e)=>Reflect.apply(t.rt,t,[...e]),te:(e,n)=>t.te(e,n),tm:e=>t.tm(e),getLocaleMessage:e=>t.getLocaleMessage(e),setLocaleMessage(e,n){t.setLocaleMessage(e,n)},mergeLocaleMessage(e,n){t.mergeLocaleMessage(e,n)},d:(...e)=>Reflect.apply(t.d,t,[...e]),getDateTimeFormat:e=>t.getDateTimeFormat(e),setDateTimeFormat(e,n){t.setDateTimeFormat(e,n)},mergeDateTimeFormat(e,n){t.mergeDateTimeFormat(e,n)},n:(...e)=>Reflect.apply(t.n,t,[...e]),getNumberFormat:e=>t.getNumberFormat(e),setNumberFormat(e,n){t.setNumberFormat(e,n)},mergeNumberFormat(e,n){t.mergeNumberFormat(e,n)}};return o.__extender=n,o}function sS(e,t){e.locale=t.locale||e.locale,e.fallbackLocale=t.fallbackLocale||e.fallbackLocale,e.missing=t.missing||e.missing,e.silentTranslationWarn=t.silentTranslationWarn||e.silentFallbackWarn,e.silentFallbackWarn=t.silentFallbackWarn||e.silentFallbackWarn,e.formatFallbackMessages=t.formatFallbackMessages||e.formatFallbackMessages,e.postTranslation=t.postTranslation||e.postTranslation,e.warnHtmlInMessage=t.warnHtmlInMessage||e.warnHtmlInMessage,e.escapeParameterHtml=t.escapeParameterHtml||e.escapeParameterHtml,e.sync=t.sync||e.sync,e.__composer[qT](t.pluralizationRules||e.pluralizationRules);const n=KT(e.locale,{messages:t.messages,__i18n:t.__i18n});return Object.keys(n).forEach(t=>e.mergeLocaleMessage(t,n[t])),t.datetimeFormats&&Object.keys(t.datetimeFormats).forEach(n=>e.mergeDateTimeFormat(n,t.datetimeFormats[n])),t.numberFormats&&Object.keys(t.numberFormats).forEach(n=>e.mergeNumberFormat(n,t.numberFormats[n])),e}const aS={tag:{type:[String,Object]},locale:{type:String},scope:{type:String,validator:e=>"parent"===e||"global"===e,default:"parent"},i18n:{type:Object}};function lS(){return Ei}const cS=ir({name:"i18n-t",props:yT({keypath:{type:String,required:!0},plural:{type:[Number,String],validator:e=>gT(e)||!isNaN(e)}},aS),setup(e,t){const{slots:n,attrs:o}=t,r=e.i18n||gS({useScope:e.scope,__useComponent:!0});return()=>{const i=Object.keys(n).filter(e=>"_"!==e[0]),s=_T();e.locale&&(s.locale=e.locale),void 0!==e.plural&&(s.plural=ET(e.plural)?+e.plural:e.plural);const a=function({slots:e},t){if(1===t.length&&"default"===t[0])return(e.default?e.default():[]).reduce((e,t)=>[...e,...t.type===Ei?t.children:[t]],[]);return t.reduce((t,n)=>{const o=e[n];return o&&(t[n]=o()),t},_T())}(t,i),l=r[HT](e.keypath,a,s),c=yT(_T(),o);return ms(ET(e.tag)||PT(e.tag)?e.tag:lS(),c,l)}}});function uS(e,t,n,o){const{slots:r,attrs:i}=t;return()=>{const t={part:!0};let s=_T();e.locale&&(t.locale=e.locale),ET(e.format)?t.key=e.format:PT(e.format)&&(ET(e.format.key)&&(t.key=e.format.key),s=Object.keys(e.format).reduce((t,o)=>n.includes(o)?yT(_T(),t,{[o]:e.format[o]}):t,_T()));const a=o(e.value,t,s);let l=[t.key];ST(a)?l=a.map((e,t)=>{const n=r[e.type],o=n?n({[e.type]:e.value,index:t,parts:a}):[e.value];var i;return ST(i=o)&&!ET(i[0])&&(o[0].key=`${e.type}-${t}`),o}):ET(a)&&(l=[a]);const c=yT(_T(),i);return ms(ET(e.tag)||PT(e.tag)?e.tag:lS(),c,l)}}const dS=ir({name:"i18n-n",props:yT({value:{type:Number,required:!0},format:{type:[String,Object]}},aS),setup(e,t){const n=e.i18n||gS({useScope:e.scope,__useComponent:!0});return uS(e,t,Qx,(...e)=>n[UT](...e))}});function fS(e){if(ET(e))return{path:e};if(IT(e)){if(!("path"in e))throw WT(FT);return e}throw WT(BT)}function pS(e){const{path:t,locale:n,args:o,choice:r,plural:i}=e,s={},a=o||{};return ET(n)&&(s.locale=n),gT(r)&&(s.plural=r),gT(i)&&(s.plural=i),[t,a,s]}function hS(e,t,...n){const o=IT(n[0])?n[0]:{};(!CT(o.globalInstall)||o.globalInstall)&&([cS.name,"I18nT"].forEach(t=>e.component(t,cS)),[dS.name,"I18nN"].forEach(t=>e.component(t,dS)),[bS.name,"I18nD"].forEach(t=>e.component(t,bS))),e.directive("t",function(e){const t=t=>{const{instance:n,value:o}=t;if(!n||!n.$)throw WT(VT);const r=function(e,t){const n=e;if("composition"===e.mode)return n.__getInstance(t)||e.global;{const o=n.__getInstance(t);return null!=o?o.__composer:e.global.__composer}}(e,n.$),i=fS(o);return[Reflect.apply(r.t,r,[...pS(i)]),r]};return{created:(n,o)=>{const[r,i]=t(o);hT&&e.global===i&&(n.__i18nWatcher=Vo(i.locale,()=>{o.instance&&o.instance.$forceUpdate()})),n.__composer=i,n.textContent=r},unmounted:e=>{hT&&e.__i18nWatcher&&(e.__i18nWatcher(),e.__i18nWatcher=void 0,delete e.__i18nWatcher),e.__composer&&(e.__composer=void 0,delete e.__composer)},beforeUpdate:(e,{value:t})=>{if(e.__composer){const n=e.__composer,o=fS(t);e.textContent=Reflect.apply(n.t,n,[...pS(o)])}},getSSRProps:e=>{const[n]=t(e);return{textContent:n}}}}(t))}const mS=mT("global-vue-i18n");function gS(e={}){const t=ns();if(null==t)throw WT(NT);if(!t.isCE&&null!=t.appContext.app&&!t.appContext.app.__VUE_I18N_SYMBOL__)throw WT(DT);const n=function(e){const t=si(e.isCE?mS:e.appContext.app.__VUE_I18N_SYMBOL__);if(!t)throw WT(e.isCE?jT:VT);return t}(t),o=function(e){return"composition"===e.mode?e.global:e.global.__composer}(n),r=JT(t),i=function(e,t){return n=e,IT(n)&&0===Object.keys(n).length?"__i18n"in t?"local":"global":e.useScope?e.useScope:"local";var n}(e,r);if("global"===i)return ZT(o,e,r),o;if("parent"===i){let r=function(e,t,n=!1){let o=null;const r=t.root;let i=function(e,t=!1){if(null==e)return null;return t&&e.vnode.ctx||e.parent}(t,n);for(;null!=i;){const t=e;if("composition"===e.mode)o=t.__getInstance(i);else{const e=t.__getInstance(i);null!=e&&(o=e.__composer,n&&o&&!o[YT]&&(o=null))}if(null!=o)break;if(r===i)break;i=i.parent}return o}(n,t,e.__useComponent);return null==r&&(r=o),r}const s=n;let a=s.__getInstance(t);if(null==a){const n=yT({},e);"__i18n"in r&&(n.__i18n=r.__i18n),o&&(n.__root=o),a=rS(n),s.__composerExtend&&(a[XT]=s.__composerExtend(a)),function(e,t,n){kr(()=>{},t),Lr(()=>{const o=n;e.__deleteInstance(t);const r=o[XT];r&&(r(),delete o[XT])},t)}(s,t,a),s.__setInstance(t,a)}return a}const vS=["locale","fallbackLocale","availableLocales"],yS=["t","rt","d","n","tm","te"];const bS=ir({name:"i18n-d",props:yT({value:{type:[Number,Date],required:!0},format:{type:[String,Object]}},aS),setup(e,t){const n=e.i18n||gS({useScope:e.scope,__useComponent:!0});return uS(e,t,Gx,(...e)=>n[zT](...e))}});"boolean"!=typeof __INTLIFY_DROP_MESSAGE_COMPILER__&&((wT||(wT="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:_T())).__INTLIFY_DROP_MESSAGE_COMPILER__=!1),Nx=function(e,t){if(!__INTLIFY_DROP_MESSAGE_COMPILER__&&iw(e)){!sw(t.warnHtmlMessage)||t.warnHtmlMessage;const n=(t.onCacheKey||dx)(e),o=fx[n];if(o)return o;const{ast:r,detectError:i}=function(e,t={}){let n=!1;const o=t.onError||Lw;return t.onError=e=>{n=!0,o(e)},{...Yw(e,t),detectError:n}}(e,{...t,location:!1,jit:!0}),s=lx(r);return i?s:fx[n]=s}{const t=e.cacheKey;if(t){const n=fx[t];return n||(fx[t]=lx(e))}return lx(e)}},Dx=function(e,t){if(!aw(e))return null;let n=Ax.get(t);if(n||(n=function(e){const t=[];let n,o,r,i,s,a,l,c=-1,u=0,d=0;const f=[];function p(){const t=e[c+1];if(5===u&&"'"===t||6===u&&'"'===t)return c++,r="\\"+t,f[0](),!0}for(f[0]=()=>{void 0===o?o=r:o+=r},f[1]=()=>{void 0!==o&&(t.push(o),o=void 0)},f[2]=()=>{f[0](),d++},f[3]=()=>{if(d>0)d--,u=4,f[0]();else{if(d=0,void 0===o)return!1;if(o=Ix(o),!1===o)return!1;f[1]()}};null!==u;)if(c++,n=e[c],"\\"!==n||!p()){if(i=Ox(n),l=Px[u],s=l[i]||l.l||8,8===s)return;if(u=s[0],void 0!==s[1]&&(a=f[s[1]],a&&(r=n,!1===a())))return;if(7===u)return t}}(t),n&&Ax.set(t,n)),!n)return null;const o=n.length;let r=e,i=0;for(;i<o;){const e=n[i];if(sx.includes(e)&&Xw(r))return null;const t=r[e];if(void 0===t)return null;if(rw(r))return null;r=t,i++}return r},Fx=Sx;const _S={index:{hotTitle:"熱門項目",highest:"單人最高",date:"最新資料",tip:"已滑到底部~",goNow:"現在開始",later:"稍後填寫"},team:{title:"我的團隊",button:"按鈕",reward:"我的獎勵",ppl:"下級人數",ppl2:"次級人數",member:"邀請成員",code:"邀請碼",invite:"邀請",siginUp:"注册",success:"成功",tipTitle:"如何組建團隊",popTitle:"邀請下屬",promotionPoster:"宣傳海報",downloadSuccess:"下載成功",saveFailed:"保存失敗",desc:"分享：如果有人通過您的連結注册，您將獲得相應的補貼獎勵。"},my:{logintip:"現在登入",balancelance:"帳戶餘額（美元）",message:"通知",service:"客戶服務",filings:"我的報備",information:"填寫資訊",income:"收益明細",fund:"資金明細",order:"訂單歷史",settlement:"結算記錄",access:"快速訪問",finances:"財政",media:"媒體庫",language:"選擇語言",emailVerif:"電子郵件驗證",currentPWD:"當前密碼",RegistrationDetails:"注册詳細資訊"},tab:{home:"首頁",account:"賬戶",team:"團隊"},login:{title:"登入",tip:"輸入有效的電子郵件和",tip2:"密碼繼續。",tip3:"驗證碼繼續。",way1:"使用驗證碼登入",way2:"使用密碼登入",emailPlaceholder:"電子郵件",passwordPlaceholder:"密碼",repeatPasswordPlaceholder:"重複密碼",namePlaceholder:"綽號",verificationCodePlactholder:"邀請碼",code:"電子郵件驗證碼",codeText:"獲取驗證碼",loginBtn:"登錄",tourist:"訪客模式",prompt:"沒有帳號？",register:"註冊",aliTitle:"安全保護",aliTip:"驗證您的帳戶的安全性",setTitle:"電子郵件資訊",setTitle2:"密碼",registerTip:"請輸入您的註冊資訊",unsubscribe:"取消訂閱"},agreement:{or:"或",and:"和",agree:"我已閱讀並同意",service:"服務條款",privacy:"隱私政策",tit:"在繼續下一步之前，請閱讀並同意Rento的",no:"不同意",yes:"同意並繼續",accountDeletion:"我已閱讀並同意Rento",accountDeletionAgreement:"帳戶註銷協定"},setLanguage:{title:"選擇語言"},media:{title:"媒體資訊提交成功",tips:"媒體資訊稽核後，可以直接訪問，下次無需填寫",platform:"平臺",fans:"粉絲",reviewFailed:"稽核失敗",select:"選擇媒體",fillInByHand:"手工填寫",tip1:"現時，只有以下媒體平臺",tip2:"支持報告。 不符合項目方規範的媒體平臺已自動為您隱藏。"},sys:{logoutTip:"您確定要註銷嗎？",exportExcel:"匯出Excel",reviewStatus:"審查狀態",saveDownload:"保存下載",serviceAgreementAndPrivacyPolicy:"服務協定和隱私政策",reviewStatusObj:{all:"所有",pending:"待定",success:"成功",passed:"通過",rejected:"拒絕",failed:"失敗的"},addNow:"立即添加",select:"選擇",upload:"上傳",requestFailed:"請求失敗",requestTimeout:"請求超時，自動重試",networkError:"網絡連接錯誤",noNetwork:"網絡不可用",next:"下一步",sure:"當然",previous:"上一次的",emptyState:"無可用數據",searchEmpty:"未找到結果",invite:"立即邀請",enter:"輸入",loseEfficacy:"驗證無效",searchTip:"蒐索內容",btnTitle:"我已知曉",more:"更多",synopsis:"簡介",reset:"重置",confirm:"確認",filter:"篩選",copysuccess:"複製成功",copyfail:"複製失敗",cancel:"取消",pleaseEnter:"請輸入",start:"開始",end:"結束",delete:"删去",pleaseLogin:"請登入",loginTip:"更多精彩，請登入體驗～",goLogin:"轉到登入",back:"<返回",expand:"擴大",resubmit:"重新提交",time:"時間",startTime:"開始時間",endTime:"結束時間",pleaseSelect:"請選擇",loading:"加載。 ..",noData:"已滑到底部~",accountCancellationRisk:"帳戶註銷風險",accountCancellationRisk2:"解除綁定驗證",accountCancellationRisktip:"驗證碼已發送到電子郵件",accountCancellationConfirm:"確認取消",accountCancellationSteps:{unableToLogin:{title:"無法登入",desc:"一旦您註銷，您將無法使用此帳戶登入Rento。"},informationErasure:{title:"資訊擦除和不可恢復性",desc:"帳戶註銷將删除您的個人資訊，清除您的餘額，拒絕已報告和提交的憑證，删除經過身份驗證的實名資訊、綁定的銀行帳戶資訊等。 同時，從屬綁定關係將被終止，無法恢復。 申請取消前請確認！"},reRegistration:{title:"重新注册",desc:"綁定到帳戶的手機號碼只能在取消後7天內重新注册。"}},applyNow:"立即申請",tip:"提示",addMedia:"添加媒體",deleteTip:"是否删除",deleteTip1:"您想删除以下媒體資訊嗎[",deleteTip2:"]？此操作無法撤銷。",deleteTip3:"您想删除以下資訊嗎[",return:"回",save:"保存",modify:"修改",thinkAgain:"重新考慮",status:"狀態",days:"一天",theFileSizeExceedsTheLimit:"文件大小超出限制",retry:"重試"},userInfo:{title:"設定",id:"ID",name:"顯示名稱",inviteCode:"邀請碼",account:"帳戶和安全",language:"語言",version:"當前版本",logOut:"登出",email:"電子郵件",changeEmail:"更改電子郵件",changePassword:"更改密碼",deleteAccount:"刪除帳戶",emailTip:"已綁定電子郵件",newEmail:"綁定新電子郵件",newEmailTip:"用於密碼檢索和身份驗證",verifyIdentity:"驗證身份",tips1:"選擇任何方法驗證身份",tips2:"密碼應為8-20個字元，至少由大寫字母、小寫字母、數位和特殊字元中的兩個組成。"},details:{title:"項目詳細資訊",total:"總收入",promotion:"推廣碼",history:"歷史憑證",notice:"通知",noticeContent:"項目變更公告",content:"內容庫",noticeTitle:"變更公告",proj:"項目公告",data:"數據與結算",chg:"變更通知",tit:"詳情",chapter:"章節清單",taster:"調味汁含量",applyForPromotion:"申請推廣",informationSubmitted:"資訊提交成功",checkProgressIn:"您可以在",checkProgressIn2:"中查看進度",myPromotionCode:"我的推廣碼",checkProgress:"檢查進度",promoteBtn:"立即推廣",filterShortDramas:"篩選短片",priceTitle:"項目價格",projectInfo:"項目資訊",integrityCommitmentLetter:"誠信承諾書",readAndagree:"我已閱讀並同意",sign:"同意並簽字",selectPromotionCode:"選擇推廣碼"},search:{placeholder:"蒐索",actionText:"取消"},promotion:{title:"推廣碼",Total:"總的",Deals:"交易",Income:"收入",formTitle:"填寫申請信息",copy:"複製一條",add:"添加一條",selectSettlementMethod:"選擇方式",referralCode:"推薦碼",PromotionCodeType:"推廣碼類型",belongedProject:"所屬項目",mobilePhoneNumber:"手機號碼",keywords:"關鍵字",applicationTime:"申請時間",kuakeUid:"Kuake UID/ID",mainPlatform:"共亯主平臺",followers:"關注者/用戶數量",dailyOrderVolume:"預計每日訂單量",total:"總的",deals:"交易",income:"收入",details:"詳情",expand:"擴大",collapse:"崩潰",resubmit:"重新提交",orderRecords:"訂單記錄"},navTitle:{promotion:"申請推廣",promotionDetails:"推廣碼詳細資訊",applicationRecords:"申請記錄",notifications:"通知",filings:"我的報備",voucher:"提交憑證",media:"媒體庫",mediaDetails:"媒體詳細資訊",income:"收入記錄",incomeDetails:"收入明細",orderTitle:"訂單歷史",settlement:"我的結算帳戶",record:"歷史記錄",addSettlementAccount:"添加結算帳戶",fundDetails:"資金明細"},notice:{all:"所有",notice:"通知",mail:"郵件",projectNotice:"項目通知",platform:"平臺",compliance:"遵從"},wallet:{settle:"立即結算",monthlyIncome:"月收入（$）",totalIncome:"總收入（$）",accountBalance:"帳戶餘額（$）",earningsPlatform:"盈利平臺",settlementRecords:"結算記錄",title:"我的錢包",tip1:"已成功提交正在稽核中",tip2:"查看【結算記錄】中的進度"},income:{income:"收入",totalEarnings:"總收入",cumulativeGain:"累計收益",timeSlot:"時隙",teamDate:"團隊日期",detailTeamTip:"注：不包括間接收入",searchPlaceholder:"按姓名/電話號碼蒐索"},voucher:{title:"已提交憑證記錄"},bankCard:{paymentMethod:"結算方式",remittancePathCode:"匯款路徑程式碼",account:"賬戶",region:"區域",city:"都市",address:"住址",zipCode:"郵政的",handlingFee:"手續費",accountLength:"請輸入有效的帳號",selectSettlementAccount:"選擇結算帳戶"},fund:{taskTitle:"任務標題",type:"類型",orderNo:"訂單號",status:"狀態",description:"描述",time:"時間",report:"彙報",singularNumber:"單數",settlementAccount:"結算帳戶",paymentDetails:"付款資料",balance:"平衡",withdrawAll:"全部撤回",withdrawalInstructions:"提款說明：",tip1:"1.請確保付款帳戶資訊填寫正確，帳戶屬於您",tip2:"2.提款處理和貸記期為3個工作日。 如果進行貨幣兌換，匯率將根據付款時間實时計算",tip3:"3.Wise平臺不收取提款費",tip4:"3.銀行卡的提款費由協力廠商平臺收取，我們的平臺不收取任何服務費。",Input:"輸入"}},wS={index:{hotTitle:"Popular projects",highest:"Single highest",date:"atest Data",tip:"You've Reached the End~",goNow:"Go Now",later:"Fill it in later"},team:{title:"My Team",button:"button",reward:"My Rewards",ppl:"Subordinate",ppl2:"Secondary",member:"Invite Members",code:"Invitation Code",invite:"Invite",siginUp:"Sign Up",success:"Success",tipTitle:"How to Build a Team",popTitle:"Invite Subordinates",promotionPoster:"Promotion Poster",downloadSuccess:"Download Success",saveFailed:"Save Failed",desc:"Share: If someone registers through your link, you will receive the corresponding subsidy rewards."},my:{logintip:"Log in Now",balancelance:"Account balance (USD)",message:"Notifications",service:"Customer Service",filings:"Filings",information:"Complete the information",income:"Earnings Details",fund:"Fund Details",order:"Order History",settlement:"Settlement Record",access:"Quick Access",finances:"Finances",media:"Media Library",language:"Select Language",emailVerif:"Email Verif",currentPWD:"Current Password",RegistrationDetails:"Registration Details"},tab:{home:"Home",account:"Account",team:"Team"},login:{title:"Sign ln",tip:"Enter valid user name & ",tip2:"password to continue.",tip3:"code to continues.",way1:"Sign ln with Code",way2:"Sign ln with Password",emailPlaceholder:"Eamail",passwordPlaceholder:"Password",repeatPasswordPlaceholder:"Repeat Password",namePlaceholder:"Nick name",verificationCodePlactholder:"Invitation Code",code:"Email Code",codeText:"Get Code",loginBtn:"Log in",tourist:"Guest Mode",prompt:"Don't have an account? ",register:"Sign up",aliTitle:"Security Protection",aliTip:"Verify Your Account for Security",setTitle:"Email Info",setTitle2:"Password",registerTip:"Please fll in your registration information",unsubscribe:"Unsubscribe"},agreement:{or:"or",and:" and ",agree:"I have read and agree to the ",service:"Terms of Service",privacy:"Privacy Policy",tit:"Before proceeding to the next step, please read and agree to Rento's",no:"Disagree",yes:"Agree and Continue",accountDeletion:"I have read and agree to the Rento ",accountDeletionAgreement:"Account Cancellation Agreement"},setLanguage:{title:"Select Language"},media:{title:"Media Information Submitted Successfully",tips:"After the media information is approved, it can be directly accessed without filling in the next time",platform:"Platform",fans:"Fans",reviewFailed:"Review Failed",select:"Select Media",fillInByHand:"Fill in by hand",tip1:"Currently, only media platforms such as ",tip2:" are supported for reporting. Media platforms that do not meet the project party's specifications have been automatically hidden for you."},sys:{logoutTip:"Are you sure you want to log out?",exportExcel:"Export Excel",reviewStatus:"Review Status",saveDownload:"Save Download",serviceAgreementAndPrivacyPolicy:"Service Agreement and Privacy Policy",reviewStatusObj:{all:"All",pending:"Pending",success:"Success",passed:"Passed",rejected:"Rejected",failed:"Failed"},addNow:"Add Now",select:"Select",upload:"Upload",requestFailed:"Request Failed",requestTimeout:"Request timeout, auto retry",networkError:"Network connection error",noNetwork:"Network unavailable",next:"Next",sure:"Sure",previous:"Previous",emptyState:"No data available",searchEmpty:"No results found",invite:"Invite Now",enter:"Enter the ",loseEfficacy:"Verification Invalid",searchTip:"Search content",btnTitle:"I acknowledge",more:"More",synopsis:"Synopsis",reset:"Reset",confirm:"Confirm",filter:"Filter",copysuccess:"Copy Success",copyfail:"Copy Failed",cancel:"Cancel",pleaseEnter:"Please enter",start:"Start",end:"End",delete:"Delete",pleaseLogin:"Please log in",loginTip:"For more excitement, please log in and experience it ～",goLogin:"Go to Login",back:"Back",expand:"Expand",resubmit:"Resubmit",time:"Time",startTime:"Start Time",endTime:"End Time",pleaseSelect:"Please select",loading:"Loading...",noData:"You've Reached the End~",accountCancellationRisk:"Account cancellation risk",accountCancellationRisk2:"Unbinding Verification",accountCancellationRisktip:"The verification code has been sent to the email ",accountCancellationConfirm:"Confirm Cancellation",accountCancellationSteps:{unableToLogin:{title:"Unable to Log In",desc:"Once you log out, you will not be able to use this account to log in to Rento."},informationErasure:{title:"Information Erasure and Irrecoverability",desc:"Account cancellation will delete your personal information, clear your balance, reject the vouchers that have been reported and submitted, delete the authenticated real - name information, bound bank account information, etc. At the same time, the subordinate binding relationship will be terminated, and it cannot be restored. Please confirm before applying for cancellation!"},reRegistration:{title:"Re - registration",desc:"The mobile phone number bound to the account can only be re - registered 7 days after cancellation."}},applyNow:"Apply Now",tip:"Tip",addMedia:"Add Media",deleteTip:"Delete or not",deleteTip1:"Do you want to delete the media information of [",deleteTip2:"]? This action cannot be undone.",deleteTip3:"Do you want to delete the information of [",return:"Return",save:"Save",modify:"Modify",thinkAgain:"Think Again",status:"Status",days:"days",theFileSizeExceedsTheLimit:"File size exceeds the limit",retry:"Retry"},userInfo:{title:"Settings",id:"ID",name:"Display Name",inviteCode:"Invite Code",account:"Account & Security",language:"Language",version:"Current Version",logOut:"Log Out",email:"Email",changeEmail:"Change Email",changePassword:"Change Password",deleteAccount:"Delete Account",emailTip:"Already Bound Email",newEmail:"Bind a new email",newEmailTip:"Used for password retrieval and identity verification",verifyIdentity:"Verify Identity",tips1:"Choose any method to verify identity",tips2:"Password should be 8 - 20 characters, composed of at least two of uppercase letters, lowercase letters, numbers and special characters."},details:{title:"Project Details",total:"Total income",promotion:"Promotion Code",history:"History Vouchers",notice:"Notice",noticeContent:"Project Change Announcement",content:"Content Library",noticeTitle:"Change Announcement",proj:"Proj. Annc",data:"Data & Sett",chg:"Chg. Notice",tit:"Details",chapter:"Chapter List",taster:"Taster Content",applyForPromotion:"Apply for Promotion",informationSubmitted:"Information submitted successfully",checkProgressIn:"You can check the progress in",myPromotionCode:"My Promotion Code",checkProgress:"Check the progress",promoteBtn:"Promote Now",filterShortDramas:"Filter short dramas",priceTitle:"Project Price",projectInfo:"Project Information",integrityCommitmentLetter:"Integrity Commitment Letter",readAndagree:"I have read and agree to the",sign:"Agree and Sign",selectPromotionCode:"Select Promotion Code"},search:{placeholder:"search",actionText:"Cancel"},promotion:{title:"Promotion Code",Total:"Total",Deals:"Deals",Income:"Income",formTitle:"Application Information",copy:"Copy this one",add:"Add a new entry",selectSettlementMethod:"Select method",referralCode:"Referral Code",PromotionCodeType:"Promotion Code Type",belongedProject:"Belonged Project",mobilePhoneNumber:"Mobile Phone Number",keywords:"Keywords",applicationTime:"Application Time",kuakeUid:"Kuake UID/ID",mainPlatform:"Main Platform for Sharing",followers:"Number of Followers/Users",dailyOrderVolume:"Estimated Daily Order Volume",total:"Total",deals:"Deals",income:"Income",details:"Details",expand:"Expand",collapse:"Collapse",resubmit:"Resubmit",orderRecords:"Order Records"},navTitle:{promotion:"Apply for Promotion",promotionDetails:"Promotion Code Details",applicationRecords:"Application Records",notifications:"Notifications",filings:"Filings",voucher:"Submit certificates",media:"Media Library",mediaDetails:"Media Details",income:"Income Records",incomeDetails:"Income Details",orderTitle:"Order History",settlement:"My Settlement Account",record:"History Record",addSettlementAccount:"Add Settlement Account",fundDetails:"Fund Details"},notice:{all:"All",notice:"Notice",mail:"Mail",projectNotice:"Project Notice",platform:"Platform",compliance:"Compliance"},wallet:{settle:"Settle Now",monthlyIncome:"Monthly Income ($)",totalIncome:"Total Income ($)",accountBalance:"Account Balance ($)",earningsPlatform:"Earnings Platform",settlementRecords:"Settlement Records",title:"My Wallet",tip1:"Submitted successfully Under review",tip2:"Check the progress in 【Settlement Record】"},income:{income:"My Income",totalEarnings:"Total Earnings",cumulativeGain:"Cumulative Gain",timeSlot:"Time Slot",teamDate:"Team Date",detailTeamTip:"Note: Does not include Indirects income",searchPlaceholder:"Search by Name/Phone Number"},voucher:{title:"Submitted Voucher Records"},bankCard:{paymentMethod:"Settlement Method",remittancePathCode:"Remittance Path Code",account:"Account",region:"Region",city:"City",address:"Address",zipCode:"postal",handlingFee:"Handling Fee",accountLength:"Please enter a valid account number",selectSettlementAccount:"Select Settlement Account"},fund:{taskTitle:"Task Title",type:"Type",orderNo:"OrderNo",status:"Status",description:"Description",time:"Time",report:"Report",singularNumber:"Singular number",settlementAccount:"Settlement Account",paymentDetails:"Payment Details",balance:"Balance",withdrawAll:"Withdraw All",withdrawalInstructions:"Withdrawal Instructions:",tip1:"1. Please ensure that the payment account information is filled in correctly and that the account belongs to you",tip2:"2. The withdrawal processing and crediting period is 3 business days. In case of currency exchange, the exchange rate will be calculated in real-time based on the payment time",tip3:"3. No withdrawal fee is charged on the Wise platform",tip4:"3. The withdrawal fee for bank cards is charged by third-party platforms, and our platform does not charge any service fees.",Input:"Input"}},xS={index:{hotTitle:"热门项目",highest:"单人最高",date:"最新数据",tip:"已滑到底部~",goNow:"现在开始",later:"稍后填写"},team:{title:"我的团队",button:"按钮",reward:"我的奖励",ppl:"下级人数",ppl2:"次级人数",member:"邀请成员",code:"邀请码",invite:"邀请",siginUp:"注册",success:"成功",tipTitle:"如何组建团队",popTitle:"邀请下属",promotionPoster:"宣传海报",downloadSuccess:"下载成功",saveFailed:"保存失败",desc:"分享：如果有人通过您的链接注册，您将获得相应的补贴奖励。"},my:{logintip:"现在登录",balancelance:"账户余额（美元）",message:"通知",service:"客户服务",filings:"我的报备",information:"填写信息",income:"收益明细",fund:"资金明细",order:"订单历史",settlement:"结算记录",access:"快速访问",finances:"财政",media:"媒体库",language:"选择语言",emailVerif:"电子邮件验证",currentPWD:"当前密码",RegistrationDetails:"注册详细信息"},tab:{home:"首页",account:"账户",team:"团队"},login:{title:"登录",tip:"输入有效的电子邮箱与",tip2:"密码继续。",tip3:"验证码继续。",way1:"使用验证码登录",way2:"使用密码登录",emailPlaceholder:"电子邮件",passwordPlaceholder:"密码",repeatPasswordPlaceholder:"重复密码",namePlaceholder:"绰号",verificationCodePlactholder:"邀请码",code:"电子邮件验证码",codeText:"获取验证码",loginBtn:"登录",tourist:"访客模式",prompt:"没有账号？",register:"注册",aliTitle:"安全保护",aliTip:"验证您的帐户的安全性",setTitle:"电子邮件信息",setTitle2:"密码",registerTip:"请输入您的注册信息",unsubscribe:"取消订阅"},agreement:{or:"或",and:"和",agree:"我已阅读并同意",service:"服务条款",privacy:"隐私政策",tit:"在继续下一步之前，请阅读并同意Rento的",no:"不同意",yes:"同意并继续",accountDeletion:"我已阅读并同意Rento",accountDeletionAgreement:"账户注销协议"},setLanguage:{title:"选择语言"},media:{title:"媒体信息提交成功",tips:"媒体信息审核后，可以直接访问，下次无需填写",platform:"平台",fans:"粉丝",reviewFailed:"审核失败",select:"选择媒体",fillInByHand:"手工填写",tip1:"目前，只有以下媒体平台",tip2:"支持报告。不符合项目方规范的媒体平台已自动为您隐藏。"},sys:{logoutTip:"您确定要注销吗？",exportExcel:"导出Excel",reviewStatus:"审查状态",saveDownload:"保存下载",serviceAgreementAndPrivacyPolicy:"服务协议和隐私政策",reviewStatusObj:{all:"所有",pending:"待定",success:"成功",passed:"通过",rejected:"拒绝",failed:"失败的"},addNow:"立即添加",select:"选择",upload:"上传",requestFailed:"请求失败",requestTimeout:"请求超时，已自动重试",networkError:"网络连接异常",noNetwork:"网络不可用",next:"下一步",sure:"当然",previous:"上一次的",emptyState:"无可用数据",searchEmpty:"未找到结果",invite:"立即邀请",enter:"输入",loseEfficacy:"验证无效",searchTip:"搜索内容",btnTitle:"我已知晓",more:"更多",synopsis:"简介",reset:"重置",confirm:"确认",filter:"筛选",copysuccess:"复制成功",copyfail:"复制失败",cancel:"取消",pleaseEnter:"请输入",start:"开始",end:"结束",delete:"删去",pleaseLogin:"请登录",loginTip:"更多精彩，请登录体验～",goLogin:"转到登录",back:"< 返回",expand:"扩大",resubmit:"重新提交",time:"时间",startTime:"开始时间",endTime:"结束时间",pleaseSelect:"请选择",loading:"加载。..",noData:"已滑到底部~",accountCancellationRisk:"账户注销风险",accountCancellationRisk2:"解除绑定验证",accountCancellationRisktip:"验证码已发送到电子邮件",accountCancellationConfirm:"确认取消",accountCancellationSteps:{unableToLogin:{title:"无法登录",desc:"一旦您注销，您将无法使用此帐户登录Rento。"},informationErasure:{title:"信息擦除和不可恢复性",desc:"账户注销将删除您的个人信息，清除您的余额，拒绝已报告和提交的凭证，删除经过身份验证的实名信息、绑定的银行账户信息等。同时，从属绑定关系将被终止，无法恢复。申请取消前请确认！"},reRegistration:{title:"重新注册",desc:"绑定到帐户的手机号码只能在取消后7天内重新注册。"}},applyNow:"立即申请",tip:"提示",addMedia:"添加媒体",deleteTip:"是否删除",deleteTip1:"您想删除以下媒体信息吗[",deleteTip2:"]？此操作无法撤消。",deleteTip3:"您想删除以下信息吗[",return:"回",save:"保存",modify:"修改",thinkAgain:"重新考虑",status:"状态",days:"一天",theFileSizeExceedsTheLimit:"文件大小超出限制",retry:"重试"},userInfo:{title:"设置",id:"ID",name:"显示名称",inviteCode:"邀请码",account:"帐户和安全",language:"语言",version:"当前版本",logOut:"退出",email:"电子邮件",changeEmail:"更改电子邮件",changePassword:"更改密码",deleteAccount:"删除帐户",emailTip:"已绑定电子邮件",newEmail:"绑定新电子邮件",newEmailTip:"用于密码检索和身份验证",verifyIdentity:"验证身份",tips1:"选择任何方法验证身份",tips2:"密码应为8-20个字符，至少由大写字母、小写字母、数字和特殊字符中的两个组成。"},details:{title:"项目详细信息",total:"总收入",promotion:"推广码",history:"历史凭证",notice:"通知",noticeContent:"项目变更公告",content:"内容库",noticeTitle:"变更公告",proj:"项目公告",data:"数据与结算",chg:"变更通知",tit:"详情",chapter:"章节列表",taster:"调味汁含量",applyForPromotion:"申请推广",informationSubmitted:"信息提交成功",checkProgressIn:"您可以在",checkProgressIn2:"中查看进度",myPromotionCode:"我的推广码",checkProgress:"检查进度",promoteBtn:"立即推广",filterShortDramas:"筛选短片",priceTitle:"项目价格",projectInfo:"项目信息",integrityCommitmentLetter:"诚信承诺书",readAndagree:"我已阅读并同意",sign:"同意并签字",selectPromotionCode:"选择推广码"},search:{placeholder:"搜索",actionText:"取消"},promotion:{title:"推广码",Total:"总的",Deals:"交易",Income:"收入",formTitle:"填写申请信息",copy:"复制一条",add:"添加一条",selectSettlementMethod:"选择方式",referralCode:"推荐码",PromotionCodeType:"推广码类型",belongedProject:"所属项目",mobilePhoneNumber:"手机号码",keywords:"关键词",applicationTime:"申请时间",kuakeUid:"Kuake UID/ID",mainPlatform:"共享主平台",followers:"关注者/用户数量",dailyOrderVolume:"预计每日订单量",total:"总的",deals:"交易",income:"收入",details:"详情",expand:"扩大",collapse:"崩溃",resubmit:"重新提交",orderRecords:"订单记录"},navTitle:{promotion:"申请推广",promotionDetails:"推广码详细信息",applicationRecords:"申请记录",notifications:"通知",filings:"我的报备",voucher:"提交凭证",media:"媒体库",mediaDetails:"媒体详细信息",income:"收入记录",incomeDetails:"收入明细",orderTitle:"订单历史",settlement:"我的结算账户",record:"历史记录",addSettlementAccount:"添加结算账户",fundDetails:"资金明细"},notice:{all:"所有",notice:"通知",mail:"邮件",projectNotice:"项目通知",platform:"平台",compliance:"遵从"},wallet:{settle:"立即结算",monthlyIncome:"月收入（$）",totalIncome:"总收入（$）",accountBalance:"账户余额（$）",earningsPlatform:"盈利平台",settlementRecords:"结算记录",title:"我的钱包",tip1:"已成功提交正在审核中",tip2:"查看【结算记录】中的进度"},income:{income:"收入",totalEarnings:"总收入",cumulativeGain:"累计收益",timeSlot:"时隙",teamDate:"团队日期",detailTeamTip:"注：不包括间接收入",searchPlaceholder:"按姓名/电话号码搜索"},voucher:{title:"已提交凭证记录"},bankCard:{paymentMethod:"结算方式",remittancePathCode:"汇款路径代码",account:"账户",region:"区域",city:"城市",address:"住址",zipCode:"邮政的",handlingFee:"手续费",accountLength:"请输入有效的帐号",selectSettlementAccount:"选择结算账户"},fund:{taskTitle:"任务标题",type:"类型",orderNo:"订单号",status:"状态",description:"描述",time:"时间",report:"汇报",singularNumber:"单数",settlementAccount:"结算账户",paymentDetails:"付款资料",balance:"平衡",withdrawAll:"全部撤回",withdrawalInstructions:"提款说明：",tip1:"1.请确保付款账户信息填写正确，账户属于您",tip2:"2.提款处理和贷记期为3个工作日。如果进行货币兑换，汇率将根据付款时间实时计算",tip3:"3.Wise平台不收取提款费",tip4:"3.银行卡的提款费由第三方平台收取，我们的平台不收取任何服务费。",Input:"输入"}},TS={"zh-Hans":"zh-Hans",zh:"zh-Hans","zh-Hant":"zh-Hant",cht:"zh-Hant",en:"en"};const SS=function(e={}){const t=!CT(e.legacy)||e.legacy,n=!CT(e.globalInjection)||e.globalInjection,o=new Map,[r,i]=function(e,t){const n=gt(),o=t?n.run(()=>iS(e)):n.run(()=>rS(e));if(null==o)throw WT(VT);return[n,o]}(e,t),s=mT(""),a={get mode(){return t?"legacy":"composition"},async install(e,...o){if(e.__VUE_I18N_SYMBOL__=s,e.provide(e.__VUE_I18N_SYMBOL__,a),IT(o[0])){const e=o[0];a.__composerExtend=e.__composerExtend,a.__vueI18nExtend=e.__vueI18nExtend}let r=null;!t&&n&&(r=function(e,t){const n=Object.create(null);vS.forEach(e=>{const o=Object.getOwnPropertyDescriptor(t,e);if(!o)throw WT(VT);const r=Fn(o.value)?{get:()=>o.value.value,set(e){o.value.value=e}}:{get:()=>o.get&&o.get()};Object.defineProperty(n,e,r)}),e.config.globalProperties.$i18n=n,yS.forEach(n=>{const o=Object.getOwnPropertyDescriptor(t,n);if(!o||!o.value)throw WT(VT);Object.defineProperty(e.config.globalProperties,`$${n}`,o)});const o=()=>{delete e.config.globalProperties.$i18n,yS.forEach(t=>{delete e.config.globalProperties[`$${t}`]})};return o}(e,a.global)),hS(e,a,...o),t&&e.mixin(function(e,t,n){return{beforeCreate(){const o=ns();if(!o)throw WT(VT);const r=this.$options;if(r.i18n){const o=r.i18n;if(r.__i18n&&(o.__i18n=r.__i18n),o.__root=t,this===this.$root)this.$i18n=sS(e,o);else{o.__injectWithOption=!0,o.__extender=n.__vueI18nExtend,this.$i18n=iS(o);const e=this.$i18n;e.__extender&&(e.__disposer=e.__extender(this.$i18n))}}else if(r.__i18n)if(this===this.$root)this.$i18n=sS(e,r);else{this.$i18n=iS({__i18n:r.__i18n,__injectWithOption:!0,__extender:n.__vueI18nExtend,__root:t});const e=this.$i18n;e.__extender&&(e.__disposer=e.__extender(this.$i18n))}else this.$i18n=e;r.__i18nGlobal&&ZT(t,r,r),this.$t=(...e)=>this.$i18n.t(...e),this.$rt=(...e)=>this.$i18n.rt(...e),this.$te=(e,t)=>this.$i18n.te(e,t),this.$d=(...e)=>this.$i18n.d(...e),this.$n=(...e)=>this.$i18n.n(...e),this.$tm=e=>this.$i18n.tm(e),n.__setInstance(o,this.$i18n)},mounted(){},unmounted(){const e=ns();if(!e)throw WT(VT);const t=this.$i18n;delete this.$t,delete this.$rt,delete this.$te,delete this.$d,delete this.$n,delete this.$tm,t.__disposer&&(t.__disposer(),delete t.__disposer,delete t.__extender),n.__deleteInstance(e),delete this.$i18n}}}(i,i.__composer,a));const l=e.unmount;e.unmount=()=>{r&&r(),a.dispose(),l()}},get global(){return i},dispose(){r.stop()},__instances:o,__getInstance:function(e){return o.get(e)||null},__setInstance:function(e,t){o.set(e,t)},__deleteInstance:function(e){o.delete(e)}};return a}({legacy:!1,locale:function(){let e=Cv("userLanguage")||"en";return e=TS[e]||"en",document.documentElement.lang=e,e}(),fallbackLocale:"en",globalInjection:!0,messages:{"zh-CN":xS,"zh-Hant":_S,cht:_S,en:wS,zh:xS}});
/*!
  * pinia v2.0.36
  * (c) 2023 Eduardo San Martin Morote
  * @license MIT
  */let kS;const ES=e=>kS=e,CS=Symbol();function PS(e){return e&&"object"==typeof e&&"[object Object]"===Object.prototype.toString.call(e)&&"function"!=typeof e.toJSON}var LS,OS;(OS=LS||(LS={})).direct="direct",OS.patchObject="patch object",OS.patchFunction="patch function";const IS="undefined"!=typeof window;function AS(){const e=gt(!0),t=e.run(()=>Bn({}));let n=[],o=[];const r=An({install(e){ES(r),r._a=e,e.provide(CS,r),e.config.globalProperties.$pinia=r,o.forEach(e=>n.push(e)),o=[]},use(e){return this._a?n.push(e):o.push(e),this},_p:n,_a:null,_e:e,_s:new Map,state:t});return r}const MS=()=>{};function RS(e,t,n,o=MS){e.push(t);const r=()=>{const n=e.indexOf(t);n>-1&&(e.splice(n,1),o())};var i;return!n&&vt()&&(i=r,pt&&pt.cleanups.push(i)),r}function $S(e,...t){e.slice().forEach(e=>{e(...t)})}function NS(e,t){e instanceof Map&&t instanceof Map&&t.forEach((t,n)=>e.set(n,t)),e instanceof Set&&t instanceof Set&&t.forEach(e.add,e);for(const n in t){if(!t.hasOwnProperty(n))continue;const o=t[n],r=e[n];PS(r)&&PS(o)&&e.hasOwnProperty(n)&&!Fn(o)&&!Cn(o)?e[n]=NS(r,o):e[n]=o}return e}const DS=Symbol();function FS(e){return!PS(e)||!e.hasOwnProperty(DS)}const{assign:BS}=Object;function jS(e){return!(!Fn(e)||!e.effect)}function VS(e,t,n,o){const{state:r,actions:i,getters:s}=t,a=n.state.value[e];let l;return l=WS(e,function(){a||(n.state.value[e]=r?r():{});const t=function(e){const t=m(e)?new Array(e.length):{};for(const n in e)t[n]=Kn(e,n);return t}(n.state.value[e]);return BS(t,i,Object.keys(s||{}).reduce((t,o)=>(t[o]=An(ps(()=>{ES(n);const t=n._s.get(e);return s[o].call(t,t)})),t),{}))},t,n,o,!0),l}function WS(e,t,n={},o,r,i){let s;const a=BS({actions:{}},n),l={deep:!0};let c,u,d,f=An([]),p=An([]);const h=o.state.value[e];let m;function g(t){let n;c=u=!1,"function"==typeof t?(t(o.state.value[e]),n={type:LS.patchFunction,storeId:e,events:d}):(NS(o.state.value[e],t),n={type:LS.patchObject,payload:t,storeId:e,events:d});const r=m=Symbol();uo().then(()=>{m===r&&(c=!0)}),u=!0,$S(f,n,o.state.value[e])}i||h||(o.state.value[e]={}),Bn({});const v=i?function(){const{state:e}=n,t=e?e():{};this.$patch(e=>{BS(e,t)})}:MS;function y(t,n){return function(){ES(o);const r=Array.from(arguments),i=[],s=[];let a;$S(p,{args:r,name:t,store:b,after:function(e){i.push(e)},onError:function(e){s.push(e)}});try{a=n.apply(this&&this.$id===e?this:b,r)}catch(l){throw $S(s,l),l}return a instanceof Promise?a.then(e=>($S(i,e),e)).catch(e=>($S(s,e),Promise.reject(e))):($S(i,a),a)}}const b=Tn({_p:o,$id:e,$onAction:RS.bind(null,p),$patch:g,$reset:v,$subscribe(t,n={}){const r=RS(f,t,n.detached,()=>i()),i=s.run(()=>Vo(()=>o.state.value[e],o=>{("sync"===n.flush?u:c)&&t({storeId:e,type:LS.direct,events:d},o)},BS({},l,n)));return r},$dispose:function(){s.stop(),f=[],p=[],o._s.delete(e)}});o._s.set(e,b);const _=o._e.run(()=>(s=gt(),s.run(()=>t())));for(const w in _){const t=_[w];if(Fn(t)&&!jS(t)||Cn(t))i||(h&&FS(t)&&(Fn(t)?t.value=h[w]:NS(t,h[w])),o.state.value[e][w]=t);else if("function"==typeof t){const e=y(w,t);_[w]=e,a.actions[w]=t}}return BS(b,_),BS(In(b),_),Object.defineProperty(b,"$state",{get:()=>o.state.value[e],set:e=>{g(t=>{BS(t,e)})}}),o._p.forEach(e=>{BS(b,s.run(()=>e({store:b,app:o._a,pinia:o,options:a})))}),h&&i&&n.hydrate&&n.hydrate(b.$state,h),c=!0,u=!0,b}function HS(e,t,n){let o,r;const i="function"==typeof t;function s(e,n){const s=ns();(e=e||s&&si(CS,null))&&ES(e),(e=kS)._s.has(o)||(i?WS(o,t,r,e):VS(o,r,e));return e._s.get(o)}return"string"==typeof e?(o=e,r=i?n:t):(r=e,o=e.id),s.$id=o,s}let zS="Store";function US(e,t){return Array.isArray(t)?t.reduce((t,n)=>(t[n]=function(){return e(this.$pinia)[n]},t),{}):Object.keys(t).reduce((n,o)=>(n[o]=function(){const n=e(this.$pinia),r=t[o];return"function"==typeof r?r.call(this,n):n[r]},n),{})}const qS=US;const YS=Object.freeze(Object.defineProperty({__proto__:null,get MutationType(){return LS},PiniaVuePlugin:function(e){e.mixin({beforeCreate(){const e=this.$options;if(e.pinia){const t=e.pinia;if(!this._provided){const e={};Object.defineProperty(this,"_provided",{get:()=>e,set:t=>Object.assign(e,t)})}this._provided[CS]=t,this.$pinia||(this.$pinia=t),t._a=this,IS&&ES(t)}else!this.$pinia&&e.parent&&e.parent.$pinia&&(this.$pinia=e.parent.$pinia)},destroyed(){delete this._pStores}})},acceptHMRUpdate:function(e,t){return()=>{}},createPinia:AS,defineStore:HS,getActivePinia:()=>ns()&&si(CS)||kS,mapActions:function(e,t){return Array.isArray(t)?t.reduce((t,n)=>(t[n]=function(...t){return e(this.$pinia)[n](...t)},t),{}):Object.keys(t).reduce((n,o)=>(n[o]=function(...n){return e(this.$pinia)[t[o]](...n)},n),{})},mapGetters:qS,mapState:US,mapStores:function(...e){return e.reduce((e,t)=>(e[t.$id+zS]=function(){return t(this.$pinia)},e),{})},mapWritableState:function(e,t){return Array.isArray(t)?t.reduce((t,n)=>(t[n]={get(){return e(this.$pinia)[n]},set(t){return e(this.$pinia)[n]=t}},t),{}):Object.keys(t).reduce((n,o)=>(n[o]={get(){return e(this.$pinia)[t[o]]},set(n){return e(this.$pinia)[t[o]]=n}},n),{})},setActivePinia:ES,setMapStoreSuffix:function(e){zS=e},skipHydrate:function(e){return Object.defineProperty(e,DS,{})},storeToRefs:function(e){{e=In(e);const t={};for(const n in e){const o=e[n];(Fn(o)||Cn(o))&&(t[n]=Gn(e,n))}return t}}},Symbol.toStringTag,{value:"Module"})),XS=/"(?:_|\\u0{2}5[Ff]){2}(?:p|\\u0{2}70)(?:r|\\u0{2}72)(?:o|\\u0{2}6[Ff])(?:t|\\u0{2}74)(?:o|\\u0{2}6[Ff])(?:_|\\u0{2}5[Ff]){2}"\s*:/,GS=/"(?:c|\\u0063)(?:o|\\u006[Ff])(?:n|\\u006[Ee])(?:s|\\u0073)(?:t|\\u0074)(?:r|\\u0072)(?:u|\\u0075)(?:c|\\u0063)(?:t|\\u0074)(?:o|\\u006[Ff])(?:r|\\u0072)"\s*:/,KS=/^\s*["[{]|^\s*-?\d{1,16}(\.\d{1,17})?([Ee][+-]?\d+)?\s*$/;function JS(e,t){if(!("__proto__"===e||"constructor"===e&&t&&"object"==typeof t&&"prototype"in t))return t}function ZS(e,t){if(null==e)return;let n=e;for(let o=0;o<t.length;o++){if(null==n||null==n[t[o]])return;n=n[t[o]]}return n}function QS(e,t,n){if(0===n.length)return t;const o=n[0];return n.length>1&&(t=QS("object"==typeof e&&null!==e&&Object.prototype.hasOwnProperty.call(e,o)?e[o]:Number.isInteger(Number(n[1]))?[]:{},t,Array.prototype.slice.call(n,1))),Number.isInteger(Number(o))&&Array.isArray(e)?e.slice()[o]:Object.assign({},e,{[o]:t})}function ek(e,t){if(null==e||0===t.length)return e;if(1===t.length){if(null==e)return e;if(Number.isInteger(t[0])&&Array.isArray(e))return Array.prototype.slice.call(e,0).splice(t[0],1);const n={};for(const t in e)n[t]=e[t];return delete n[t[0]],n}if(null==e[t[0]]){if(Number.isInteger(t[0])&&Array.isArray(e))return Array.prototype.concat.call([],e);const n={};for(const t in e)n[t]=e[t];return n}return QS(e,ek(e[t[0]],Array.prototype.slice.call(t,1)),[t[0]])}function tk(e,t){return t.map(e=>e.split(".")).map(t=>[t,ZS(e,t)]).filter(e=>void 0!==e[1]).reduce((e,t)=>QS(e,t[1],t[0]),{})}function nk(e,t){return t.map(e=>e.split(".")).reduce((e,t)=>ek(e,t),e)}function ok(e,{storage:t,serializer:n,key:o,debug:r,pick:i,omit:s,beforeHydrate:a,afterHydrate:l},c,u=!0){try{u&&(null==a||a(c));const r=t.getItem(o);if(r){const t=n.deserialize(r),o=i?tk(t,i):t,a=s?nk(o,s):o;e.$patch(a)}u&&(null==l||l(c))}catch(d){}}function rk(e,{storage:t,serializer:n,key:o,debug:r,pick:i,omit:s}){try{const r=i?tk(e,i):e,a=s?nk(r,s):r,l=n.serialize(a);t.setItem(o,l)}catch(a){}}var ik=function(e={}){return function(t){!function(e,t,n){const{pinia:o,store:r,options:{persist:i=n}}=e;if(!i)return;if(!(r.$id in o.state.value)){const e=o._s.get(r.$id.replace("__hot:",""));return void(e&&Promise.resolve().then(()=>e.$persist()))}const s=(Array.isArray(i)?i:!0===i?[{}]:[i]).map(t);r.$hydrate=({runHooks:t=!0}={})=>{s.forEach(n=>{ok(r,n,e,t)})},r.$persist=()=>{s.forEach(e=>{rk(r.$state,e)})},s.forEach(t=>{ok(r,t,e),r.$subscribe((e,n)=>rk(n,t),{detached:!0})})}(t,n=>({key:(e.key?e.key:e=>e)(n.key??t.store.$id),debug:n.debug??e.debug??!1,serializer:n.serializer??e.serializer??{serialize:e=>JSON.stringify(e),deserialize:e=>function(e,t={}){if("string"!=typeof e)return e;if('"'===e[0]&&'"'===e[e.length-1]&&-1===e.indexOf("\\"))return e.slice(1,-1);const n=e.trim();if(n.length<=9)switch(n.toLowerCase()){case"true":return!0;case"false":return!1;case"undefined":return;case"null":return null;case"nan":return Number.NaN;case"infinity":return Number.POSITIVE_INFINITY;case"-infinity":return Number.NEGATIVE_INFINITY}if(!KS.test(e)){if(t.strict)throw new SyntaxError("[destr] Invalid JSON");return e}try{if(XS.test(e)||GS.test(e)){if(t.strict)throw new Error("[destr] Possible prototype pollution");return JSON.parse(e,JS)}return JSON.parse(e)}catch(o){if(t.strict)throw o;return e}}(e)},storage:n.storage??e.storage??window.localStorage,beforeHydrate:n.beforeHydrate,afterHydrate:n.afterHydrate,pick:n.pick,omit:n.omit}),e.auto??!1)}}();const sk=[{label:"English",value:"en",lang:"en"},{label:"繁体中文",value:"cht",lang:"zh-Hant"},{label:"简体中文",value:"zh",lang:"zh-Hans"}],ak=HS("counter",{state:()=>({token:"",languageList:sk,userLanguage:"en",firstLogin:!0,redirectUrl:"",noticeList:[],userInfo:{},appSystem:{}}),actions:{getToken(){return this.token},setToken(e){this.token=e,uf("page:show")},deleteToken(){this.token="",this.userInfo={},Pv("token"),uf("page:show"),NE("/pages/login/index")},getUserLanguage:e=>e,setUserLanguage(e,t){this.userLanguage=e,kv("userLanguage",t),jf(t||"en"),document.documentElement.lang=t||"en",uf("page:show")},getRedirectUrl(){return this.redirectUrl},setRedirectUrl(e){this.redirectUrl=e},getNoticeList(){return this.noticeList},setNoticeList(e){const t=this.noticeList.findIndex(t=>t.xm_id===e.xm_id);-1!==t?this.noticeList[t]=e:this.noticeList.push(e)},clearNoticeList(){this.noticeList=[]},getUserInfo(){return this.userInfo},setUserInfo(e){this.userInfo=e},getAppSystem(){return this.appSystem},setAppSystem(e){this.appSystem=e}},persist:{key:"rtb-store",storage:{getItem:e=>Cv(e),setItem:(e,t)=>kv(e,t)}}}),lk={"zh-Hant":"cht","zh-Hans":"zh-Hans",cht:"cht",en:"en"},ck={"zh-Hans":xS,"zh-Hant":_S,en:wS},uk=(e,t)=>t.split(".").reduce((e,t)=>e&&void 0!==e[t]?e[t]:void 0,e),dk=(e,t)=>{try{const n=Cv("userLanguage")||"en",o=lk[n]||"en",r=ck[o];if(r){const t=uk(r,e);if(t&&"string"==typeof t)return t}if("en"!==o){const t=uk(ck.en,e);if(t&&"string"==typeof t)return t}return t||e}catch(n){return t||e}},fk=["/api/bb/adds","/api/pz/adds","/api/user/check_logoff"];const pk={baseURL:"",timeout:2e4,header:{"Content-Type":"application/json; charset=UTF-8"}};const hk=new class{constructor(e={}){t(this,"config"),t(this,"requestInterceptor"),t(this,"responseInterceptor"),t(this,"errorCache"),this.config={...pk,...e},this.requestInterceptor=this.setupRequestInterceptor(),this.responseInterceptor=this.setupResponseInterceptor(),this.errorCache=new Map}setupRequestInterceptor(){return e=>{const t=ak().getToken(),n=Cv("userLanguage")||"en";return e.header={...e.header,"Accept-Language":n},t&&(e.header={...e.header,Authorization:`Bearer ${t}`}),e}}setupResponseInterceptor(){return(e,t)=>{const{statusCode:n,data:o}=e;if(200!==n)return Ly({title:dk("sys.networkError"),icon:"none",duration:2e3}),Promise.reject(new Error("Network Error"));const r=o;if(1!==r.code){const e=t.url;if(fk.some(t=>null==e?void 0:e.includes(t)))return r;if(this.errorCache.has(r.code)){const e=this.errorCache.get(r.code)||0;return this.errorCache.set(r.code,e+1),Promise.reject(r)}return this.errorCache.set(r.code,1),-1004!==r.code&&-1005!==r.code||(ak().setRedirectUrl(""),ak().setToken(""),ly({url:"/pages/login/index"})),Ly({title:r.msg||dk("sys.requestFailed"),icon:"none",duration:2e3}),setTimeout(()=>{this.errorCache.delete(r.code)},2e3),Promise.reject(r)}return r}}async request(e){if(!(await this.checkNetworkStatus()))return Ly({title:dk("sys.noNetwork"),icon:"none",duration:2e3}),Promise.reject(new Error("No Network Connection"));e={...this.config,...e},e=this.requestInterceptor(e);let t=0;const n=async()=>{var o,r,i;try{const t=await this.sendRequest(e);return this.responseInterceptor(t,e)}catch(s){t++;if(((null==(o=null==s?void 0:s.errMsg)?void 0:o.includes("timeout"))||(null==(r=null==s?void 0:s.errMsg)?void 0:r.includes("fail"))||(null==(i=null==s?void 0:s.errMsg)?void 0:i.includes("network")))&&t<3){const e=1e3*Math.pow(2,t-1);await new Promise(t=>setTimeout(t,e));if(await this.checkNetworkStatus())return n()}return Ly(t>=3?{title:dk("sys.requestTimeout"),icon:"none",duration:3e3}:{title:dk("sys.requestFailed"),icon:"none",duration:2e3}),Promise.reject(s)}};return n()}checkNetworkStatus(){return new Promise(e=>{Tv({success:t=>{e("none"!==t.networkType)},fail:()=>{e(!1)}})})}sendRequest(e){return new Promise((t,n)=>{ey({...e,url:(e.baseURL||this.config.baseURL)+e.url,timeout:e.timeout||this.config.timeout,success:t,fail:n})})}},mk=hk.request.bind(hk);function gk(e){return mk({url:"/api/comm_auth/send_code",method:"POST",data:e})}const vk=e=>{const t=ak().token?"/api/xm/get_list":"/api/xm/get_visitor_list";return mk({url:t,method:"POST",data:e})},yk=e=>mk({url:"/api/xm_nrk/get_list",method:"POST",data:e}),bk=e=>mk({url:"/api/xm_nrk/get_info",method:"POST",data:{id:e}}),_k=e=>{const t={mark:e,sb:VE(),ver:""};return mk({url:"/api/site_ads/get_ads",method:"POST",data:t})},wk=(e,t)=>new Promise(n=>{sy({url:"/api/comm_file/up_file",filePath:e,name:"file",formData:{lx:t},header:{authorization:ak().getToken()?ak().getToken():""},success:e=>{200===e.statusCode&&e.data&&setTimeout(()=>{n(JSON.parse(e.data))},1e3)},fail:e=>{n(e)}})}),xk=e=>mk({url:"/api/user_main/edit_email_step1",method:"POST",data:{code:e}}),Tk=(e,t)=>mk({url:"/api/user_main/edit_email_step2",method:"POST",data:{email:t,code:e}}),Sk=e=>mk({url:"/api/user_main/edit_pwd",method:"POST",data:e}),kk=(e,t)=>{const n=ak().token?"/api/article_xy/get_info":"/api/article_xy/get_visitor_info";return mk({url:n,method:"POST",data:{id:0,mark:t}})},Ek=(e,t)=>mk({url:"/api/article_xy/agree",method:"POST",data:{id:e,mark:t}}),Ck=()=>mk({url:"/api/site_notice/get_tj",method:"POST"}),Pk=e=>mk({url:"/api/user_qian/money_list",method:"POST",data:e}),Lk=e=>mk({url:"/api/user_qian/withdraw_record",method:"POST",data:e}),Ok=e=>mk({url:"/api/user_qian/withdraw_info",method:"POST",data:{id:e}}),Ik=()=>mk({url:"/api/user_qian/get_user_tx_tongji",method:"POST"}),{entries:Ak,setPrototypeOf:Mk,isFrozen:Rk,getPrototypeOf:$k,getOwnPropertyDescriptor:Nk}=Object;let{freeze:Dk,seal:Fk,create:Bk}=Object,{apply:jk,construct:Vk}="undefined"!=typeof Reflect&&Reflect;Dk||(Dk=function(e){return e}),Fk||(Fk=function(e){return e}),jk||(jk=function(e,t,n){return e.apply(t,n)}),Vk||(Vk=function(e,t){return new e(...t)});const Wk=oE(Array.prototype.forEach),Hk=oE(Array.prototype.lastIndexOf),zk=oE(Array.prototype.pop),Uk=oE(Array.prototype.push),qk=oE(Array.prototype.splice),Yk=oE(String.prototype.toLowerCase),Xk=oE(String.prototype.toString),Gk=oE(String.prototype.match),Kk=oE(String.prototype.replace),Jk=oE(String.prototype.indexOf),Zk=oE(String.prototype.trim),Qk=oE(Object.prototype.hasOwnProperty),eE=oE(RegExp.prototype.test),tE=(nE=TypeError,function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return Vk(nE,t)});var nE;function oE(e){return function(t){t instanceof RegExp&&(t.lastIndex=0);for(var n=arguments.length,o=new Array(n>1?n-1:0),r=1;r<n;r++)o[r-1]=arguments[r];return jk(e,t,o)}}function rE(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Yk;Mk&&Mk(e,null);let o=t.length;for(;o--;){let r=t[o];if("string"==typeof r){const e=n(r);e!==r&&(Rk(t)||(t[o]=e),r=e)}e[r]=!0}return e}function iE(e){for(let t=0;t<e.length;t++){Qk(e,t)||(e[t]=null)}return e}function sE(e){const t=Bk(null);for(const[n,o]of Ak(e)){Qk(e,n)&&(Array.isArray(o)?t[n]=iE(o):o&&"object"==typeof o&&o.constructor===Object?t[n]=sE(o):t[n]=o)}return t}function aE(e,t){for(;null!==e;){const n=Nk(e,t);if(n){if(n.get)return oE(n.get);if("function"==typeof n.value)return oE(n.value)}e=$k(e)}return function(){return null}}const lE=Dk(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),cE=Dk(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),uE=Dk(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),dE=Dk(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),fE=Dk(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),pE=Dk(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),hE=Dk(["#text"]),mE=Dk(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","popover","popovertarget","popovertargetaction","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","wrap","xmlns","slot"]),gE=Dk(["accent-height","accumulate","additive","alignment-baseline","amplitude","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","exponent","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","intercept","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","slope","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","tablevalues","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),vE=Dk(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),yE=Dk(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),bE=Fk(/\{\{[\w\W]*|[\w\W]*\}\}/gm),_E=Fk(/<%[\w\W]*|[\w\W]*%>/gm),wE=Fk(/\$\{[\w\W]*/gm),xE=Fk(/^data-[\-\w.\u00B7-\uFFFF]+$/),TE=Fk(/^aria-[\-\w]+$/),SE=Fk(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp|matrix):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),kE=Fk(/^(?:\w+script|data):/i),EE=Fk(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),CE=Fk(/^html$/i),PE=Fk(/^[a-z][.\w]*(-[.\w]+)+$/i);var LE=Object.freeze({__proto__:null,ARIA_ATTR:TE,ATTR_WHITESPACE:EE,CUSTOM_ELEMENT:PE,DATA_ATTR:xE,DOCTYPE_NAME:CE,ERB_EXPR:_E,IS_ALLOWED_URI:SE,IS_SCRIPT_OR_DATA:kE,MUSTACHE_EXPR:bE,TMPLIT_EXPR:wE});const OE=1,IE=3,AE=7,ME=8,RE=9;var $E=function e(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"undefined"==typeof window?null:window;const n=t=>e(t);if(n.version="3.2.6",n.removed=[],!t||!t.document||t.document.nodeType!==RE||!t.Element)return n.isSupported=!1,n;let{document:o}=t;const r=o,i=r.currentScript,{DocumentFragment:s,HTMLTemplateElement:a,Node:l,Element:c,NodeFilter:u,NamedNodeMap:d=t.NamedNodeMap||t.MozNamedAttrMap,HTMLFormElement:f,DOMParser:p,trustedTypes:h}=t,m=c.prototype,g=aE(m,"cloneNode"),v=aE(m,"remove"),y=aE(m,"nextSibling"),b=aE(m,"childNodes"),_=aE(m,"parentNode");if("function"==typeof a){const e=o.createElement("template");e.content&&e.content.ownerDocument&&(o=e.content.ownerDocument)}let w,x="";const{implementation:T,createNodeIterator:S,createDocumentFragment:k,getElementsByTagName:E}=o,{importNode:C}=r;let P={afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]};n.isSupported="function"==typeof Ak&&"function"==typeof _&&T&&void 0!==T.createHTMLDocument;const{MUSTACHE_EXPR:L,ERB_EXPR:O,TMPLIT_EXPR:I,DATA_ATTR:A,ARIA_ATTR:M,IS_SCRIPT_OR_DATA:R,ATTR_WHITESPACE:$,CUSTOM_ELEMENT:N}=LE;let{IS_ALLOWED_URI:D}=LE,F=null;const B=rE({},[...lE,...cE,...uE,...fE,...hE]);let j=null;const V=rE({},[...mE,...gE,...vE,...yE]);let W=Object.seal(Bk(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),H=null,z=null,U=!0,q=!0,Y=!1,X=!0,G=!1,K=!0,J=!1,Z=!1,Q=!1,ee=!1,te=!1,ne=!1,oe=!0,re=!1,ie=!0,se=!1,ae={},le=null;const ce=rE({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let ue=null;const de=rE({},["audio","video","img","source","image","track"]);let fe=null;const pe=rE({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),he="http://www.w3.org/1998/Math/MathML",me="http://www.w3.org/2000/svg",ge="http://www.w3.org/1999/xhtml";let ve=ge,ye=!1,be=null;const _e=rE({},[he,me,ge],Xk);let we=rE({},["mi","mo","mn","ms","mtext"]),xe=rE({},["annotation-xml"]);const Te=rE({},["title","style","font","a","script"]);let Se=null;const ke=["application/xhtml+xml","text/html"];let Ee=null,Ce=null;const Pe=o.createElement("form"),Le=function(e){return e instanceof RegExp||e instanceof Function},Oe=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!Ce||Ce!==e){if(e&&"object"==typeof e||(e={}),e=sE(e),Se=-1===ke.indexOf(e.PARSER_MEDIA_TYPE)?"text/html":e.PARSER_MEDIA_TYPE,Ee="application/xhtml+xml"===Se?Xk:Yk,F=Qk(e,"ALLOWED_TAGS")?rE({},e.ALLOWED_TAGS,Ee):B,j=Qk(e,"ALLOWED_ATTR")?rE({},e.ALLOWED_ATTR,Ee):V,be=Qk(e,"ALLOWED_NAMESPACES")?rE({},e.ALLOWED_NAMESPACES,Xk):_e,fe=Qk(e,"ADD_URI_SAFE_ATTR")?rE(sE(pe),e.ADD_URI_SAFE_ATTR,Ee):pe,ue=Qk(e,"ADD_DATA_URI_TAGS")?rE(sE(de),e.ADD_DATA_URI_TAGS,Ee):de,le=Qk(e,"FORBID_CONTENTS")?rE({},e.FORBID_CONTENTS,Ee):ce,H=Qk(e,"FORBID_TAGS")?rE({},e.FORBID_TAGS,Ee):sE({}),z=Qk(e,"FORBID_ATTR")?rE({},e.FORBID_ATTR,Ee):sE({}),ae=!!Qk(e,"USE_PROFILES")&&e.USE_PROFILES,U=!1!==e.ALLOW_ARIA_ATTR,q=!1!==e.ALLOW_DATA_ATTR,Y=e.ALLOW_UNKNOWN_PROTOCOLS||!1,X=!1!==e.ALLOW_SELF_CLOSE_IN_ATTR,G=e.SAFE_FOR_TEMPLATES||!1,K=!1!==e.SAFE_FOR_XML,J=e.WHOLE_DOCUMENT||!1,ee=e.RETURN_DOM||!1,te=e.RETURN_DOM_FRAGMENT||!1,ne=e.RETURN_TRUSTED_TYPE||!1,Q=e.FORCE_BODY||!1,oe=!1!==e.SANITIZE_DOM,re=e.SANITIZE_NAMED_PROPS||!1,ie=!1!==e.KEEP_CONTENT,se=e.IN_PLACE||!1,D=e.ALLOWED_URI_REGEXP||SE,ve=e.NAMESPACE||ge,we=e.MATHML_TEXT_INTEGRATION_POINTS||we,xe=e.HTML_INTEGRATION_POINTS||xe,W=e.CUSTOM_ELEMENT_HANDLING||{},e.CUSTOM_ELEMENT_HANDLING&&Le(e.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(W.tagNameCheck=e.CUSTOM_ELEMENT_HANDLING.tagNameCheck),e.CUSTOM_ELEMENT_HANDLING&&Le(e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(W.attributeNameCheck=e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),e.CUSTOM_ELEMENT_HANDLING&&"boolean"==typeof e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(W.allowCustomizedBuiltInElements=e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),G&&(q=!1),te&&(ee=!0),ae&&(F=rE({},hE),j=[],!0===ae.html&&(rE(F,lE),rE(j,mE)),!0===ae.svg&&(rE(F,cE),rE(j,gE),rE(j,yE)),!0===ae.svgFilters&&(rE(F,uE),rE(j,gE),rE(j,yE)),!0===ae.mathMl&&(rE(F,fE),rE(j,vE),rE(j,yE))),e.ADD_TAGS&&(F===B&&(F=sE(F)),rE(F,e.ADD_TAGS,Ee)),e.ADD_ATTR&&(j===V&&(j=sE(j)),rE(j,e.ADD_ATTR,Ee)),e.ADD_URI_SAFE_ATTR&&rE(fe,e.ADD_URI_SAFE_ATTR,Ee),e.FORBID_CONTENTS&&(le===ce&&(le=sE(le)),rE(le,e.FORBID_CONTENTS,Ee)),ie&&(F["#text"]=!0),J&&rE(F,["html","head","body"]),F.table&&(rE(F,["tbody"]),delete H.tbody),e.TRUSTED_TYPES_POLICY){if("function"!=typeof e.TRUSTED_TYPES_POLICY.createHTML)throw tE('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if("function"!=typeof e.TRUSTED_TYPES_POLICY.createScriptURL)throw tE('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');w=e.TRUSTED_TYPES_POLICY,x=w.createHTML("")}else void 0===w&&(w=function(e,t){if("object"!=typeof e||"function"!=typeof e.createPolicy)return null;let n=null;const o="data-tt-policy-suffix";t&&t.hasAttribute(o)&&(n=t.getAttribute(o));const r="dompurify"+(n?"#"+n:"");try{return e.createPolicy(r,{createHTML:e=>e,createScriptURL:e=>e})}catch(i){return null}}(h,i)),null!==w&&"string"==typeof x&&(x=w.createHTML(""));Dk&&Dk(e),Ce=e}},Ie=rE({},[...cE,...uE,...dE]),Ae=rE({},[...fE,...pE]),Me=function(e){Uk(n.removed,{element:e});try{_(e).removeChild(e)}catch(t){v(e)}},Re=function(e,t){try{Uk(n.removed,{attribute:t.getAttributeNode(e),from:t})}catch(o){Uk(n.removed,{attribute:null,from:t})}if(t.removeAttribute(e),"is"===e)if(ee||te)try{Me(t)}catch(o){}else try{t.setAttribute(e,"")}catch(o){}},$e=function(e){let t=null,n=null;if(Q)e="<remove></remove>"+e;else{const t=Gk(e,/^[\r\n\t ]+/);n=t&&t[0]}"application/xhtml+xml"===Se&&ve===ge&&(e='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+e+"</body></html>");const r=w?w.createHTML(e):e;if(ve===ge)try{t=(new p).parseFromString(r,Se)}catch(s){}if(!t||!t.documentElement){t=T.createDocument(ve,"template",null);try{t.documentElement.innerHTML=ye?x:r}catch(s){}}const i=t.body||t.documentElement;return e&&n&&i.insertBefore(o.createTextNode(n),i.childNodes[0]||null),ve===ge?E.call(t,J?"html":"body")[0]:J?t.documentElement:i},Ne=function(e){return S.call(e.ownerDocument||e,e,u.SHOW_ELEMENT|u.SHOW_COMMENT|u.SHOW_TEXT|u.SHOW_PROCESSING_INSTRUCTION|u.SHOW_CDATA_SECTION,null)},De=function(e){return e instanceof f&&("string"!=typeof e.nodeName||"string"!=typeof e.textContent||"function"!=typeof e.removeChild||!(e.attributes instanceof d)||"function"!=typeof e.removeAttribute||"function"!=typeof e.setAttribute||"string"!=typeof e.namespaceURI||"function"!=typeof e.insertBefore||"function"!=typeof e.hasChildNodes)},Fe=function(e){return"function"==typeof l&&e instanceof l};function Be(e,t,o){Wk(e,e=>{e.call(n,t,o,Ce)})}const je=function(e){let t=null;if(Be(P.beforeSanitizeElements,e,null),De(e))return Me(e),!0;const o=Ee(e.nodeName);if(Be(P.uponSanitizeElement,e,{tagName:o,allowedTags:F}),K&&e.hasChildNodes()&&!Fe(e.firstElementChild)&&eE(/<[/\w!]/g,e.innerHTML)&&eE(/<[/\w!]/g,e.textContent))return Me(e),!0;if(e.nodeType===AE)return Me(e),!0;if(K&&e.nodeType===ME&&eE(/<[/\w]/g,e.data))return Me(e),!0;if(!F[o]||H[o]){if(!H[o]&&We(o)){if(W.tagNameCheck instanceof RegExp&&eE(W.tagNameCheck,o))return!1;if(W.tagNameCheck instanceof Function&&W.tagNameCheck(o))return!1}if(ie&&!le[o]){const t=_(e)||e.parentNode,n=b(e)||e.childNodes;if(n&&t){for(let o=n.length-1;o>=0;--o){const r=g(n[o],!0);r.__removalCount=(e.__removalCount||0)+1,t.insertBefore(r,y(e))}}}return Me(e),!0}return e instanceof c&&!function(e){let t=_(e);t&&t.tagName||(t={namespaceURI:ve,tagName:"template"});const n=Yk(e.tagName),o=Yk(t.tagName);return!!be[e.namespaceURI]&&(e.namespaceURI===me?t.namespaceURI===ge?"svg"===n:t.namespaceURI===he?"svg"===n&&("annotation-xml"===o||we[o]):Boolean(Ie[n]):e.namespaceURI===he?t.namespaceURI===ge?"math"===n:t.namespaceURI===me?"math"===n&&xe[o]:Boolean(Ae[n]):e.namespaceURI===ge?!(t.namespaceURI===me&&!xe[o])&&!(t.namespaceURI===he&&!we[o])&&!Ae[n]&&(Te[n]||!Ie[n]):!("application/xhtml+xml"!==Se||!be[e.namespaceURI]))}(e)?(Me(e),!0):"noscript"!==o&&"noembed"!==o&&"noframes"!==o||!eE(/<\/no(script|embed|frames)/i,e.innerHTML)?(G&&e.nodeType===IE&&(t=e.textContent,Wk([L,O,I],e=>{t=Kk(t,e," ")}),e.textContent!==t&&(Uk(n.removed,{element:e.cloneNode()}),e.textContent=t)),Be(P.afterSanitizeElements,e,null),!1):(Me(e),!0)},Ve=function(e,t,n){if(oe&&("id"===t||"name"===t)&&(n in o||n in Pe))return!1;if(q&&!z[t]&&eE(A,t));else if(U&&eE(M,t));else if(!j[t]||z[t]){if(!(We(e)&&(W.tagNameCheck instanceof RegExp&&eE(W.tagNameCheck,e)||W.tagNameCheck instanceof Function&&W.tagNameCheck(e))&&(W.attributeNameCheck instanceof RegExp&&eE(W.attributeNameCheck,t)||W.attributeNameCheck instanceof Function&&W.attributeNameCheck(t))||"is"===t&&W.allowCustomizedBuiltInElements&&(W.tagNameCheck instanceof RegExp&&eE(W.tagNameCheck,n)||W.tagNameCheck instanceof Function&&W.tagNameCheck(n))))return!1}else if(fe[t]);else if(eE(D,Kk(n,$,"")));else if("src"!==t&&"xlink:href"!==t&&"href"!==t||"script"===e||0!==Jk(n,"data:")||!ue[e]){if(Y&&!eE(R,Kk(n,$,"")));else if(n)return!1}else;return!0},We=function(e){return"annotation-xml"!==e&&Gk(e,N)},He=function(e){Be(P.beforeSanitizeAttributes,e,null);const{attributes:t}=e;if(!t||De(e))return;const o={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:j,forceKeepAttr:void 0};let r=t.length;for(;r--;){const s=t[r],{name:a,namespaceURI:l,value:c}=s,u=Ee(a),d=c;let f="value"===a?d:Zk(d);if(o.attrName=u,o.attrValue=f,o.keepAttr=!0,o.forceKeepAttr=void 0,Be(P.uponSanitizeAttribute,e,o),f=o.attrValue,!re||"id"!==u&&"name"!==u||(Re(a,e),f="user-content-"+f),K&&eE(/((--!?|])>)|<\/(style|title)/i,f)){Re(a,e);continue}if(o.forceKeepAttr)continue;if(!o.keepAttr){Re(a,e);continue}if(!X&&eE(/\/>/i,f)){Re(a,e);continue}G&&Wk([L,O,I],e=>{f=Kk(f,e," ")});const p=Ee(e.nodeName);if(Ve(p,u,f)){if(w&&"object"==typeof h&&"function"==typeof h.getAttributeType)if(l);else switch(h.getAttributeType(p,u)){case"TrustedHTML":f=w.createHTML(f);break;case"TrustedScriptURL":f=w.createScriptURL(f)}if(f!==d)try{l?e.setAttributeNS(l,a,f):e.setAttribute(a,f),De(e)?Me(e):zk(n.removed)}catch(i){Re(a,e)}}else Re(a,e)}Be(P.afterSanitizeAttributes,e,null)},ze=function e(t){let n=null;const o=Ne(t);for(Be(P.beforeSanitizeShadowDOM,t,null);n=o.nextNode();)Be(P.uponSanitizeShadowNode,n,null),je(n),He(n),n.content instanceof s&&e(n.content);Be(P.afterSanitizeShadowDOM,t,null)};return n.sanitize=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=null,i=null,a=null,c=null;if(ye=!e,ye&&(e="\x3c!--\x3e"),"string"!=typeof e&&!Fe(e)){if("function"!=typeof e.toString)throw tE("toString is not a function");if("string"!=typeof(e=e.toString()))throw tE("dirty is not a string, aborting")}if(!n.isSupported)return e;if(Z||Oe(t),n.removed=[],"string"==typeof e&&(se=!1),se){if(e.nodeName){const t=Ee(e.nodeName);if(!F[t]||H[t])throw tE("root node is forbidden and cannot be sanitized in-place")}}else if(e instanceof l)o=$e("\x3c!----\x3e"),i=o.ownerDocument.importNode(e,!0),i.nodeType===OE&&"BODY"===i.nodeName||"HTML"===i.nodeName?o=i:o.appendChild(i);else{if(!ee&&!G&&!J&&-1===e.indexOf("<"))return w&&ne?w.createHTML(e):e;if(o=$e(e),!o)return ee?null:ne?x:""}o&&Q&&Me(o.firstChild);const u=Ne(se?e:o);for(;a=u.nextNode();)je(a),He(a),a.content instanceof s&&ze(a.content);if(se)return e;if(ee){if(te)for(c=k.call(o.ownerDocument);o.firstChild;)c.appendChild(o.firstChild);else c=o;return(j.shadowroot||j.shadowrootmode)&&(c=C.call(r,c,!0)),c}let d=J?o.outerHTML:o.innerHTML;return J&&F["!doctype"]&&o.ownerDocument&&o.ownerDocument.doctype&&o.ownerDocument.doctype.name&&eE(CE,o.ownerDocument.doctype.name)&&(d="<!DOCTYPE "+o.ownerDocument.doctype.name+">\n"+d),G&&Wk([L,O,I],e=>{d=Kk(d,e," ")}),w&&ne?w.createHTML(d):d},n.setConfig=function(){Oe(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}),Z=!0},n.clearConfig=function(){Ce=null,Z=!1},n.isValidAttribute=function(e,t,n){Ce||Oe({});const o=Ee(e),r=Ee(t);return Ve(o,r,n)},n.addHook=function(e,t){"function"==typeof t&&Uk(P[e],t)},n.removeHook=function(e,t){if(void 0!==t){const n=Hk(P[e],t);return-1===n?void 0:qk(P[e],n,1)[0]}return zk(P[e])},n.removeHooks=function(e){P[e]=[]},n.removeAllHooks=function(){P={afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]}},n}();const NE=(e,t,n)=>{({redirect:()=>Rp({url:e}),reLaunch:()=>Np({url:e}),switchTab:()=>Ap({url:e}),back:()=>{const e=nh();e.length<=1||(n||1)>=e.length?Ap({url:"/pages/index/index"}):ay({delta:n||1})}}[t]||(()=>ly({url:e})))()};const DE=()=>{try{const e=xv(),{windowWidth:t}=e,n=(navigator.userAgent||"").toLowerCase(),o=navigator.userAgentData||{},r=(o.platform||navigator.platform||"").toLowerCase();if(function(e,t,n){return!!/chrome-devtools/.test(t)||!(!/chrome\/[0-9]+/.test(t)||!/safari\/[0-9]+/.test(t)||/(android|mobile|iphone|ipad)/.test(t)||void 0!==window.orientation)||!!(/(mac|win)/.test(n)&&e>=1024)}(t,n,r))return!0;if(function(e,t,n){if(/chrome-devtools|electron|vscode/.test(t))return!0;if(/win/.test(n))return!0;if(/mac/.test(n)&&!/mobile|android|iphone|ipad/.test(t))return!0;if(e>=1440)return!0;if(e>=1024&&(/chrome\/[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+\s+safari\/[0-9]+\.[0-9]+/.test(t)||/firefox\/[0-9]+/.test(t)||/safari\/[0-9]+/.test(t)&&!/mobile/.test(t)))return!0;let o=!1;try{o=window.matchMedia&&window.matchMedia("(pointer: fine)").matches}catch{}return!!(e>=1024&&o)||!!(/linux/.test(n)&&!/android|mobile/.test(t)&&e>=1024)||e>=1200&&!/mobile|android|iphone/.test(t)}(t,n,r))return!0;if(function(e,t,n){var o;return!0===n.mobile||/(android|iphone|ipod|ipad)/.test(t)||/linux/.test((null==(o=navigator.platform)?void 0:o.toLowerCase())||"")&&/mobile|android/.test(t)}(0,n,o)){return!!(t>=1200||/(mac|win)/.test(r)||void 0===window.orientation)}if(/(ipad|macintosh)/.test(n)){const e=navigator.maxTouchPoints||0,o=/chrome-devtools|simulator/.test(n);return!(e>1&&!o&&t<=1366)}if(function(e,t){const n=navigator.maxTouchPoints||0;let o=!1,r=!1;try{o=window.matchMedia&&window.matchMedia("(pointer: coarse)").matches,r=window.matchMedia&&window.matchMedia("(pointer: fine)").matches}catch{}return e>=1024&&(r||!o||n<=1||n<=2&&/(mac|win)/.test(t)||void 0===window.orientation)}(t,r))return!0;if(function(e){const t=navigator.maxTouchPoints||0,n="ontouchstart"in window;let o=!1;try{o=window.matchMedia&&window.matchMedia("(pointer: coarse)").matches}catch{}return o&&n&&t>1&&e<=768&&void 0!==window.orientation}(t))return!1;return t>=1024}catch(e){try{return xv().windowWidth>=768}catch(t){return!0}}},FE=(e,t="none")=>{Ly({title:e,icon:t,duration:2e3,mask:!0})};function BE(e,t){if(t){e&&["/pages/index/index","/pages/team/index","/pages/my/index"].includes(e)?NE(e,"switchTab"):NE(e)}else window.open(e,"_blank")}const jE=e=>{Sv({data:e,success:()=>{Ly({title:dk("sys.copysuccess","复制成功"),icon:"none"})},fail:()=>{Ly({title:dk("sys.copyfail","复制失败"),icon:"none"})}})},VE=()=>"4",WE=()=>{const e=nh(),t=e[e.length-1],n=t.__route__||t.route,o=t.options;let r=`/${n}`;return Object.entries(o).forEach(([e,t],n)=>{0==n&&(r+="?"),r+=`${e}=${t}${n<Object.keys(o).length-1?"&":""}`}),ak().setRedirectUrl(r),r},HE=()=>{const e=ak().getRedirectUrl();if(!e)return NE("/pages/index/index","switchTab"),ak().setRedirectUrl("");return e&&["/pages/index/index","/pages/team/index","/pages/my/index"].includes(e)?(NE(e,"switchTab"),ak().setRedirectUrl("")):(NE(e,"redirect"),ak().setRedirectUrl(""))},zE=e=>{if(!e)return"";if(e.startsWith("http://")||e.startsWith("https://"))return e;return`https://lxzcms.top/${e}`},UE=async()=>{try{const{data:e}=await mk({url:"/api/user_main/get_user_info",method:"POST"});return ak().userInfo=e,e}catch(e){throw e}};function qE(e){return e=e.replace(/&amp;/g,"&").replace(/&lt;/g,"<").replace(/&#60;/g,"<").replace(/&gt;/g,">").replace(/&#62;/g,">").replace(/&quot;/g,'"').replace(/&#34;/g,'"').replace(/&#39;/g,"'").replace(/&#96;/g,"`").replace(/&#x2F;/g,"/").replace(/&#92;/g,"//")}const YE={RICH_TEXT_TAGS:["p","br","strong","em","u","span","div","h1","h2","h3","h4","h5","h6","ul","ol","li","a","img","blockquote","pre","code","table","thead","tbody","tr","td","th","hr","sub","sup","del","ins","mark","video"],RICH_TEXT_ATTR:["href","src","alt","title","class","id","style","target","rel","width","height","rowspan","colspan","align","valign","video","controls","poster","preload","autoplay","muted","loop","playsinline","webkit-playsinline","x5-playsinline","x5-video-player-type","x5-video-player-fullscreen","x5-video-orientation","x5-video-ignore-metadata","webkit-airplay","data-layer-fixed","data-original-layer","data-video-src","data-video-poster","data-video-id","data-src","data-poster","data-id","onclick","will-change","transform","backface-visibility","-webkit-transform","-webkit-backface-visibility","contain"],SAFE_URI_REGEXP:/^(?:(?:https?|ftp|mailto|tel|data):|[^a-z]|[a-z+.-]+(?:[^a-z+.-:]|$))/i};function XE(e,t){if(!e)return"";let n;n=DE()?function(e){return e&&"string"==typeof e?e.replace(/<video([^>]*)>([\s\S]*?)<\/video>/gi,(e,t,n)=>{let o="";const r=t.match(/\s(?:src|data-src)\s*=\s*["']([^"']+)["']/i);if(r&&(o=r[1]),!o&&n){const e=n.match(/<source[^>]*\s(?:src|data-src)\s*=\s*["']([^"']+)["']/i);e&&(o=e[1])}return o?`<Video src="${o}" class="videos" playsinline="isiPhoneShowPlaysinline"\nwebkit-playsinline="isiPhoneShowPlaysinline"\nx5-video-player-type="h5-page"  controls></Video>`:e}):e}(e):function(e){return e&&"string"==typeof e?e.replace(/<video([^>]*)>([\s\S]*?)<\/video>/gi,(e,t,n)=>{const{src:o,poster:r,width:i,height:s}=function(e,t){let n="",o="",r="",i="";const s=e.match(/\s(?:src|data-src)\s*=\s*["']([^"']+)["']/i);s&&(n=s[1]);if(!n&&t){const e=t.match(/<source[^>]*\s(?:src|data-src)\s*=\s*["']([^"']+)["']/i);e&&(n=e[1])}const a=e.match(/\sposter\s*=\s*["']([^"']+)["']/i);a&&(o=a[1]);const l=e.match(/\swidth\s*=\s*["']?([^"'\s>]+)["']?/i);l&&(r=l[1]);const c=e.match(/\sheight\s*=\s*["']?([^"'\s>]+)["']?/i);c&&(i=c[1]);return{src:n,poster:o,width:r,height:i}}(t,n);if(o){const e=function(e,t,n,o){const r=`video_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,i=encodeURIComponent(e),s=t?encodeURIComponent(t):"",a=n||"100%",l=o||"200px",c=`navigateToVideoPage('${i}', '${s}')`;return`\n    <div class="mobile-video-play-button"\n         data-video-id="${r}"\n         data-video-src="${e}"\n         data-video-poster="${t||""}"\n         style="\n           position: relative;\n           width: ${a};\n           height: ${l};\n           background: ${t?`url('${t}')`:"#000"} center/cover;\n           border-radius: 8px;\n           overflow: hidden;\n           cursor: pointer;\n           display: flex;\n           align-items: center;\n           justify-content: center;\n           border: 1px solid #e0e0e0;\n           transition: all 0.3s ease;\n         "\n         onclick="if(window.${c.split("(")[0]}) { window.${c}; } else { console.error('视频导航函数未初始化'); }"\n         onmouseover="this.style.transform='scale(1.02)';"\n         onmouseout="this.style.transform='scale(1)';"\n         ontouchstart="this.style.transform='scale(0.98)';"\n         ontouchend="this.style.transform='scale(1)';">\n\n      \x3c!-- 遮罩层 --\x3e\n      <div style="\n        position: absolute;\n        top: 0;\n        left: 0;\n        right: 0;\n        bottom: 0;\n        background: rgba(0, 0, 0, 0.3);\n        transition: all 0.3s ease;\n      "></div>\n\n      \x3c!-- 播放按钮播放视频的三角形白色 --\x3e\n      <div style="\n        position: relative;\n        border-left: 40px solid #FFF;\n        border-top: 20px solid transparent;\n        border-bottom: 20px solid transparent;\n\n      ">\n        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="30" height="30">\n          <path fill="#000" d="M8 5v14l11-7z"/>\n        </svg>\n      </div>\n\n\n\n\n      \x3c!-- 提示文字 --\x3e\n\n    </div>\n  `}(o,r,i,s);return e}return e}):e}(e);const o={...{ALLOWED_TAGS:YE.RICH_TEXT_TAGS,ALLOWED_ATTR:YE.RICH_TEXT_ATTR,ALLOWED_URI_REGEXP:YE.SAFE_URI_REGEXP,KEEP_CONTENT:!0,RETURN_DOM:!1,RETURN_DOM_FRAGMENT:!1,RETURN_TRUSTED_TYPE:!1},...t};try{const e=$E.sanitize(qE(n),o);return"string"==typeof e?e:String(e)}catch(r){return qE(n)}}var GE=(e=>(e[e.PROJECT_NOTICE=0]="PROJECT_NOTICE",e[e.BILL_NOTICE=1]="BILL_NOTICE",e[e.CHANGE_NOTICE=2]="CHANGE_NOTICE",e))(GE||{});const KE=(e,t,n,o)=>{if(!e||!t||!o)return;let r=o.getNoticeList().find(t=>t.xm_id===e.id);r||(r={xm_id:e.id,userlist:[]});let i=r.userlist.find(e=>e.uid===t);i||(i={uid:t},r.userlist.push(i));const s=(new Date).toISOString();switch(n){case 0:i.notice_content_time=s,i.notice_content=e.notice_content;break;case 1:i.notice_bill_time=e.notice_bill_time||s;break;case 2:i.notice_change_time=e.notice_change_time||s}o.setNoticeList(r)},JE=(e,t,n,o,r)=>{e&&t&&o&&r&&uo(()=>{switch(n){case 0:e.open(t.notice_content,n);break;case 1:case 2:e.open(t,n)}KE(t,o,n,r)})},ZE=(e,t,n,o)=>{const r=((e,t,n)=>{var o;const r=[];if(!e||!t||!n)return r;const i=n.getNoticeList().find(t=>t.xm_id===e.id),s=(null==(o=null==i?void 0:i.userlist)?void 0:o.find(e=>e.uid===t))||{};if(e.notice_content){const t=s.notice_content_time,n=s.notice_content;t&&n===e.notice_content||r.push(0)}if(e.notice_bill_time){const t=s.notice_bill_time,n=e.notice_bill_time;(!t||new Date(n)>new Date(t))&&r.push(1)}if(e.notice_change_time){const t=s.notice_change_time,n=e.notice_change_time;(!t||new Date(n)>new Date(t))&&r.push(2)}return r})(t,n,o);if(r.length>0){const i=[0,1,2];for(const s of i)if(r.includes(s))return JE(e,t,s,n,o),!0}return!1},QE=e=>{const t="ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678";let n="",o="";for(let r=0;r<10;r++)n+=t.charAt(Math.floor(48*Math.random()));for(let r=0;r<10;r++)o+=t.charAt(Math.floor(48*Math.random()));return n+btoa(encodeURIComponent(`${e}kn0cvgz`).replace(/%([0-9A-F]{2})/g,function(e,t){return String.fromCharCode(parseInt(t,16))})).replace(/=/g,"")+o},eC=(e,t={})=>{const{containerWidth:n=150,baseFontSize:o=48,addSymbol:r=!0,currency:i="$",usePreciseCalculation:s=!1}=t,a=String(e);let l=a;r&&!a.startsWith(i)&&(l=`${i}${a}`);const c=s?tC(l,n,o):nC(l);return{formattedAmount:l,scale:c,fontSize:`${Math.round(o*c)}rpx`}},tC=(e,t=200,n=48)=>{if(!e)return 1;const o=e.replace(/[^\d.,]/g,"").length*(.6*n);if(o>t){const e=t/o;return Math.max(.4,Math.min(1,.95*e))}return 1},nC=e=>{if(!e)return 1;const t=e.replace(/[^\d.,]/g,"").length;return t<=3?1:t<=4?.95:t<=5?.9:t<=6?.85:t<=7?.8:t<=8?.75:t<=9?.7:t<=10?.65:t<=11?.6:t<=12?.55:.5};function oC(){document.querySelectorAll("video").forEach((e,t)=>{try{const t=e;t.pause(),t.src="",t.removeAttribute("src"),t.removeAttribute("poster");t.querySelectorAll("source").forEach(e=>{e.src="",e.removeAttribute("src"),e.remove()});try{t.load()}catch(n){}t.parentNode&&t.parentNode.removeChild(t)}catch(n){}});document.querySelectorAll("sq-video").forEach((e,t)=>{try{e.querySelectorAll("video").forEach(e=>{const t=e;try{t.pause(),t.src="",t.load()}catch(n){}}),e.parentNode&&e.parentNode.removeChild(e)}catch(n){}});const e=navigator.userAgent.toLowerCase();if(/iphone|ipad|ipod/.test(e)&&(/qq\//.test(e)||/qqbrowser/.test(e))){document.querySelectorAll('video[style*="position"], video[style*="z-index"], body > video').forEach((e,t)=>{try{const t=e;t.pause(),t.src="",t.load(),t.parentNode&&t.parentNode.removeChild(t)}catch(n){}}),setTimeout(()=>{const e=document.body,t=e.style.display;e.style.display="none",e.offsetHeight,e.style.display=t},100)}}function rC(e,t){if(e)try{const n=decodeURIComponent(e),o=t?decodeURIComponent(t):"",r={video:n};o&&(r.poster=o),ly({url:`/pages/system/video?${Object.keys(r).map(e=>`${e}=${encodeURIComponent(r[e])}`).join("&")}`,success:()=>{},fail:e=>{iC(n,o)}})}catch(n){iC(e,t)}}function iC(e,t){try{const n=`video_modal_${Date.now()}`,o=`\n      <div id="${n}" style="\n        position: fixed;\n        top: 0;\n        left: 0;\n        width: 100%;\n        height: 100%;\n        background: rgba(0, 0, 0, 0.9);\n        z-index: 9999;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        flex-direction: column;\n      ">\n        <div style="\n          position: relative;\n          width: 90%;\n          max-width: 600px;\n          background: #000;\n          border-radius: 8px;\n          overflow: hidden;\n        ">\n          <video\n            src="${e}"\n            ${t?`poster="${t}"`:""}\n            controls\n            autoplay\n            playsinline\n            webkit-playsinline\n            x5-playsinline\n            style="width: 100%; height: auto; display: block;"\n          ></video>\n\n          <button onclick="document.getElementById('${n}').remove()" style="\n            position: absolute;\n            top: 10px;\n            right: 10px;\n            background: rgba(255, 255, 255, 0.8);\n            border: none;\n            border-radius: 50%;\n            width: 32px;\n            height: 32px;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            cursor: pointer;\n            font-size: 18px;\n            font-weight: bold;\n            color: #333;\n          ">&times;</button>\n        </div>\n\n        <div style="\n          margin-top: 20px;\n          color: white;\n          text-align: center;\n          font-size: 14px;\n        ">\n          点击右上角 × 关闭视频\n        </div>\n      </div>\n    `;document.body.insertAdjacentHTML("beforeend",o);const r=document.getElementById(n);r&&r.addEventListener("click",e=>{e.target===r&&r.remove()})}catch(n){alert(`视频播放失败，视频地址：${e}`)}}"undefined"!=typeof window&&(window.globalDestroyAllVideos=oC,window.forceCleanIOSQQVideos=function(){oC(),setTimeout(()=>{const e=document.querySelectorAll("video, sq-video");e.length>0&&e.forEach(e=>{try{if("VIDEO"===e.tagName){const t=e;t.pause(),t.src="",t.load()}e.parentNode&&e.parentNode.removeChild(e)}catch(t){}}),window.webkit&&window.webkit.messageHandlers},200)});const sC=new class{constructor(){t(this,"state",Tn({updateInfo:null,downloadPath:"",isDownloading:!1,isDownloaded:!1,modalVisible:!1}))}async checkUpdate(){try{const e=xv(),t=Cv("app_version")||"1.0.0",n=await this.requestUpdateInfo(t,e);return n.data.hasUpdate?(this.state.updateInfo=n.data,n.data):null}catch(e){return null}}downloadUpdate(){return!this.state.updateInfo||this.state.isDownloading||this.state.isDownloaded?Promise.resolve(!1):(this.state.isDownloading=!0,new Promise((e,t)=>{var n;const o=ry({url:this.state.updateInfo.downloadUrl,success:n=>{200===n.statusCode?(this.state.downloadPath=n.tempFilePath,this.state.isDownloaded=!0,this.state.isDownloading=!1,this.showUpdateModal(),e(!0)):(this.state.isDownloading=!1,t(new Error(`下载失败，状态码: ${n.statusCode}`)))},fail:e=>{this.state.isDownloading=!1,t(e)}});null==(n=o.onProgressUpdate)||n.call(o,e=>{uf("updateDownloadProgress",{progress:e.progress,totalBytesExpectedToWrite:e.totalBytesExpectedToWrite,totalBytesWritten:e.totalBytesWritten})})}))}showUpdateModal(e={}){var t,n,o,r,i;const s={version:(null==(t=this.state.updateInfo)?void 0:t.version)||e.version||"V10.06",description:(null==(n=this.state.updateInfo)?void 0:n.description)||e.content||"本次更新包含多项功能优化",content:e.content||(null==(o=this.state.updateInfo)?void 0:o.description)||"本次更新包含多项功能优化",forceUpdate:(null==(r=this.state.updateInfo)?void 0:r.forceUpdate)||e.forceUpdate||!1,features:(null==(i=this.state.updateInfo)?void 0:i.features)||["增加图片二维码功能","增加好友私信功能","修复了一些已知问题，优化用户体验"],...e};this.showNativeModal(s)}sendEventToSubNVue(e){try{uf("showUpdateModal",e)}catch(t){}setTimeout(()=>{try{uf("showUpdateModal",e)}catch(t){}},500),setTimeout(()=>{try{"undefined"!=typeof window&&(window.updateModalData=e,window.updateModalTimestamp=Date.now());const t=Gg();t&&(t.globalData=t.globalData||{},t.globalData.updateModalData=e,t.globalData.updateModalTimestamp=Date.now())}catch(t){}},1500)}showNativeModal(e){this.showFallbackModal(e)}createNativeModal(e){var t,n;const o=xv(),r=o.screenWidth,i=o.screenHeight,s=Math.min(.85*r,320),a=460,l=(r-s)/2,c=(i-a)/2,u=(null==(t=this.state.updateInfo)?void 0:t.version)||e.version||"V10.06",d=(null==(n=this.state.updateInfo)?void 0:n.features)||["增加图片二维码功能","增加好友私信功能","我的文案很长我的文案很长我的文案很长我的文案很长"],f=new plus.nativeObj.View("updateMask",{top:"0px",left:"0px",width:"100%",height:"100%",backgroundColor:"rgba(0,0,0,0.5)"}),p=new plus.nativeObj.View("updateModal",{top:`${c}px`,left:`${l}px`,width:`${s}px`,height:"460px"});globalThis.updateModalView=p,globalThis.updateMaskView=f,this.drawModalContent(p,s,a,u,d),this.setupModalEvents(p,f,s,a,e),f.show(),p.show(),this.state.modalVisible=!0}drawModalContent(e,t,n,o,r){const i=this.buildDrawArray(t,n,o,r);e.draw(i)}buildDrawArray(e,t,n,o){return[...this.buildBackgroundElements(e),...this.buildHeaderElements(e,n),...this.buildFeatureElements(e,o),...this.buildButtonElements(e,t)]}buildBackgroundElements(e){return[{tag:"rect",id:"mainBg",rectStyles:{color:"#ffffff",radius:"16px"}},{tag:"img",id:"headerImg",src:"/static/image/index/improve.png",position:{top:"0px",left:"0px",width:`${e}px`,height:"160px"}}]}buildHeaderElements(e,t){return[{tag:"font",id:"title",text:"检测到新版本",textStyles:{size:"22px",color:"#181c29",weight:"bold"},position:{top:"180px",left:"24px",width:e-48+"px"}},{tag:"rect",id:"versionBg",rectStyles:{color:"#00b38a",radius:"30px"},position:{top:"220px",left:"24px",width:12*t.length+40+"px",height:"32px"}},{tag:"font",id:"versionText",text:`v${t}`,textStyles:{size:"12px",color:"#ffffff",weight:"bold",align:"center"},position:{top:"230px",left:"24px",width:12*t.length+40+"px"}}]}buildFeatureElements(e,t){return[{tag:"font",id:"feature1",text:`• ${t[0]||"增加图片二维码功能"}`,textStyles:{size:"14px",color:"#666666",lineSpacing:"6px"},position:{top:"280px",left:"24px",width:e-48+"px"}},{tag:"font",id:"feature2",text:`• ${t[1]||"增加好友私信功能"}`,textStyles:{size:"14px",color:"#666666",lineSpacing:"6px"},position:{top:"305px",left:"24px",width:e-48+"px"}},{tag:"font",id:"feature3",text:`• ${t[2]||"修复已知问题，优化用户体验"}`,textStyles:{size:"14px",color:"#666666",lineSpacing:"6px"},position:{top:"330px",left:"24px",width:e-48+"px",height:"40px"}}]}buildButtonElements(e,t){return[{tag:"rect",id:"updateBtn",rectStyles:{color:"#00b38a",radius:"12px"},position:{top:t-120+"px",left:"24px",width:e-48+"px",height:"44px"}},{tag:"font",id:"updateBtnText",text:"立即更新",textStyles:{size:"16px",color:"#ffffff",weight:"bold",align:"center"},position:{top:t-108+"px",left:"0px",width:"100%"}}]}setupModalEvents(e,t,n,o,r){e.addEventListener("click",e=>{const t=e.clientX,i=e.clientY;t>=24&&t<=n-24&&i>=o-120&&i<=o-76&&(this.closeNativeModal(),this.handleUpdateConfirm(r))}),r.forceUpdate||t.addEventListener("click",()=>{var e;this.closeNativeModal(),null==(e=r.onCancel)||e.call(r)})}startAppDownload(e){var t,n;const o=null==(t=this.state.updateInfo)?void 0:t.downloadUrl;if(!o)return;this.showDownloadProgress();const r=ry({url:o,success:e=>{this.hideDownloadProgress(),200===e.statusCode&&this.showInstallConfirm(e.tempFilePath)},fail:e=>{this.hideDownloadProgress(),Ly({title:"下载失败，请重试",icon:"error"})}});null==(n=r.onProgressUpdate)||n.call(r,e=>{this.updateDownloadProgress(e.progress)})}showDownloadProgress(){}updateDownloadProgress(e){}hideDownloadProgress(){}showInstallConfirm(e){yy({title:"",content:"更新成功，确定现在重启吗？",confirmText:"重启",confirmColor:"#00b38a",success:t=>{t.confirm&&this.installAndRestart(e)}})}installAndRestart(e){}closeNativeModal(){}showFallbackModal(e){yy({title:`发现新版本 ${e.version||"V10.06"}`,content:e.content||"本次更新包含多项功能优化",showCancel:!e.forceUpdate,success:t=>{var n,o;t.confirm?null==(n=e.onConfirm)||n.call(e):null==(o=e.onCancel)||o.call(e)}})}hideModal(){this.state.modalVisible=!1,uf("hideUpdateModal")}getDownloadPath(){return this.state.downloadPath}reset(){Object.assign(this.state,{updateInfo:null,downloadPath:"",isDownloading:!1,isDownloaded:!1,modalVisible:!1})}getStatus(){return{hasUpdate:!!this.state.updateInfo,isDownloading:this.state.isDownloading,isDownloaded:this.state.isDownloaded,modalVisible:this.state.modalVisible,updateInfo:this.state.updateInfo}}getReactiveState(){return this.state}requestUpdateInfo(e,t){return new Promise(e=>{setTimeout(()=>{e({data:{hasUpdate:!0,version:"V10.06",description:"本次更新包含多项功能优化",downloadUrl:"https://example.com/update.apk",forceUpdate:!1,packageSize:"15.2MB",features:["增加图片二维码功能","增加好友私信功能","我的文案很长我的文案很长我的文案很长我的文案很长"]}})},1e3)})}handleUpdateConfirm(e){var t;const n=this.state.updateInfo;(null==n?void 0:n.forceUpdate)&&!n.packageSize?this.startAppDownload(e):null==(t=e.onConfirm)||t.call(e)}};function aC(e,t){return"string"==typeof e?t:e}const lC=e=>(t,n=ns())=>{!ls&&xr(e,t,n)},cC=lC(ae),uC=lC(le),dC=lC(ce),fC=lC(fe),pC=lC(he),hC=lC(me),mC=lC(_e),gC=lC(xe),vC=ir({__name:"App",setup(e){const t=ak(),n=Bn(!1);dC(async()=>{await l(),c()}),cC(()=>{uf("onAppShow")}),kr(()=>{n.value=DE(),document.documentElement.setAttribute("data-device",n.value?"pc":"mobile"),ii("isPCDevice",n),u(),d(),f(),o()}),Pr(()=>{r()});const o=()=>{lf("getUpdatePath",i),lf("updateLater",s),lf("retryShowUpdate",a)},r=()=>{cf("getUpdatePath",i),cf("updateLater",s),cf("retryShowUpdate",a)},i=e=>{const t=sC.getDownloadPath();e&&e(t)},s=()=>{sC.hideModal()},a=()=>{const e=sC.getStatus();e.isDownloaded&&!e.modalVisible&&sC.showUpdateModal()},l=async()=>{try{if(!(await sC.checkUpdate()))return}catch(e){}},c=async()=>{try{const{data:e}=await mk({url:"/api/comm_base/get_config",method:"POST"});t.setAppSystem(e||{})}catch(e){}},u=(e="Promote Apps, Earn More")=>{document.title=e},d=()=>{const e=new MutationObserver(e=>{e.forEach(e=>{"childList"===e.type&&"TITLE"===e.target.nodeName&&"Promote Apps, Earn More"!==document.title&&(document.title="Promote Apps, Earn More")})}),t=document.querySelector("title");t&&e.observe(t,{childList:!0}),setInterval(()=>{"Promote Apps, Earn More"!==document.title&&(document.title="Promote Apps, Earn More")},1e3)},f=()=>{lf("onPageShow",()=>{setTimeout(()=>{u()},100)}),lf("onAppShow",()=>{setTimeout(()=>{u()},100)})};return fC(()=>{NE("pages/system/404")}),()=>{}}});Zg(vC,{init:Kg,setup(e){const t=yd(),n=()=>{var n;n=e,Object.keys(Vf).forEach(e=>{Vf[e].forEach(t=>{xr(e,t,n)})});const{onLaunch:o,onShow:r,onPageNotFound:i}=e,s=function({path:e,query:t}){return d(Bh,{path:e,query:t}),d(jh,Bh),d({},Bh)}({path:t.path.slice(1)||__uniRoutes[0].meta.route,query:Qe(t.query)});if(o&&N(o,s),r&&N(r,s),!t.matched.length){const e={notFound:!0,openType:"appLaunch",path:t.path,query:{},scene:1001};Fp(),i&&N(i,e)}};return si(Nl).isReady().then(n),kr(()=>{window.addEventListener("resize",nt(ev,50,{setTimeout:setTimeout,clearTimeout:clearTimeout})),window.addEventListener("message",tv),document.addEventListener("visibilitychange",nv),function(){let e=null;try{e=window.matchMedia("(prefers-color-scheme: dark)")}catch(t){}if(e){let t=e=>{ib.emit(de,{theme:e.matches?"dark":"light"})};e.addEventListener?e.addEventListener("change",t):e.addListener(t)}}()}),t.query},before(e){e.mpType="app";const{setup:t}=e,n=()=>(Ai(),Di(nb));e.setup=(e,o)=>{const r=t&&t(e,o);return y(r)?n:r},e.render=n}}),function(){const e=pa(vC),t=AS();return e.use(SS),e.use(t),t.use(ik),"undefined"!=typeof window&&(window.navigateToVideoPage=rC),{app:e,Pinia:YS}}().app.use(Vg).mount("#app");export{cf as $,$r as A,hg as B,BE as C,Tn as D,gC as E,Ei as F,vk as G,uo as H,lf as I,pC as J,Ck as K,_k as L,Vy as M,Hi as N,zE as O,qE as P,eC as Q,ly as R,gg as S,lv as T,bg as U,pf as V,Hy as W,Ly as X,Oy as Y,Wy as Z,jE as _,aC as a,bk as a$,mk as a0,XE as a1,UE as a2,nh as a3,Bf as a4,Lk as a5,Ik as a6,Fn as a7,fg as a8,qo as a9,GE as aA,ZE as aB,DE as aC,yk as aD,$y as aE,Ff as aF,sf as aG,Rp as aH,Np as aI,Ao as aJ,hC as aK,Jv as aL,kf as aM,cg as aN,Ry as aO,Em as aP,Lv as aQ,hd as aR,cv as aS,Jm as aT,rg as aU,Tg as aV,$v as aW,Qv as aX,Mv as aY,wk as aZ,ob as a_,Ds as aa,ua as ab,sk as ac,ps as ad,FE as ae,gk as af,xk as ag,Tk as ah,QE as ai,Sk as aj,Vo as ak,Mf as al,kr as am,Ok as an,Wr as ao,hs as ap,uf as aq,ay as ar,Ap as as,Pk as at,Cv as au,ns as av,Pr as aw,HE as ax,Ek as ay,KE as az,Ro as b,kk as b0,Ni as c,ir as d,zi as e,rm as f,qi as g,Hn as h,Sg as i,NE as j,ak as k,xv as l,mC as m,cC as n,Ai as o,uC as p,ze as q,Bn as r,Ue as s,G as t,gS as u,Di as v,Eo as w,Yi as x,WE as y,Rr as z};
