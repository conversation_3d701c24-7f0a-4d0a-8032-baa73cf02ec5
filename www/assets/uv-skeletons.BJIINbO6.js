var e,t;import{v as s,o as l,w as a,i as n,c as i,F as o,z as u,q as r,s as y,A as d}from"./index-BBirLt11.js";import{_ as c,m as h,a as p}from"./uv-icon.Dp0oPivN.js";const f=c({name:"uv-skeletons",mixins:[h,p],props:{loading:{type:Boolean,default:!0},skeleton:{type:Array,default:()=>[]},animate:{type:Boolean,default:!0},round:{type:Boolean,default:!1},...null==(t=null==(e=uni.$uv)?void 0:e.props)?void 0:t.skeleton},data:()=>({elements:[],opacity:.5,duration:600}),computed:{animateClass(){return this.animate?"uv-skeleton--animation":"uv-skeleton--static"},roundClass(){return this.round?"uv-skeleton--round":"uv-skeleton--radius"},style(e){return e=>this.$uv.deepMerge({},this.$uv.addStyle(e.style))}},created(){this.init()},methods:{init(){let e=[];if(!this.$uv.test.array(this.skeleton))return this.$uv.error("skeleton参数必须为数组形式，参考文档示例：");this.skeleton.forEach(t=>{const s=this.getElCounts(t);e=e.concat(s)}),this.elements=[...e]},getElCounts(e){let t=[],s=[];if(this.$uv.test.number(e))return t.push({type:"gap",height:e}),t;{const l=e.type?e.type:"line",a=e.num?e.num:1,n=e.style?e.style:{},i=this.$uv.test.array(n),o=e.gap?e.gap:"20rpx",u=e.children?e.children:[];for(let e=0;e<u.length;e++)s[e]={clas:u[e].type.indexOf("avatar")>-1||u[e].type.indexOf("custom")>-1?"":"uv-skeleton__group__sub",elements:this.getElCounts(u[e])};for(let e=0;e<a;e++)o&&e<a&&e>0&&t.push({type:"gap",height:o}),t.push({clas:`uv-skeleton__${l}`,type:l,style:i?n[e]:n,gap:o,children:s});return t}},createAnimation(e=1){if(!this.loading||!this.animate)return;let t=0;const s=this.$refs.skeleton;s.forEach(l=>{animation.transition(l,{styles:{opacity:e},duration:this.duration},()=>{t++,t>=s.length&&setTimeout(()=>{this.createAnimation(e<1?1:this.opacity)},200)})})}}},[["render",function(e,t,c,h,p,f){const m=n;return l(),s(m,{class:"uv-skeleton"},{default:a(()=>[c.loading?(l(),s(m,{key:0},{default:a(()=>[(l(!0),i(o,null,u(p.elements,(t,n)=>(l(),s(m,{key:n},{default:a(()=>["flex"==t.type?(l(),s(m,{key:0,class:"uv-skeleton__group",style:r([f.style(t)])},{default:a(()=>[(l(!0),i(o,null,u(t.children,(t,n)=>(l(),s(m,{class:y([t.clas]),style:r([f.style(t)]),key:n},{default:a(()=>[(l(!0),i(o,null,u(t.elements,(t,n)=>(l(),s(m,{key:n},{default:a(()=>["gap"==t.type?(l(),s(m,{key:0,style:r({height:e.$uv.addUnit(t.height,"rpx")})},null,8,["style"])):(l(),s(m,{key:1,class:y([t.clas,f.roundClass,f.animateClass]),style:r([f.style(t)]),ref_for:!0,ref:"skeleton"},null,8,["class","style"]))]),_:2},1024))),128))]),_:2},1032,["class","style"]))),128))]),_:2},1032,["style"])):"custom"==t.type?(l(),s(m,{key:1,class:y([t.clas,f.animateClass]),style:r([f.style(t)]),ref_for:!0,ref:"skeleton"},null,8,["class","style"])):"gap"==t.type?(l(),s(m,{key:2,style:r({height:e.$uv.addUnit(t.height,"rpx")})},null,8,["style"])):(l(),s(m,{key:3,class:y([t.clas,f.roundClass,f.animateClass]),style:r([f.style(t)]),ref_for:!0,ref:"skeleton"},null,8,["class","style"]))]),_:2},1024))),128))]),_:1})):(l(),s(m,{key:1},{default:a(()=>[d(e.$slots,"default",{},void 0,!0)]),_:3}))]),_:3})}],["__scopeId","data-v-d144c73d"]]);export{f as _};
