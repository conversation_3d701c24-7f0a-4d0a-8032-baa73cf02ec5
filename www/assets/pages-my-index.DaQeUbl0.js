import{_ as e}from"./set-language.BfCwdxCn.js";import{d as t,u as s,k as a,r as l,a as n,b as o,c as r,o as i,e as u,w as c,i as d,g as f,t as m,h as p,F as x,z as b,v as g,x as y,s as _,f as k,j as v,l as w,m as h,n as j,p as C,q as F,a2 as L,y as z,M as E,H as O}from"./index-CIPK2z2P.js";import{b as T,_ as P}from"./uv-icon.UcuauzO0.js";import{L as A,S as I}from"./config.DJhcrVV7.js";import{_ as R}from"./prompt-popup.vue_vue_type_script_setup_true_lang.CHEY8D5W.js";import{_ as q}from"./uv-skeletons.D1UL33yi.js";import"./uv-popup.ewhZSqs9.js";import"./uv-transition.tIadgx1N.js";import"./uv-status-bar.BkslDeIT.js";import"./debounce.Ce2HeGXN.js";import"./uv-parse.wZPhbfSD.js";const H=t({__name:"system",emits:["showLoginPopup"],setup(t,{emit:w}){const h=w,{t:j}=s(),C=a(),F=l(),L=e=>{var t;if(!C.token&&"language"!==e.title)return h("showLoginPopup");switch(e.title){case"message":v("/pages/my/notice/index");break;case"information":v("/pages/my/about/index");break;case"media":v("/pages/my/media/index");break;case"filings":v("/pages/my/filings/index");break;case"fund":v("/pages/my/fund/index");break;case"privacy":v("/pages/my/privacy/index");break;case"order":v("/pages/my/orderHistory/index");break;case"settlement":v("/pages/my/settlement/index");break;case"language":null==(t=F.value)||t.showOpen()}};return(t,s)=>{const a=d,l=k,v=n(o("uv-icon"),T),w=e;return i(),r(x,null,[u(a,{class:"p-32"},{default:c(()=>[u(a,{class:"text-32 leading-44 font-semibold text-blackOne mb-24"},{default:c(()=>[f(m(p(j)("my.access")),1)]),_:1}),u(a,{class:"bg-white px-32 rounded-30 w-686"},{default:c(()=>[(i(!0),r(x,null,b(p(A),(e,t)=>(i(),g(a,{key:e.icon,onClick:t=>L(e)},{default:c(()=>[t<=2?(i(),g(a,{key:0,class:_(["item w-auto py-26 flex items-center justify-between",t<2?"border-b box-border border-[#E8E9EA] border-dashed":"border-b-0"])},{default:c(()=>[u(a,{class:"h-48 flex items-center"},{default:c(()=>[u(l,{class:"w-48 h-48 mr-20",src:e.icon,mode:"aspectFill"},null,8,["src"]),u(a,{class:"text-blackOne text-28"},{default:c(()=>[f(m(p(j)(`my.${e.title}`)),1)]),_:2},1024)]),_:2},1024),u(v,{name:"arrow-right",size:"28rpx",color:"#727A86"})]),_:2},1032,["class"])):y("",!0)]),_:2},1032,["onClick"]))),128))]),_:1}),u(a,{class:"text-32 leading-44 font-semibold text-blackOne mb-24 mt-32"},{default:c(()=>[f(m(p(j)("my.finances")),1)]),_:1}),u(a,{class:"bg-white px-32 rounded-30 w-686"},{default:c(()=>[(i(!0),r(x,null,b(p(A),(e,t)=>(i(),g(a,{key:e.icon,onClick:t=>L(e)},{default:c(()=>[t>2?(i(),g(a,{key:0,class:_(["item w-auto py-26 flex items-center justify-between",t<5?"border-b box-border border-[#E8E9EA] border-dashed":"border-b-0"])},{default:c(()=>[u(a,{class:"h-48 flex items-center"},{default:c(()=>[u(l,{class:"w-48 h-48 mr-20",src:e.icon,mode:"aspectFill"},null,8,["src"]),u(a,{class:"text-blackOne text-28"},{default:c(()=>[f(m(p(j)(`my.${e.title}`)),1)]),_:2},1024)]),_:2},1024),u(v,{name:"arrow-right",size:"28rpx",color:"#727A86"})]),_:2},1032,["class"])):y("",!0)]),_:2},1032,["onClick"]))),128))]),_:1})]),_:1}),u(w,{ref_key:"setLanguageRef",ref:F},null,512)],64)}}}),S=P(t({__name:"user",setup(e,{expose:t}){const b=a(),{t:_}=s(),E=w(),O=l(E.statusBarHeight||0),P=l(),A=l(b.userInfo),I=l(0),q=l(!0),H=e=>{if(b.token)return e?void v(e):void v("/pages/my/info/index");P.value.open()},S=()=>{z(),P.value.close(),v("/pages/login/index","reLaunch")};h(e=>{q.value&&(I.value=e.scrollTop)});return j(()=>{a().token&&(async()=>{const e=await L();A.value=e})(),q.value=!0,A.value=b.userInfo}),C(()=>{q.value=!1}),t({handleGoPath:H}),(e,t)=>{const s=d,a=k,l=n(o("uv-icon"),T),b=R;return i(),r(x,null,[p(I)?(i(),g(s,{key:0,class:"w-750 bg-white fixed top-0 text-40 font-semibold text-blackOne h-88 z-1 px-32 flex items-center",onClick:t[0]||(t[0]=()=>H())},{default:c(()=>{var e;return[f(m((null==(e=p(A))?void 0:e.nickname)||p(_)("my.logintip")),1)]}),_:1})):y("",!0),u(s,{class:"user"},{default:c(()=>[u(s,{class:"status-bar !bg-transparent",style:F({height:p(O)+"px"})},null,8,["style"]),u(s,{class:"user-info pl-32 pr-32 pt-40 flex items-center justify-between",onClick:t[1]||(t[1]=()=>H())},{default:c(()=>[u(s,{class:"flex flex-1"},{default:c(()=>[u(a,{src:"/assets/tx-Ct3ykqzy.png",class:"w-96 h-96 rounded-full",mode:"aspectFill"}),u(s,{class:"ml-24 h-96 flex flex-col justify-center"},{default:c(()=>{var e;return[u(s,{class:"text-[#fff] text-40 font-semibold"},{default:c(()=>{var e;return[f(m((null==(e=p(A))?void 0:e.nickname)||p(_)("my.logintip")),1)]}),_:1}),(null==(e=p(A))?void 0:e.uid)?(i(),g(s,{key:0,class:"text-24 text-[#DAE9FF]"},{default:c(()=>[f(" ID："+m(p(A).uid),1)]),_:1})):y("",!0)]}),_:1})]),_:1}),u(l,{name:"arrow-right",size:"28rpx",color:"#fff"})]),_:1}),u(s,{class:"ml-32 mt-40 flex items-center justify-between p-32 bg-white rounded-30 w-686 box-border card-shadow",onClick:t[2]||(t[2]=e=>H("/pages/my/wallet/index"))},{default:c(()=>[u(s,null,{default:c(()=>[u(s,{class:"text-blackTwo text-24 leading-32 mb-8"},{default:c(()=>[f(m(p(_)("my.balancelance")),1)]),_:1}),u(s,{class:"font-din text-blackOne text-60 leading-68"},{default:c(()=>{var e;return[f(m((null==(e=p(A))?void 0:e.zqian)?p(A).zqian:"***"),1)]}),_:1})]),_:1}),u(s,{class:"w-200 h-72 rounded-102 bg-[#E6F0FF] text-blueOne text-center text-26 font-semibold leading-72"},{default:c(()=>[f(m(p(_)("wallet.settle")),1)]),_:1})]),_:1})]),_:1}),u(b,{ref_key:"warningRef",ref:P,title:p(_)("sys.pleaseLogin"),content:p(_)("sys.loginTip"),showIcon:!1,tipType:"login",iconName:"warning-fill",iconColor:"#FF6B35",showCancel:!1,confirmText:p(_)("sys.goLogin"),onConfirm:S},null,8,["title","content","confirmText"])],64)}}}),[["__scopeId","data-v-0d29fbc5"]]),B=t({__name:"index",setup(e){const{t:t}=s(),a=l(),r=l(!0);const f=()=>{O(()=>{var e;null==(e=a.value)||e.handleGoPath()})};return j(()=>{E({index:0,text:t("tab.home")}),E({index:1,text:t("tab.team")}),E({index:2,text:t("tab.account")}),setTimeout(()=>{r.value=!1},100)}),(e,t)=>{const s=S,l=H,m=d,x=n(o("uv-skeletons"),q);return i(),g(m,{class:"warp"},{default:c(()=>[u(x,{loading:p(r),skeleton:p(I)},{default:c(()=>[u(m,null,{default:c(()=>[u(s,{ref_key:"userRef",ref:a},null,512),u(l,{ref:"systemRef",onShowLoginPopup:f},null,512)]),_:1})]),_:1},8,["loading","skeleton"])]),_:1})}}});export{B as default};
