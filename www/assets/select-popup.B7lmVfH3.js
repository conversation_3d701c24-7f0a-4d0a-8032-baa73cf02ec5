import{_ as e}from"./empty.vue_vue_type_script_setup_true_lang.SETk5GIM.js";import{d as a,u as l,r as s,D as t,n as o,$ as n,I as c,a as u,b as r,c as i,o as p,e as m,w as d,i as f,g as b,t as A,h as g,v,x as _,a8 as y,F as k,z as w,f as x,j as h}from"./index-BBirLt11.js";import{b as C,_ as j}from"./uv-icon.Dp0oPivN.js";import{_ as L}from"./uv-skeletons.BJIINbO6.js";import{_ as I}from"./uv-popup.BGprV-fU.js";import{a as D}from"./bank.DK3OlB0r.js";import{D as F}from"./decimal.B1oHnkff.js";const G=j(a({__name:"select-popup",props:{jump:{type:<PERSON>olean,default:!0}},emits:["select:bank"],setup(a,{expose:j,emit:G}){const K=G,{t:N}=l(),E=a,J=[{type:"line",num:4,style:"width:100%;marginBottom: 20rpx;height: 168rpx;"}],Q=s(),R=s(!0),S=t({page:1,limit:5}),U=s(),q=s(0),z=s([]),M=()=>{if(F(S.page*S.limit).toNumber()>=q.value)return U.value="noMore";U.value="loading",S.page++,B()},O=()=>{h("/pages/my/bankCard/add")},B=async()=>{try{const{data:e,count:a}=await D(S);1===S.page?z.value=e||[]:z.value=z.value.concat(e||[]),q.value=a||0,S.limit*S.page>=q.value?U.value="nomore":U.value="",q.value=a??0}catch(e){}finally{R.value=!1}};return j({open:()=>{B(),Q.value.open()},close:()=>{Q.value.close()}}),o(()=>{n("back:bank"),c("back:bank",()=>{S.page=1,R.value=!0,B()})}),(a,l)=>{const s=f,t=x,o=u(r("uv-icon"),C),n=y,c=e,j=u(r("uv-skeletons"),L),D=u(r("uv-popup"),I);return p(),i("div",null,[m(D,{ref_key:"popup",ref:Q,mode:"bottom",overlayStyle:"background: rgba(0, 0, 0, 0.3);",round:"15",closeable:""},{default:d(()=>[m(s,{class:"w-750 min-h-800 pt-26 pl-32 pr-32 box-border bg-[#F6F7F9]"},{default:d(()=>[m(s,{class:"text-black font-semibold mb-40"},{default:d(()=>[b(A(g(N)("bankCard.selectSettlementAccount")),1)]),_:1}),m(j,{loading:g(R),skeleton:J},{default:d(()=>[g(z).length?(p(),v(n,{key:0,ref:"scrollViewRef","scroll-y":"",class:"h-[40vh] mb-32",onScrolltolower:M},{default:d(()=>[m(s,null,{default:d(()=>[(p(!0),i(k,null,w(g(z),e=>(p(),v(s,{class:"bg-white rounded-30 p-32 mb-20",key:e.id,onClick:a=>(e=>{E.jump?h(`/pages/my/wallet/fund?id=${e.id}`):(K("select:bank",e.id),Q.value.close())})(e)},{default:d(()=>[m(s,{class:"flex justify-between"},{default:d(()=>[m(s,{class:"flex flex-col"},{default:d(()=>[m(s,{class:"flex items-center"},{default:d(()=>[m(t,{class:"w-48 h-48 mr-20",src:e.type_logo,mode:"aspectFill"},null,8,["src"]),m(s,{class:"max-w-426 line-clamp-1 font-semibold text-28 leading-48"},{default:d(()=>[b(A(e.type_name),1)]),_:2},1024)]),_:2},1024),m(s,{class:"mt-28 text-blackOne text-40 leading-40 font-din"},{default:d(()=>[b(A(e.account),1)]),_:2},1024)]),_:2},1024),m(o,{name:"arrow-right",class:"ml-8",size:"28rpx",color:"#727A86"})]),_:2},1024)]),_:2},1032,["onClick"]))),128))]),_:1})]),_:1},512)):_("",!0),g(z).length?_("",!0):(p(),v(c,{key:1,class:"mt-[20%] mb-100"})),m(s,{onClick:O,class:"footerBtn mb-40 text-blueOne bg-white rounded-20 py-30 flex items-center justify-center"},{default:d(()=>[m(t,{class:"w-40 h-40 mr-8",src:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAAAXNSR0IArs4c6QAAAKxJREFUWEftl9EJgDAMRC+bOUydQ51Dh3GzaMEfP2oD10KFC/SvKcfLJaGGwcMG1wcJZCskgiLIEmDz23kw+WKGNQtyx4rDNlZczpdAlqIIimCYgLo4jKpwUQRLBJMvQbiTGaZnk5wA8qlHZeNU56DN9+LqGL7fC/Ij/i8Qo5c4XF11cRiV5uCLQLWLw2DlwTAqeVAeHPTbyXq4kN9uzEhgJwLssyqxCLIE2PwLvPGOKe+0pJ8AAAAASUVORK5CYII=",mode:"scaleToFill"}),b(" "+A(g(N)("sys.addNow")),1)]),_:1})]),_:1},8,["loading"])]),_:1})]),_:1},512)])}}}),[["__scopeId","data-v-8ee9b0b8"]]);export{G as S};
