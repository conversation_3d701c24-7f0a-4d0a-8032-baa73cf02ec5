import{a0 as a}from"./index-BBirLt11.js";const d=d=>a({url:"/api/user_qian/bank_list",method:"POST",data:d}),i=d=>a({url:"/api/user_qian/get_bank_info",method:"POST",data:{id:d}}),t=d=>a({url:"/api/user_qian/add_bank",method:"POST",data:d}),e=d=>a({url:"/api/user_qian/del_bank",method:"POST",data:{id:d}}),r=(d,i)=>a({url:"/api/user_qian/withdraw",method:"POST",data:{txzh_id:d,tx_money:i}});export{d as a,t as b,e as d,i as g,r as w};
