var e,t,n,o;import{v as l,x as i,o as a,w as s,e as r,i as c,U as u,q as d,g as m,t as h,ab as p,s as f,a as v,b as C,aT as y,c as g,F as x,z as _,aU as I}from"./index-CIPK2z2P.js";import{_ as k,m as b,a as $}from"./uv-icon.UcuauzO0.js";import{_ as S}from"./uv-loading-icon.Bi7ZFsTo.js";import{_ as w}from"./uv-popup.ewhZSqs9.js";const T=k({name:"uv-toolbar",emits:["confirm","cancel"],mixins:[b,$,{props:{show:{type:Boolean,default:!0},showBorder:{type:Boolean,default:!1},cancelText:{type:String,default:"取消"},confirmText:{type:String,default:"确认"},cancelColor:{type:String,default:"#909193"},confirmColor:{type:String,default:"#3c9cff"},title:{type:String,default:""},...null==(t=null==(e=uni.$uv)?void 0:e.props)?void 0:t.toolbar}}],methods:{cancel(){this.$emit("cancel")},confirm(){this.$emit("confirm")}}},[["render",function(e,t,n,o,v,C){const y=u,g=c;return e.show?(a(),l(g,{key:0,class:f(["uv-toolbar",{"uv-border-bottom":e.showBorder}]),onTouchmove:p(e.noop,["stop","prevent"])},{default:s(()=>[r(g,{class:"uv-toolbar__cancel__wrapper","hover-class":"uv-hover-class"},{default:s(()=>[r(y,{class:"uv-toolbar__wrapper__cancel",onClick:C.cancel,style:d({color:e.cancelColor})},{default:s(()=>[m(h(e.cancelText),1)]),_:1},8,["onClick","style"])]),_:1}),e.title?(a(),l(y,{key:0,class:"uv-toolbar__title uv-line-1"},{default:s(()=>[m(h(e.title),1)]),_:1})):i("",!0),r(g,{class:"uv-toolbar__confirm__wrapper","hover-class":"uv-hover-class"},{default:s(()=>[r(y,{class:"uv-toolbar__wrapper__confirm",onClick:C.confirm,style:d({color:e.confirmColor})},{default:s(()=>[m(h(e.confirmText),1)]),_:1},8,["onClick","style"])]),_:1})]),_:1},8,["class","onTouchmove"])):i("",!0)}],["__scopeId","data-v-9874ca2e"]]);const B=k({name:"uv-picker",emits:["confirm","cancel","close","change"],mixins:[b,$,{props:{showToolbar:{type:Boolean,default:!0},title:{type:String,default:""},round:{type:[String,Number],default:0},columns:{type:Array,default:()=>[]},loading:{type:Boolean,default:!1},itemHeight:{type:[String,Number],default:44},cancelText:{type:String,default:"取消"},confirmText:{type:String,default:"确定"},cancelColor:{type:String,default:"#909193"},confirmColor:{type:String,default:"#3c9cff"},color:{type:String,default:""},activeColor:{type:String,default:""},visibleItemCount:{type:[String,Number],default:5},keyName:{type:String,default:"text"},closeOnClickOverlay:{type:Boolean,default:!0},closeOnClickConfirm:{type:Boolean,default:!0},defaultIndex:{type:Array,default:()=>[]},immediateChange:{type:Boolean,default:!0},...null==(o=null==(n=uni.$uv)?void 0:n.props)?void 0:o.picker}}],computed:{textStyle(){return(e,t)=>{const n={display:"block"};return this.color&&(n.color=this.color),this.activeColor&&t===this.innerIndex[e]&&(n.color=this.activeColor),n}}},data:()=>({lastIndex:[],innerIndex:[],innerColumns:[],columnIndex:0}),watch:{defaultIndex:{immediate:!0,handler(e){this.setIndexs(e,!0)}},columns:{deep:!0,immediate:!0,handler(e){this.setColumns(e)}}},methods:{open(){this.$refs.pickerPopup.open()},close(){this.$refs.pickerPopup.close()},popupChange(e){e.show||this.$emit("close")},getItemText(e){return this.$uv.test.object(e)?e[this.keyName]:e},cancel(){this.$emit("cancel"),this.close()},confirm(){this.$emit("confirm",this.$uv.deepClone({indexs:this.innerIndex,value:this.innerColumns.map((e,t)=>e[this.innerIndex[t]]),values:this.innerColumns})),this.closeOnClickConfirm&&this.close()},changeHandler(e){const{value:t}=e.detail;let n=0,o=0;for(let i=0;i<t.length;i++){let e=t[i];if(e!==(this.lastIndex[i]||0)){o=i,n=e;break}}this.columnIndex=o;const l=this.innerColumns;this.setLastIndex(t),this.setIndexs(t),this.$emit("change",{value:this.innerColumns.map((e,n)=>e[t[n]]),index:n,indexs:t,values:l,columnIndex:o})},setIndexs(e,t){this.innerIndex=this.$uv.deepClone(e),t&&this.setLastIndex(e)},setLastIndex(e){this.lastIndex=this.$uv.deepClone(e)},setColumnValues(e,t){this.innerColumns.splice(e,1,t);let n=this.$uv.deepClone(this.innerIndex);for(let o=0;o<this.innerColumns.length;o++)o>this.columnIndex&&(n[o]=0);this.setIndexs(n)},getColumnValues(e){return(async()=>{await this.$uv.sleep()})(),this.innerColumns[e]},setColumns(e){this.innerColumns=this.$uv.deepClone(e),0===this.innerIndex.length&&(this.innerIndex=new Array(e.length).fill(0))},getIndexs(){return this.innerIndex},getValues(){return(async()=>{await this.$uv.sleep()})(),this.innerColumns.map((e,t)=>e[this.innerIndex[t]])}}},[["render",function(e,t,n,o,p,f){const k=v(C("uv-toolbar"),T),b=u,$=I,B=y,H=v(C("uv-loading-icon"),S),O=c,U=v(C("uv-popup"),w);return a(),l(U,{ref:"pickerPopup",mode:"bottom",round:e.round,"close-on-click-overlay":e.closeOnClickOverlay,onChange:f.popupChange},{default:s(()=>[r(O,{class:"uv-picker"},{default:s(()=>[e.showToolbar?(a(),l(k,{key:0,cancelColor:e.cancelColor,confirmColor:e.confirmColor,cancelText:e.cancelText,confirmText:e.confirmText,title:e.title,onCancel:f.cancel,onConfirm:f.confirm},null,8,["cancelColor","confirmColor","cancelText","confirmText","title","onCancel","onConfirm"])):i("",!0),r(B,{class:"uv-picker__view",indicatorStyle:`height: ${e.$uv.addUnit(e.itemHeight)}`,value:p.innerIndex,immediateChange:e.immediateChange,style:d({height:`${e.$uv.addUnit(e.visibleItemCount*e.itemHeight)}`}),onChange:f.changeHandler},{default:s(()=>[(a(!0),g(x,null,_(p.innerColumns,(t,n)=>(a(),l($,{key:n,class:"uv-picker__view__column"},{default:s(()=>[e.$uv.test.array(t)?(a(!0),g(x,{key:0},_(t,(t,o)=>(a(),l(b,{class:"uv-picker__view__column__item uv-line-1",key:o,style:d([{height:e.$uv.addUnit(e.itemHeight),lineHeight:e.$uv.addUnit(e.itemHeight),fontWeight:o===p.innerIndex[n]?"bold":"normal"},f.textStyle(n,o)])},{default:s(()=>[m(h(f.getItemText(t)),1)]),_:2},1032,["style"]))),128)):i("",!0)]),_:2},1024))),128))]),_:1},8,["indicatorStyle","value","immediateChange","style","onChange"]),e.loading?(a(),l(O,{key:1,class:"uv-picker--loading"},{default:s(()=>[r(H,{mode:"circle"})]),_:1})):i("",!0)]),_:1})]),_:1},8,["round","close-on-click-overlay","onChange"])}],["__scopeId","data-v-25adab23"]]);export{B as _};
