var t,e;import{_ as a}from"./empty.vue_vue_type_script_setup_true_lang.BXZPGwCi.js";import{d as s,v as i,o as l,w as r,e as n,a9 as o,i as u,g as d,t as p,h as c,P as h,aa as m,A as v,x as _,q as g,s as y,c as f,F as w,z as b,a0 as x,u as k,r as j,D as $,m as S,E as T,n as N,H as W,a as H,b as U,j as C,K as O}from"./index-CIPK2z2P.js";import{_ as A}from"./navbar.vue_vue_type_script_setup_true_lang.CSl8hyKC.js";import{_ as z}from"./uv-tabs.CWR5nOXF.js";import{_ as L}from"./uv-sticky.CXYYNICz.js";import{_ as E}from"./uv-load-more.DsJkRw2i.js";import{_ as B,m as D,a as I}from"./uv-icon.UcuauzO0.js";import{g as R}from"./details.B83uqO-n.js";import{N as M}from"./system.DZjCJFRG.js";import{D as P}from"./decimal.B1oHnkff.js";import"./uv-empty.BH_ZJrMJ.js";import"./uv-status-bar.BkslDeIT.js";import"./debounce.Ce2HeGXN.js";import"./uv-line.CaGHsg1_.js";import"./uv-loading-icon.Bi7ZFsTo.js";const q=s({__name:"notice-item",props:{item:{type:Object,required:!0},code:{type:Number,default:1}},setup:t=>(e,a)=>{const s=u;return l(),i(s,{class:"p-32 bg-white rounded-30 relative"},{default:r(()=>[n(s,{class:"text-blackOne text-28 leading-40 font-semibold"},{default:r(()=>[d(p(c(h)(t.item.title)),1)]),_:1}),n(s,{class:"text-blackTwo text-24 leading-34 my-20 line-clamp-2"},{default:r(()=>[d(p(t.item.nr?c(h)(t.item.nr):c(h)(t.item.ms)),1)]),_:1}),n(s,{class:"text-blackThree leading-34 text-24"},{default:r(()=>[d(p(t.item.tjsj?c(h)(t.item.tjsj):c(h)(t.item.publish_sj)),1)]),_:1}),o(n(s,{class:"absolute right-20 rounded-20 top-20 w-16 h-16 bg-orange rounded-full flex items-center justify-center"},null,512),[[m,!t.item.is_read]])]),_:1})}});const F=B({name:"uv-skeleton",mixins:[D,I,{props:{loading:{type:Boolean,default:!0},animate:{type:Boolean,default:!0},rows:{type:[String,Number],default:0},rowsWidth:{type:[String,Number,Array],default:"100%"},rowsHeight:{type:[String,Number,Array],default:18},rowsLeft:{type:[String,Number,Array],default:0},title:{type:Boolean,default:!0},titleWidth:{type:[String,Number],default:"50%"},titleHeight:{type:[String,Number],default:18},avatar:{type:Boolean,default:!1},avatarSize:{type:[String,Number],default:32},avatarShape:{type:String,default:"circle"},...null==(e=null==(t=uni.$uv)?void 0:t.props)?void 0:e.skeleton}}],data:()=>({width:0}),watch:{loading(){this.getComponentWidth()}},computed:{rowsArray(){/%$/.test(this.rowsHeight)&&this.$uv.error("rowsHeight参数不支持百分比单位");const t=[];for(let e=0;e<this.rows;e++){let a={},s=this.$uv.test.array(this.rowsWidth)?this.rowsWidth[e]||(e===this.row-1?"70%":"100%"):e===this.rows-1?"70%":this.rowsWidth,i=this.$uv.test.array(this.rowsHeight)?this.rowsHeight[e]||"18px":this.rowsHeight,l=this.$uv.test.array(this.rowsLeft)?this.rowsLeft[e]||0:this.rowsLeft;a.marginTop=this.title||0!==e?this.title&&0===e?"20px":"12px":0,/%$/.test(s)?a.width=this.$uv.addUnit(this.width*parseInt(s)/100):a.width=this.$uv.addUnit(s),a.height=this.$uv.addUnit(i),a.marginLeft=this.$uv.addUnit(l),t.push(a)}return t},uTitleWidth(){let t=0;return t=/%$/.test(this.titleWidth)?this.$uv.addUnit(this.width*parseInt(this.titleWidth)/100):this.$uv.addUnit(this.titleWidth),this.$uv.addUnit(t)}},mounted(){this.init()},methods:{init(){this.getComponentWidth()},async setNvueAnimation(){},async getComponentWidth(){await this.$uv.sleep(20),this.$uvGetRect(".uv-skeleton__wrapper__content").then(t=>{this.width=t.width})}}},[["render",function(t,e,a,s,o,d){const p=u;return l(),i(p,{class:"uv-skeleton"},{default:r(()=>[t.loading?(l(),i(p,{key:0,class:"uv-skeleton__wrapper",ref:"uv-skeleton__wrapper",style:{display:"flex","flex-direction":"row"}},{default:r(()=>[t.avatar?(l(),i(p,{key:0,class:y(["uv-skeleton__wrapper__avatar",[`uv-skeleton__wrapper__avatar--${t.avatarShape}`,t.animate&&"animate"]]),style:g({height:t.$uv.addUnit(t.avatarSize),width:t.$uv.addUnit(t.avatarSize)})},null,8,["class","style"])):_("",!0),n(p,{class:"uv-skeleton__wrapper__content",ref:"uv-skeleton__wrapper__content",style:{flex:"1"}},{default:r(()=>[t.title?(l(),i(p,{key:0,class:y(["uv-skeleton__wrapper__content__title",[t.animate&&"animate"]]),style:g({width:d.uTitleWidth,height:t.$uv.addUnit(t.titleHeight)})},null,8,["style","class"])):_("",!0),(l(!0),f(w,null,b(d.rowsArray,(e,a)=>(l(),i(p,{class:y(["uv-skeleton__wrapper__content__rows",[t.animate&&"animate"]]),key:a,style:g({width:e.width,height:e.height,marginTop:e.marginTop,marginLeft:e.marginLeft})},null,8,["class","style"]))),128))]),_:1},512)]),_:1},512)):v(t.$slots,"default",{key:1},void 0,!0)]),_:3})}],["__scopeId","data-v-997a7d7e"]]),J=s({__name:"index",setup(t){const{t:e}=k(),s=j(!0),o=j([]),h=j(!0),m=j(1),v=j("loading"),g=j([]),B=$({type:1,page:1,limit:10}),D=j(0),I=j(0),J=j(0);S(t=>{I.value=t.scrollTop});const K=t=>{t.index&&(t.badge.value=0),o.value=[],B.page=1,D.value=0,s.value=!0,h.value=!t.index,1===t.index?B.type=2:Object.assign(B,JSON.parse(null==t?void 0:t.search_json)),X()},X=async()=>{try{const{data:t,count:e}=await(t=>{const e=2===(null==t?void 0:t.type)?{page:t.page,limit:t.limit}:{page:t.page,limit:t.limit,type:t.type},a=2!==t.type?"/api/site_notice/get_list":"/api/site_notice/get_znx_list";return x({url:a,method:"POST",data:e})})(B);t&&t.length&&(1===B.page?o.value=t:o.value=o.value.concat(t)),J.value=e??0,P(B.page*B.limit).toNumber()>=J.value?v.value="noMore":v.value=""}catch(t){}finally{s.value=!1}};return T(()=>{P(B.page*B.limit).toNumber()<J.value?(v.value="loading",B.page+=1,X()):v.value="noMore"}),N(()=>{W(()=>{(async()=>{g.value=[];try{const{data:t}=await O();null==t||t.forEach((t,e)=>{g.value.push({...t,badge:{value:0==e?t.child[1].count:t.count}})}),m.value=0,X()}catch(t){}})()})}),(t,x)=>{const k=A,j=H(U("uv-tabs"),z),$=u,S=H(U("uv-sticky"),L),T=q,N=H(U("uv-load-more"),E),W=a,O=H(U("uv-skeleton"),F);return l(),f(w,null,[n(k,{"bg-color":"transparent",title:c(e)("navTitle.notifications"),scrollTextColor:"blackOne",isDarkMode:!0},null,8,["title"]),n($,null,{default:r(()=>[n(S,null,{default:r(()=>[n($,{class:y(["px-32",c(I)?"bg-white pb-32  shadow-md":"pb-0"])},{default:r(()=>[n(j,{list:c(g),activeStyle:{color:"#131726",fontWeight:"bold",fontSize:"28rpx"},itemStyle:{height:"72rpx"},lineColor:"#0165ff",inactiveStyle:{color:"#444B56"},current:c(m),onClick:K},null,8,["list","current"]),c(h)&&c(g).length?(l(),i($,{key:0,class:"flex mt-20"},{default:r(()=>[(l(!0),f(w,null,b(c(g)[0].child,(t,e)=>(l(),i($,{class:y(["mr-16 py-14 border px-20 border-box text-26 text-blackTwo rounded-20 border-transparent",{"!border-blueOne !text-blueOne ":e===c(D),"bg-white":c(I)||e===c(D)||!c(I)&&e!==c(D),"!bg-[#f5f5f5]":c(I)&&e!==c(D)}]),key:t.name,onClick:a=>((t,e)=>{e&&(g.value[0].badge.value=0),D.value=e,o.value=[],B.page=1,s.value=!0,Object.assign(B,JSON.parse(t.search_json)),X()})(t,e)},{default:r(()=>[d(p(t.name),1)]),_:2},1032,["class","onClick"]))),128))]),_:1})):_("",!0)]),_:1},8,["class"])]),_:1}),n($,{class:"mt-32 px-32"},{default:r(()=>[n(O,{loading:c(s),rows:"4",rowsHeight:"200rpx"},{default:r(()=>[(l(!0),f(w,null,b(c(o),t=>(l(),i($,{key:t.id,class:"mb-20 last:mb-0"},{default:r(()=>[n(T,{code:t.type,item:t,onClick:e=>(async t=>{switch(t.is_read=1,t.tz_type){case M.RICH_TEXT:case M.TABLE:C(`/pages/my/notice/detail?id=${t.id}&zt=4`);break;case M.FUND:await R(t.id),C(`/pages/details/index?id=${t.xm_id}`);break;case M.THREE_PARTIES:C(`/pages/my/notice/project?id=${t.xm_id}`);break;default:C(`/pages/my/notice/detail?id=${t.id}&code=${t.code}`)}})(t)},null,8,["code","item","onClick"])]),_:2},1024))),128)),c(o).length&&c(B).page>1?(l(),i(N,{key:0,status:c(v),class:"p-32",loadingText:c(e)("sys.loading"),nomoreText:c(e)("sys.noData")},null,8,["status","loadingText","nomoreText"])):_("",!0),c(s)||c(o).length?_("",!0):(l(),i(W,{key:1,class:"mt-[30%]"}))]),_:1},8,["loading"])]),_:1})]),_:1})],64)}}});export{J as default};
