import{_ as a}from"./prompt-popup.vue_vue_type_script_setup_true_lang.DNXGzWqX.js";import{_ as e}from"./submitButton.0smgT9Uf.js";import{_ as t}from"./promotion-item.Byf1_4lC.js";import{_ as s}from"./navbar.vue_vue_type_script_setup_true_lang.DC1OHhyC.js";import{d as l,u as i,r as o,J as u,c as n,e as r,h as d,w as p,a as v,b as c,o as m,v as _,x as f,i as b,g as y,t as j,F as g,z as h,s as k,au as x,ae as w,j as D,aq as I}from"./index-BBirLt11.js";import{_ as T}from"./uv-skeletons.BJIINbO6.js";import{b as M,c as E,s as B,d as z}from"./media.D7Io5yin.js";import{a as S}from"./config.CV-betc9.js";import{_ as A}from"./uv-icon.Dp0oPivN.js";import"./uv-parse.CpsDxc7n.js";import"./uv-popup.BGprV-fU.js";import"./uv-transition.B3tXKydv.js";import"./uv-status-bar.CB0-bOg6.js";import"./empty.vue_vue_type_script_setup_true_lang.SETk5GIM.js";import"./uv-empty.Xz_IG7Bm.js";import"./uv-input.Q1TIxLPl.js";import"./uv-radio-group.CF_hqWfL.js";import"./uv-load-more.SNzw0348.js";import"./uv-line.CNQanRFo.js";import"./uv-loading-icon.G79bokgG.js";import"./debounce.Ce2HeGXN.js";import"./uv-datetime-picker.C3YPVc3n.js";import"./uv-picker.CEmVlaLq.js";import"./uploadImag.CANC8dMl.js";import"./system.DZjCJFRG.js";import"./decimal.B1oHnkff.js";const C=A(l({__name:"add",setup(l){const{t:A}=i(),C=o(),U=o(),q=o(""),R=o(!0),$=o(""),J=o([]),O=o(""),P=o(""),F=o(""),K=(a,e)=>{const t=J.value[0];if(e||(J.value=[t]),J.value.splice(0,J.value.length),J.value.push(t),a&&a.length){const e=a.map(a=>({...a}));J.value.push(...e)}else J.value=[t]},L=async()=>{var a;try{const e={id:$.value,lang:x("userLanguage")||"en",media_data:{}};for(const a of J.value)if("1"===a.is_required&&!a.value)throw w(A("sys.pleaseEnter")+a.name),new Error("validation_failed");for(const a of J.value)e.media_data[a.bs]=a.value||"";const{data:t}=await B(e);D((null==(a=U.value)?void 0:a.xm_id)?`/pages/my/media/tips?id=${t}&index=${U.value.index}&bs=${U.value.bs}`:"/pages/my/media/tips")}catch(e){}},N=()=>{q.value=A("sys.deleteTip1")+O.value+A("sys.deleteTip2"),C.value.open()},Q=async()=>{try{if(!$.value)return;await z($.value),I("back-media",!0),D("","back",1)}catch(a){}},V=async()=>{var a;try{const{data:e}=await E($.value);if(!(null==e?void 0:e.post_data))return;P.value=e.u_bz,F.value=e.zt;const t=e.post_data.find(a=>"BS_MEDIA_ID"===a.bs);if(!(null==t?void 0:t.value))return;const s=J.value.find(a=>"BS_MEDIA_ID"===a.bs),l=null==s?void 0:s.child.find(a=>a.id===t.value);(null==l?void 0:l.child)&&l.child.length>0&&J.value.push(...l.child),e.post_data.forEach(a=>{const e=J.value.find(e=>e.bs===a.bs);e&&(e.value=a.value)}),O.value=(null==(a=e.post_data.find(a=>"BS_MEDIA_ID"===a.bs))?void 0:a.value_display)||"",R.value=!1}catch(e){R.value=!1}};return u(a=>{a&&a.id&&($.value=a.id),a&&a.xm_id&&(U.value=a),(async a=>{try{const{data:e}=await M(a);if((null==e?void 0:e.zd_list)&&Object.assign(J.value,e.zd_list),!$.value)return R.value=!1;V()}catch(e){R.value=!1}})((null==a?void 0:a.rules)?a.rules:"")}),(l,i)=>{const o=s,u=b,x=t,w=e,D=v(c("uv-skeletons"),T),I=a;return m(),n("div",null,[r(o,{"bg-color":"transparent",title:d($)?d(A)("navTitle.mediaDetails"):d(A)("sys.addMedia"),scrollTextColor:"blackOne",isDarkMode:!0,"right-icon":d($)?"dalajitong":"",onRightClick:N},null,8,["title","right-icon"]),r(D,{loading:d(R),skeleton:d(S)},{default:p(()=>[r(u,{class:"p-32 env-bottom"},{default:p(()=>[d($)&&d(O)?(m(),_(u,{key:0,class:""},{default:p(()=>[r(u,{class:"text-32 leading-44 font-semibold mb-24"},{default:p(()=>[y(j(d(O)),1)]),_:1})]),_:1})):f("",!0),d(P)?(m(),_(u,{key:1,class:"mb-24 text-orange text-24 leading-34"},{default:p(()=>[y(j(d(P)),1)]),_:1})):f("",!0),r(u,{class:"w-full bg-white p-32 pb-8 rounded-30"},{default:p(()=>{return[(m(!0),n(g,null,h((a=d(J),$.value?a.filter(a=>"BS_MEDIA_ID"!==a.bs):a),a=>(m(),_(u,{class:"",key:a.id},{default:p(()=>[r(x,{value:a.value,"onUpdate:value":e=>a.value=e,obj:a,fromTheMedia:"myMedia","onUpdate:list":K},null,8,["value","onUpdate:value","obj"])]),_:2},1024))),128))];var a}),_:1})]),_:1}),"2"!==d(F)?(m(),_(w,{key:0,title:d($)?"sys.modify":"sys.save",onBack:L,class:k(d($)?"submitbutton":"1233"),bg:"bg-transparent"},null,8,["title","class"])):f("",!0)]),_:1},8,["loading","skeleton"]),r(I,{ref_key:"promptPopupRef",ref:C,title:d(A)("sys.deleteTip"),confirmType:"outline",content:d(q),confirmText:d(A)("sys.delete"),"cancel-text":d(A)("sys.return"),showCancel:!0,onConfirm:Q},null,8,["title","content","confirmText","cancel-text"])])}}}),[["__scopeId","data-v-a2ed8473"]]);export{C as default};
