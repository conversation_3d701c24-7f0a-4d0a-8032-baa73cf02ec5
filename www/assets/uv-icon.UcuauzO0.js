var e,t;import{aG as n,l as i,X as o,a3 as r,R as c,aH as u,as as a,aI as l,ar as s,aq as f,al as p,v,o as d,w as h,x as m,f as g,q as y,U as b,s as $,g as w,t as S,i as k}from"./index-CIPK2z2P.js";const x={};function j(e){return!!e&&(A(e)&&(e=+e),!/Invalid|NaN/.test(new Date(e).toString()))}function A(e){return/^[\+-]?(\d+\.?\d*|\.\d+|\d\.\d+e\+\d+)$/.test(e)}function O(e){switch(typeof e){case"undefined":return!0;case"string":if(0==e.replace(/(^[ \t\n\r]*)|([ \t\n\r]*$)/g,"").length)return!0;break;case"boolean":if(!e)return!0;break;case"number":if(0===e||isNaN(e))return!0;break;case"object":if(null===e||0===e.length)return!0;for(const t in e)return!1;return!0}return!1}function P(e){return"function"==typeof Array.isArray?Array.isArray(e):"[object Array]"===Object.prototype.toString.call(e)}function M(e){return"[object Object]"===Object.prototype.toString.call(e)}function N(e){return"function"==typeof e}const _=Object.freeze(Object.defineProperty({__proto__:null,amount:function(e){return/^[1-9]\d*(,\d{3})*(\.\d{1,2})?$|^0\.\d{1,2}$/.test(e)},array:P,carNo:function(e){const t=/^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}(([0-9]{5}[DF]$)|([DF][A-HJ-NP-Z0-9][0-9]{4}$))/,n=/^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳]{1}$/;return 7===e.length?n.test(e):8===e.length&&t.test(e)},chinese:function(e){return/^[\u4e00-\u9fa5]+$/gi.test(e)},code:function(e,t=6){return new RegExp(`^\\d{${t}}$`).test(e)},contains:function(e,t){return e.indexOf(t)>=0},date:j,dateISO:function(e){return/^\d{4}[\/\-](0?[1-9]|1[012])[\/\-](0?[1-9]|[12][0-9]|3[01])$/.test(e)},digits:function(e){return/^\d+$/.test(e)},email:function(e){return/^\w+((-\w+)|(\.\w+))*\@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*\.[A-Za-z0-9]+$/.test(e)},empty:O,enOrNum:function(e){return/^[0-9a-zA-Z]*$/g.test(e)},func:N,idCard:function(e){return/^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}([0-9]|X)$/.test(e)},image:function(e){const t=e.split("?")[0];return/\.(jpeg|jpg|gif|png|svg|webp|jfif|bmp|dpg)/i.test(t)},jsonString:function(e){if("string"==typeof e)try{const t=JSON.parse(e);return!("object"!=typeof t||!t)}catch(t){return!1}return!1},landline:function(e){return/^\d{3,4}-\d{7,8}(-\d{3,4})?$/.test(e)},letter:function(e){return/^[a-zA-Z]*$/.test(e)},mobile:function(e){return/^1([3589]\d|4[5-9]|6[1-2,4-7]|7[0-8])\d{8}$/.test(e)},number:A,object:M,promise:function(e){return M(e)&&N(e.then)&&N(e.catch)},range:function(e,t){return e>=t[0]&&e<=t[1]},rangeLength:function(e,t){return e.length>=t[0]&&e.length<=t[1]},regExp:function(e){return e&&"[object RegExp]"===Object.prototype.toString.call(e)},string:function(e){return"string"==typeof e},url:function(e){return/^((https|http|ftp|rtsp|mms):\/\/)(([0-9a-zA-Z_!~*'().&=+$%-]+: )?[0-9a-zA-Z_!~*'().&=+$%-]+@)?(([0-9]{1,3}.){3}[0-9]{1,3}|([0-9a-zA-Z_!~*'()-]+.)*([0-9a-zA-Z][0-9a-zA-Z-]{0,61})?[0-9a-zA-Z].[a-zA-Z]{2,6})(:[0-9]{1,4})?((\/?)|(\/[0-9a-zA-Z_!~*'().;?:@&=+$,%#-]+)+\/?)$/.test(e)},video:function(e){return/\.(mp4|mpg|mpeg|dat|asf|avi|rm|rmvb|mov|wmv|flv|mkv|m3u8)/i.test(e)}},Symbol.toStringTag,{value:"Module"}));function T(e,t=15){return+parseFloat(Number(e).toPrecision(t))}function z(e){const t=e.toString().split(/[eE]/),n=(t[0].split(".")[1]||"").length-+(t[1]||0);return n>0?n:0}function D(e){if(-1===e.toString().indexOf("e"))return Number(e.toString().replace(".",""));const t=z(e);return t>0?T(Number(e)*Math.pow(10,t)):Number(e)}function E(e){e>Number.MAX_SAFE_INTEGER||Number.MIN_SAFE_INTEGER}function I(e,t){const[n,i,...o]=e;let r=t(n,i);return o.forEach(e=>{r=t(r,e)}),r}function Z(...e){if(e.length>2)return I(e,Z);const[t,n]=e,i=D(t),o=D(n),r=z(t)+z(n),c=i*o;return E(c),c/Math.pow(10,r)}function R(...e){if(e.length>2)return I(e,R);const[t,n]=e,i=D(t),o=D(n);return E(i),E(o),Z(i/o,T(Math.pow(10,z(n)-z(t))))}function C(e=void 0){let t=this.$parent;for(;t;){if(!t.$options||t.$options.name===e)return t;t=t.$parent}return!1}function U(e,t=new WeakMap){if(null===e||"object"!=typeof e)return e;if(t.has(e))return t.get(e);let n;if(e instanceof Date)n=new Date(e.getTime());else if(e instanceof RegExp)n=new RegExp(e);else if(e instanceof Map)n=new Map(Array.from(e,([e,n])=>[e,U(n,t)]));else if(e instanceof Set)n=new Set(Array.from(e,e=>U(e,t)));else if(Array.isArray(e))n=e.map(e=>U(e,t));else if("[object Object]"===Object.prototype.toString.call(e)){n=Object.create(Object.getPrototypeOf(e)),t.set(e,n);for(const[i,o]of Object.entries(e))n[i]=U(o,t)}else n=Object.assign({},e);return t.set(e,n),n}function F(e={},t={}){if("object"!=typeof(e=U(e))||null===e||"object"!=typeof t||null===t)return e;const n=Array.isArray(e)?e.slice():Object.assign({},e);for(const i in t){if(!t.hasOwnProperty(i))continue;const e=t[i],o=n[i];e instanceof Date?n[i]=new Date(e):e instanceof RegExp?n[i]=new RegExp(e):e instanceof Map?n[i]=new Map(e):e instanceof Set?n[i]=new Set(e):n[i]="object"==typeof e&&null!==e?F(o,e):e}return n}function q(e){}function H(e=null,t="yyyy-mm-dd"){let n;n=e?/^\d{10}$/.test(null==e?void 0:e.toString().trim())?new Date(1e3*e):"string"==typeof e&&/^\d+$/.test(e.trim())?new Date(Number(e)):"string"==typeof e&&e.includes("-")&&!e.includes("T")?new Date(e.replace(/-/g,"/")):new Date(e):new Date;const i={y:n.getFullYear().toString(),m:(n.getMonth()+1).toString().padStart(2,"0"),d:n.getDate().toString().padStart(2,"0"),h:n.getHours().toString().padStart(2,"0"),M:n.getMinutes().toString().padStart(2,"0"),s:n.getSeconds().toString().padStart(2,"0")};for(const o in i){const[e]=new RegExp(`${o}+`).exec(t)||[];if(e){const n="y"===o&&2===e.length?2:0;t=t.replace(e,i[o].slice(n))}}return t}function B(e,t="both"){return e=String(e),"both"==t?e.replace(/^\s+|\s+$/g,""):"left"==t?e.replace(/^\s*/,""):"right"==t?e.replace(/(\s*$)/g,""):"all"==t?e.replace(/\s+/g,""):e}function G(e={},t=!0,n="brackets"){const i=t?"?":"",o=[];-1==["indices","brackets","repeat","comma"].indexOf(n)&&(n="brackets");for(const r in e){const t=e[r];if(!(["",void 0,null].indexOf(t)>=0))if(t.constructor===Array)switch(n){case"indices":for(let n=0;n<t.length;n++)o.push(`${r}[${n}]=${t[n]}`);break;case"brackets":default:t.forEach(e=>{o.push(`${r}[]=${e}`)});break;case"repeat":t.forEach(e=>{o.push(`${r}=${e}`)});break;case"comma":let e="";t.forEach(t=>{e+=(e?",":"")+t}),o.push(`${r}=${e}`)}else o.push(`${r}=${t}`)}return o.length?i+o.join("&"):""}function L(e,t=0,n=".",i=","){e=`${e}`.replace(/[^0-9+-Ee.]/g,"");const o=isFinite(+e)?+e:0,r=isFinite(+t)?Math.abs(t):0,c=void 0===i?",":i,u=void 0===n?".":n;let a="";a=(r?function(e,t){const n=Math.pow(10,t);let i=R(Math.round(Math.abs(Z(e,n))),n);return e<0&&0!==i&&(i=Z(i,-1)),i}(o,r)+"":`${Math.round(o)}`).split(".");const l=/(-?\d+)(\d{3})/;for(;l.test(a[0]);)a[0]=a[0].replace(l,`$1${c}$2`);return(a[1]||"").length<r&&(a[1]=a[1]||"",a[1]+=new Array(r-a[1].length+1).join("0")),a.join(u)}function J(){var e;const t=r(),n=null==(e=t[t.length-1])?void 0:e.route;return`/${n||""}`}String.prototype.padStart||(String.prototype.padStart=function(e,t=" "){if("[object String]"!==Object.prototype.toString.call(t))throw new TypeError("fillString must be String");const n=this;if(n.length>=e)return String(n);const i=e-n.length;let o=Math.ceil(i/t.length);for(;o>>=1;)t+=t,1===o&&(t+=t);return t.slice(0,i)+n});const X=Object.freeze(Object.defineProperty({__proto__:null,$parent:C,addStyle:function(e,t="object"){if(O(e)||"object"==typeof e&&"object"===t||"string"===t&&"string"==typeof e)return e;if("object"===t){const t=(e=B(e)).split(";"),n={};for(let e=0;e<t.length;e++)if(t[e]){const i=t[e].split(":");n[B(i[0])]=B(i[1])}return n}let n="";for(const i in e){n+=`${i.replace(/([A-Z])/g,"-$1").toLowerCase()}:${e[i]};`}return B(n)},addUnit:function(e="auto",t=((e=>{return null==(e=null==(t=null==uni?void 0:uni.$uv)?void 0:t.config)?void 0:e.unit;var t})()?(e=>{return null==(e=null==(t=null==uni?void 0:uni.$uv)?void 0:t.config)?void 0:e.unit;var t})():"px")){return A(e=String(e))?`${e}${t}`:e},deepClone:U,deepMerge:F,error:q,formValidate:function(e,t){const n=C.call(e,"uv-form-item"),i=C.call(e,"uv-form");n&&i&&i.validateField(n.prop,()=>{},t)},getDuration:function(e,t=!0){const n=parseInt(e);return t?/s$/.test(e)?e:e>30?`${e}ms`:`${e}s`:/ms$/.test(e)?n:/s$/.test(e)?n>30?n:1e3*n:n},getHistoryPage:function(e=0){const t=r();return t[t.length-1+e]},getProperty:function(e,t){if(e){if("string"!=typeof t||""===t)return"";if(-1!==t.indexOf(".")){const n=t.split(".");let i=e[n[0]]||{};for(let e=1;e<n.length;e++)i&&(i=i[n[e]]);return i}return e[t]}},getPx:function(e,t=!1){return A(e)?t?`${e}px`:Number(e):/(rpx|upx)$/.test(e)?t?`${n(parseInt(e))}px`:Number(n(parseInt(e))):t?`${parseInt(e)}px`:parseInt(e)},guid:function(e=32,t=!0,n=null){const i="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz".split(""),o=[];if(n=n||i.length,e)for(let r=0;r<e;r++)o[r]=i[0|Math.random()*n];else{let e;o[8]=o[13]=o[18]=o[23]="-",o[14]="4";for(let t=0;t<36;t++)o[t]||(e=0|16*Math.random(),o[t]=i[19==t?3&e|8:e])}return t?(o.shift(),`u${o.join("")}`):o.join("")},os:function(){return i().platform.toLowerCase()},padZero:function(e){return`00${e}`.slice(-2)},page:J,pages:function(){return r()},priceFormat:L,queryParams:G,random:function(e,t){if(e>=0&&t>0&&t>=e){const n=t-e+1;return Math.floor(Math.random()*n+e)}return 0},randomArray:function(e=[]){return e.sort(()=>Math.random()-.5)},range:function(e=0,t=0,n=0){return Math.max(e,Math.min(t,Number(n)))},setConfig:function({props:e={},config:t={},color:n={},zIndex:i={}}){const{deepMerge:o}=uni.$uv;uni.$uv.config=o(uni.$uv.config,t),uni.$uv.props=o(uni.$uv.props,e),uni.$uv.color=o(uni.$uv.color,n),uni.$uv.zIndex=o(uni.$uv.zIndex,i)},setProperty:function(e,t,n){if(!e)return;const i=function(e,t,n){if(1!==t.length)for(;t.length>1;){const o=t[0];e[o]&&"object"==typeof e[o]||(e[o]={}),t.shift(),i(e[o],t,n)}else e[t[0]]=n};if("string"!=typeof t||""===t);else if(-1!==t.indexOf(".")){const o=t.split(".");i(e,o,n)}else e[t]=n},sleep:function(e=30){return new Promise(t=>{setTimeout(()=>{t()},e)})},sys:function(){return i()},timeFormat:H,timeFrom:function(e=null,t="yyyy-mm-dd"){null==e&&(e=Number(new Date)),10==(e=parseInt(e)).toString().length&&(e*=1e3);let n=(new Date).getTime()-e;n=parseInt(n/1e3);let i="";switch(!0){case n<300:i="刚刚";break;case n>=300&&n<3600:i=`${parseInt(n/60)}分钟前`;break;case n>=3600&&n<86400:i=`${parseInt(n/3600)}小时前`;break;case n>=86400&&n<2592e3:i=`${parseInt(n/86400)}天前`;break;default:i=!1===t?n>=2592e3&&n<31536e3?`${parseInt(n/2592e3)}个月前`:`${parseInt(n/31536e3)}年前`:H(e,t)}return i},toast:function(e,t=2e3){o({title:String(e),icon:"none",duration:t})},trim:B,type2icon:function(e="success",t=!1){-1==["primary","info","error","warning","success"].indexOf(e)&&(e="success");let n="";switch(e){case"primary":case"info":n="info-circle";break;case"error":n="close-circle";break;case"warning":n="error-circle";break;default:n="checkmark-circle"}return t&&(n+="-fill"),n}},Symbol.toStringTag,{value:"Module"}));const W=(new class{constructor(){this.config={type:"navigateTo",url:"",delta:1,params:{},animationType:"pop-in",animationDuration:300,intercept:!1,events:{}},this.route=this.route.bind(this)}addRootPath(e){return"/"===e[0]?e:`/${e}`}mixinParam(e,t){e=e&&this.addRootPath(e);let n="";return/.*\/.*\?.*=.*/.test(e)?(n=G(t,!1),e+`&${n}`):(n=G(t),e+n)}async route(e={},t={}){let n={};if("string"==typeof e?(n.url=this.mixinParam(e,t),n.type="navigateTo"):(n=F(this.config,e),n.url=this.mixinParam(e.url,e.params)),n.url!==J())if(t.intercept&&(n.intercept=t.intercept),n.params=t,n=F(this.config,n),"function"==typeof n.intercept){await new Promise((e,t)=>{n.intercept(n,e)})&&this.openPage(n)}else this.openPage(n)}openPage(e){const{url:t,type:n,delta:i,animationType:o,animationDuration:r,events:f}=e;"navigateTo"!=e.type&&"to"!=e.type||c({url:t,animationType:o,animationDuration:r,events:f}),"redirectTo"!=e.type&&"redirect"!=e.type||u({url:t}),"switchTab"!=e.type&&"tab"!=e.type||a({url:t}),"reLaunch"!=e.type&&"launch"!=e.type||l({url:t}),"navigateBack"!=e.type&&"back"!=e.type||s({delta:i})}}).route;let V,Y=null;function K(e,t=500,n=!1){if(null!==Y&&clearTimeout(Y),n){const n=!Y;Y=setTimeout(()=>{Y=null},t),n&&"function"==typeof e&&e()}else Y=setTimeout(()=>{"function"==typeof e&&e()},t)}function Q(e,t=500,n=!0){n?V||(V=!0,"function"==typeof e&&e(),setTimeout(()=>{V=!1},t)):V||(V=!0,setTimeout(()=>{V=!1,"function"==typeof e&&e()},t))}const ee={props:{customStyle:{type:[Object,String],default:()=>({})},customClass:{type:String,default:""},url:{type:String,default:""},linkType:{type:String,default:"navigateTo"}},data:()=>({}),onLoad(){this.$uv.getRect=this.$uvGetRect},created(){this.$uv.getRect=this.$uvGetRect},computed:{$uv(){var e,t;return{...X,test:_,route:W,debounce:K,throttle:Q,unit:null==(t=null==(e=null==uni?void 0:uni.$uv)?void 0:e.config)?void 0:t.unit}},bem:()=>function(e,t,n){const i=`uv-${e}--`,o={};return t&&t.map(e=>{o[i+this[e]]=!0}),n&&n.map(e=>{this[e]?o[i+e]=this[e]:delete o[i+e]}),Object.keys(o)}},methods:{openPage(e="url"){const t=this[e];t&&uni[this.linkType]({url:t})},$uvGetRect(e,t){return new Promise(n=>{p().in(this)[t?"selectAll":"select"](e).boundingClientRect(e=>{t&&Array.isArray(e)&&e.length&&n(e),!t&&e&&n(e)}).exec()})},getParentData(e=""){this.parent||(this.parent={}),this.parent=this.$uv.$parent.call(this,e),this.parent.children&&-1===this.parent.children.indexOf(this)&&this.parent.children.push(this),this.parent&&this.parentData&&Object.keys(this.parentData).map(e=>{this.parentData[e]=this.parent[e]})},preventEvent(e){e&&"function"==typeof e.stopPropagation&&e.stopPropagation()},noop(e){this.preventEvent(e)}},onReachBottom(){f("uvOnReachBottom")},beforeDestroy(){if(this.parent&&P(this.parent.children)){const e=this.parent.children;e.map((t,n)=>{t===this&&e.splice(n,1)})}},unmounted(){if(this.parent&&P(this.parent.children)){const e=this.parent.children;e.map((t,n)=>{t===this&&e.splice(n,1)})}}},te=(e,t)=>{const n=e.__vccOpts||e;for(const[i,o]of t)n[i]=o;return n},ne={"uvicon-level":"e68f","uvicon-checkbox-mark":"e659","uvicon-folder":"e694","uvicon-movie":"e67c","uvicon-star-fill":"e61e","uvicon-star":"e618","uvicon-phone-fill":"e6ac","uvicon-phone":"e6ba","uvicon-apple-fill":"e635","uvicon-backspace":"e64d","uvicon-attach":"e640","uvicon-empty-data":"e671","uvicon-empty-address":"e68a","uvicon-empty-favor":"e662","uvicon-empty-car":"e657","uvicon-empty-order":"e66b","uvicon-empty-list":"e672","uvicon-empty-search":"e677","uvicon-empty-permission":"e67d","uvicon-empty-news":"e67e","uvicon-empty-history":"e685","uvicon-empty-coupon":"e69b","uvicon-empty-page":"e60e","uvicon-empty-wifi-off":"e6cc","uvicon-reload":"e627","uvicon-order":"e695","uvicon-server-man":"e601","uvicon-search":"e632","uvicon-more-dot-fill":"e66f","uvicon-scan":"e631","uvicon-map":"e665","uvicon-map-fill":"e6a8","uvicon-tags":"e621","uvicon-tags-fill":"e613","uvicon-eye":"e664","uvicon-eye-fill":"e697","uvicon-eye-off":"e69c","uvicon-eye-off-outline":"e688","uvicon-mic":"e66d","uvicon-mic-off":"e691","uvicon-calendar":"e65c","uvicon-trash":"e623","uvicon-trash-fill":"e6ce","uvicon-play-left":"e6bf","uvicon-play-right":"e6b3","uvicon-minus":"e614","uvicon-plus":"e625","uvicon-info-circle":"e69f","uvicon-info-circle-fill":"e6a7","uvicon-question-circle":"e622","uvicon-question-circle-fill":"e6bc","uvicon-close":"e65a","uvicon-checkmark":"e64a","uvicon-checkmark-circle":"e643","uvicon-checkmark-circle-fill":"e668","uvicon-setting":"e602","uvicon-setting-fill":"e6d0","uvicon-heart":"e6a2","uvicon-heart-fill":"e68b","uvicon-camera":"e642","uvicon-camera-fill":"e650","uvicon-more-circle":"e69e","uvicon-more-circle-fill":"e684","uvicon-chat":"e656","uvicon-chat-fill":"e63f","uvicon-bag":"e647","uvicon-error-circle":"e66e","uvicon-error-circle-fill":"e655","uvicon-close-circle":"e64e","uvicon-close-circle-fill":"e666","uvicon-share":"e629","uvicon-share-fill":"e6bb","uvicon-share-square":"e6c4","uvicon-shopping-cart":"e6cb","uvicon-shopping-cart-fill":"e630","uvicon-bell":"e651","uvicon-bell-fill":"e604","uvicon-list":"e690","uvicon-list-dot":"e6a9","uvicon-zhifubao-circle-fill":"e617","uvicon-weixin-circle-fill":"e6cd","uvicon-weixin-fill":"e620","uvicon-qq-fill":"e608","uvicon-qq-circle-fill":"e6b9","uvicon-moments-circel-fill":"e6c2","uvicon-moments":"e6a0","uvicon-car":"e64f","uvicon-car-fill":"e648","uvicon-warning-fill":"e6c7","uvicon-warning":"e6c1","uvicon-clock-fill":"e64b","uvicon-clock":"e66c","uvicon-edit-pen":"e65d","uvicon-edit-pen-fill":"e679","uvicon-email":"e673","uvicon-email-fill":"e683","uvicon-minus-circle":"e6a5","uvicon-plus-circle":"e603","uvicon-plus-circle-fill":"e611","uvicon-file-text":"e687","uvicon-file-text-fill":"e67f","uvicon-pushpin":"e6d1","uvicon-pushpin-fill":"e6b6","uvicon-grid":"e68c","uvicon-grid-fill":"e698","uvicon-play-circle":"e6af","uvicon-play-circle-fill":"e62a","uvicon-pause-circle-fill":"e60c","uvicon-pause":"e61c","uvicon-pause-circle":"e696","uvicon-gift-fill":"e6b0","uvicon-gift":"e680","uvicon-kefu-ermai":"e660","uvicon-server-fill":"e610","uvicon-coupon-fill":"e64c","uvicon-coupon":"e65f","uvicon-integral":"e693","uvicon-integral-fill":"e6b1","uvicon-home-fill":"e68e","uvicon-home":"e67b","uvicon-account":"e63a","uvicon-account-fill":"e653","uvicon-thumb-down-fill":"e628","uvicon-thumb-down":"e60a","uvicon-thumb-up":"e612","uvicon-thumb-up-fill":"e62c","uvicon-lock-fill":"e6a6","uvicon-lock-open":"e68d","uvicon-lock-opened-fill":"e6a1","uvicon-lock":"e69d","uvicon-red-packet":"e6c3","uvicon-photo-fill":"e6b4","uvicon-photo":"e60d","uvicon-volume-off-fill":"e6c8","uvicon-volume-off":"e6bd","uvicon-volume-fill":"e624","uvicon-volume":"e605","uvicon-download":"e670","uvicon-arrow-up-fill":"e636","uvicon-arrow-down-fill":"e638","uvicon-play-left-fill":"e6ae","uvicon-play-right-fill":"e6ad","uvicon-arrow-downward":"e634","uvicon-arrow-leftward":"e63b","uvicon-arrow-rightward":"e644","uvicon-arrow-upward":"e641","uvicon-arrow-down":"e63e","uvicon-arrow-right":"e63c","uvicon-arrow-left":"e646","uvicon-arrow-up":"e633","uvicon-skip-back-left":"e6c5","uvicon-skip-forward-right":"e61f","uvicon-arrow-left-double":"e637","uvicon-man":"e675","uvicon-woman":"e626","uvicon-en":"e6b8","uvicon-twitte":"e607","uvicon-twitter-circle-fill":"e6cf"};const ie=te({name:"uv-icon",emits:["click"],mixins:[x,ee,{props:{name:{type:String,default:""},color:{type:String,default:"#606266"},size:{type:[String,Number],default:"16px"},bold:{type:Boolean,default:!1},index:{type:[String,Number],default:null},hoverClass:{type:String,default:""},customPrefix:{type:String,default:"uvicon"},label:{type:[String,Number],default:""},labelPos:{type:String,default:"right"},labelSize:{type:[String,Number],default:"15px"},labelColor:{type:String,default:"#606266"},space:{type:[String,Number],default:"3px"},imgMode:{type:String,default:"aspectFit"},width:{type:[String,Number],default:""},height:{type:[String,Number],default:""},top:{type:[String,Number],default:0},stop:{type:Boolean,default:!1},...null==(t=null==(e=uni.$uv)?void 0:e.props)?void 0:t.icon}}],data:()=>({colorType:["primary","success","info","error","warning"]}),computed:{uClasses(){let e=[];return e.push(this.customPrefix),e.push(this.customPrefix+"-"+this.name),this.color&&this.colorType.includes(this.color)&&e.push("uv-icon__icon--"+this.color),e},iconStyle(){let e={};return e={fontSize:this.$uv.addUnit(this.size),lineHeight:this.$uv.addUnit(this.size),fontWeight:this.bold?"bold":"normal",top:this.$uv.addUnit(this.top)},this.color&&!this.colorType.includes(this.color)&&(e.color=this.color),e},isImg(){const e=this.name.indexOf("data:")>-1&&this.name.indexOf("base64")>-1;return-1!==this.name.indexOf("/")||e},imgStyle(){let e={};return e.width=this.width?this.$uv.addUnit(this.width):this.$uv.addUnit(this.size),e.height=this.height?this.$uv.addUnit(this.height):this.$uv.addUnit(this.size),e},icon(){const e=ne["uvicon-"+this.name];return e?unescape(`%u${e}`):["uvicon"].indexOf(this.customPrefix)>-1?this.name:""}},methods:{clickHandler(e){this.$emit("click",this.index),this.stop&&this.preventEvent(e)}}},[["render",function(e,t,n,i,o,r){const c=g,u=b,a=k;return d(),v(a,{class:$(["uv-icon",["uv-icon--"+e.labelPos]]),onClick:r.clickHandler},{default:h(()=>[r.isImg?(d(),v(c,{key:0,class:"uv-icon__img",src:e.name,mode:e.imgMode,style:y([r.imgStyle,e.$uv.addStyle(e.customStyle)])},null,8,["src","mode","style"])):(d(),v(u,{key:1,class:$(["uv-icon__icon",r.uClasses]),style:y([r.iconStyle,e.$uv.addStyle(e.customStyle)]),"hover-class":e.hoverClass},{default:h(()=>[w(S(r.icon),1)]),_:1},8,["class","style","hover-class"])),""!==e.label?(d(),v(u,{key:2,class:"uv-icon__label",style:y({color:e.labelColor,fontSize:e.$uv.addUnit(e.labelSize),marginLeft:"right"==e.labelPos?e.$uv.addUnit(e.space):0,marginTop:"bottom"==e.labelPos?e.$uv.addUnit(e.space):0,marginRight:"left"==e.labelPos?e.$uv.addUnit(e.space):0,marginBottom:"top"==e.labelPos?e.$uv.addUnit(e.space):0})},{default:h(()=>[w(S(e.label),1)]),_:1},8,["style"])):m("",!0)]),_:1},8,["onClick","class"])}],["__scopeId","data-v-553bbf60"]]);export{te as _,ee as a,ie as b,j as d,q as e,N as f,x as m,L as p,H as t};
