<!doctype html>
<html lang="en">
  <head>
    <link rel="stylesheet" href="/assets/uni.80f71d6d.css">

    <meta charset="UTF-8" />
    <!-- index.html -->

    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Expires" content="0" />
    <!-- 设置icon -->
    <link rel="icon" type="image/x-icon" href="/static/favicon.ico" />
    <script>
      var coverSupport =
        'CSS' in window &&
        typeof CSS.supports === 'function' &&
        (CSS.supports('top: env(a)') || CSS.supports('top: constant(a)'));
      document.write(
        '<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' +
          (coverSupport ? ', viewport-fit=cover' : '') +
          '" />',
      );
    </script>
    <!-- <script
      type="text/javascript"
      src="https://o.alicdn.com/captcha-frontend/aliyunCaptcha/AliyunCaptcha.js"
    ></script> -->

    <title>RenTo</title>
    <!--preload-links-->
    <!--app-context-->
    <script type="module" crossorigin src="/assets/index-BBirLt11.js"></script>
    <link rel="stylesheet" crossorigin href="/assets/index-DbyYuH-e.css">
  </head>
  <body>
    <div id="app"><!--app-html--></div>
  </body>
  <style>
    /* html,
    body {
      height: 100%;
      max-width: 600px;
      margin: 0 auto;
    } */
  </style>
</html>
