<?php

/**
 * 框架函数和类定义文件
 * 此文件仅用于IDE代码提示和跳转，不用于实际运行
 */

// 定义常用常量
if (!defined('DS')) {
    define('DS', DIRECTORY_SEPARATOR);
}
if (!defined('ROOT')) {
    define('ROOT', dirname(__DIR__) . DS);
}
if (!defined('APP')) {
    define('APP', ROOT . 'app' . DS);
}
if (!defined('JU')) {
    define('JU', ROOT . 'ju' . DS);
}
if (!defined('EXT')) {
    define('EXT', APP . 'ext' . DS);
}
if (!defined('LIB')) {
    define('LIB', APP . 'lib' . DS);
}
if (!defined('APP_X')) {
    define('APP_X', APP . 'x' . DS);
}
if (!defined('XPATH')) {
    define('XPATH', ROOT . 'x' . DS);
}

// =========== 框架核心函数 ===========

/**
 * 自动加载类
 * @param string $class 类名
 */
function _ju_autoload($class) {}

/**
 * 判断类名合法性
 * @param string $name 类名
 * @return bool
 */
function _is_classname($name) {}

/**
 * 调试输出
 * @param mixed ...$args 要输出的内容
 */
function p(...$args) {}

/**
 * 调试输出html
 * @param mixed ...$args 要输出的内容
 */
function phtml(...$args) {}

/**
 * 引入EXT下的扩展函数文件
 * @param string|array $files 文件名
 */
function F($files) {}

/**
 * 引入LIB下的扩展类
 * @param string $filename 文件名
 * @param string $dirname 目录名
 */
function L($filename = '', $dirname = '') {}

/**
 * 返回APP_X下的扩展配置文件配置
 * @param string $filename 文件名
 * @param string $key 配置键名
 * @return mixed
 */
function RX($filename, $key = '') {}

/**
 * 返回XPATH下的扩展配置文件配置(用于存放敏感信息)
 * @param string $filename 文件名
 * @param string $key 配置键名
 * @return mixed
 */
function RXX($filename, $key = '') {}

/**
 * 实例化类
 * @param string $classname 类名
 * @param mixed ...$args 参数
 * @return object
 */
function D($classname, ...$args) {}

/**
 * 配置中心
 * @param string $key 键名
 * @param mixed $str 要设置的值
 * @return mixed
 */
function J($key, $str = null) {}

/**
 * 实例化数据库模型
 * @param string $config 配置名
 * @return db 数据库实例
 */
function M($config = '') {}

/**
 * 实例化表模型
 * @param string $table_name 表名
 * @return object
 */
function MM($table_name = '') {}

/**
 * 锁定某个键
 * @param string $k 键名
 * @param int $time 锁定时间
 * @return bool|string
 */
function lock($k, $time) {}

/**
 * 解锁某个键
 * @param string $k 键名
 * @param string $v 锁值
 * @return bool
 */
function unlock($k, $v) {}

/**
 * 返回标准的结果数组
 * @param string $msg 消息
 * @param int $code 状态码
 * @param mixed $data 数据
 * @param array $qt 其他参数
 * @return array
 */
function rs($msg = '', $code = -1, $data = '', $qt = []) {}

/**
 * 返回AJAX结果
 * @param string $msg 消息
 * @param int $code 状态码
 * @param mixed $data 数据
 * @param array $qt 其他参数
 */
function ar($msg = '', $code = -1, $data = '', $qt = []) {}

/**
 * 提示信息
 * @param string $msg 消息
 * @param int $code 状态码
 * @param string $url 跳转URL
 */
function tips($msg = '', $code = 2, $url = '') {}

/**
 * AJAX返回数据
 * @param mixed $data 数据
 * @param string $type 返回类型
 */
function ajaxReturn($data, $type = 'JSON') {}

/**
 * 检测是否为POST请求
 * @return bool
 */
function is_post() {}

/**
 * 检测是否为AJAX请求
 * @return bool
 */
function is_ajax() {}

/**
 * 检测是否使用SSL
 * @param int $fs 格式
 * @return bool|string
 */
function is_ssl($fs = 0) {}

/**
 * 检测是否为JSON数据
 * @param mixed $data 数据
 * @return bool
 */
function is_json($data) {}

/**
 * 检测是否为日期
 * @param string $str 日期字符串
 * @return bool
 */
function is_date($str) {}

/**
 * 检测是否为移动设备
 * @param string $ua 用户代理
 * @return bool
 */
function is_mobile($ua = '') {}

/**
 * 返回请求中的参数值
 * @param string $name 参数名
 * @param mixed $default 默认值
 * @param int $glwb 是否过滤危险字符
 * @param int $arrok 是否允许数组
 * @return mixed
 */
function re($name = '', $default = '', $glwb = '1', $arrok = '0') {}

/**
 * 获取数组参数
 * @param string $name 参数名
 * @param int $glwb 是否过滤危险字符
 * @param int $arrok 是否允许数组
 * @return array
 */
function rea($name, $glwb = '1', $arrok = 1) {}

/**
 * 过滤数组中的危险字符
 * @param array $arr 数组
 * @param int $arrok 是否允许数组
 * @param int $wei 维度
 * @return array
 */
function glarr($arr, $arrok = 1, $wei = 1) {}

/**
 * 获取IP地址
 * @param int $type 类型
 * @param int $ms 模式
 * @return string
 */
function getip($type = 1, $ms = 1) {}

/**
 * 输出JSON格式的数据
 * @param mixed $data 数据
 * @param int $fs JSON格式化选项
 * @return string
 */
function je($d, $fs = JSON_UNESCAPED_UNICODE) {}

/**
 * 解析JSON数据
 * @param string $json JSON字符串
 * @param bool $fs 是否返回数组
 * @return mixed
 */
function jd($json, $fs = true) {}

/**
 * 过滤危险字符
 * @param string $data 要过滤的数据
 * @return string
 */
function glwb($data = '') {}

/**
 * 反过滤危险字符
 * @param string $str 要反过滤的字符串
 * @return string
 */
function glwb_f($str) {}

/**
 * 获取当前时间
 * @param string $str 时间格式
 * @param int $time 时间戳
 * @return string
 */
function getsj($str = 'Y-m-d H:i:s', $time = '') {}

/**
 * Session前缀
 * @return string
 */
function session_prefix() {}

/**
 * 设置Session
 * @param mixed $data 数据
 * @return bool
 */
function session_set($data = '') {}

/**
 * 获取Session
 * @param string $key 键名
 * @return mixed
 */
function session_get($key = '') {}

/**
 * 删除Session
 * @param string $key 键名
 * @return bool
 */
function session_del($key = '') {}

/**
 * Cookie操作
 * @param string $key 键名
 * @param mixed $data 数据
 * @param int $sj 有效期
 * @return mixed
 */
function cookie($key, $data = '', $sj = '86400') {}

/**
 * 文件写入
 * @param string $pathfile 文件路径
 * @param mixed $data 数据
 * @param int $fs 写入方式
 * @return bool
 */
function wjxie($pathfile, $data, $fs = 0) {}

/**
 * 记录日志
 * @param string $path 日志路径
 * @param mixed $data 数据
 * @param int $lx 类型
 * @param int $oss_fs OSS方式
 */
function _log($path = 'mylog', $data = '', $lx = 0, $oss_fs = 0) {}

/**
 * 缓存操作
 * @param string $key 键名
 * @param mixed $value 值
 * @param int $t 有效期
 * @return mixed
 */
function S($key, $value = '', $t = 0) {}
/**
 * 显示模板
 * @param string $filename 模板文件名
 * @param string $dir 目录
 * @param int $inc 包含方式
 */
function show($filename = '', $dir = '', $inc = 0) {}

/**
 * 错误处理
 * @param string $error 错误信息
 * @param string $log_path 日志路径
 * @param string $fs 处理方式
 */
function _e404($error = '', $log_path = '', $fs = '1') {}

/**
 * HTTP请求
 * @param array $d 配置参数
 * @return mixed
 */
function chttp($d = []) {}

/**
 * 页面跳转
 * @param string $url 跳转地址
 * @param int $time 延迟时间
 * @param string $msg 提示消息
 */
function tiao($url, $time = 0, $msg = '') {}

/**
 * JS页面跳转
 * @param string $url 跳转地址
 * @param string $msg 提示消息
 */
function tiao_js($url, $msg = '') {}

/**
 * 数据加密
 * @param mixed $data 数据
 * @param int $expire 有效期
 * @param string $key 密钥
 * @return string
 */
function jiami($data, $expire = 0, $key = '') {}

/**
 * 数据解密
 * @param string $string 密文
 * @param string $key 密钥
 * @return mixed
 */
function jiemi($string, $key = '') {}

// =========== 框架核心类 ===========

/**
 * 路由类
 */
class router
{
    public function __construct() {}
    public function parseUrl() {}
}

/**
 * 调试跟踪类
 */
class trace
{
    public function __construct() {}
    public function page_trace() {}
}

/**
 * 语言处理类
 */
class lang
{
    public function __construct() {}
    public function load($lang_file) {}
    public function getLanguage() {}
}

/**
 * 控制器基类
 */
class x_base
{
    public function __construct() {}
    public function __call($name, $arguments) {}
    public function display($template = '', $dir = '') {}
    protected function assign($name, $value = '') {}
    protected function error($msg = '', $url = '') {}
    protected function success($msg = '', $url = '') {}
}

/**
 * 模型基类
 */
class mo_base
{
    protected $table;
    protected $db;

    public function __construct($table = '') {}
    public function query($sql, $time = '-1', $shuaxin = false) {}
    public function find($where = '', $field = '*', $order = '') {}
    /**
     * 查询数据
     * @param string $table 表名
     * @param string $columns 查询字段，可以是字符串或数组
     * @param string|array $where 查询条件
     * @param string|array $order 排序
     * @param string $limit 限制条数
     * @param string $time 缓存时间
     * @param bool $shuaxin 是否刷新缓存
     * @param array $group 分组
     * @return array 查询结果
     */
    public function select($table, $columns = '*', $where = '', $order = '', $limit = '', $time = '-1', $shuaxin = false, $group = '') {}
    public function insert($table, $data, $id = 0) {}
    public function inserts($table, $data, $row = 0) {}
    public function update($table, $data, $where = '', $row = 0) {}
    public function updates($table, $data = [], $index = '', $row = 0) {}
    public function delete($table, $where, $row = 0) {}
    public function count($table, $where = '', $time = '-1', $shuaxin = false) {}
    public function s_page($table, $columns = "*", $where = '', $order = [], $pagenum = '10', $page = '1', $time = '-1', $shuaxin = false, $group = '') {}
    public function get($table, $columns = "*", $where = '', $time = '-1', $shuaxin = false) {}
    public function get_order($table, $columns = '*', $where = '', $order = '', $time = '-1', $shuaxin = false) {}
    public function get_field($table, $field, $where = '', $time = '-1', $shuaxin = false) {}
    public function has($table, $where, $time = '-1', $shuaxin = false) {}
    public function sum($table, $columns, $where = '', $time = '-1', $shuaxin = false) {}
    public function key($hcname = '') {}
    public function debug() {}
    public function begin() {}
    public function startTrans()
    {
        return $this->begin();
    }
    public function commit() {}
    public function rollback() {}
    public function close() {}
    public function log() {}
    public function last() {}
    public function _build_where() {}
}

/**
 * DB类 基于mysqli
 * @package ju\lib
 */
class db extends mo_base
{
    /**
     * @var mysqli
     */
    protected $db;

    /**
     * 获取数据库实例
     * @param string $config 数据库配置
     * @return db 数据库实例
     */
    public static function inst($config = '') {}

    // ... existing code ...
}
