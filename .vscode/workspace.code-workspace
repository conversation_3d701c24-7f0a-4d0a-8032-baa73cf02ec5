{
    "folders": [
        {
            "path": "."
        }
    ],
    "settings": {
        // 继承 settings.json 的配置
        "php.validate.enable": true,
        "intelephense.diagnostics.enable": true,
        // 工作区特定设置
        "search.exclude": {
            "**/node_modules": true,
            "**/vendor": true,
            "**/cache": true,
            "**/logs": true,
            "**/.git": true
        },
        "files.exclude": {
            "**/cache": true,
            "**/logs": true,
            "**/.DS_Store": true
        },
        "files.watcherExclude": {
            "**/node_modules/**": true,
            "**/vendor/**": true,
            "**/cache/**": true,
            "**/logs/**": true
        },
        // PHP 特定配置
        "php.suggest.basic": true,
        "php.validate.run": "onType",
        // 代码提示和自动完成
        "editor.suggest.insertMode": "replace",
        "editor.acceptSuggestionOnCommitCharacter": true,
        "editor.acceptSuggestionOnEnter": "on",
        // 错误和警告
        "problems.showCurrentInStatus": true,
        "editor.gotoLocation.multipleReferences": "peek",
        "editor.gotoLocation.multipleDefinitions": "peek",
        // 自动保存和格式化
        "editor.formatOnSave": true,
        "editor.codeActionsOnSave": {
            "source.fixAll": true,
            "source.organizeImports": true
        }
    },
    "extensions": {
        "recommendations": [
            "bmewburn.vscode-intelephense-client",
            "xdebug.php-debug",
            "ms-vscode.vscode-json"
        ]
    },
    "tasks": {
        "version": "2.0.0",
        "tasks": [
            {
                "label": "Check PHP Types",
                "type": "shell",
                "command": "find",
                "args": [
                    "app/",
                    "-name",
                    "*.php",
                    "-exec",
                    "php",
                    "-l",
                    "{}",
                    ";"
                ],
                "group": "build",
                "presentation": {
                    "echo": true,
                    "reveal": "always",
                    "focus": false,
                    "panel": "shared"
                }
            }
        ]
    }
}