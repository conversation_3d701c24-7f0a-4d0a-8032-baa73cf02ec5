{"Array Access Check": {"prefix": "arraycheck", "body": ["if (isset(${1:array}['${2:key}'])) {", "    $${3:value} = ${1:array}['${2:key}'];", "} else {", "    $${3:value} = ${4:default_value};", "}"], "description": "安全的数组访问检查"}, "Null Coalescing": {"prefix": "nullcoal", "body": ["$${1:variable} = ${2:array}['${3:key}'] ?? ${4:default};"], "description": "使用空合并操作符"}, "Type Check": {"prefix": "typecheck", "body": ["if (!is_${1:array}($${2:variable})) {", "    throw new InvalidArgumentException('Expected ${1:array}, got ' . gettype($${2:variable}));", "}"], "description": "类型检查"}, "Safe Array Column": {"prefix": "arraycolumn", "body": ["$${1:result} = array_column(${2:array}, '${3:column}');", "if (empty($${1:result})) {", "    $${1:result} = [];", "}"], "description": "安全的 array_column 使用"}, "Database Result Check": {"prefix": "dbcheck", "body": ["$${1:result} = M()->${2:method}(${3:params});", "if (empty($${1:result})) {", "    return rs('${4:error_message}');", "}"], "description": "数据库结果检查"}, "User Map Access": {"prefix": "usermap", "body": ["$${1:user} = $${2:userMap}[$${3:uid}] ?? [];", "if (empty($${1:user})) {", "    ${4:// 处理用户不存在的情况}", "    continue;", "}"], "description": "安全的用户映射访问"}}