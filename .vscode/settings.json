{
  // PHP 相关配置
  "php.validate.enable": true,
  "php.validate.executablePath": "",
  "php.suggest.basic": true,
  // PHP Intelephense 配置（如果安装了）
  "intelephense.diagnostics.enable": true,
  "intelephense.diagnostics.run": "onType",
  "intelephense.diagnostics.typeErrors": true,
  "intelephense.diagnostics.undefinedVariables": true,
  "intelephense.diagnostics.undefinedMethods": true,
  "intelephense.diagnostics.undefinedProperties": true,
  "intelephense.diagnostics.undefinedConstants": true,
  "intelephense.diagnostics.undefinedClassConstants": true,
  "intelephense.diagnostics.undefinedFunctions": true,
  "intelephense.diagnostics.undefinedTypes": true,
  "intelephense.diagnostics.argumentCount": true,
  // 类型推断配置
  "intelephense.completion.insertUseDeclaration": true,
  "intelephense.completion.fullyQualifyGlobalConstantsAndFunctions": false,
  "intelephense.format.enable": true,
  "intelephense.trace.server": "off",
  // 禁用一些过于严格的检查
  "intelephense.diagnostics.unusedSymbols": false,
  "intelephense.diagnostics.undefinedSymbols": false,
  // 包含类型定义文件
  "intelephense.stubs": [
    "apache",
    "bcmath",
    "bz2",
    "calendar",
    "com_dotnet",
    "core",
    "ctype",
    "curl",
    "date",
    "dba",
    "dom",
    "enchant",
    "exif",
    "fileinfo",
    "filter",
    "fpm",
    "ftp",
    "gd",
    "hash",
    "iconv",
    "imap",
    "interbase",
    "intl",
    "json",
    "ldap",
    "libxml",
    "mbstring",
    "mcrypt",
    "meta",
    "mssql",
    "mysql",
    "mysqli",
    "oci8",
    "odbc",
    "openssl",
    "pcntl",
    "pcre",
    "pdo",
    "pgsql",
    "phar",
    "posix",
    "pspell",
    "readline",
    "recode",
    "reflection",
    "regex",
    "session",
    "shmop",
    "simplexml",
    "snmp",
    "soap",
    "sockets",
    "sodium",
    "spl",
    "sqlite3",
    "standard",
    "superglobals",
    "sybase",
    "sysvmsg",
    "sysvsem",
    "sysvshm",
    "tidy",
    "tokenizer",
    "wddx",
    "xml",
    "xmlreader",
    "xmlrpc",
    "xmlwriter",
    "xsl",
    "zip",
    "zlib"
  ],
  "intelephense.files.include": ["**/*.php", "stubs/**/*.php"],
  // 代码格式化
  "editor.formatOnSave": false,
  "editor.formatOnType": false,
  // 错误和警告显示
  "problems.decorations.enabled": true,
  "editor.renderValidationDecorations": "on",
  // 自动保存
  "files.autoSave": "onFocusChange",
  // PHP 文件关联
  "files.associations": {
    "*.php": "php"
  },
  // 代码提示
  "editor.quickSuggestions": {
    "other": true,
    "comments": false,
    "strings": true
  },
  // 显示空白字符
  "editor.renderWhitespace": "boundary",
  // 括号匹配
  "editor.matchBrackets": "always",
  // 自动补全括号
  "editor.autoClosingBrackets": "always",
  "editor.autoClosingQuotes": "always",
  // 错误提示
  "editor.showUnused": true,
  "editor.showDeprecated": true
}

