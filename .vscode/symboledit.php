<?php

/**
 * 这是一个辅助文件，用于提供框架函数的代码跳转支持
 * 该文件仅供IDE使用，不参与实际运行
 */

// 代码跳转辅助函数 - 以下函数定义与ju/fun.php中的函数保持一致

/**
 * 返回请求中的参数值
 * @param string $name 参数名
 * @param mixed $default 默认值
 * @param int $glwb 是否过滤危险字符
 * @param int $arrok 是否允许数组
 * @return mixed
 */
function re($name = '', $default = '', $glwb = '1', $arrok = '0') {}

/**
 * 获取数组参数
 * @param string $name 参数名
 * @param int $glwb 是否过滤危险字符
 * @param int $arrok 是否允许数组
 * @return array
 */
function rea($name, $glwb = '1', $arrok = 1) {}

/**
 * 调试输出
 * @param mixed ...$args 要输出的内容
 */
function p(...$args) {}

/**
 * 输出JSON格式的数据
 * @param mixed $data 数据
 * @param int $fs JSON格式化选项
 * @return string
 */
function je($d, $fs = JSON_UNESCAPED_UNICODE) {}

/**
 * 解析JSON数据
 * @param string $json JSON字符串
 * @param bool $fs 是否返回数组
 * @return mixed
 */
function jd($json, $fs = true) {}

/**
 * 返回操作结果
 * @param string $msg 消息
 * @param int $code 状态码
 * @param mixed $data 数据
 * @param array $qt 其他参数
 * @return array
 */
function rs($msg = '', $code = -1, $data = '', $qt = []) {}

/**
 * 过滤危险字符
 * @param string $data 要过滤的数据
 * @return string
 */
function glwb($data = "") {}

/**
 * 实例化类
 * @param string $classname 类名
 * @param mixed ...$args 参数
 * @return object
 */
function D($classname, ...$args) {}

// /**
//  * 实例化数据库模型
//  * @param string $config 配置名
//  * @return \mo_base 返回数据库模型实例
//  */
// function M($config = "") {}

/**
 * 实例化数据库模型
 * @param string $config 配置名
 * @return \db 返回数据库模型实例
 */
function M($config = "") {}

/**
 * 检测是否为POST请求
 * @return bool
 */
function is_post() {}

/**
 * 检测是否为AJAX请求
 * @return bool
 */
function is_ajax() {}

/**
 * 检测是否为JSON数据
 * @param mixed $data 数据
 * @return bool
 */
function is_json($data) {}

// 常用类定义

/**
 * 路由类
 */
class router {}

/**
 * 控制器基类
 */
class x_base {}

/**
 * 模型基类
 */
class mo_base
{
    protected $table;
    protected $db;

    public function __construct($table = '') {}
    public function query($sql, $time = '-1', $shuaxin = false) {}
    public function find($where = '', $field = '*', $order = '') {}
    /**
     * 查询数据
     * @param string $table 表名
     * @param string $columns 查询字段
     * @param string|array $where 查询条件
     * @param array $order 排序
     * @param string $limit 限制条数
     * @param string $time 缓存时间
     * @param bool $shuaxin 是否刷新缓存
     * @param array $group 分组
     * @return array 查询结果
     */
public function select($table, $columns = '*', $where = '', $order = '', $limit = '', $time = '-1', $shuaxin = false, $group = '') {}
    public function insert($table, $data, $id = 0) {}
    public function inserts($table, $data, $row = 0) {}
    public function update($table, $data, $where = '', $row = 0) {}
    public function updates($table, $data = [], $index = '', $row = 0) {}
    public function delete($table, $where, $row = 0) {}
    public function count($table, $where = '', $time = '-1', $shuaxin = false) {}
    public function s_page($table, $columns = "*", $where = '', $order = [], $pagenum = '10', $page = '1', $time = '-1', $shuaxin = false, $group = '') {}
    public function get($table, $columns = "*", $where = '', $time = '-1', $shuaxin = false) {}
    public function get_order($table, $columns = '*', $where = '', $order = '', $time = '-1', $shuaxin = false) {}
    public function get_field($table, $field, $where = '', $time = '-1', $shuaxin = false) {}
    public function has($table, $where, $time = '-1', $shuaxin = false) {}
    public function sum($table, $columns, $where = '', $time = '-1', $shuaxin = false) {}
    public function key($hcname = '') {}
    public function debug() {}
    public function begin() {}
    public function startTrans()
    {
        return $this->begin();
    }
    public function commit() {}
    public function rollback() {}
    public function close() {}
    public function log() {}
    public function last() {}
    public function _build_where() {}
    public function insertOrUpdate() {}
    public function insertsOrUpdates() {}
}

/**
 * DB类 基于mysqli
 * @package ju\lib
 */
class db extends mo_base
{
    /**
     * @var mysqli
     */
    protected $db;

    /**
     * 获取数据库实例
     * @param string $config 数据库配置
     * @return db 数据库实例
     */
    public static function inst($config = '') {}

    // 继承 mo_base 的所有方法
}

/**
 * 调试跟踪类
 */
class trace {}

/**
 * 语言处理类
 */
class lang {}
