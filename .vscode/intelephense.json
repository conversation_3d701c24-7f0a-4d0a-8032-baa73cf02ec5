{"diagnostics": {"enable": true, "run": "onType", "typeErrors": true, "undefinedVariables": true, "undefinedMethods": false, "undefinedProperties": false, "undefinedConstants": false, "undefinedClassConstants": false, "undefinedFunctions": false, "undefinedTypes": false, "argumentCount": true, "unusedSymbols": false, "undefinedSymbols": false}, "completion": {"insertUseDeclaration": true, "fullyQualifyGlobalConstantsAndFunctions": false, "triggerParameterHints": true, "maxItems": 100}, "format": {"enable": true, "braces": "psr12"}, "environment": {"documentRoot": "", "includePaths": ["stubs/"]}, "files": {"maxSize": 5000000, "associations": {"*.php": "php"}, "exclude": ["**/.git/**", "**/.svn/**", "**/.hg/**", "**/CVS/**", "**/.DS_Store/**", "**/node_modules/**", "**/bower_components/**", "**/vendor/**/{Tests,tests}/**", "**/.history/**", "**/cache/**", "**/logs/**"]}, "stubs": ["apache", "bcmath", "bz2", "calendar", "com_dotnet", "core", "ctype", "curl", "date", "dba", "dom", "enchant", "exif", "fileinfo", "filter", "fpm", "ftp", "gd", "hash", "iconv", "imap", "interbase", "intl", "json", "ldap", "libxml", "mbstring", "mcrypt", "meta", "mssql", "mysql", "mysq<PERSON>", "oci8", "odbc", "openssl", "pcntl", "pcre", "pdo", "pgsql", "phar", "posix", "pspell", "readline", "recode", "reflection", "regex", "session", "shmop", "simplexml", "snmp", "soap", "sockets", "sodium", "spl", "sqlite3", "standard", "superglobals", "sybase", "sysvmsg", "sysvsem", "sysvshm", "tidy", "tokenizer", "wddx", "xml", "xmlreader", "xmlrpc", "xmlwriter", "xsl", "zip", "zlib"]}