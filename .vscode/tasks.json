{"version": "2.0.0", "tasks": [{"label": "生成PHP类型映射", "type": "shell", "command": "php", "args": ["${workspaceFolder}/.vscode/generate_meta.php"], "problemMatcher": [], "presentation": {"reveal": "always", "panel": "new"}, "group": {"kind": "build", "isDefault": true}}, {"label": "重启PHP语言服务", "type": "shell", "command": "${command:workbench.action.reloadWindow}", "problemMatcher": [], "presentation": {"reveal": "never"}}, {"label": "更新PHP智能跳转", "dependsOrder": "sequence", "dependsOn": ["生成PHP类型映射", "重启PHP语言服务"], "problemMatcher": []}, {"label": "PHP Syntax Check", "type": "shell", "command": "php", "args": ["-l", "${file}"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": {"owner": "php", "fileLocation": "absolute", "pattern": {"regexp": "^Parse error: (.+) in (.+) on line (\\d+)$", "message": 1, "file": 2, "line": 3}}}, {"label": "PHP Code Sniffer", "type": "shell", "command": "phpcs", "args": ["--standard=PSR12", "${file}"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": {"owner": "phpcs", "fileLocation": "absolute", "pattern": {"regexp": "^(.+):(\\d+):(\\d+): (.+) - (.+)$", "file": 1, "line": 2, "column": 3, "severity": 4, "message": 5}}}]}