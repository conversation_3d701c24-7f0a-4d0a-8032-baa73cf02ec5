{"Safe Database Get": {"scope": "php", "prefix": "dbget", "body": ["$${1:result} = M()->get('${2:table}', '${3:fields}', [${4:conditions}]);", "if (empty($${1:result})) {", "    return rs('${5:Record not found}');", "}", "// 确保 $${1:result} 是数组", "if (!is_array($${1:result})) {", "    return rs('${6:Invalid data format}');", "}"], "description": "安全的数据库 get 操作"}, "Safe Database Select": {"scope": "php", "prefix": "dbselect", "body": ["$${1:results} = M()->select('${2:table}', '${3:fields}', [${4:conditions}]);", "if (empty($${1:results})) {", "    $${1:results} = [];", "}", "// 确保 $${1:results} 是数组", "if (!is_array($${1:results})) {", "    $${1:results} = [];", "}"], "description": "安全的数据库 select 操作"}, "Type Safe Array Access": {"scope": "php", "prefix": "safeaccess", "body": ["// 类型安全的数组访问", "if (!is_array($${1:array})) {", "    throw new InvalidArgumentException('Expected array, got ' . gettype($${1:array}));", "}", "$${2:value} = $${1:array}['${3:key}'] ?? ${4:null};"], "description": "类型安全的数组访问"}, "Validate User Map": {"scope": "php", "prefix": "validateuser", "body": ["// 验证用户映射", "$${1:user} = $${2:userMap}[$${3:uid}] ?? [];", "if (empty($${1:user}) || !is_array($${1:user})) {", "    $log[] = \"用户不存在或数据格式错误：ID: {$${4:id}}, Uid: {$${3:uid}}\";", "    $failed_count++;", "    continue;", "}", "", "// 验证必需字段", "if (!isset($${1:user}['${5:required_field}'])) {", "    $log[] = \"用户数据缺少必需字段 ${5:required_field}：ID: {$${4:id}}, Uid: {$${3:uid}}\";", "    $failed_count++;", "    continue;", "}"], "description": "验证用户映射数据"}, "Safe Supplier Handling": {"scope": "php", "prefix": "supplier", "body": ["/** @var array{uid: int} $supplier */", "$supplier = null;", "", "if ($supplier_id) {", "    $supplier = M()->get('x_supplier', 'uid', ['uid' => $supplier_id, 'zt' => 1]);", "    if (empty($supplier)) {", "        return rs('指定的供应商不存在或已禁用');", "    }", "} else {", "    $suppliers = M()->select('x_supplier', 'uid', \"FIND_IN_SET('{$xm_id}',xm_ids) and zt=1\");", "    if (empty($suppliers)) {", "        return rs('当前项目未关联供应商');", "    }", "    if (count($suppliers) > 1) {", "        return rs('当前项目归属于多个供应商');", "    }", "    $supplier = reset($suppliers);", "}", "", "if (!ints($supplier['uid'])) {", "    return rs('供应商账号无效');", "}"], "description": "安全的供应商处理模式"}}