<?php
/**
 * 路径映射文件
 * 此文件用于提供路径映射信息，帮助IDE正确解析引用关系
 */

namespace PHPSTORM_META {
    // 为全局函数D()提供参数类型提示
    override(\D(0), map([
        'c_' => '@c_',
        'm_' => '@m_',
        'x_' => '@x_',
        '' => '@'
    ]));

    // 为M()函数提供返回类型提示
    override(\M(0), map(['' => '\mo_base']));
    
    // 为MM()函数提供返回类型提示
    override(\MM(0), map(['' => '\mo_base']));

    // 为自定义类命名空间提供映射
    override(\RX(0), map([
        'db' => 'array<string, mixed>',
        'config' => 'array<string, mixed>',
        'redis' => 'array<string, mixed>'
    ]));
} 