{"patterns": [{"name": "Array Access Without Check", "pattern": "\\$\\w+\\['\\w+'\\](?!\\s*\\?\\?)", "message": "建议使用 ?? 操作符或 isset() 检查数组键是否存在", "severity": "warning", "example": "$user['name'] ?? 'default'"}, {"name": "Database Result Without Check", "pattern": "M\\(\\)->(?:get|select)\\([^)]+\\);\\s*\\$\\w+\\s*=\\s*\\$\\w+\\['\\w+'\\]", "message": "数据库查询结果应该先检查是否为空", "severity": "error", "example": "if (empty($result)) { return rs('error'); }"}, {"name": "Reset Without Array Check", "pattern": "reset\\(\\$\\w+\\)(?!.*is_array)", "message": "使用 reset() 前应该检查变量是否为数组", "severity": "warning", "example": "if (is_array($data)) { $first = reset($data); }"}, {"name": "Array Column Without Check", "pattern": "array_column\\([^)]+\\)(?!.*empty)", "message": "array_column 结果应该检查是否为空", "severity": "info", "example": "$ids = array_column($data, 'id'); if (empty($ids)) { $ids = []; }"}, {"name": "Variable Assignment Without Type Check", "pattern": "\\$\\w+\\s*=\\s*\\$\\w+Map\\[\\$\\w+\\](?!\\s*\\?\\?)", "message": "从映射数组获取值时建议使用 ?? 操作符", "severity": "warning", "example": "$user = $userMap[$uid] ?? [];"}]}