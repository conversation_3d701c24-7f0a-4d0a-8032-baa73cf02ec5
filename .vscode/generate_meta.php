<?php
/**
 * 自动生成代码跳转映射文件
 * 使用方法: php .vscode/generate_meta.php
 */

// 配置项
$modelDirs = [
    __DIR__ . '/../app/m/',
    __DIR__ . '/../app/m/user/',
    __DIR__ . '/../app/m/comm/',
    __DIR__ . '/../app/m/admin/',
    __DIR__ . '/../app/m/rw/',
    __DIR__ . '/../app/m/qian/',
    __DIR__ . '/../app/m/queue/',
    __DIR__ . '/../app/m/utils/',
    __DIR__ . '/../ju/lib/',
    __DIR__ . '/../ju/model/',
    // 添加其他模型目录...
];

$outputFile = __DIR__ . '/../.phpstorm.meta.php';

// 收集所有模型类
$models = [];

// 递归扫描目录中的模型文件
function scanModelDir($dir, &$models) {
    if (!is_dir($dir)) return;
    
    $files = glob($dir . '*.php');
    foreach ($files as $file) {
        $contents = file_get_contents($file);
        
        // 检查文件是否包含类定义
        if (preg_match('/class\s+(\w+)\s+(?:extends\s+\w+\s*)?{/i', $contents, $matches)) {
            $className = $matches[1];
            // 检查是否为模型类 (以m_开头或包含model关键字)
            if (strpos($className, 'm_') === 0 || 
                strpos($className, 'Model') !== false || 
                strpos(strtolower($className), 'model') !== false) {
                $models[$className] = $className;
            }
        }
    }
    
    // 递归扫描子目录
    $subDirs = glob($dir . '*', GLOB_ONLYDIR);
    foreach ($subDirs as $subDir) {
        scanModelDir($subDir . '/', $models);
    }
}

// 扫描所有目录
foreach ($modelDirs as $dir) {
    scanModelDir($dir, $models);
}

// 添加主要的系统类
$models['db'] = 'db';
// $models['m_base'] = 'm_base';

// 生成meta文件
$metaContent = '<?php
/**
 * 自动生成的类型映射文件
 * 生成时间: ' . date('Y-m-d H:i:s') . '
 * 请勿手动修改
 */

namespace PHPSTORM_META {
    // D() 函数返回类型映射
    override(\D(0), map([';

foreach ($models as $className => $classPath) {
    $metaContent .= "\n        '$className' => \\$className::class,";
}

$metaContent .= '
    ]));
    
    // M() 函数映射
    override(\M(0), map([
        "" => \db::class,
    ]));
    
    // M()->方法映射
    override(\db::update(0), type(1));
    override(\db::insert(0), type(1));
    override(\db::delete(0), type(1));
    override(\db::select(0), type(1));
    override(\db::get(0), type(1));
    
    // MM() 函数映射
    override(\MM(0), map([
        "" => \db::class,
    ]));
}
';

// 写入文件
file_put_contents($outputFile, $metaContent);

echo "已生成类型映射文件: $outputFile\n";
echo "找到 " . count($models) . " 个模型类\n"; 